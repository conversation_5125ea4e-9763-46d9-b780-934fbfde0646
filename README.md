# FitScanAI - 智能营养分析 iOS 应用

## 项目简介

FitScanAI 是一款基于 SwiftUI 开发的智能营养分析 iOS 应用，通过拍照识别食物并提供详细的营养成分分析。应用集成了用户认证、营养分析、健康计划等功能，为用户提供个性化的健康管理服务。

## 主要功能

### 🔐 用户认证系统
- **邮箱注册/登录**：支持邮箱验证码注册和登录
- **第三方登录**：集成 Apple ID 和 Google 账号登录
- **忘记密码**：支持邮箱验证码重置密码
- **会话管理**：完善的 CSRF Token 和 JSESSIONID 管理机制

### 📸 智能食物识别
- **拍照识别**：使用相机拍摄食物照片进行识别
- **相册选择**：从相册中选择食物图片进行分析
- **营养分析**：AI 智能分析食物营养成分

### 📊 健康管理
- **体重记录**：记录和追踪体重变化
- **目标设定**：设置个人健康目标
- **数据可视化**：图表展示健康数据趋势

### 👤 个人中心
- **个人资料**：头像上传、个人信息管理
- **账户设置**：密码修改、账户删除等
- **数据同步**：云端数据同步和备份

## 技术架构

### 开发环境
- **开发语言**：Swift 5.0
- **UI 框架**：SwiftUI
- **最低系统版本**：iOS 17.0
- **开发工具**：Xcode 16.3

### 核心依赖
- **GoogleSignIn (8.0.0)**：Google 账号登录
- **AppAuth (1.7.6)**：OAuth 认证流程
- **StoreKit**：应用内购买功能

### 网络架构
- **RESTful API**：基于 HTTPS 的 API 接口
- **会话管理**：CSRF Token + JSESSIONID 双重验证
- **数据格式**：JSON 数据交换格式

## 核心技术实现

### 会话管理优化
在开发过程中，我们发现并解决了关键的会话一致性问题：

**问题描述**：
- 发送验证码时使用强制刷新的 JSESSIONID
- 验证验证码时可能使用缓存的旧 JSESSIONID
- 导致验证码验证失败，提示"无效的号码或验证码"

**解决方案**：
1. **统一会话管理**：确保验证码发送和验证使用相同的 JSESSIONID
2. **时序优化**：改进异步会话重置逻辑，避免竞态条件
3. **详细日志**：添加会话状态跟踪日志，便于调试

### 网络服务架构
```swift
class NetworkService {
    // CSRF Token 和 JSESSIONID 管理
    private var jsessionId: String?
    private var csrfToken: String?
    
    // 确保会话一致性
    func ensureCSRFToken(forceRefresh: Bool, completion: @escaping (Bool) -> Void)
    
    // 重置会话状态（支持回调）
    func resetSessionState(completion: @escaping (Bool) -> Void)
}
```

### 用户界面设计
- **现代化 UI**：遵循 iOS 设计规范，提供直观的用户体验
- **响应式布局**：适配不同屏幕尺寸的 iPhone 设备
- **流畅动画**：使用 SwiftUI 动画提升交互体验

## 项目结构

```
FitScanAI/
├── FitScanAI/
│   ├── App/
│   │   ├── FitScanAIApp.swift          # 应用入口
│   │   └── ContentView.swift           # 主视图
│   ├── Views/
│   │   ├── Auth/
│   │   │   ├── LoginView.swift         # 登录页面
│   │   │   ├── SignUpView.swift        # 注册页面
│   │   │   ├── CreateAccountView.swift # 创建账户
│   │   │   └── ResetPasswordView.swift # 重置密码
│   │   ├── Onboarding/
│   │   │   ├── WelcomeView.swift       # 欢迎页面
│   │   │   ├── OnboardingView.swift    # 引导页面
│   │   │   └── SplashScreenView.swift  # 启动页面
│   │   └── Main/
│   │       ├── WeightView.swift        # 体重管理
│   │       └── PlanView.swift          # 健康计划
│   ├── Services/
│   │   ├── NetworkService.swift        # 网络服务
│   │   ├── UserService.swift           # 用户服务
│   │   ├── ImageUploadService.swift    # 图片上传
│   │   ├── GoogleAuthService.swift     # Google 认证
│   │   └── AppleAuthService.swift      # Apple 认证
│   ├── Models/
│   │   └── UserData.swift              # 用户数据模型
│   ├── Utils/
│   │   ├── KeychainHelper.swift        # 钥匙串管理
│   │   ├── Extensions.swift            # 扩展方法
│   │   └── SharedComponents.swift      # 共享组件
│   └── Resources/
│       ├── Assets.xcassets/            # 图片资源
│       ├── Info.plist                  # 应用配置
│       └── GoogleService-Info.plist    # Google 服务配置
├── FitScanAITests/                     # 单元测试
├── FitScanAIUITests/                   # UI 测试
└── FitScanAI.xcodeproj/               # Xcode 项目文件
```

## 安全特性

### 数据安全
- **HTTPS 通信**：所有网络请求使用 HTTPS 加密
- **Token 管理**：安全的 CSRF Token 和会话管理
- **钥匙串存储**：敏感信息存储在系统钥匙串中

### 隐私保护
- **权限管理**：相机和相册访问权限控制
- **数据最小化**：仅收集必要的用户数据
- **本地处理**：敏感数据优先本地处理

## 开发亮点

### 1. 会话一致性修复
通过深入分析网络请求流程，发现并修复了验证码验证失败的根本原因，确保了用户注册和密码重置功能的稳定性。

### 2. 异步编程优化
使用 Swift 的现代异步编程模式，优化了网络请求的时序控制，避免了竞态条件。

### 3. 错误处理机制
实现了完善的错误处理和用户反馈机制，提供清晰的错误提示和恢复建议。

### 4. 代码质量
- 遵循 Swift 编码规范
- 详细的代码注释和日志
- 模块化的架构设计

## 安装和运行

### 环境要求
- macOS 14.0+
- Xcode 16.0+
- iOS 17.0+ 设备或模拟器

### 运行步骤
1. 克隆项目到本地
```bash
git clone [repository-url]
cd FitScanAI
```

2. 打开 Xcode 项目
```bash
open FitScanAI.xcodeproj
```

3. 配置开发者账号和证书

4. 选择目标设备并运行

## 贡献指南

欢迎提交 Issue 和 Pull Request 来改进项目。在提交代码前，请确保：

1. 代码符合项目的编码规范
2. 添加必要的测试用例
3. 更新相关文档

## 许可证

本项目采用 MIT 许可证，详见 LICENSE 文件。

## 联系方式

如有问题或建议，请通过以下方式联系：

- 项目 Issues：[GitHub Issues](link-to-issues)
- 邮箱：[<EMAIL>]

---

**FitScanAI** - 让健康管理更智能，让生活更美好！ 🌟 