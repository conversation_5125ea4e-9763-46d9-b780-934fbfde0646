import Foundation
#if canImport(FoundationNetworking)
import FoundationNetworking
#endif

var semaphore = DispatchSemaphore (value: 0)

let parameters = "{\n    \"id\": 0,\n    \"username\": \"string\",\n    \"mobile\": \"string\",\n    \"email\": \"string\",\n    \"password\": \"string\",\n    \"name\": \"string\",\n    \"nickname\": \"string\",\n    \"description\": \"string\",\n    \"code\": \"string\",\n    \"userGroups\": [\n        0\n    ],\n    \"departments\": [\n        0\n    ],\n    \"clientType\": \"string\",\n    \"activity\": \"string\",\n    \"activityCode\": \"string\"\n}"
let postData = parameters.data(using: .utf8)

var request = URLRequest(url: URL(string: "https://cpsp-oauth.techrightcloud.cn/api/email-register")!,timeoutInterval: Double.infinity)
request.addValue("application/json", forHT<PERSON><PERSON>eaderField: "Content-Type")

request.httpMethod = "POST"
request.httpBody = postData

let task = URLSession.shared.dataTask(with: request) { data, response, error in 
   guard let data = data else {
      print(String(describing: error))
      semaphore.signal()
      return
   }
   print(String(data: data, encoding: .utf8)!)
   semaphore.signal()
}

task.resume()
semaphore.wait()