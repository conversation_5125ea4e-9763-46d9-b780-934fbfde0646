// !$*UTF8*$!
{
	archiveVersion = 1;
	classes = {
	};
	objectVersion = 77;
	objects = {

/* Begin PBXBuildFile section */
		3B9A62A12E333B850022E438 /* FirebaseAnalyticsWithoutAdIdSupport in Frameworks */ = {isa = PBXBuildFile; productRef = 3B9A62A02E333B850022E438 /* FirebaseAnalyticsWithoutAdIdSupport */; };
		3BA399E32DE5495A00436460 /* StoreKit.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = 3BA399E22DE5495A00436460 /* StoreKit.framework */; };
		3BB88F902E16159300B6E465 /* GoogleSignIn in Frameworks */ = {isa = PBXBuildFile; productRef = 3BB88F8F2E16159300B6E465 /* GoogleSignIn */; };
		3BB88F922E16159300B6E465 /* GoogleSignInSwift in Frameworks */ = {isa = PBXBuildFile; productRef = 3BB88F912E16159300B6E465 /* GoogleSignInSwift */; };
		D02062422E4912DC00D8DB16 /* AppsFlyerLib in Frameworks */ = {isa = PBXBuildFile; productRef = D02062412E4912DC00D8DB16 /* AppsFlyerLib */; };
/* End PBXBuildFile section */

/* Begin PBXContainerItemProxy section */
		3B8B6FB32DD5F909008CC6C4 /* PBXContainerItemProxy */ = {
			isa = PBXContainerItemProxy;
			containerPortal = 3B8B6F9D2DD5F907008CC6C4 /* Project object */;
			proxyType = 1;
			remoteGlobalIDString = 3B8B6FA42DD5F907008CC6C4;
			remoteInfo = FitScanAI;
		};
		3B8B6FBD2DD5F909008CC6C4 /* PBXContainerItemProxy */ = {
			isa = PBXContainerItemProxy;
			containerPortal = 3B8B6F9D2DD5F907008CC6C4 /* Project object */;
			proxyType = 1;
			remoteGlobalIDString = 3B8B6FA42DD5F907008CC6C4;
			remoteInfo = FitScanAI;
		};
/* End PBXContainerItemProxy section */

/* Begin PBXFileReference section */
		3B8B6FA52DD5F907008CC6C4 /* FitScanAI.app */ = {isa = PBXFileReference; explicitFileType = wrapper.application; includeInIndex = 0; path = FitScanAI.app; sourceTree = BUILT_PRODUCTS_DIR; };
		3B8B6FB22DD5F909008CC6C4 /* FitScanAITests.xctest */ = {isa = PBXFileReference; explicitFileType = wrapper.cfbundle; includeInIndex = 0; path = FitScanAITests.xctest; sourceTree = BUILT_PRODUCTS_DIR; };
		3B8B6FBC2DD5F909008CC6C4 /* FitScanAIUITests.xctest */ = {isa = PBXFileReference; explicitFileType = wrapper.cfbundle; includeInIndex = 0; path = FitScanAIUITests.xctest; sourceTree = BUILT_PRODUCTS_DIR; };
		3BA399E22DE5495A00436460 /* StoreKit.framework */ = {isa = PBXFileReference; lastKnownFileType = wrapper.framework; name = StoreKit.framework; path = System/Library/Frameworks/StoreKit.framework; sourceTree = SDKROOT; };
/* End PBXFileReference section */

/* Begin PBXFileSystemSynchronizedBuildFileExceptionSet section */
		3B8B702C2DD5FBB2008CC6C4 /* Exceptions for "FitScanAI" folder in "FitScanAI" target */ = {
			isa = PBXFileSystemSynchronizedBuildFileExceptionSet;
			membershipExceptions = (
				Info.plist,
			);
			target = 3B8B6FA42DD5F907008CC6C4 /* FitScanAI */;
		};
/* End PBXFileSystemSynchronizedBuildFileExceptionSet section */

/* Begin PBXFileSystemSynchronizedRootGroup section */
		3B8B6FA72DD5F907008CC6C4 /* FitScanAI */ = {
			isa = PBXFileSystemSynchronizedRootGroup;
			exceptions = (
				3B8B702C2DD5FBB2008CC6C4 /* Exceptions for "FitScanAI" folder in "FitScanAI" target */,
			);
			path = FitScanAI;
			sourceTree = "<group>";
		};
		3B8B6FB52DD5F909008CC6C4 /* FitScanAITests */ = {
			isa = PBXFileSystemSynchronizedRootGroup;
			path = FitScanAITests;
			sourceTree = "<group>";
		};
		3B8B6FBF2DD5F909008CC6C4 /* FitScanAIUITests */ = {
			isa = PBXFileSystemSynchronizedRootGroup;
			path = FitScanAIUITests;
			sourceTree = "<group>";
		};
/* End PBXFileSystemSynchronizedRootGroup section */

/* Begin PBXFrameworksBuildPhase section */
		3B8B6FA22DD5F907008CC6C4 /* Frameworks */ = {
			isa = PBXFrameworksBuildPhase;
			buildActionMask = 2147483647;
			files = (
				3BB88F922E16159300B6E465 /* GoogleSignInSwift in Frameworks */,
				D02062422E4912DC00D8DB16 /* AppsFlyerLib in Frameworks */,
				3BA399E32DE5495A00436460 /* StoreKit.framework in Frameworks */,
				3BB88F902E16159300B6E465 /* GoogleSignIn in Frameworks */,
				3B9A62A12E333B850022E438 /* FirebaseAnalyticsWithoutAdIdSupport in Frameworks */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		3B8B6FAF2DD5F909008CC6C4 /* Frameworks */ = {
			isa = PBXFrameworksBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		3B8B6FB92DD5F909008CC6C4 /* Frameworks */ = {
			isa = PBXFrameworksBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXFrameworksBuildPhase section */

/* Begin PBXGroup section */
		3B8B6F9C2DD5F907008CC6C4 = {
			isa = PBXGroup;
			children = (
				3B8B6FA72DD5F907008CC6C4 /* FitScanAI */,
				3B8B6FB52DD5F909008CC6C4 /* FitScanAITests */,
				3B8B6FBF2DD5F909008CC6C4 /* FitScanAIUITests */,
				3BA399E12DE5495A00436460 /* Frameworks */,
				3B8B6FA62DD5F907008CC6C4 /* Products */,
			);
			sourceTree = "<group>";
		};
		3B8B6FA62DD5F907008CC6C4 /* Products */ = {
			isa = PBXGroup;
			children = (
				3B8B6FA52DD5F907008CC6C4 /* FitScanAI.app */,
				3B8B6FB22DD5F909008CC6C4 /* FitScanAITests.xctest */,
				3B8B6FBC2DD5F909008CC6C4 /* FitScanAIUITests.xctest */,
			);
			name = Products;
			sourceTree = "<group>";
		};
		3BA399E12DE5495A00436460 /* Frameworks */ = {
			isa = PBXGroup;
			children = (
				3BA399E22DE5495A00436460 /* StoreKit.framework */,
			);
			name = Frameworks;
			sourceTree = "<group>";
		};
/* End PBXGroup section */

/* Begin PBXNativeTarget section */
		3B8B6FA42DD5F907008CC6C4 /* FitScanAI */ = {
			isa = PBXNativeTarget;
			buildConfigurationList = 3B8B6FC62DD5F909008CC6C4 /* Build configuration list for PBXNativeTarget "FitScanAI" */;
			buildPhases = (
				3B8B6FA12DD5F907008CC6C4 /* Sources */,
				3B8B6FA22DD5F907008CC6C4 /* Frameworks */,
				3B8B6FA32DD5F907008CC6C4 /* Resources */,
			);
			buildRules = (
			);
			dependencies = (
			);
			fileSystemSynchronizedGroups = (
				3B8B6FA72DD5F907008CC6C4 /* FitScanAI */,
			);
			name = FitScanAI;
			packageProductDependencies = (
				3BB88F8F2E16159300B6E465 /* GoogleSignIn */,
				3BB88F912E16159300B6E465 /* GoogleSignInSwift */,
				3B9A62A02E333B850022E438 /* FirebaseAnalyticsWithoutAdIdSupport */,
				D02062412E4912DC00D8DB16 /* AppsFlyerLib */,
			);
			productName = FitScanAI;
			productReference = 3B8B6FA52DD5F907008CC6C4 /* FitScanAI.app */;
			productType = "com.apple.product-type.application";
		};
		3B8B6FB12DD5F909008CC6C4 /* FitScanAITests */ = {
			isa = PBXNativeTarget;
			buildConfigurationList = 3B8B6FC92DD5F909008CC6C4 /* Build configuration list for PBXNativeTarget "FitScanAITests" */;
			buildPhases = (
				3B8B6FAE2DD5F909008CC6C4 /* Sources */,
				3B8B6FAF2DD5F909008CC6C4 /* Frameworks */,
				3B8B6FB02DD5F909008CC6C4 /* Resources */,
			);
			buildRules = (
			);
			dependencies = (
				3B8B6FB42DD5F909008CC6C4 /* PBXTargetDependency */,
			);
			fileSystemSynchronizedGroups = (
				3B8B6FB52DD5F909008CC6C4 /* FitScanAITests */,
			);
			name = FitScanAITests;
			packageProductDependencies = (
			);
			productName = FitScanAITests;
			productReference = 3B8B6FB22DD5F909008CC6C4 /* FitScanAITests.xctest */;
			productType = "com.apple.product-type.bundle.unit-test";
		};
		3B8B6FBB2DD5F909008CC6C4 /* FitScanAIUITests */ = {
			isa = PBXNativeTarget;
			buildConfigurationList = 3B8B6FCC2DD5F909008CC6C4 /* Build configuration list for PBXNativeTarget "FitScanAIUITests" */;
			buildPhases = (
				3B8B6FB82DD5F909008CC6C4 /* Sources */,
				3B8B6FB92DD5F909008CC6C4 /* Frameworks */,
				3B8B6FBA2DD5F909008CC6C4 /* Resources */,
			);
			buildRules = (
			);
			dependencies = (
				3B8B6FBE2DD5F909008CC6C4 /* PBXTargetDependency */,
			);
			fileSystemSynchronizedGroups = (
				3B8B6FBF2DD5F909008CC6C4 /* FitScanAIUITests */,
			);
			name = FitScanAIUITests;
			packageProductDependencies = (
			);
			productName = FitScanAIUITests;
			productReference = 3B8B6FBC2DD5F909008CC6C4 /* FitScanAIUITests.xctest */;
			productType = "com.apple.product-type.bundle.ui-testing";
		};
/* End PBXNativeTarget section */

/* Begin PBXProject section */
		3B8B6F9D2DD5F907008CC6C4 /* Project object */ = {
			isa = PBXProject;
			attributes = {
				BuildIndependentTargetsInParallel = 1;
				LastSwiftUpdateCheck = 1630;
				LastUpgradeCheck = 1630;
				TargetAttributes = {
					3B8B6FA42DD5F907008CC6C4 = {
						CreatedOnToolsVersion = 16.3;
					};
					3B8B6FB12DD5F909008CC6C4 = {
						CreatedOnToolsVersion = 16.3;
						TestTargetID = 3B8B6FA42DD5F907008CC6C4;
					};
					3B8B6FBB2DD5F909008CC6C4 = {
						CreatedOnToolsVersion = 16.3;
						TestTargetID = 3B8B6FA42DD5F907008CC6C4;
					};
				};
			};
			buildConfigurationList = 3B8B6FA02DD5F907008CC6C4 /* Build configuration list for PBXProject "FitScanAI" */;
			developmentRegion = en;
			hasScannedForEncodings = 0;
			knownRegions = (
				en,
				Base,
				"zh-Hans",
			);
			mainGroup = 3B8B6F9C2DD5F907008CC6C4;
			minimizedProjectReferenceProxies = 1;
			packageReferences = (
				3BB88F8E2E16159200B6E465 /* XCRemoteSwiftPackageReference "GoogleSignIn-iOS" */,
				3B9A629F2E333B850022E438 /* XCRemoteSwiftPackageReference "firebase-ios-sdk" */,
				D02062402E4912DC00D8DB16 /* XCRemoteSwiftPackageReference "AppsFlyerFramework" */,
			);
			preferredProjectObjectVersion = 77;
			productRefGroup = 3B8B6FA62DD5F907008CC6C4 /* Products */;
			projectDirPath = "";
			projectRoot = "";
			targets = (
				3B8B6FA42DD5F907008CC6C4 /* FitScanAI */,
				3B8B6FB12DD5F909008CC6C4 /* FitScanAITests */,
				3B8B6FBB2DD5F909008CC6C4 /* FitScanAIUITests */,
			);
		};
/* End PBXProject section */

/* Begin PBXResourcesBuildPhase section */
		3B8B6FA32DD5F907008CC6C4 /* Resources */ = {
			isa = PBXResourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		3B8B6FB02DD5F909008CC6C4 /* Resources */ = {
			isa = PBXResourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		3B8B6FBA2DD5F909008CC6C4 /* Resources */ = {
			isa = PBXResourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXResourcesBuildPhase section */

/* Begin PBXSourcesBuildPhase section */
		3B8B6FA12DD5F907008CC6C4 /* Sources */ = {
			isa = PBXSourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		3B8B6FAE2DD5F909008CC6C4 /* Sources */ = {
			isa = PBXSourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		3B8B6FB82DD5F909008CC6C4 /* Sources */ = {
			isa = PBXSourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXSourcesBuildPhase section */

/* Begin PBXTargetDependency section */
		3B8B6FB42DD5F909008CC6C4 /* PBXTargetDependency */ = {
			isa = PBXTargetDependency;
			target = 3B8B6FA42DD5F907008CC6C4 /* FitScanAI */;
			targetProxy = 3B8B6FB32DD5F909008CC6C4 /* PBXContainerItemProxy */;
		};
		3B8B6FBE2DD5F909008CC6C4 /* PBXTargetDependency */ = {
			isa = PBXTargetDependency;
			target = 3B8B6FA42DD5F907008CC6C4 /* FitScanAI */;
			targetProxy = 3B8B6FBD2DD5F909008CC6C4 /* PBXContainerItemProxy */;
		};
/* End PBXTargetDependency section */

/* Begin XCBuildConfiguration section */
		3B8B6FC42DD5F909008CC6C4 /* Debug */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ALWAYS_SEARCH_USER_PATHS = NO;
				ASSETCATALOG_COMPILER_GENERATE_SWIFT_ASSET_SYMBOL_EXTENSIONS = YES;
				CLANG_ANALYZER_NONNULL = YES;
				CLANG_ANALYZER_NUMBER_OBJECT_CONVERSION = YES_AGGRESSIVE;
				CLANG_CXX_LANGUAGE_STANDARD = "gnu++20";
				CLANG_ENABLE_MODULES = YES;
				CLANG_ENABLE_OBJC_ARC = YES;
				CLANG_ENABLE_OBJC_WEAK = YES;
				CLANG_WARN_BLOCK_CAPTURE_AUTORELEASING = YES;
				CLANG_WARN_BOOL_CONVERSION = YES;
				CLANG_WARN_COMMA = YES;
				CLANG_WARN_CONSTANT_CONVERSION = YES;
				CLANG_WARN_DEPRECATED_OBJC_IMPLEMENTATIONS = YES;
				CLANG_WARN_DIRECT_OBJC_ISA_USAGE = YES_ERROR;
				CLANG_WARN_DOCUMENTATION_COMMENTS = YES;
				CLANG_WARN_EMPTY_BODY = YES;
				CLANG_WARN_ENUM_CONVERSION = YES;
				CLANG_WARN_INFINITE_RECURSION = YES;
				CLANG_WARN_INT_CONVERSION = YES;
				CLANG_WARN_NON_LITERAL_NULL_CONVERSION = YES;
				CLANG_WARN_OBJC_IMPLICIT_RETAIN_SELF = YES;
				CLANG_WARN_OBJC_LITERAL_CONVERSION = YES;
				CLANG_WARN_OBJC_ROOT_CLASS = YES_ERROR;
				CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER = YES;
				CLANG_WARN_RANGE_LOOP_ANALYSIS = YES;
				CLANG_WARN_STRICT_PROTOTYPES = YES;
				CLANG_WARN_SUSPICIOUS_MOVE = YES;
				CLANG_WARN_UNGUARDED_AVAILABILITY = YES_AGGRESSIVE;
				CLANG_WARN_UNREACHABLE_CODE = YES;
				CLANG_WARN__DUPLICATE_METHOD_MATCH = YES;
				COPY_PHASE_STRIP = NO;
				DEBUG_INFORMATION_FORMAT = dwarf;
				DEVELOPMENT_TEAM = B64Y6DZ7B7;
				ENABLE_STRICT_OBJC_MSGSEND = YES;
				ENABLE_TESTABILITY = YES;
				ENABLE_USER_SCRIPT_SANDBOXING = YES;
				GCC_C_LANGUAGE_STANDARD = gnu17;
				GCC_DYNAMIC_NO_PIC = NO;
				GCC_NO_COMMON_BLOCKS = YES;
				GCC_OPTIMIZATION_LEVEL = 0;
				GCC_PREPROCESSOR_DEFINITIONS = (
					"DEBUG=1",
					"$(inherited)",
				);
				GCC_WARN_64_TO_32_BIT_CONVERSION = YES;
				GCC_WARN_ABOUT_RETURN_TYPE = YES_ERROR;
				GCC_WARN_UNDECLARED_SELECTOR = YES;
				GCC_WARN_UNINITIALIZED_AUTOS = YES_AGGRESSIVE;
				GCC_WARN_UNUSED_FUNCTION = YES;
				GCC_WARN_UNUSED_VARIABLE = YES;
				IPHONEOS_DEPLOYMENT_TARGET = 18.4;
				LOCALIZATION_PREFERS_STRING_CATALOGS = YES;
				MTL_ENABLE_DEBUG_INFO = INCLUDE_SOURCE;
				MTL_FAST_MATH = YES;
				ONLY_ACTIVE_ARCH = YES;
				SDKROOT = iphoneos;
				SWIFT_ACTIVE_COMPILATION_CONDITIONS = "DEBUG $(inherited)";
				SWIFT_OPTIMIZATION_LEVEL = "-Onone";
			};
			name = Debug;
		};
		3B8B6FC52DD5F909008CC6C4 /* Release */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ALWAYS_SEARCH_USER_PATHS = NO;
				ASSETCATALOG_COMPILER_GENERATE_SWIFT_ASSET_SYMBOL_EXTENSIONS = YES;
				CLANG_ANALYZER_NONNULL = YES;
				CLANG_ANALYZER_NUMBER_OBJECT_CONVERSION = YES_AGGRESSIVE;
				CLANG_CXX_LANGUAGE_STANDARD = "gnu++20";
				CLANG_ENABLE_MODULES = YES;
				CLANG_ENABLE_OBJC_ARC = YES;
				CLANG_ENABLE_OBJC_WEAK = YES;
				CLANG_WARN_BLOCK_CAPTURE_AUTORELEASING = YES;
				CLANG_WARN_BOOL_CONVERSION = YES;
				CLANG_WARN_COMMA = YES;
				CLANG_WARN_CONSTANT_CONVERSION = YES;
				CLANG_WARN_DEPRECATED_OBJC_IMPLEMENTATIONS = YES;
				CLANG_WARN_DIRECT_OBJC_ISA_USAGE = YES_ERROR;
				CLANG_WARN_DOCUMENTATION_COMMENTS = YES;
				CLANG_WARN_EMPTY_BODY = YES;
				CLANG_WARN_ENUM_CONVERSION = YES;
				CLANG_WARN_INFINITE_RECURSION = YES;
				CLANG_WARN_INT_CONVERSION = YES;
				CLANG_WARN_NON_LITERAL_NULL_CONVERSION = YES;
				CLANG_WARN_OBJC_IMPLICIT_RETAIN_SELF = YES;
				CLANG_WARN_OBJC_LITERAL_CONVERSION = YES;
				CLANG_WARN_OBJC_ROOT_CLASS = YES_ERROR;
				CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER = YES;
				CLANG_WARN_RANGE_LOOP_ANALYSIS = YES;
				CLANG_WARN_STRICT_PROTOTYPES = YES;
				CLANG_WARN_SUSPICIOUS_MOVE = YES;
				CLANG_WARN_UNGUARDED_AVAILABILITY = YES_AGGRESSIVE;
				CLANG_WARN_UNREACHABLE_CODE = YES;
				CLANG_WARN__DUPLICATE_METHOD_MATCH = YES;
				COPY_PHASE_STRIP = NO;
				DEBUG_INFORMATION_FORMAT = "dwarf-with-dsym";
				DEVELOPMENT_TEAM = B64Y6DZ7B7;
				ENABLE_NS_ASSERTIONS = NO;
				ENABLE_STRICT_OBJC_MSGSEND = YES;
				ENABLE_USER_SCRIPT_SANDBOXING = YES;
				GCC_C_LANGUAGE_STANDARD = gnu17;
				GCC_NO_COMMON_BLOCKS = YES;
				GCC_WARN_64_TO_32_BIT_CONVERSION = YES;
				GCC_WARN_ABOUT_RETURN_TYPE = YES_ERROR;
				GCC_WARN_UNDECLARED_SELECTOR = YES;
				GCC_WARN_UNINITIALIZED_AUTOS = YES_AGGRESSIVE;
				GCC_WARN_UNUSED_FUNCTION = YES;
				GCC_WARN_UNUSED_VARIABLE = YES;
				IPHONEOS_DEPLOYMENT_TARGET = 18.4;
				LOCALIZATION_PREFERS_STRING_CATALOGS = YES;
				MTL_ENABLE_DEBUG_INFO = NO;
				MTL_FAST_MATH = YES;
				SDKROOT = iphoneos;
				SWIFT_COMPILATION_MODE = wholemodule;
				VALIDATE_PRODUCT = YES;
			};
			name = Release;
		};
		3B8B6FC72DD5F909008CC6C4 /* Debug */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ASSETCATALOG_COMPILER_APPICON_NAME = AppIcon;
				ASSETCATALOG_COMPILER_GLOBAL_ACCENT_COLOR_NAME = AccentColor;
				ASSETCATALOG_COMPILER_INCLUDE_ALL_APPICON_ASSETS = NO;
				CODE_SIGN_ENTITLEMENTS = FitScanAI/FitScanAI.entitlements;
				CODE_SIGN_STYLE = Automatic;
				CURRENT_PROJECT_VERSION = 1;
				DEVELOPMENT_TEAM = 53XM8XABBT;
				ENABLE_PREVIEWS = YES;
				GENERATE_INFOPLIST_FILE = YES;
				INFOPLIST_FILE = FitScanAI/Info.plist;
				INFOPLIST_KEY_CFBundleDisplayName = FitScanAI;
				INFOPLIST_KEY_ITSAppUsesNonExemptEncryption = NO;
				INFOPLIST_KEY_LSApplicationCategoryType = "public.app-category.healthcare-fitness";
				INFOPLIST_KEY_NSCameraUsageDescription = "FitScanAI uses your camera to take photos of food for AI-powered nutrition analysis. This helps you track your diet and make healthier food choices.";
				INFOPLIST_KEY_NSPhotoLibraryAddUsageDescription = "FitScanAI needs access to save analyzed food photos to your photo library for your records.";
				INFOPLIST_KEY_NSPhotoLibraryUsageDescription = "FitScanAI needs access to your photo library to let you select food images for nutrition analysis and to set your profile picture. We only access the specific photos you choose to share with the app.";
				INFOPLIST_KEY_UIApplicationSceneManifest_Generation = YES;
				INFOPLIST_KEY_UIApplicationSupportsIndirectInputEvents = YES;
				INFOPLIST_KEY_UILaunchScreen_Generation = YES;
				INFOPLIST_KEY_UISupportedInterfaceOrientations = "UIInterfaceOrientationLandscapeLeft UIInterfaceOrientationLandscapeRight UIInterfaceOrientationPortrait";
				INFOPLIST_KEY_UISupportedInterfaceOrientations_iPad = "UIInterfaceOrientationLandscapeLeft UIInterfaceOrientationLandscapeRight UIInterfaceOrientationPortrait UIInterfaceOrientationPortraitUpsideDown";
				INFOPLIST_KEY_UIUserInterfaceStyle = Light;
				IPHONEOS_DEPLOYMENT_TARGET = 17.0;
				LD_RUNPATH_SEARCH_PATHS = (
					"$(inherited)",
					"@executable_path/Frameworks",
				);
				MARKETING_VERSION = 1.3;
				PRODUCT_BUNDLE_IDENTIFIER = cc.littlegrass.fitscanai;
				PRODUCT_NAME = "$(TARGET_NAME)";
				SUPPORTED_PLATFORMS = "iphoneos iphonesimulator";
				SUPPORTS_MACCATALYST = NO;
				SUPPORTS_MAC_DESIGNED_FOR_IPHONE_IPAD = NO;
				SUPPORTS_XR_DESIGNED_FOR_IPHONE_IPAD = NO;
				SWIFT_EMIT_LOC_STRINGS = YES;
				SWIFT_VERSION = 5.0;
				TARGETED_DEVICE_FAMILY = 1;
			};
			name = Debug;
		};
		3B8B6FC82DD5F909008CC6C4 /* Release */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ASSETCATALOG_COMPILER_APPICON_NAME = AppIcon;
				ASSETCATALOG_COMPILER_GLOBAL_ACCENT_COLOR_NAME = AccentColor;
				ASSETCATALOG_COMPILER_INCLUDE_ALL_APPICON_ASSETS = NO;
				CODE_SIGN_ENTITLEMENTS = FitScanAI/FitScanAI.entitlements;
				CODE_SIGN_STYLE = Automatic;
				CURRENT_PROJECT_VERSION = 1;
				DEVELOPMENT_TEAM = 53XM8XABBT;
				ENABLE_PREVIEWS = YES;
				GENERATE_INFOPLIST_FILE = YES;
				INFOPLIST_FILE = FitScanAI/Info.plist;
				INFOPLIST_KEY_CFBundleDisplayName = FitScanAI;
				INFOPLIST_KEY_ITSAppUsesNonExemptEncryption = NO;
				INFOPLIST_KEY_LSApplicationCategoryType = "public.app-category.healthcare-fitness";
				INFOPLIST_KEY_NSCameraUsageDescription = "FitScanAI uses your camera to take photos of food for AI-powered nutrition analysis. This helps you track your diet and make healthier food choices.";
				INFOPLIST_KEY_NSPhotoLibraryAddUsageDescription = "FitScanAI needs access to save analyzed food photos to your photo library for your records.";
				INFOPLIST_KEY_NSPhotoLibraryUsageDescription = "FitScanAI needs access to your photo library to let you select food images for nutrition analysis and to set your profile picture. We only access the specific photos you choose to share with the app.";
				INFOPLIST_KEY_UIApplicationSceneManifest_Generation = YES;
				INFOPLIST_KEY_UIApplicationSupportsIndirectInputEvents = YES;
				INFOPLIST_KEY_UILaunchScreen_Generation = YES;
				INFOPLIST_KEY_UISupportedInterfaceOrientations = "UIInterfaceOrientationLandscapeLeft UIInterfaceOrientationLandscapeRight UIInterfaceOrientationPortrait";
				INFOPLIST_KEY_UISupportedInterfaceOrientations_iPad = "UIInterfaceOrientationLandscapeLeft UIInterfaceOrientationLandscapeRight UIInterfaceOrientationPortrait UIInterfaceOrientationPortraitUpsideDown";
				INFOPLIST_KEY_UIUserInterfaceStyle = Light;
				IPHONEOS_DEPLOYMENT_TARGET = 17.0;
				LD_RUNPATH_SEARCH_PATHS = (
					"$(inherited)",
					"@executable_path/Frameworks",
				);
				MARKETING_VERSION = 1.3;
				PRODUCT_BUNDLE_IDENTIFIER = cc.littlegrass.fitscanai;
				PRODUCT_NAME = "$(TARGET_NAME)";
				SUPPORTED_PLATFORMS = "iphoneos iphonesimulator";
				SUPPORTS_MACCATALYST = NO;
				SUPPORTS_MAC_DESIGNED_FOR_IPHONE_IPAD = NO;
				SUPPORTS_XR_DESIGNED_FOR_IPHONE_IPAD = NO;
				SWIFT_EMIT_LOC_STRINGS = YES;
				SWIFT_VERSION = 5.0;
				TARGETED_DEVICE_FAMILY = 1;
			};
			name = Release;
		};
		3B8B6FCA2DD5F909008CC6C4 /* Debug */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				BUNDLE_LOADER = "$(TEST_HOST)";
				CODE_SIGN_STYLE = Automatic;
				CURRENT_PROJECT_VERSION = 1;
				DEVELOPMENT_TEAM = B64Y6DZ7B7;
				GENERATE_INFOPLIST_FILE = YES;
				IPHONEOS_DEPLOYMENT_TARGET = 18.4;
				MARKETING_VERSION = 1.0;
				PRODUCT_BUNDLE_IDENTIFIER = myself.FitScanAITests;
				PRODUCT_NAME = "$(TARGET_NAME)";
				SWIFT_EMIT_LOC_STRINGS = NO;
				SWIFT_VERSION = 5.0;
				TARGETED_DEVICE_FAMILY = "1,2";
				TEST_HOST = "$(BUILT_PRODUCTS_DIR)/FitScanAI.app/$(BUNDLE_EXECUTABLE_FOLDER_PATH)/FitScanAI";
			};
			name = Debug;
		};
		3B8B6FCB2DD5F909008CC6C4 /* Release */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				BUNDLE_LOADER = "$(TEST_HOST)";
				CODE_SIGN_STYLE = Automatic;
				CURRENT_PROJECT_VERSION = 1;
				DEVELOPMENT_TEAM = B64Y6DZ7B7;
				GENERATE_INFOPLIST_FILE = YES;
				IPHONEOS_DEPLOYMENT_TARGET = 18.4;
				MARKETING_VERSION = 1.0;
				PRODUCT_BUNDLE_IDENTIFIER = myself.FitScanAITests;
				PRODUCT_NAME = "$(TARGET_NAME)";
				SWIFT_EMIT_LOC_STRINGS = NO;
				SWIFT_VERSION = 5.0;
				TARGETED_DEVICE_FAMILY = "1,2";
				TEST_HOST = "$(BUILT_PRODUCTS_DIR)/FitScanAI.app/$(BUNDLE_EXECUTABLE_FOLDER_PATH)/FitScanAI";
			};
			name = Release;
		};
		3B8B6FCD2DD5F909008CC6C4 /* Debug */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				CODE_SIGN_STYLE = Automatic;
				CURRENT_PROJECT_VERSION = 1;
				DEVELOPMENT_TEAM = B64Y6DZ7B7;
				GENERATE_INFOPLIST_FILE = YES;
				MARKETING_VERSION = 1.0;
				PRODUCT_BUNDLE_IDENTIFIER = myself.FitScanAIUITests;
				PRODUCT_NAME = "$(TARGET_NAME)";
				SWIFT_EMIT_LOC_STRINGS = NO;
				SWIFT_VERSION = 5.0;
				TARGETED_DEVICE_FAMILY = "1,2";
				TEST_TARGET_NAME = FitScanAI;
			};
			name = Debug;
		};
		3B8B6FCE2DD5F909008CC6C4 /* Release */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				CODE_SIGN_STYLE = Automatic;
				CURRENT_PROJECT_VERSION = 1;
				DEVELOPMENT_TEAM = B64Y6DZ7B7;
				GENERATE_INFOPLIST_FILE = YES;
				MARKETING_VERSION = 1.0;
				PRODUCT_BUNDLE_IDENTIFIER = myself.FitScanAIUITests;
				PRODUCT_NAME = "$(TARGET_NAME)";
				SWIFT_EMIT_LOC_STRINGS = NO;
				SWIFT_VERSION = 5.0;
				TARGETED_DEVICE_FAMILY = "1,2";
				TEST_TARGET_NAME = FitScanAI;
			};
			name = Release;
		};
/* End XCBuildConfiguration section */

/* Begin XCConfigurationList section */
		3B8B6FA02DD5F907008CC6C4 /* Build configuration list for PBXProject "FitScanAI" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				3B8B6FC42DD5F909008CC6C4 /* Debug */,
				3B8B6FC52DD5F909008CC6C4 /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
		3B8B6FC62DD5F909008CC6C4 /* Build configuration list for PBXNativeTarget "FitScanAI" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				3B8B6FC72DD5F909008CC6C4 /* Debug */,
				3B8B6FC82DD5F909008CC6C4 /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
		3B8B6FC92DD5F909008CC6C4 /* Build configuration list for PBXNativeTarget "FitScanAITests" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				3B8B6FCA2DD5F909008CC6C4 /* Debug */,
				3B8B6FCB2DD5F909008CC6C4 /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
		3B8B6FCC2DD5F909008CC6C4 /* Build configuration list for PBXNativeTarget "FitScanAIUITests" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				3B8B6FCD2DD5F909008CC6C4 /* Debug */,
				3B8B6FCE2DD5F909008CC6C4 /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
/* End XCConfigurationList section */

/* Begin XCRemoteSwiftPackageReference section */
		3B9A629F2E333B850022E438 /* XCRemoteSwiftPackageReference "firebase-ios-sdk" */ = {
			isa = XCRemoteSwiftPackageReference;
			repositoryURL = "https://github.com/firebase/firebase-ios-sdk";
			requirement = {
				kind = exactVersion;
				version = 11.13.0;
			};
		};
		3BB88F8E2E16159200B6E465 /* XCRemoteSwiftPackageReference "GoogleSignIn-iOS" */ = {
			isa = XCRemoteSwiftPackageReference;
			repositoryURL = "https://github.com/google/GoogleSignIn-iOS.git";
			requirement = {
				kind = upToNextMajorVersion;
				minimumVersion = 8.0.0;
			};
		};
		D02062402E4912DC00D8DB16 /* XCRemoteSwiftPackageReference "AppsFlyerFramework" */ = {
			isa = XCRemoteSwiftPackageReference;
			repositoryURL = "https://github.com/AppsFlyerSDK/AppsFlyerFramework";
			requirement = {
				kind = upToNextMajorVersion;
				minimumVersion = 6.17.2;
			};
		};
/* End XCRemoteSwiftPackageReference section */

/* Begin XCSwiftPackageProductDependency section */
		3B9A62A02E333B850022E438 /* FirebaseAnalyticsWithoutAdIdSupport */ = {
			isa = XCSwiftPackageProductDependency;
			package = 3B9A629F2E333B850022E438 /* XCRemoteSwiftPackageReference "firebase-ios-sdk" */;
			productName = FirebaseAnalyticsWithoutAdIdSupport;
		};
		3BB88F8F2E16159300B6E465 /* GoogleSignIn */ = {
			isa = XCSwiftPackageProductDependency;
			package = 3BB88F8E2E16159200B6E465 /* XCRemoteSwiftPackageReference "GoogleSignIn-iOS" */;
			productName = GoogleSignIn;
		};
		3BB88F912E16159300B6E465 /* GoogleSignInSwift */ = {
			isa = XCSwiftPackageProductDependency;
			package = 3BB88F8E2E16159200B6E465 /* XCRemoteSwiftPackageReference "GoogleSignIn-iOS" */;
			productName = GoogleSignInSwift;
		};
		D02062412E4912DC00D8DB16 /* AppsFlyerLib */ = {
			isa = XCSwiftPackageProductDependency;
			package = D02062402E4912DC00D8DB16 /* XCRemoteSwiftPackageReference "AppsFlyerFramework" */;
			productName = AppsFlyerLib;
		};
/* End XCSwiftPackageProductDependency section */
	};
	rootObject = 3B8B6F9D2DD5F907008CC6C4 /* Project object */;
}
