import Foundation
#if canImport(FoundationNetworking)
import FoundationNetworking
#endif

var semaphore = DispatchSemaphore (value: 0)

let parameters = "{\n    \"deviceId\": \"string\",\n    \"bundleId\": \"string\",\n    \"authorizationCode\": \"string\",\n    \"clientId\": \"string\"\n}"
let postData = parameters.data(using: .utf8)

var request = URLRequest(url: URL(string: "https://fsai.pickgoodspro.com/apple/device/login")!,timeoutInterval: Double.infinity)
request.addValue("application/json", forHTTPHeaderField: "Content-Type")

request.httpMethod = "POST"
request.httpBody = postData

let task = URLSession.shared.dataTask(with: request) { data, response, error in 
   guard let data = data else {
      print(String(describing: error))
      semaphore.signal()
      return
   }
   print(String(data: data, encoding: .utf8)!)
   semaphore.signal()
}

task.resume()
semaphore.wait()