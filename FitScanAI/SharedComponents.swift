import SwiftUI

// 数字键盘输入视图 - 用于OnboardingView和WeightView共享
struct SharedNumberKeyboardView: View {
    @Binding var value: String
    @Binding var showKeyboard: Bool
    @Binding var selectedUnit: String
    let title: String
    let units: [String]
    let onConfirm: (String, String) -> Void
    
    var body: some View {
        ZStack {
            // 半透明背景
            Color.black.opacity(0.5)
                .edgesIgnoringSafeArea(.all)
                .onTapGesture {
                    showKeyboard = false
                }
            
            VStack(spacing: 0) {
                // 标题栏
                HStack {
                    Text(title)
                        .font(.headline)
                    
                    Spacer()
                    
                    Button(action: {
                        showKeyboard = false
                    }) {
                        Image(systemName: "xmark")
                            .foregroundColor(.gray)
                    }
                }
                .padding()
                .background(Color.white)
                
                // 数值和单位选择
                HStack {
                    Spacer()
                    
                    Text(value)
                        .font(.system(size: 40, weight: .bold))
                    
                    // 单位选择
                    Menu {
                        ForEach(units, id: \.self) { unit in
                            Button(unit) {
                                if unit != selectedUnit {
                                    // 转换单位
                                    convertValue(from: selectedUnit, to: unit)
                                    selectedUnit = unit
                                }
                            }
                        }
                    } label: {
                        HStack {
                            Text(selectedUnit)
                                .font(.headline)
                                .foregroundColor(.green)
                            Image(systemName: "chevron.down")
                                .font(.caption)
                                .foregroundColor(.green)
                        }
                    }
                }
                .padding()
                .background(Color.white)
                
                // 数字键盘
                VStack(spacing: 15) {
                    // 第一行: 1 2 3
                    HStack(spacing: 30) {
                        keyButton("1") { appendDigit("1") }
                        keyButton("2") { appendDigit("2") }
                        keyButton("3") { appendDigit("3") }
                    }
                    
                    // 第二行: 4 5 6
                    HStack(spacing: 30) {
                        keyButton("4") { appendDigit("4") }
                        keyButton("5") { appendDigit("5") }
                        keyButton("6") { appendDigit("6") }
                    }
                    
                    // 第三行: 7 8 9
                    HStack(spacing: 30) {
                        keyButton("7") { appendDigit("7") }
                        keyButton("8") { appendDigit("8") }
                        keyButton("9") { appendDigit("9") }
                    }
                    
                    // 第四行: . 0 delete
                    HStack(spacing: 30) {
                        keyButton(".") { appendDigit(".") }
                        keyButton("0") { appendDigit("0") }
                        keyButton {
                            removeLastDigit()
                        } content: {
                            Image(systemName: "delete.left")
                                .font(.title)
                                .frame(width: 60, height: 60)
                                .foregroundColor(.primary)
                        }
                    }
                }
                
                // 确认按钮
                Button(action: {
                    onConfirm(value, selectedUnit)
                    showKeyboard = false
                }) {
                    Text("Confirm")
                        .font(.headline)
                        .foregroundColor(.white)
                        .frame(maxWidth: .infinity)
                        .padding()
                        .background(Color.green)
                }
            }
            .background(Color.white)
            .cornerRadius(12)
            .padding(.horizontal, 20)
            .frame(maxWidth: .infinity)
        }
    }
    
    // 添加数字
    private func appendDigit(_ digit: String) {
        // 如果是小数点，确保只有一个小数点
        if digit == "." && value.contains(".") {
            return
        }
        
        // 当值为"0.0"时，如果输入数字，清空并重新开始
        if value == "0.0" && digit != "." && digit.rangeOfCharacter(from: CharacterSet.decimalDigits) != nil {
            value = digit
            return
        }
        
        // 修复的输入限制逻辑：小数点前3位，小数点后1位
        if digit == "." && value.isEmpty {
            value = "0."
        } else if value == "0" && digit != "." {
            value = digit
        } else {
            // 检查当前值的格式
            let parts = value.components(separatedBy: ".")
            
            if digit == "." {
                // 添加小数点：只有在整数部分不超过3位且还没有小数点时才允许
                if parts.count == 1 && parts[0].count <= 3 {
                    value += digit
                }
            } else {
                // 添加数字
                if parts.count == 1 {
                    // 还没有小数点，检查整数部分是否已达到3位
                    if parts[0].count < 3 {
                        value += digit
                    }
                } else if parts.count == 2 {
                    // 已有小数点，检查小数部分是否已达到1位
                    if parts[1].count < 1 {
                        value += digit
                    }
                } else if parts.count == 0 {
                    // 空值情况
                    value += digit
                }
            }
        }
    }
    
    // 删除最后一位
    private func removeLastDigit() {
        if !value.isEmpty {
            value.removeLast()
        }
        
        // 如果删空了，设置为0
        if value.isEmpty {
            value = "0"
        }
    }
    
    // 转换单位
    private func convertValue(from sourceUnit: String, to targetUnit: String) {
        guard let numValue = Double(value) else { return }
        
        if sourceUnit == "kg" && targetUnit == "lbs" {
            // 从千克转换为磅
            value = String(format: "%.1f", numValue * 2.20462)
        } else if sourceUnit == "lbs" && targetUnit == "kg" {
            // 从磅转换为千克
            value = String(format: "%.1f", numValue / 2.20462)
        } else if sourceUnit == "cm" && targetUnit == "ft" {
            // 从厘米转换为英尺
            let inches = numValue / 2.54
            value = String(format: "%.1f", inches / 12)
        } else if sourceUnit == "ft" && targetUnit == "cm" {
            // 从英尺转换为厘米
            value = String(format: "%.1f", numValue * 12 * 2.54)
        }
    }
    
    // 键盘按钮
    private func keyButton(_ text: String, action: @escaping () -> Void) -> some View {
        Button(action: action) {
            Text(text)
                .font(.title)
                .frame(width: 60, height: 60)
                .foregroundColor(.primary)
        }
    }
    
    // 自定义内容键盘按钮
    private func keyButton(action: @escaping () -> Void, content: @escaping () -> some View) -> some View {
        Button(action: action) {
            content()
        }
    }
}

// 单位选择视图 - 用于OnboardingView和WeightView共享
struct SharedUnitPickerView: View {
    @Binding var isShowing: Bool
    @Binding var selectedUnit: String
    let title: String
    let units: [(String, String)]
    
    var body: some View {
        ZStack {
            // 半透明背景
            Color.black.opacity(0.5)
                .edgesIgnoringSafeArea(.all)
                .onTapGesture {
                    isShowing = false
                }
            
            VStack {
                // 标题
                HStack {
                    Text(title)
                        .font(.headline)
                    
                    Spacer()
                    
                    Button(action: {
                        isShowing = false
                    }) {
                        Image(systemName: "xmark")
                            .foregroundColor(.gray)
                    }
                }
                .padding()
                
                // 选项列表
                VStack(spacing: 0) {
                    ForEach(units, id: \.0) { unit in
                        Button(action: {
                            selectedUnit = unit.0
                            isShowing = false
                        }) {
                            HStack {
                                Text(unit.1)
                                    .foregroundColor(.primary)
                                
                                Spacer()
                                
                                if selectedUnit == unit.0 {
                                    Image(systemName: "checkmark")
                                        .foregroundColor(.green)
                                }
                            }
                            .padding()
                            .background(selectedUnit == unit.0 ? Color.green.opacity(0.1) : Color.white)
                        }
                    }
                }
            }
            .background(Color.white)
            .cornerRadius(12)
            .padding(.horizontal, 20)
            .frame(maxWidth: .infinity, maxHeight: 200)
        }
    }
}

// 添加可按压按钮样式
struct PressableButtonStyle: ButtonStyle {
    var color: Color = .green
    
    func makeBody(configuration: Configuration) -> some View {
        configuration.label
            .opacity(configuration.isPressed ? 0.7 : 1.0)
            .scaleEffect(configuration.isPressed ? 0.98 : 1.0)
            .animation(.easeInOut(duration: 0.1), value: configuration.isPressed)
    }
}
