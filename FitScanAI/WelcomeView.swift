import SwiftUI

struct WelcomeView: View {
    @EnvironmentObject private var userData: UserData
    @State private var isGetStartedPressed = false
    @State private var showOnboarding = false
    
    var body: some View {
        GeometryReader { geometry in
            // 计算底部固定区域的预留高度，避免滚动内容被遮挡
            let footerHeight = (50 + 20 + 16 + 20) * geometry.size.height / 812 + max(20 * geometry.size.height / 812, geometry.safeAreaInsets.bottom + 10)
            ZStack(alignment: .bottom) {
                // 上方图片内容可滚动
                ScrollView(showsIndicators: false) {
                    VStack(spacing: 0) {
                        Image("new")
                            .resizable()
                            .aspectRatio(contentMode: .fill)
                            .frame(
                                width: geometry.size.width,
                                height: 897 * geometry.size.width / 375
                            )
                            .clipped()
                    }
                    .padding(.bottom, footerHeight)
                }
                .ignoresSafeArea()

                // 固定在底部的按钮区域
                VStack(spacing: 0) {
                    VStack(spacing: 16 * geometry.size.height / 812) {
                        HStack {
                            // Skip 按钮（游客登录并进入主界面）
                            Button(action: {
                                print("Skip button tapped")
                                userData.setGuestMode()
                                NotificationCenter.default.post(name: Notification.Name("WelcomeSkipped"), object: nil)
                                NotificationCenter.default.post(name: Notification.Name("GuestLoggedIn"), object: nil)
                            }) {
                                Text("Skip")
                                    .font(.custom("MyriadPro", size: 16 * min(geometry.size.width / 375, geometry.size.height / 812)))
                                    .foregroundColor(Color(red: 145/255.0, green: 157/255.0, blue: 150/255.0))
                                    .frame(height: 16 * geometry.size.height / 812)
                            }
                            .padding(.leading, 24 * geometry.size.width / 375)

                            Spacer()

                            // Get Started 按钮
                            Button(action: {
                                print("Get Started button tapped")
                                isGetStartedPressed = true
                                showOnboarding = true
                            }) {
                                ZStack {
                                    LinearGradient(
                                        gradient: Gradient(colors: [
                                            Color(red: 0x44/255.0, green: 0xAE/255.0, blue: 0x49/255.0),
                                            Color(red: 0x2C/255.0, green: 0x99/255.0, blue: 0x31/255.0)
                                        ]),
                                        startPoint: UnitPoint(x: 0.5 + 0.5 * cos(157 * .pi / 180), y: 0.5 + 0.5 * sin(157 * .pi / 180)),
                                        endPoint: UnitPoint(x: 0.5 - 0.5 * cos(157 * .pi / 180), y: 0.5 - 0.5 * sin(157 * .pi / 180))
                                    )
                                    .frame(
                                        width: 255 * geometry.size.width / 375,
                                        height: 50 * geometry.size.height / 812
                                    )
                                    .cornerRadius(8)

                                    Text("Get Started")
                                        .font(.custom("MyriadPro", size: 18 * min(geometry.size.width / 375, geometry.size.height / 812)))
                                        .fontWeight(.semibold)
                                        .foregroundColor(.white)
                                }
                            }
                            .scaleEffect(isGetStartedPressed ? 0.95 : 1.0)
                            .animation(.easeInOut(duration: 0.1), value: isGetStartedPressed)
                            .onTapGesture {
                                withAnimation(.easeInOut(duration: 0.1)) {
                                    isGetStartedPressed = true
                                }
                                DispatchQueue.main.asyncAfter(deadline: .now() + 0.1) {
                                    withAnimation(.easeInOut(duration: 0.1)) {
                                        isGetStartedPressed = false
                                    }
                                }
                            }
                            .padding(.trailing, 24 * geometry.size.width / 375)
                        }

                        Text("Your journey to better health starts now")
                            .font(.custom("MyriadPro", size: 16 * min(geometry.size.width / 375, geometry.size.height / 812)))
                            .italic()
                            .foregroundColor(Color(red: 0x4B/255.0, green: 0x6C/255.0, blue: 0x58/255.0))
                            .multilineTextAlignment(.center)
                            .lineSpacing(3 * geometry.size.height / 812)
                            .frame(maxWidth: min(253 * geometry.size.width / 375, geometry.size.width - 60))
                            .padding(.horizontal, 30 * geometry.size.width / 375)
                    }
                    .padding(.top, 20 * geometry.size.height / 812)
                    .padding(.bottom, max(20 * geometry.size.height / 812, geometry.safeAreaInsets.bottom + 10))
                }
                .frame(maxWidth: .infinity)
                .background(
                    LinearGradient(
                        gradient: Gradient(colors: [
                            Color(red: 0x0D/255.0, green: 0x23/255.0, blue: 0x14/255.0),
                            Color(red: 0x0F/255.0, green: 0x26/255.0, blue: 0x16/255.0)
                        ]),
                        startPoint: .top,
                        endPoint: .bottom
                    )
                    .ignoresSafeArea(edges: .bottom)
                )
            }
            // 为页面设置与底部按钮区域一致的背景，避免滚动到底部出现白边
            .background(
                LinearGradient(
                    gradient: Gradient(colors: [
                        Color(red: 0x0D/255.0, green: 0x23/255.0, blue: 0x14/255.0),
                        Color(red: 0x0F/255.0, green: 0x26/255.0, blue: 0x16/255.0)
                    ]),
                    startPoint: .top,
                    endPoint: .bottom
                )
                .ignoresSafeArea(edges: .bottom) // 仅覆盖底部安全区，顶部保持不变
            )
            .ignoresSafeArea()
        }
        .navigationBarHidden(true)
        .fullScreenCover(isPresented: $showOnboarding) {
            NavigationView {
                // 仅在未登录时展示引导
                if userData.accessToken.isEmpty && !UserDefaults.standard.bool(forKey: "isLoggedIn") {
                    OnboardingView()
                        .environmentObject(userData)
                } else {
                    ContentView()
                        .environmentObject(userData)
                }
            }
        }
        .onAppear {
            // 统计
            XDTrackTool.shared.appear("欢迎页面")
        }
    }
}

struct WelcomeView_Previews: PreviewProvider {
    static var previews: some View {
        WelcomeView()
            .environmentObject(UserData())
            .previewDevice("iPhone 14")
        
        WelcomeView()
            .environmentObject(UserData())
            .previewDevice("iPhone SE (3rd generation)")
            .previewDisplayName("iPhone SE")
        
        WelcomeView()
            .environmentObject(UserData())
            .previewDevice("iPhone 14 Pro")
            .previewDisplayName("iPhone 14 Pro (Dynamic Island)")
        
        WelcomeView()
            .environmentObject(UserData())
            .previewDevice("iPhone 14 Pro Max")
            .previewDisplayName("iPhone 14 Pro Max")
    }
} 