//
//  ContentView.swift
//  FitScanAI
//
//  Created by 陈亚宁 on 2025/4/9.
//

import SwiftUI
import UIKit
import WebKit
import PhotosUI
import UserNotifications
import Combine

// 添加WebView组件
struct WebView: UIViewRepresentable {
    let url: URL
    @Binding var isLoading: Bool
    
    func makeUIView(context: Context) -> WKWebView {
        let webView = WKWebView()
        webView.navigationDelegate = context.coordinator
        return webView
    }
    
    func updateUIView(_ webView: WKWebView, context: Context) {
        let request = URLRequest(url: url)
        webView.load(request)
    }
    
    func makeCoordinator() -> Coordinator {
        Coordinator(self)
    }
    
    class Coordinator: NSObject, WKNavigationDelegate {
        let parent: WebView
        
        init(_ parent: WebView) {
            self.parent = parent
        }
        
        func webView(_ webView: WKWebView, didStartProvisionalNavigation navigation: WKNavigation!) {
            parent.isLoading = true
        }
        
        func webView(_ webView: WKWebView, didFinish navigation: WKNavigation!) {
            parent.isLoading = false
        }
        
        func webView(_ webView: WKWebView, didFail navigation: WKNavigation!, withError error: Error) {
            parent.isLoading = false
        }
    }
}

// 网页视图组件
struct WebPageView: View {
    let url: URL
    let title: String
    @Environment(\.presentationMode) var presentationMode
    @State private var isLoading = true
    @State private var hasError = false
    @State private var errorMessage = ""
    
    var body: some View {
        VStack(spacing: 0) {
            // 自定义导航栏
            HStack {
                Button(action: {
                    presentationMode.wrappedValue.dismiss()
                }) {
                    Image(systemName: "chevron.left")
                        .foregroundColor(.green)
                        .font(.title2)
                }
                
                Spacer()
                
                Text(title)
                    .font(.headline)
                    .fontWeight(.semibold)
                
                Spacer()
                
                // 占位符，保持标题居中
                Image(systemName: "chevron.left")
                    .foregroundColor(.clear)
                    .font(.title2)
            }
            .padding()
            .background(Color(UIColor.systemBackground))
            
            // 内容区域
            ZStack {
                if hasError {
                    // 错误状态
                    VStack(spacing: 20) {
                        Image(systemName: "wifi.exclamationmark")
                            .font(.system(size: 50))
                            .foregroundColor(.gray)
                        
                        Text("Failed to load page")
                            .font(.headline)
                        
                        Text(errorMessage)
                            .font(.subheadline)
                            .foregroundColor(.secondary)
                            .multilineTextAlignment(.center)
                            .padding(.horizontal)
                        
                        Button(action: {
                            hasError = false
                            isLoading = true
                        }) {
                            Text("Retry")
                                .fontWeight(.semibold)
                                .foregroundColor(.white)
                                .padding(.horizontal, 30)
                                .padding(.vertical, 12)
                                .background(Color.green)
                                .cornerRadius(8)
                        }
                    }
                    .frame(maxWidth: .infinity, maxHeight: .infinity)
                } else {
                    // WebView
                    ImprovedWebView(
                        url: url,
                        isLoading: $isLoading,
                        hasError: $hasError,
                        errorMessage: $errorMessage
                    )
                    
                    // 加载指示器
                    if isLoading {
                        VStack(spacing: 15) {
                            ProgressView()
                                .scaleEffect(1.2)
                            
                            Text("Loading...")
                                .font(.subheadline)
                                .foregroundColor(.secondary)
                        }
                        .frame(maxWidth: .infinity, maxHeight: .infinity)
                        .background(Color(UIColor.systemBackground))
                    }
                }
            }
        }
        .navigationBarHidden(true)
        .onAppear {
            // 统计
            XDTrackTool.shared.appear("网页页面_\(title)")
        }
    }
}

// 改进的WebView组件
struct ImprovedWebView: UIViewRepresentable {
    let url: URL
    @Binding var isLoading: Bool
    @Binding var hasError: Bool
    @Binding var errorMessage: String
    
    func makeUIView(context: Context) -> WKWebView {
        let configuration = WKWebViewConfiguration()
        configuration.allowsInlineMediaPlayback = true
        configuration.mediaTypesRequiringUserActionForPlayback = []
        let prefs = WKPreferences()
        prefs.javaScriptCanOpenWindowsAutomatically = true
        configuration.preferences = prefs
        
        let webView = WKWebView(frame: .zero, configuration: configuration)
        webView.navigationDelegate = context.coordinator
        webView.uiDelegate = context.coordinator
        webView.allowsBackForwardNavigationGestures = true
        webView.backgroundColor = UIColor.systemBackground
        
        // 设置用户代理
        webView.customUserAgent = "Mozilla/5.0 (iPhone; CPU iPhone OS 16_0 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/16.0 Mobile/15E148 Safari/604.1"
        
        return webView
    }
    
    func updateUIView(_ webView: WKWebView, context: Context) {
        // 只在URL不同或有错误时才重新加载
        if webView.url != url || hasError {
            loadURL(in: webView)
        }
    }
    
    private func loadURL(in webView: WKWebView) {
        var request = URLRequest(url: url)
        request.timeoutInterval = 30.0
        request.cachePolicy = .returnCacheDataElseLoad
        
        webView.load(request)
    }
    
    func makeCoordinator() -> Coordinator {
        Coordinator(self)
    }
    
    class Coordinator: NSObject, WKNavigationDelegate, WKUIDelegate {
        let parent: ImprovedWebView
        private var retryCount = 0
        private let maxRetries = 2
        
        init(_ parent: ImprovedWebView) {
            self.parent = parent
        }
        
        func webView(_ webView: WKWebView, didStartProvisionalNavigation navigation: WKNavigation!) {
            DispatchQueue.main.async {
                self.parent.isLoading = true
                self.parent.hasError = false
            }
        }
        
        func webView(_ webView: WKWebView, didFinish navigation: WKNavigation!) {
            DispatchQueue.main.async {
                self.parent.isLoading = false
                self.parent.hasError = false
                self.retryCount = 0
            }
        }
        
        func webView(_ webView: WKWebView, didFail navigation: WKNavigation!, withError error: Error) {
            handleError(webView: webView, error: error)
        }
        
        func webView(_ webView: WKWebView, didFailProvisionalNavigation navigation: WKNavigation!, withError error: Error) {
            handleError(webView: webView, error: error)
        }
        
        private func handleError(webView: WKWebView, error: Error) {
            let nsError = error as NSError
            print("WebView error: \(error.localizedDescription), code: \(nsError.code)")
            
            DispatchQueue.main.async {
                self.parent.isLoading = false
                
                // -999 表示上一次导航被取消（如拦截 target=_blank 或发起了新的请求），不应视为错误且不应重试
                if nsError.code == -999 {
                    self.parent.hasError = false
                    self.retryCount = 0
                    return
                }
                
                self.parent.hasError = true
                switch nsError.code {
                case -1001:
                    self.parent.errorMessage = "Request timed out. Please check your network."
                case -1009:
                    self.parent.errorMessage = "No internet connection."
                default:
                    self.parent.errorMessage = "Failed to load the page."
                }
            }
        }
        
        func webView(_ webView: WKWebView, decidePolicyFor navigationAction: WKNavigationAction, decisionHandler: @escaping (WKNavigationActionPolicy) -> Void) {
            guard let url = navigationAction.request.url else {
                decisionHandler(.allow)
                return
            }
            
            // 检查是否是外部链接（不同域名）
            let currentHost = webView.url?.host
            let targetHost = url.host
            
            // 如果是外部链接或者是特定的权威网站链接，在Safari中打开
            let authoritativeDomains = ["dietaryguidelines.gov", "fdc.nal.usda.gov", "jamanetwork.com", "nejm.org"]
            let isAuthoritativeLink = authoritativeDomains.contains { domain in
                url.host?.contains(domain) == true
            }
            
            if isAuthoritativeLink || (currentHost != nil && targetHost != nil && currentHost != targetHost) {
                // 在Safari中打开外部链接
                DispatchQueue.main.async {
                    if UIApplication.shared.canOpenURL(url) {
                        UIApplication.shared.open(url)
                    }
                }
                decisionHandler(.cancel)
                return
            }
            
            // 处理 target="_blank" 或无主框架的跳转，在当前 WebView 中打开
            if navigationAction.targetFrame == nil {
                webView.load(navigationAction.request)
                decisionHandler(.cancel)
                return
            }
            
            decisionHandler(.allow)
        }

        // 处理需要新窗口的场景（如 window.open 或 target="_blank"）
        func webView(_ webView: WKWebView, createWebViewWith configuration: WKWebViewConfiguration, for navigationAction: WKNavigationAction, windowFeatures: WKWindowFeatures) -> WKWebView? {
            guard let url = navigationAction.request.url else {
                return nil
            }
            
            // 检查是否是权威网站链接
            let authoritativeDomains = ["dietaryguidelines.gov", "fdc.nal.usda.gov", "jamanetwork.com", "nejm.org"]
            let isAuthoritativeLink = authoritativeDomains.contains { domain in
                url.host?.contains(domain) == true
            }
            
            if isAuthoritativeLink {
                // 在Safari中打开权威网站链接
                DispatchQueue.main.async {
                    if UIApplication.shared.canOpenURL(url) {
                        UIApplication.shared.open(url)
                    }
                }
                return nil
            }
            
            // 对于其他链接，在当前WebView中打开
            if navigationAction.targetFrame == nil {
                webView.load(navigationAction.request)
            }
            return nil
        }
    }
}

struct ContentView: View {
    @EnvironmentObject var userData: UserData
    @StateObject private var planAdviceService = PlanAdviceService.shared
    @State private var isPremiumExpanded = false
    @State private var showImagePicker = false
    @State private var showCamera = false
    @State private var showAnalyzing = false
    @State private var showResult = false
    @State private var selectedImage: UIImage?
    @State private var showOnboarding = false  // 控制是否显示引导页面
    @State private var isNewUserChecked = false  // 是否已检查新用户状态
    @State private var showExerciseDetail = false // 添加状态变量用于控制ExerciseFullScreenView的显示
    @State private var showGoalWeightDetail = false // 添加状态变量用于控制GoalWeightDetailView的显示
    @State private var cancellables = Set<AnyCancellable>() // 用于存储订阅
    @State private var showGoalWeightFullScreen = false // 控制是否显示全屏目标体重编辑页面
    @State private var isLoadingTodayRecords = false
    @State private var todayAPIRecords: [DietRecordResponse] = []

    // 添加新的状态变量来存储API数据
    @State private var todayAPICalories: Int = 0
    @State private var todayAPIProtein: Int = 0
    @State private var isLoadingNutritionData = false
    
    // 添加日营养分母的状态变量
    @State private var dailyCalorieGoal: Int = 0
    @State private var dailyProteinGoal: Int = 0
    
    // 访客模式权限提示弹窗
    @State private var showGuestPermissionAlert = false
    
    var body: some View {
        NavigationStack {
        ZStack {
            // 主应用内容
            TabView(selection: $userData.selectedTab) {
                // 主页标签（Nutrition）
                homeView
                    .tabItem {
                        Image("nutrition")
                            .renderingMode(.template)
                        Text("Nutrition")
                    }
                    .tag(0)
                    .onReceive(NotificationCenter.default.publisher(for: Notification.Name("APIFoodRecordAdded"))) { _ in
                        print("ContentView (homeView): Received APIFoodRecordAdded notification, reloading today\'s API records.")
                        loadTodayAPIRecords()
                    }
                    
                // 计划标签
                PlanView()
                    .environmentObject(userData)
                    .tabItem {
                        Image("plan")
                            .renderingMode(.template)
                        Text("Plan")
                    }
                    .tag(1)
                
                // 体重标签
                WeightView()
                    .environmentObject(userData)
                    .tabItem {
                        Image("weight")
                            .renderingMode(.template)
                        Text("Weight")
                    }
                    .tag(2)
                
                // 个人资料标签
                ProfileView()
                    .environmentObject(userData)
                    .tabItem {
                        Image("profile")
                            .renderingMode(.template)
                        Text("Profile")
                    }
                    .tag(3)
            }
            .tint(Color(red: 0.18, green: 0.8, blue: 0.44))
            .onAppear {
                // 设置 TabBar 的选中颜色和渲染模式
                configureTabBarAppearance()
            }
            .overlay(
                Group {
                    // 图片选择弹窗（条件显示）
                    if showImagePicker {
                        Color.black.opacity(0.5)
                            .ignoresSafeArea()
                        
                        ImagePickerView(isPresented: $showImagePicker, showCamera: $showCamera, showPhotosPicker: $showAnalyzing, selectedImage: $selectedImage)
                            .padding(.horizontal, 20)
                    }
                    
                    // 相机视图（条件显示）
                    if showCamera {
                        // 使用真实相机模块中定义的RealCameraView
                        RealCameraView(isPresented: $showCamera, showAnalyzing: $showAnalyzing, selectedImage: $selectedImage)
                    }
                    
                    // 分析中视图（条件显示）
                    if showAnalyzing {
                        Color.white
                            .ignoresSafeArea()
                        
                        AnalyzingView(isPresented: $showAnalyzing, showResult: $showResult, selectedImage: $selectedImage)
                            .environmentObject(userData)
                            .zIndex(50) // 确保在合适的层级
                            .transition(.opacity) // 添加过渡动画
                    }
                    
                    // 结果视图（条件显示）
                    if showResult {
                        Color.white
                            .ignoresSafeArea()
                        
                        ResultView(isPresented: $showResult, selectedImage: $selectedImage, addToFoodDiary: { name, calories, protein, carbs, fat in
                            // 添加到食物日记
                            userData.addFoodEntry(
                                name: name,
                                calories: calories,
                                protein: protein,
                                carbs: carbs,
                                fat: fat,
                                date: Date(),
                                image: selectedImage
                            )
                            
                            // 发送通知，告知添加了新的食物记录
                            NotificationCenter.default.post(name: Notification.Name("FoodEntryAdded"), object: nil)
                        })
                    }
                    
                    // 食物日志详情视图（条件显示）
                    if userData.selectedTab == -1 {
                        Color.white
                            .ignoresSafeArea()
                        
                        FoodJournalView(isPresented: Binding<Bool>(
                            get: { userData.selectedTab == -1 },
                            set: { if !$0 { userData.selectedTab = 0 } } // 修改为返回到营养标签页(现在的索引为0)
                        ))
                        .environmentObject(userData)
                    }
                    
                    // 引导页面（条件显示）
                    if showOnboarding {
                        ZStack {
                            Color.white
                                .ignoresSafeArea()
                            
                            OnboardingView()
                                .environmentObject(userData)
                                .onReceive(NotificationCenter.default.publisher(for: Notification.Name("UserCompletedOnboarding"))) { _ in
                                    withAnimation {
                                        showOnboarding = false
                                    }
                                    
                                    // 导航到Nutrition页面(现在的索引为0)
                                    userData.selectedTab = 0
                                    userData.objectWillChange.send()
                                    userData.saveSettings()
                                    
                                    // 发送通知刷新Plan页面
                                    NotificationCenter.default.post(name: Notification.Name("RefreshPlanView"), object: nil)
                                    
                                    print("ContentView接收到UserCompletedOnboarding通知，隐藏引导页面并导航到Nutrition页面")
                                }
                        }
                        .transition(.opacity)
                        .zIndex(100) // 确保在最上层
                    }
                }
            )
            .onAppear {
                // 统计
                XDTrackTool.shared.appear("主页面")

                // 应用启动时检查是否为新用户
                if !isNewUserChecked {
                    checkIfNewUser()
                }

                // 加载AI建议数据
                planAdviceService.loadAllAdvices()
                
                // 🔧 修复：添加PlanAdvice数据更新监听器，确保数据加载完成后立即更新营养分母
                NotificationCenter.default.addObserver(forName: Notification.Name("PlanAdviceDataUpdated"), object: nil, queue: .main) { _ in
                    Task { @MainActor in
                        print("📢 [ContentView] 收到PlanAdvice数据更新通知，立即更新营养分母")
                        let dailyGoals = self.planAdviceService.getSafeDailyNutritionGoals()
                        
                        // 如果用户满足条件且获取到有效数据，立即更新
                        if self.userData.isPremium && self.userData.hasPlan && dailyGoals.calories > 0 && dailyGoals.protein > 0 {
                            self.dailyCalorieGoal = dailyGoals.calories
                            self.dailyProteinGoal = dailyGoals.protein
                            print("🥗 [ContentView] 通知驱动的营养分母更新:")
                            print("  - 卡路里目标: \(self.dailyCalorieGoal)")
                            print("  - 蛋白质目标: \(self.dailyProteinGoal)")
                        } else {
                            print("ℹ️ [ContentView] 用户条件不满足或数据无效，等待状态变化")
                        }
                    }
                }
                
                // 等待AI建议数据加载完成后再检查分母数据
                DispatchQueue.main.asyncAfter(deadline: .now() + 2.0) {
                    // 更新日营养分母数据
                    if planAdviceService.isWeeklyAdviceDataComplete() {
                        print("✅ [ContentView] 周建议数据完整，开始更新日营养分母")
                        
                        let dailyGoals = planAdviceService.getSafeDailyNutritionGoals()
                        
                        // 🔧 修复：只有在用户满足条件时才更新分母
                        if self.userData.isPremium && self.userData.hasPlan && dailyGoals.calories > 0 && dailyGoals.protein > 0 {
                            self.dailyCalorieGoal = dailyGoals.calories
                            self.dailyProteinGoal = dailyGoals.protein
                            
                            print("🥗 [ContentView] 日营养分母已更新:")
                            print("  - 卡路里目标: \(self.dailyCalorieGoal)")
                            print("  - 蛋白质目标: \(self.dailyProteinGoal)")
                        } else {
                            print("ℹ️ [ContentView] 延迟检查 - 用户条件: isPremium=\(self.userData.isPremium), hasPlan=\(self.userData.hasPlan)")
                            print("ℹ️ [ContentView] 延迟检查 - 营养数据: 卡路里=\(dailyGoals.calories), 蛋白质=\(dailyGoals.protein)")
                        }
                    } else {
                        print("⚠️ [ContentView] 周建议数据不完整，尝试重新加载")
                        planAdviceService.refreshAdvices()
                        
                        // 🆕 添加更强制性的数据检查
                        DispatchQueue.main.asyncAfter(deadline: .now() + 3.0) {
                            self.forceUpdateNutritionDenominator()
                        }
                    }
                }
                
                // 从API获取用户信息
                if !userData.accessToken.isEmpty {
                    print("ContentView 正在尝试获取用户信息，当前用户昵称: \(userData.nickname)")
                    UserService.shared.updateUserDataWithFetchedInfo(userData: userData)
                    
                    // 每次显示主页时，都从API检查用户计划状态，确保计划状态为最新
                    print("ContentView 正在从API检查用户计划状态")
                    UserPlanService.shared.checkUserHasPlan(userData: userData) { hasPlan in
                        DispatchQueue.main.async {
                            print("API返回最新计划状态: hasPlan=\(hasPlan)")
                            // API结果会自动保存到userData.hasPlan
                        }
                    }
                }
            }
            .onReceive(NotificationCenter.default.publisher(for: Notification.Name("ShowExerciseHistory"))) { _ in
                // 当收到ShowExerciseHistory通知时，切换到Weight标签页
                userData.selectedTab = 2
            }
            .onReceive(NotificationCenter.default.publisher(for: Notification.Name("DirectShowExerciseHistory"))) { _ in
                    // 当收到DirectShowExerciseHistory通知时，直接显示ExerciseFullScreenView
                        showExerciseDetail = true
            }
            .onReceive(NotificationCenter.default.publisher(for: Notification.Name("DirectShowWeightGoalEdit"))) { _ in
                    // 当收到DirectShowWeightGoalEdit通知时，使用导航方式显示全屏编辑页面
                    showGoalWeightFullScreen = true
                    
                // 确保其他sheet不会同时显示
                    showExerciseDetail = false
                    showGoalWeightDetail = false
            }
            .onReceive(NotificationCenter.default.publisher(for: Notification.Name("NavigateToPlanView"))) { _ in
                // 当收到NavigateToPlanView通知时，导航到Plan标签页(现在的索引为1)
                userData.selectedTab = 1
                
                // 通知UI更新
                userData.objectWillChange.send()
                
                // 隐藏任何可能显示的Sheet或视图
                showOnboarding = false
                
                // 强制刷新Plan页面数据
                NotificationCenter.default.post(name: Notification.Name("RefreshPlanView"), object: nil)
                
                // 打印日志用于调试
                print("收到NavigateToPlanView通知，已设置selectedTab = 1，并发送RefreshPlanView通知")
            }
            .onReceive(NotificationCenter.default.publisher(for: Notification.Name("ShowOnboarding"))) { _ in
                // 当收到ShowOnboarding通知时，显示引导页面
                withAnimation {
                    showOnboarding = true
                }
                isNewUserChecked = true
                print("收到ShowOnboarding通知，显示引导页面")
            }
            .onReceive(NotificationCenter.default.publisher(for: Notification.Name("UserCompletedOnboarding"))) { _ in
                // 当收到UserCompletedOnboarding通知时，关闭引导页面
                withAnimation {
                    showOnboarding = false
                }
                
                // 打印日志用于调试
                print("收到UserCompletedOnboarding通知，已关闭引导页面")
            }
            .onReceive(NotificationCenter.default.publisher(for: Notification.Name("NavigateToNutritionView"))) { _ in
                // 当收到NavigateToNutritionView通知时，导航到Nutrition标签页(索引为0)
                userData.selectedTab = 0
                
                // 通知UI更新
                userData.objectWillChange.send()
                
                // 隐藏任何可能显示的Sheet或视图
                showOnboarding = false
                
                // 发送通知表示引导流程已完成
                NotificationCenter.default.post(name: Notification.Name("UserCompletedOnboarding"), object: nil)
                
                // 打印日志用于调试
                print("收到NavigateToNutritionView通知，已设置selectedTab = 0（Nutrition页面）")
            }
            .onReceive(NotificationCenter.default.publisher(for: Notification.Name("ForceCloseAnalyzing"))) { _ in
                print("📢 收到ForceCloseAnalyzing通知，关闭分析页面")
                showAnalyzing = false
                showResult = false
                selectedImage = nil
                userData.currentFoodAnalysis = nil
                print("✅ 通知处理完成")
            }
            .onReceive(NotificationCenter.default.publisher(for: Notification.Name("TokenExpired"))) { _ in
                // 当收到TokenExpired通知时，立即登出用户
                print("🚨 收到TokenExpired通知，Access Token已过期，执行自动登出")
                
                // 显示用户友好的提示
                DispatchQueue.main.async {
                    // 关闭所有可能的弹窗和页面
                    showAnalyzing = false
                    showResult = false
                    showImagePicker = false
                    showCamera = false
                    selectedImage = nil
                    
                    // 执行登出操作
                    userData.logout()
                }
                
                print("✅ 已处理Token过期，用户将被重定向到登录页面")
            }
            .onReceive(NotificationCenter.default.publisher(for: Notification.Name("GuestLoggedIn"))) { _ in
                // 当收到访客登录通知时，导航到主页
                print("🚪 收到访客登录通知，导航到主页")
                DispatchQueue.main.async {
                    userData.selectedTab = 0
                    userData.objectWillChange.send()
                    
                    // 发送通知表示访客已登录
                    NotificationCenter.default.post(name: Notification.Name("UserLoggedIn"), object: nil)
                }
            }
            .onReceive(NotificationCenter.default.publisher(for: Notification.Name("GuestNeedsLogin"))) { _ in
                // 当收到访客需要登录通知时，跳转到登录页面
                print("🚪 收到访客需要登录通知，跳转到登录页面")
                
                // 关闭所有可能的弹窗和页面
                showAnalyzing = false
                showResult = false
                showImagePicker = false
                showCamera = false
                selectedImage = nil
                
                // 发送登出通知，让应用跳转到登录页面
                NotificationCenter.default.post(name: Notification.Name("UserLoggedOut"), object: nil)
            }
            // 🆕 监听AI建议数据更新通知
            .onReceive(NotificationCenter.default.publisher(for: Notification.Name("PlanAdviceDataUpdated"))) { _ in
                Task { @MainActor in
                    print("📢 [ContentView] 收到AI建议数据更新通知")
                    
                    // 检查是否满足条件，如果满足则立即更新营养分母
                    if userData.isPremium && userData.hasPlan {
                        print("🔄 [ContentView] 条件满足，响应数据更新通知刷新营养分母")
                        let dailyGoals = planAdviceService.getSafeDailyNutritionGoals()
                        if dailyGoals.calories > 0 && dailyGoals.protein > 0 {
                            self.dailyCalorieGoal = dailyGoals.calories
                            self.dailyProteinGoal = dailyGoals.protein
                            print("🥗 [ContentView] 通知响应式更新营养分母:")
                            print("  - 卡路里目标: \(self.dailyCalorieGoal)")
                            print("  - 蛋白质目标: \(self.dailyProteinGoal)")
                        }
                    }
                }
            }
            // 🆕 专门监听周建议数据就绪通知
            .onReceive(NotificationCenter.default.publisher(for: Notification.Name("WeeklyAdviceDataReady"))) { _ in
                Task { @MainActor in
                    print("📢 [ContentView] 收到周建议数据就绪通知")
                    
                    // 立即检查并更新营养分母
                    if userData.isPremium && userData.hasPlan {
                        print("🔄 [ContentView] 条件满足，立即更新营养分母")
                        let dailyGoals = planAdviceService.getSafeDailyNutritionGoals()
                        if dailyGoals.calories > 0 && dailyGoals.protein > 0 {
                            self.dailyCalorieGoal = dailyGoals.calories
                            self.dailyProteinGoal = dailyGoals.protein
                            print("🥗 [ContentView] 周建议数据就绪后立即更新营养分母:")
                            print("  - 卡路里目标: \(self.dailyCalorieGoal)")
                            print("  - 蛋白质目标: \(self.dailyProteinGoal)")
                            
                            // 发送营养分母更新通知
                            NotificationCenter.default.post(name: Notification.Name("NutritionDenominatorUpdated"), object: nil)
                        } else {
                            print("⚠️ [ContentView] 周建议数据就绪但营养目标无效: 卡路里=\(dailyGoals.calories), 蛋白质=\(dailyGoals.protein)")
                        }
                    } else {
                        print("ℹ️ [ContentView] 周建议数据就绪但用户条件不满足: isPremium=\(userData.isPremium), hasPlan=\(userData.hasPlan)")
                    }
                }
            }
            // 监听订阅状态变化，当用户成为会员时重新加载营养分母数据
            .onChange(of: userData.isPremium) { oldValue, newValue in
                print("📱 [ContentView] 检测到订阅状态变化: \(oldValue) -> \(newValue)")
                
                // 🔧 优化：无论是否有目标，都尝试获取AI建议数据
                if !oldValue && newValue {
                    print("🎉 [ContentView] 用户成为会员，尝试获取AI建议数据")
                    
                    // 🆕 如果用户有计划，立即触发完整的建议生成和获取流程
                    if userData.hasPlan {
                        print("🚀 [ContentView] 用户有计划且成为会员，执行完整建议流程")
                        planAdviceService.executeAndFetchAdvicesAfterSubscription(with: userData) { success in
                            DispatchQueue.main.async {
                                if success {
                                    print("✅ [ContentView] 订阅后建议流程完成，立即更新营养分母")
                                    let dailyGoals = self.planAdviceService.getSafeDailyNutritionGoals()
                                    if dailyGoals.calories > 0 && dailyGoals.protein > 0 {
                                        self.dailyCalorieGoal = dailyGoals.calories
                                        self.dailyProteinGoal = dailyGoals.protein
                                        print("🥗 [ContentView] 订阅成功后营养分母更新:")
                                        print("  - 卡路里目标: \(self.dailyCalorieGoal)")
                                        print("  - 蛋白质目标: \(self.dailyProteinGoal)")
                                    }
                                } else {
                                    print("❌ [ContentView] 订阅后建议流程失败，使用备用刷新方法")
                                    self.refreshNutritionDenominatorData()
                                }
                            }
                        }
                    } else {
                        print("ℹ️ [ContentView] 用户成为会员但没有计划，等待计划设置")
                        refreshNutritionDenominatorData()
                    }
                    
                    // 🆕 如果已有缓存的营养目标数据，立即应用
                    let cachedGoals = planAdviceService.getSafeDailyNutritionGoals()
                    if cachedGoals.calories > 0 && cachedGoals.protein > 0 && userData.hasPlan {
                        print("🚀 [ContentView] 发现缓存的营养目标数据，立即应用")
                        self.dailyCalorieGoal = cachedGoals.calories
                        self.dailyProteinGoal = cachedGoals.protein
                        print("🥗 [ContentView] 立即应用营养分母:")
                        print("  - 卡路里目标: \(self.dailyCalorieGoal)")
                        print("  - 蛋白质目标: \(self.dailyProteinGoal)")
                    }
                }
                
                // 如果用户失去会员身份，重置分母为0或默认值
                if oldValue && !newValue {
                    print("❌ [ContentView] 用户失去会员身份，重置日营养分母")
                    self.dailyCalorieGoal = 0
                    self.dailyProteinGoal = 0
                }
            }
            // 监听目标计划状态变化，当用户设置目标时重新加载营养分母数据
            .onChange(of: userData.hasPlan) { oldValue, newValue in
                print("📱 [ContentView] 检测到目标计划状态变化: \(oldValue) -> \(newValue)")
                
                // 🔧 优化：无论是否是会员，都尝试获取AI建议数据
                if !oldValue && newValue {
                    print("🎯 [ContentView] 用户设置目标，尝试获取AI建议数据")
                    refreshNutritionDenominatorData()
                    
                    // 🆕 如果已有缓存的营养目标数据且是会员，立即应用
                    let cachedGoals = planAdviceService.getSafeDailyNutritionGoals()
                    if cachedGoals.calories > 0 && cachedGoals.protein > 0 && userData.isPremium {
                        print("🚀 [ContentView] 发现缓存的营养目标数据，立即应用")
                        self.dailyCalorieGoal = cachedGoals.calories
                        self.dailyProteinGoal = cachedGoals.protein
                        print("🥗 [ContentView] 立即应用营养分母:")
                        print("  - 卡路里目标: \(self.dailyCalorieGoal)")
                        print("  - 蛋白质目标: \(self.dailyProteinGoal)")
                        
                        // 发送通知，告知其他视图分母数据已更新
                        NotificationCenter.default.post(name: Notification.Name("NutritionDenominatorUpdated"), object: nil)
                    }
                }
                
                // 如果用户失去目标，重置分母为0或默认值
                if oldValue && !newValue {
                    print("❌ [ContentView] 用户失去目标，重置日营养分母")
                    self.dailyCalorieGoal = 0
                    self.dailyProteinGoal = 0
                }
            }
            // 🆕 添加日期变化监听器，确保跨天后自动刷新今日食物记录
            .onReceive(NotificationCenter.default.publisher(for: NSNotification.Name.NSCalendarDayChanged)) { _ in
                print("📅 [ContentView] 检测到日期变化，自动刷新今日食物记录")
                
                // 检查用户是否已登录
                guard !userData.accessToken.isEmpty else {
                    print("⚠️ [ContentView] 用户未登录，跳过日期变化刷新")
                    return
                }
                
                // 重新加载今日API食物记录
                DispatchQueue.main.async {
                    print("🔄 [ContentView] 开始加载新一天的食物记录...")
                    self.loadTodayAPIRecords()
                    print("✅ [ContentView] 日期变化刷新完成")
                }
            }
        }
                
            .navigationDestination(isPresented: $showExerciseDetail) {
                ExerciseFullScreenView()
                .environmentObject(userData)
        }
            .navigationDestination(isPresented: $showGoalWeightFullScreen) {
                GoalWeightFullScreenView().environmentObject(userData)
        }
            .navigationBarHidden(true)
        }
        .onDisappear {
            // 🔧 清理观察者，防止内存泄漏
            NotificationCenter.default.removeObserver(self, name: Notification.Name("PlanAdviceDataUpdated"), object: nil)
        }
        .alert("Login Required", isPresented: $showGuestPermissionAlert) {
            Button("Login", role: .cancel) {
                userData.exitGuestMode()
            }
            Button("Cancel", role: .destructive) { }
        } message: {
            Text("This feature requires AI analysis and needs login to access. Please login to use the food scanning feature.")
        }
    }
    
    // 刷新营养分母数据（当用户满足条件时调用）
    private func refreshNutritionDenominatorData() {
        print("🔄 [ContentView] 开始刷新营养分母数据 - isPremium: \(userData.isPremium), hasPlan: \(userData.hasPlan)")
        
        // 🔧 优化权限检查：只要用户登录就尝试获取数据，让服务端决定权限
        guard !userData.accessToken.isEmpty else {
            print("⚠️ [ContentView] 用户未登录，跳过刷新")
            return
        }
        
        // 🔧 新增：如果不是会员或没有目标，先记录状态但仍尝试获取数据
        if !userData.isPremium || !userData.hasPlan {
            print("ℹ️ [ContentView] 当前状态 - isPremium: \(userData.isPremium), hasPlan: \(userData.hasPlan)")
            print("ℹ️ [ContentView] 仍尝试获取营养建议数据，让服务端确定访问权限")
        }
        
        // 🔧 重要修复：使用响应式更新机制，立即检查现有数据
        let currentGoals = planAdviceService.getSafeDailyNutritionGoals()
        if currentGoals.calories > 0 && currentGoals.protein > 0 {
            print("✅ [ContentView] 发现已有有效的营养分母数据，立即更新UI")
            self.dailyCalorieGoal = currentGoals.calories
            self.dailyProteinGoal = currentGoals.protein
            print("🥗 [ContentView] 营养分母已立即更新:")
            print("  - 卡路里目标: \(self.dailyCalorieGoal)")
            print("  - 蛋白质目标: \(self.dailyProteinGoal)")
        }
        
        // 重新加载AI建议数据以获取最新的营养推荐
        print("🔄 [ContentView] 重新加载AI建议数据")
        planAdviceService.refreshAdvicesWithCompletion { success in
            DispatchQueue.main.async {
                if success {
                    print("✅ [ContentView] AI建议数据刷新成功，检查营养分母数据")
                    let dailyGoals = self.planAdviceService.getSafeDailyNutritionGoals()
                    
                    // 🔧 优化：根据用户状态决定是否更新分母
                    if dailyGoals.calories > 0 && dailyGoals.protein > 0 {
                        // 检查用户是否满足使用分母的条件
                        if self.userData.isPremium && self.userData.hasPlan {
                            self.dailyCalorieGoal = dailyGoals.calories
                            self.dailyProteinGoal = dailyGoals.protein
                            print("🥗 [ContentView] 用户满足条件，更新营养分母:")
                            print("  - 卡路里目标: \(self.dailyCalorieGoal)")
                            print("  - 蛋白质目标: \(self.dailyProteinGoal)")
                            
                            // 发送通知，告知其他视图分母数据已更新
                            NotificationCenter.default.post(name: Notification.Name("NutritionDenominatorUpdated"), object: nil)
                            return // 成功更新，不需要递归重试
                        } else {
                            print("ℹ️ [ContentView] 获取到营养目标数据，但用户条件不满足:")
                            print("  - isPremium: \(self.userData.isPremium), hasPlan: \(self.userData.hasPlan)")
                            print("  - 卡路里目标: \(dailyGoals.calories), 蛋白质目标: \(dailyGoals.protein)")
                            print("ℹ️ [ContentView] 等待用户状态变化后自动更新分母")
                            
                            // 🆕 将数据缓存，等待条件满足时使用
                            // 当用户状态变化时，onChange监听器会再次调用此方法
                            return
                        }
                    } else {
                        print("⚠️ [ContentView] 获取到的营养目标为0或无效: 卡路里=\(dailyGoals.calories), 蛋白质=\(dailyGoals.protein)")
                    }
                } else {
                    print("❌ [ContentView] AI建议数据刷新失败")
                }
                
                // 🔧 优化重试逻辑：只有在真正需要时才重试
                if self.userData.isPremium && self.userData.hasPlan {
                    print("⚠️ [ContentView] 用户满足条件但数据获取失败，启用重试机制")
                    self.checkAndUpdateNutritionDenominator(retryCount: 0, maxRetries: 3)
                } else {
                    print("ℹ️ [ContentView] 用户条件不满足，跳过重试机制")
                }
            }
        }
    }
    
    // 🆕 递归检查和更新营养分母的方法
    private func checkAndUpdateNutritionDenominator(retryCount: Int, maxRetries: Int) {
        let delay = retryCount == 0 ? 1.0 : 2.0 // 首次1秒后检查，后续每2秒检查一次
        
        DispatchQueue.main.asyncAfter(deadline: .now() + delay) {
            print("🔍 [ContentView] 第\(retryCount + 1)次检查营养分母数据...")
            
            if self.planAdviceService.isWeeklyAdviceDataComplete() {
                print("✅ [ContentView] 周建议数据完整，更新日营养分母")
                
                let dailyGoals = self.planAdviceService.getSafeDailyNutritionGoals()
                
                // 只有当获取到有效数据时才更新
                if dailyGoals.calories > 0 && dailyGoals.protein > 0 {
                    self.dailyCalorieGoal = dailyGoals.calories
                    self.dailyProteinGoal = dailyGoals.protein
                    
                    print("🥗 [ContentView] 日营养分母已成功更新:")
                    print("  - 卡路里目标: \(self.dailyCalorieGoal)")
                    print("  - 蛋白质目标: \(self.dailyProteinGoal)")
                    print("  - 当前会员状态: \(self.userData.isPremium)")
                    print("  - 当前目标状态: \(self.userData.hasPlan)")
                    
                    // 🆕 发送通知，告知其他视图分母数据已更新
                    NotificationCenter.default.post(name: Notification.Name("NutritionDenominatorUpdated"), object: nil)
                    
                    return // 成功更新，结束递归
                } else {
                    print("⚠️ [ContentView] 获取到的营养目标为0，继续重试")
                }
            } else {
                print("⚠️ [ContentView] 周建议数据仍不完整，继续重试")
            }
            
            // 如果还有重试次数，继续重试
            if retryCount < maxRetries {
                print("🔄 [ContentView] 营养分母数据未准备好，将在2秒后重试 (剩余重试次数: \(maxRetries - retryCount))")
                
                // 如果重试次数过半，重新触发数据加载
                if retryCount >= maxRetries / 2 {
                    print("🔄 [ContentView] 重试次数过半，重新触发AI建议数据加载")
                    self.planAdviceService.refreshAdvices()
                }
                
                self.checkAndUpdateNutritionDenominator(retryCount: retryCount + 1, maxRetries: maxRetries)
            } else {
                print("❌ [ContentView] 营养分母数据更新失败，已达到最大重试次数")
                
                // 🆕 最后一次尝试：使用强制刷新
                print("🔄 [ContentView] 最后尝试：强制获取营养分母数据")
                let finalGoals = self.planAdviceService.getSafeDailyNutritionGoals()
                if finalGoals.calories > 0 || finalGoals.protein > 0 {
                    self.dailyCalorieGoal = max(finalGoals.calories, self.dailyCalorieGoal)
                    self.dailyProteinGoal = max(finalGoals.protein, self.dailyProteinGoal)
                    print("🥗 [ContentView] 强制更新营养分母: 卡路里=\(self.dailyCalorieGoal), 蛋白质=\(self.dailyProteinGoal)")
                } else {
                    print("❌ [ContentView] 无法获取有效的营养分母数据")
                }
            }
        }
    }
    
    // 🆕 强制更新营养分母的方法
    private func forceUpdateNutritionDenominator() {
        print("🔧 [ContentView] 强制更新营养分母数据")
        
        let dailyGoals = planAdviceService.getSafeDailyNutritionGoals()
        print("🔍 [ContentView] 强制检查 - 获取到的营养目标: 卡路里=\(dailyGoals.calories), 蛋白质=\(dailyGoals.protein)")
        print("🔍 [ContentView] 强制检查 - 用户状态: isPremium=\(userData.isPremium), hasPlan=\(userData.hasPlan)")
        
        // 如果有有效数据，无论用户条件如何都尝试更新（让UI响应状态变化）
        if dailyGoals.calories > 0 && dailyGoals.protein > 0 {
            if userData.isPremium && userData.hasPlan {
                self.dailyCalorieGoal = dailyGoals.calories
                self.dailyProteinGoal = dailyGoals.protein
                print("🥗 [ContentView] 强制更新营养分母成功:")
                print("  - 卡路里目标: \(self.dailyCalorieGoal)")
                print("  - 蛋白质目标: \(self.dailyProteinGoal)")
                
                // 发送通知告知分母已更新
                NotificationCenter.default.post(name: Notification.Name("NutritionDenominatorUpdated"), object: nil)
            } else {
                print("ℹ️ [ContentView] 强制更新 - 有数据但用户条件不满足，等待状态变化")
            }
        } else {
            print("⚠️ [ContentView] 强制更新 - 仍然无法获取有效的营养分母数据")
            
            // 最后尝试：直接从PlanAdvice中获取原始的周数据并手动除7
            if let weeklyAdvice = planAdviceService.weeklyAdvice {
                print("🔄 [ContentView] 尝试手动计算日营养分母")
                
                // 手动解析和计算
                let weeklyCalorieStr = weeklyAdvice.calorieRecommend ?? ""
                let weeklyProteinStr = weeklyAdvice.proteinRecommend ?? ""
                
                print("🔍 [ContentView] 周建议原始数据: 卡路里='\(weeklyCalorieStr)', 蛋白质='\(weeklyProteinStr)'")
                
                if let weeklyCalories = Double(weeklyCalorieStr.trimmingCharacters(in: .whitespacesAndNewlines)),
                   let weeklyProtein = Double(weeklyProteinStr.trimmingCharacters(in: .whitespacesAndNewlines)) {
                    
                    let dailyCalories = Int(weeklyCalories / 7.0)
                    let dailyProtein = Int(weeklyProtein / 7.0)
                    
                    print("🧮 [ContentView] 手动计算结果: 周卡路里=\(weeklyCalories) -> 日卡路里=\(dailyCalories)")
                    print("🧮 [ContentView] 手动计算结果: 周蛋白质=\(weeklyProtein) -> 日蛋白质=\(dailyProtein)")
                    
                    if userData.isPremium && userData.hasPlan && dailyCalories > 0 && dailyProtein > 0 {
                        self.dailyCalorieGoal = dailyCalories
                        self.dailyProteinGoal = dailyProtein
                        print("🥗 [ContentView] 手动计算更新营养分母成功:")
                        print("  - 卡路里目标: \(self.dailyCalorieGoal)")
                        print("  - 蛋白质目标: \(self.dailyProteinGoal)")
                        
                        // 发送通知告知分母已更新
                        NotificationCenter.default.post(name: Notification.Name("NutritionDenominatorUpdated"), object: nil)
                    }
                } else {
                    print("❌ [ContentView] 手动计算失败 - 无法解析周建议数据")
                }
            } else {
                print("❌ [ContentView] 无法获取weeklyAdvice数据")
            }
        }
    }
    
    // 检查是否为新用户
    private func checkIfNewUser() {
        isNewUserChecked = true
        
        // 访客模式下不检查新用户状态，直接返回
        if userData.isGuestMode {
            print("🚪 访客模式，跳过新用户检查")
            return
        }
        
        // 先加载用户设置
        userData.loadSettings()
        
        // 确保selectedTab已正确初始化
        if userData.selectedTab < 0 {
            userData.selectedTab = 0 // 默认选择Nutrition标签页
            userData.saveSettings()
        }
        
        print("检查是否为新用户: id=\(userData.id), hasPlan=\(userData.hasPlan)")
        
        // 检查UserDefaults中的isNewUser标记，这是从SignUpView设置的
        let isNewUserFromSignUp = UserDefaults.standard.bool(forKey: "isNewUser")
        
        // 如果从注册页面来且标记为新用户，直接显示引导页面
        if isNewUserFromSignUp {
            print("从SignUp检测到新用户标记，显示引导页面")
            withAnimation {
                showOnboarding = true
            }
            // 重置标记，避免下次登录时再次显示引导页面
            UserDefaults.standard.set(false, forKey: "isNewUser")
            return
        }
        
        // 直接调用API检查用户状态和计划，不再进行本地判断
        print("调用API检查用户状态和计划")
        
        // 先检查用户是否有计划
        UserPlanService.shared.checkUserHasPlan(userData: userData) { hasPlan in
            DispatchQueue.main.async {
                print("API返回用户计划状态: hasPlan=\(hasPlan)")
                
                // 如果用户没有计划，显示引导页面
                if !hasPlan {
                    print("用户无计划，显示引导页面")
                    withAnimation {
                        self.showOnboarding = true
                    }
                    return
                }
                
                // 如果有计划，再检查是否为新用户
                UserExtendService.shared.getUserExtendInfo(userData: self.userData) { result in
                    DispatchQueue.main.async {
                        switch result {
                        case .success(let isNewUser):
                            print("API返回用户状态: isNewUser=\(isNewUser)")
                            withAnimation {
                                self.showOnboarding = isNewUser
                            }
                            
                            // 打印调试信息
                            print("设置showOnboarding=\(isNewUser), selectedTab=\(self.userData.selectedTab)")
                                
                        case .failure(let error):
                            print("获取用户状态失败: \(error.localizedDescription)")
                            
                            // 发生错误时，不修改引导页面状态，保持默认值
                            print("由于API错误，保持默认状态: showOnboarding=\(self.showOnboarding)")
                        }
                    }
                }
            }
        }
    }
    
    // 主页视图
    var homeView: some View {
        // 移除嵌套的NavigationView，因为外层已经有NavigationStack
        ZStack {
            // 顶部白色背景，延伸到安全区域之外
            VStack(spacing: 0) {
                Color.white
                    .frame(height: UIScreen.main.bounds.height * 0.18) // 调整高度，确保在photo图片位置开始淡绿色背景
                
                // 淡绿色背景从拍照区域开始，占屏幕下方70%
                Color.green.opacity(0.08)
                    .ignoresSafeArea()
            }
            .ignoresSafeArea(.all, edges: .top)
            
            ScrollView {
            VStack(spacing: 0) {
                // 健康旅程头部 - 根据访客模式显示不同内容
                if userData.isGuestMode {
                    // 访客模式头部
                    VStack(alignment: .leading, spacing: 8) {
                        Text("Guest Mode")
                            .font(.system(size: 20, weight: .bold))
                            .foregroundColor(Color(red: 0.12, green: 0.16, blue: 0.22))
                        
                        Text("Login to view personal data")
                            .font(.system(size: 14, weight: .regular))
                            .foregroundColor(Color(red: 0.42, green: 0.45, blue: 0.5))
                    }
                    .frame(maxWidth: .infinity, alignment: .leading)
                    .padding()
                    .background(Color.white)
                } else {
                    // 正常登录用户头部
                    VStack(alignment: .leading, spacing: 5) {
                        Text(userData.displayName.isEmpty ? "Hi," : "Hi, \(userData.displayName)")
                            .font(.largeTitle)
                            .fontWeight(.bold)
                            .lineLimit(1)  // 限制为单行显示
                            .truncationMode(.tail)  // 超出部分用省略号显示
                        
                        // 显示当前日期格式化为 "Monday, January 15" 的格式
                        Text(formattedDate())
                            .foregroundColor(.secondary)
                    }
                    .frame(maxWidth: .infinity, alignment: .leading)
                    .padding()
                    .background(Color.white)
                }
                
                // 绿色横幅和展开/折叠的会员区域 (访客模式下不显示)
                if !userData.isPremium && !userData.isGuestMode {
                    VStack(spacing: 0) {
                        NavigationLink(destination: 
                            FullScreenPremiumView()
                                .environmentObject(userData)
                                .navigationBarTitle("Unlock Premium Features", displayMode: .inline)
                        ) {
                        HStack {
                            HStack {
                                Image(systemName: "lock.fill")
                                Text(userData.getSubscriptionButtonText())
                            }
                            .foregroundColor(.white)
                            .padding()
                            .frame(maxWidth: .infinity, alignment: .leading)

                            Button(action: {
                                // 切换展开状态，并添加动画
                                withAnimation(.easeInOut) {
                                    isPremiumExpanded.toggle()
                                }
                            }) {
                                Text(isPremiumExpanded ? "Hide" : "Subscribe")
                                    .font(.system(size: 14))
                                    .lineSpacing(20 - 14) // 行高20px减去字体大小14px
                                    .foregroundColor(Color(red: 0.22, green: 0.56, blue: 0.24))
                                    .frame(width: 99.39, height: 28)
                                    .background(Color.yellow)
                                    .cornerRadius(28)
                            }
                            .padding(.trailing)
                        }
                        .background(
                            LinearGradient(
                                gradient: Gradient(colors: [
                                    Color(red: 0.3, green: 0.69, blue: 0.31),
                                    Color(red: 0.18, green: 0.49, blue: 0.2)
                                ]),
                                startPoint: .topLeading,
                                endPoint: .bottomTrailing
                            )
                        )                 
                        }

                        // 条件性显示的会员内容区域
                        if isPremiumExpanded {
                            PremiumFeaturesSection(onSubscriptionSuccess: {
                                // 🔧 立即检查并刷新营养分母数据，不等待onChange监听器
                                print("🎉 [ContentView] 订阅购买成功回调触发")
                                
                                // 立即检查用户状态并刷新营养分母
                                DispatchQueue.main.asyncAfter(deadline: .now() + 0.5) {
                                    print("🔄 [ContentView] 订阅成功后检查状态 - isPremium: \(self.userData.isPremium), hasPlan: \(self.userData.hasPlan)")
                                    
                                    // 🔧 优化：总是尝试刷新数据，让方法内部决定如何处理
                                    self.refreshNutritionDenominatorData()
                                    
                                    // 🆕 如果已有缓存数据且条件满足，立即应用
                                    let cachedGoals = self.planAdviceService.getSafeDailyNutritionGoals()
                                    if cachedGoals.calories > 0 && cachedGoals.protein > 0 && 
                                       self.userData.isPremium && self.userData.hasPlan {
                                        print("🚀 [ContentView] 订阅成功后发现缓存数据，立即应用")
                                        self.dailyCalorieGoal = cachedGoals.calories
                                        self.dailyProteinGoal = cachedGoals.protein
                                        print("🥗 [ContentView] 立即应用营养分母:")
                                        print("  - 卡路里目标: \(self.dailyCalorieGoal)")
                                        print("  - 蛋白质目标: \(self.dailyProteinGoal)")
                                        
                                        // 发送通知
                                        NotificationCenter.default.post(name: Notification.Name("NutritionDenominatorUpdated"), object: nil)
                                    }
                                }
                            })
                                .environmentObject(userData)
                                .transition(.move(edge: .top).combined(with: .opacity))
                        }
                    }
                }
                // 注意：移除了会员状态卡片显示，现在会员用户在nutrition页面不会看到会员卡片
                
                // 扫描食物按钮 - 淡绿色背景从这里开始
                VStack(spacing: 0) {
                    Button(action: {
                        // 检查是否为访客模式
                        if userData.isGuestMode {
                            showGuestPermissionAlert = true
                        } else {
                            showImagePicker = true
                        }
                    }) {
                    ZStack {
                        // 使用photo图片作为背景
                        Image("photo")
                            .resizable()
                            .aspectRatio(contentMode: .fill)
                            .frame(maxWidth: .infinity, maxHeight: 175)
                            .clipped()
                            .cornerRadius(15)
                        
                    
                        
                        // 只保留文本，位置下移到相机图标下方
                        VStack {
                            Spacer()
                            Text("Scan Food Now")
                                .foregroundColor(.white)
                                .fontWeight(.medium)
                                .font(.headline)
                                .padding(.bottom, 40)
                        }
                    }
                    .frame(maxWidth: .infinity)
                    .frame(height: 175)
                }
                .padding(.horizontal)
                .padding(.top)
                
                    // 营养数据
                    if userData.isGuestMode {
                        // 访客模式显示特殊的营养UI
                        GuestModeNutritionView()
                            .environmentObject(userData)
                            .padding(.top, 10)
                            .padding(.horizontal)
                    } else if !userData.hasPlan {
                        // 显示健康目标提示卡片
                        NutritionHealthGoalsView()
                            .environmentObject(userData)
                            .padding(.top, 10)
                    } else if !userData.isPremium {
                        // 非Premium用户显示Premium限制的营养UI
                        DailyNutritionPremiumView()
                            .environmentObject(userData)
                            .padding(.top, 10)
                            .padding(.horizontal)
                    } else {
                        // Premium用户显示完整营养数据 - 自适应布局
                        GeometryReader { screenGeometry in
                            HStack(spacing: 0) {
                                // 卡路里组件 - 占屏幕宽度一半减去间距
                                VStack(alignment: .leading, spacing: 12) {
                                    // 图标和标题
                                    HStack(spacing: 8) {
                                        Image(systemName: "flame.fill")
                                            .foregroundColor(.red)
                                            .font(.system(size: 16))
                                        Text("Calories")
                                            .font(.headline)
                                    }
                                    
                                    if isLoadingNutritionData {
                                        ProgressView()
                                            .progressViewStyle(CircularProgressViewStyle(tint: .green))
                                            .scaleEffect(0.8)
                                            .frame(maxWidth: .infinity, alignment: .leading)
                                    } else {
                                        // 分子分母
                                        let dailyCalorieGoal = self.dailyCalorieGoal
                                        HStack(alignment: .firstTextBaseline, spacing: 2) {
                                            Text("\(todayAPICalories)")
                                                .font(.title2)
                                                .fontWeight(.bold)
                                            Text("/ \(dailyCalorieGoal)")
                                                .font(.system(size: 14))
                                                .fontWeight(.bold)
                                            Text("kcal")
                                                .font(.system(size: 14))
                                                .fontWeight(.bold)
                                                
                                        }
                                        
                                        // 进度条
                                        let calorieProgress = min(CGFloat(todayAPICalories) / CGFloat(max(1, dailyCalorieGoal)), 1.0)
                                        ProgressView(value: calorieProgress)
                                            .progressViewStyle(LinearProgressViewStyle(tint: .green))
                                            .frame(height: 8)
                                            .clipShape(Capsule())
                                    }
                                }
                                .padding(16)
                                .frame(width: (screenGeometry.size.width - 32 - 12) / 2, height: 132) // 屏幕宽度减去左右边距再减去中间间距，除以2
                                .background(Color.white)
                                .cornerRadius(12)
                                .shadow(color: .black.opacity(0.1), radius: 4, x: 0, y: 2)
                                
                                Spacer()
                                    .frame(width: 12) // 两个组件之间的间距
                                
                                // 蛋白质组件 - 占屏幕宽度一半减去间距
                                VStack(alignment: .leading, spacing: 12) {
                                    // 图标和标题
                                    HStack(spacing: 8) {
                                        Image(systemName: "bolt.fill")
                                            .foregroundColor(.blue)
                                            .font(.system(size: 16))
                                        Text("Protein")
                                            .font(.headline)
                                    }
                                    
                                    if isLoadingNutritionData {
                                        ProgressView()
                                            .progressViewStyle(CircularProgressViewStyle(tint: .blue))
                                            .scaleEffect(0.8)
                                            .frame(maxWidth: .infinity, alignment: .leading)
                                    } else {
                                        // 分子分母
                                        let dailyProteinGoal = self.dailyProteinGoal
                                        HStack(alignment: .firstTextBaseline, spacing: 2) {
                                            Text("\(todayAPIProtein)")
                                                .font(.title2)
                                                .fontWeight(.bold)
                                            Text("/ \(dailyProteinGoal)")
                                                .font(.system(size: 14))
                                                .fontWeight(.bold)
                                            Text("g")
                                                .font(.system(size: 14))
                                                .fontWeight(.bold)
                                                
                                        }
                                        
                                        // 进度条
                                        let proteinProgress = min(CGFloat(todayAPIProtein) / CGFloat(max(1, dailyProteinGoal)), 1.0)
                                        ProgressView(value: proteinProgress)
                                            .progressViewStyle(LinearProgressViewStyle(tint: .blue))
                                            .frame(height: 8)
                                            .clipShape(Capsule())
                                    }
                                }
                                .padding(16)
                                .frame(width: (screenGeometry.size.width - 32 - 12) / 2, height: 132) // 屏幕宽度减去左右边距再减去中间间距，除以2
                                .background(Color.white)
                                .cornerRadius(12)
                                .shadow(color: .black.opacity(0.1), radius: 4, x: 0, y: 2)
                            }
                            .padding(.horizontal, 16) // 与拍照区域保持相同的左右边距
                        }
                        .frame(height: 132) // 固定高度
                        .padding(.top, 20) // 添加顶部间距
                    }
                    
                    // 今日食物日记
                    VStack(alignment: .leading, spacing: 10) {
                        HStack {
                            Text("Today's Food Diary")
                                .font(.headline)
                            
                            Spacer()
                            
                            Button(action: {
                                // 跳转到食物日志详情页面
                                userData.selectedTab = -1 // 临时设置一个特殊值表示正在显示食物日志
                            }) {
                                Text("more")
                                    .font(.subheadline)
                                    .foregroundColor(.gray)
                                
                                Image(systemName: "chevron.right")
                                    .font(.caption)
                                    .foregroundColor(.gray)
                            }
                        }
                        .padding(.horizontal)
                        
                        // 只显示API食物记录
                        if !todayAPIRecords.isEmpty {
                            VStack(spacing: 8) {
                                // 按餐点类型分组显示
                                let groupedRecords = groupRecordsByMealType(todayAPIRecords.prefix(3))
                                
                                ForEach(Array(groupedRecords.keys.sorted()), id: \.self) { mealType in
                                    if let records = groupedRecords[mealType] {
                                        // 餐点类型标题
                                        HStack {
                                            Text(mealType)
                                                .font(.subheadline)
                                                .fontWeight(.medium)
                                                .foregroundColor(.secondary)
                                                .padding(.horizontal, 16)
                                                .padding(.top, 8)
                                                .padding(.bottom, 4)
                                            
                                            Spacer()
                                        }
                                        
                                        // 该餐点的食物记录
                                        ForEach(records.prefix(2)) { record in
                                            NavigationLink(destination: 
                                                DetailedFoodAnalysisView(
                                                    isPresented: .constant(true),
                                                    foodRecordId: record.id
                                                )
                                                .environmentObject(userData)
                                                .navigationBarTitle("Food Analysis", displayMode: .inline)
                                            ) {
                                                APIHomeFoodRecordRow(record: record)
                                            }
                                            .buttonStyle(PlainButtonStyle())
                                            .background(Color(red: 1, green: 1, blue: 1))
                                            .cornerRadius(12)
                                            .shadow(color: Color.black.opacity(0.1), radius: 4, x: 0, y: 2)
                                            .padding(.horizontal)
                                        }
                                    }
                                }
                            }
                        } else if isLoadingTodayRecords {
                            // 加载状态
                            HStack {
                                ProgressView()
                                    .progressViewStyle(CircularProgressViewStyle())
                                    .scaleEffect(0.8)
                                
                                Text("Loading food records...")
                                    .font(.subheadline)
                                    .foregroundColor(.secondary)
                            }
                            .frame(maxWidth: .infinity)
                            .padding()
                            .background(Color(red: 1, green: 1, blue: 1))
                            .cornerRadius(12)
                            .shadow(color: Color.black.opacity(0.1), radius: 4, x: 0, y: 2)
                            .padding(.horizontal)
                        }
                    }
                    .padding(.top)
                    .onAppear {
                        // 加载今日API食物记录
                        loadTodayAPIRecords()
                    }
                    
                    Spacer()
                    }
                }
            }
        }
        .onAppear {
            // 统计
            XDTrackTool.shared.appear("营养主页")
            
            // 从API获取用户信息
            if !userData.accessToken.isEmpty {
                print("homeView 正在尝试获取用户信息，当前用户昵称: \(userData.nickname)")
                UserService.shared.updateUserDataWithFetchedInfo(userData: userData)
                
                // 加载今日API食物记录
                loadTodayAPIRecords()
            }
        }
    }
    
    // 按餐点类型分组食物记录
    private func groupRecordsByMealType(_ records: ArraySlice<DietRecordResponse>) -> [String: [DietRecordResponse]] {
        var grouped: [String: [DietRecordResponse]] = [:]
        
        for record in records {
            let mealType = getMealType(from: record.timeStr)
            if grouped[mealType] == nil {
                grouped[mealType] = []
            }
            grouped[mealType]?.append(record)
        }
        
        return grouped
    }
    
    // 根据时间字符串判断餐点类型
    private func getMealType(from timeStr: String) -> String {
        // 解析时间字符串，timeStr格式为 "HH:MM AM/PM"
        let components = timeStr.components(separatedBy: " ")
        guard components.count >= 2 else { return "Meal" }
        
        let timeComponent = components[0]
        let ampmComponent = components[1]
        
        let timeComponents = timeComponent.split(separator: ":")
        guard let hourStr = timeComponents.first, let hour = Int(hourStr) else { return "Meal" }
        
        // 转换为24小时制
        var hour24 = hour
        if ampmComponent.uppercased() == "PM" && hour != 12 {
            hour24 += 12
        } else if ampmComponent.uppercased() == "AM" && hour == 12 {
            hour24 = 0
        }
        
        // 判断餐点类型 - 修改逻辑：凌晨0:00-4:59也算作早餐
        if (hour24 >= 0 && hour24 < 5) || (hour24 >= 5 && hour24 < 12) {
            return "Breakfast"
        } else if hour24 >= 12 && hour24 < 17 {
            return "Lunch"
        } else {
            return "Dinner"
        }
    }
    
    // 加载今日API食物记录
    private func loadTodayAPIRecords() {
        // 检查是否已登录
        guard !userData.accessToken.isEmpty else {
            print("用户未登录，跳过今日API食物记录加载")
            return
        }
        
        // 格式化今日日期 - 修改为API要求的yyyyMMdd格式
        let formatter = DateFormatter()
        formatter.dateFormat = "yyyyMMdd"
        let todayStr = formatter.string(from: Date())
        
        isLoadingTodayRecords = true
        isLoadingNutritionData = true
        
        ImageUploadService.shared.getFoodRecords(dateStr: todayStr, authToken: userData.accessToken) { result in
            DispatchQueue.main.async {
                self.isLoadingTodayRecords = false
                self.isLoadingNutritionData = false
                
                switch result {
                case .success(let records):
                    self.todayAPIRecords = records
                    
                    // 计算今日总卡路里和蛋白质
                    self.todayAPICalories = records.reduce(0) { $0 + $1.calories }
                    self.todayAPIProtein = records.reduce(0) { $0 + $1.protein }
                    
                    print("成功加载今日\(records.count)条API食物记录，总卡路里：\(self.todayAPICalories)，总蛋白质：\(self.todayAPIProtein)g")
                    
                    // 输出所有记录的ID用于调试
                    for record in records {
                        print("- 记录ID: \(record.id), 食物名称: \(record.foodName), 时间: \(record.timeStr)")
                    }
                    
                    // 预加载今日食物记录的图片
                    self.preloadTodayFoodImages(records)
                    
                    // 检查并优化图片缓存
                    ImageCacheManager.shared.smartCacheCleanup()
                    
                case .failure(let error):
                    print("加载今日API食物记录失败: \(error.localizedDescription)")
                    self.todayAPIRecords = []
                    self.todayAPICalories = 0
                    self.todayAPIProtein = 0
                }
            }
        }
    }
    
    // 预加载今日食物记录的图片
    private func preloadTodayFoodImages(_ records: [DietRecordResponse]) {
        let imageUrls = records.compactMap { record in
            record.hasImage ? record.fileLocation : nil
        }.filter { !$0.isEmpty }
        
        if !imageUrls.isEmpty {
            print("🚀 开始预加载\(imageUrls.count)张今日食物图片（智能压缩）")
            
            // 使用超高压缩级别预加载，提高加载速度
            ImageCacheManager.shared.preloadImages(from: imageUrls, compressionLevel: .ultra)
            
            // 显示缓存状态
            DispatchQueue.main.asyncAfter(deadline: .now() + 2.0) {
                ImageCacheManager.shared.getFoodDiaryCacheStatus()
            }
        }
    }
    
    // 格式化日期为 "Monday, January 15" 格式
    func formattedDate() -> String {
        let formatter = DateFormatter()
        formatter.dateFormat = "EEEE, MMMM d"
        return formatter.string(from: Date())
    }
    
    // 配置TabBar外观
    func configureTabBarAppearance() {
        let targetColor = UIColor(red: 0.18, green: 0.8, blue: 0.44, alpha: 1.0)
        
        // 设置TabBar基本颜色
        UITabBar.appearance().tintColor = targetColor
        UITabBar.appearance().unselectedItemTintColor = .gray
        
        // 设置文本颜色
        UITabBarItem.appearance().setTitleTextAttributes([
            NSAttributedString.Key.foregroundColor: targetColor
        ], for: .selected)
        
        UITabBarItem.appearance().setTitleTextAttributes([
            NSAttributedString.Key.foregroundColor: UIColor.gray
        ], for: .normal)
    }
}

// 访客模式营养组件 (复制非会员UI样式)
struct GuestModeNutritionView: View {
    @EnvironmentObject var userData: UserData
    
    var body: some View {
        ZStack {
            // 背景虚拟数据
            HStack(spacing: 0) {
                // 卡路里（虚拟数据）
                VStack(alignment: .leading, spacing: 8) {
                    HStack {
                        Image(systemName: "flame.fill")
                            .foregroundColor(.red.opacity(0.15))
                        Text("Calories")
                            .font(.headline)
                            .foregroundColor(.gray.opacity(0.15))
                    }
                    
                    HStack(alignment: .firstTextBaseline, spacing: 2) {
                        Text("? ? ?")
                            .font(.title3)
                            .fontWeight(.bold)
                            .foregroundColor(.gray.opacity(0.15))
                        Text("/ ? ? ?")
                            .font(.system(size: 14))
                            .fontWeight(.bold)
                            .foregroundColor(.gray.opacity(0.15))
                        Text("kcal")
                            .font(.system(size: 14))
                            .fontWeight(.bold)
                            .foregroundColor(.gray.opacity(0.15))
                    }
                    
                    ProgressView(value: 0.0)
                        .progressViewStyle(LinearProgressViewStyle(tint: .gray.opacity(0.15)))
                        .padding(.top, 8)
                }
                .frame(maxWidth: .infinity, alignment: .leading)
                .padding()
                
                // 蛋白质（虚拟数据）
                VStack(alignment: .leading, spacing: 8) {
                    HStack {
                        Image(systemName: "bolt.fill")
                            .foregroundColor(.blue.opacity(0.15))
                        Text("Protein")
                            .font(.headline)
                            .foregroundColor(.gray.opacity(0.15))
                    }
                    
                    HStack(alignment: .firstTextBaseline, spacing: 2) {
                        Text("? ?")
                            .font(.title3)
                            .fontWeight(.bold)
                            .foregroundColor(.gray.opacity(0.15))
                        Text("/ ? ?")
                            .font(.system(size: 14))
                            .fontWeight(.bold)
                            .foregroundColor(.gray.opacity(0.15))
                        Text("g")
                            .font(.system(size: 14))
                            .fontWeight(.bold)
                            .foregroundColor(.gray.opacity(0.15))
                    }
                    
                    ProgressView(value: 0.0)
                        .progressViewStyle(LinearProgressViewStyle(tint: .gray.opacity(0.15)))
                        .padding(.top, 8)
                }
                .frame(maxWidth: .infinity, alignment: .leading)
                .padding()
            }
            .blur(radius: 2.0) // 稍微增加模糊效果
            
            // 访客模式蒙层
            VStack(alignment: .leading, spacing: 15) {
                HStack {
                    Image(systemName: "exclamationmark.triangle.fill")
                        .foregroundColor(.orange)
                        .font(.title2)
                    
                    Text("Login for Full Features")
                        .font(.headline)
                        .fontWeight(.semibold)
                    
                    Spacer()
                }
                
                VStack(alignment: .leading, spacing: 12) {
                    Text("Login to view personal data and unlock premium features")
                        .font(.subheadline)
                        .foregroundColor(.secondary)
                        .multilineTextAlignment(.leading)
                    
                    Spacer()
                    
                    // Login按钮放在右下角
                    HStack {
                        Spacer()
                        Button(action: {
                            userData.exitGuestMode()
                        }) {
                            Text("Login")
                                .font(.subheadline)
                                .fontWeight(.semibold)
                                .foregroundColor(.white)
                                .padding(.horizontal, 20)
                                .padding(.vertical, 10)
                                .background(Color(red: 0.3, green: 0.69, blue: 0.31))
                                .cornerRadius(8)
                        }
                    }
                }
            }
            .padding(20)
            .background(
                RoundedRectangle(cornerRadius: 12)
                    .fill(Color.yellow.opacity(0.1))
                    .overlay(
                        RoundedRectangle(cornerRadius: 12)
                            .stroke(Color.orange.opacity(0.3), lineWidth: 1)
                    )
            )
        }
    }
}

// 主页食物条目行视图
struct HomeFoodEntryRow: View {
    let entry: FoodEntry
    
    var body: some View {
        HStack(spacing: 12) {
            // 食物图片
            if let imageData = entry.image, let uiImage = UIImage(data: imageData) {
                Image(uiImage: uiImage)
                    .resizable()
                    .scaledToFill()
                    .frame(width: 60, height: 60)
                    .cornerRadius(8)
            } else {
                ZStack {
                    Rectangle()
                        .fill(Color.gray.opacity(0.2))
                        .frame(width: 60, height: 60)
                        .cornerRadius(8)
                    
                    Image(systemName: "photo")
                        .foregroundColor(.gray)
                }
            }
            
            // 食物信息
            VStack(alignment: .leading, spacing: 4) {
                Text(getMealTypeFromHour(entry.time.hour))
                    .font(.caption)
                    .foregroundColor(.secondary)
                
                Text(entry.name)
                    .font(.headline)
                
                Text("\(formatTime(entry.time))")
                    .font(.caption)
                    .foregroundColor(.secondary)
            }
            
            Spacer()
            
            // 卡路里信息
            VStack(alignment: .trailing) {
                Text("\(entry.calories)")
                    .font(.headline)
                    .foregroundColor(.green)
                
                Text("kcal")
                    .font(.caption)
                    .foregroundColor(.secondary)
            }
        }
        .padding()
    }
    
    // 格式化时间
    func formatTime(_ date: Date) -> String {
        let formatter = DateFormatter()
        formatter.timeStyle = .short
        return formatter.string(from: date)
    }
}

// 扩展Date以便获取小时值
extension Date {
    var hour: Int {
        return Calendar.current.component(.hour, from: self)
    }
}

// 新增：将会员区域提取为子视图，以便复用和管理
struct PremiumFeaturesSection: View {
    @EnvironmentObject var userData: UserData
    @StateObject private var subscriptionService = SubscriptionService.shared
    @State private var selectedPlan: String = "annual" // 默认选择年度计划
    @State private var isLoading = false
    @State private var errorMessage: String?
    @State private var showAlert = false
    
    // 回调函数，在订阅成功后调用
    let onSubscriptionSuccess: () -> Void
    
    var body: some View {
        VStack(alignment: .leading, spacing: 15) {
            Text("Unlock Premium Features")
                .font(.title2)
                .fontWeight(.bold)
                .foregroundColor(.white)

            NavigationLink(destination: 
                FullScreenPremiumView()
                    .environmentObject(userData)
                    .navigationBarTitle("Unlock Premium Features", displayMode: .inline)
            ) {
            Text("Get the most out of FitScanAI >")
                .font(.custom("Inter", size: 16))
                .fontWeight(.regular) // 字重400对应regular
                .lineSpacing(8) // 行高24px - 字号16px = 8px行间距
                .foregroundColor(.white.opacity(0.8))
                }

            HStack {
                Image(systemName: "lock.fill") // 修改为锁定图标，因为这是未订阅状态
                    .foregroundColor(.white)
                Text("Premium Features")
                    .foregroundColor(.white)
                    .fontWeight(.semibold)
            }

            VStack(alignment: .leading, spacing: 10) {
                HStack {
                    Image(systemName: "checkmark.circle.fill")
                        .foregroundColor(.white)
                    Text("Unlimited food analysis")
                        .foregroundColor(.white)
                }

                HStack {
                    Image(systemName: "checkmark.circle.fill")
                        .foregroundColor(.white)
                    Text("Detailed nutrition insights")
                        .foregroundColor(.white)
                }

                HStack {
                    Image(systemName: "checkmark.circle.fill")
                        .foregroundColor(.white)
                    Text("Personal nutrition coach")
                        .foregroundColor(.white)
                }
            }
            .padding(.top, 5)

            HStack(spacing: 10) {
                // Monthly Plan Box
                VStack(spacing: 5) {
                    Text("Monthly Plan")
                        .font(.footnote)
                        .foregroundColor(selectedPlan == "monthly" ? .green : .white.opacity(0.9))

                    Text("$5.99")
                        .font(.title)
                        .fontWeight(.bold)
                        .foregroundColor(selectedPlan == "monthly" ? .green : .white)

                    Text("per month")
                        .font(.caption)
                        .foregroundColor(selectedPlan == "monthly" ? .green.opacity(0.7) : .white.opacity(0.7))
                }
                .frame(maxWidth: .infinity)
                .padding(.vertical, 15)
                .background(selectedPlan == "monthly" ? Color.white : Color.white.opacity(0.2))
                .cornerRadius(10)
                .overlay(
                    RoundedRectangle(cornerRadius: 10)
                        .stroke(selectedPlan == "monthly" ? Color.green : Color.clear, lineWidth: 2)
                )
                .onTapGesture {
                    selectedPlan = "monthly"
                }

                // Annual Plan Box
                VStack(spacing: 5) {
                    ZStack(alignment: .topTrailing) {
                        VStack(spacing: 5) {
                            Text("Annual Plan")
                                .font(.footnote)
                                .foregroundColor(selectedPlan == "annual" ? .green : .white.opacity(0.9))
                            
                            Text("$59.9")
                                .font(.title)
                                .fontWeight(.bold)
                                .foregroundColor(selectedPlan == "annual" ? .green : .white)
                            
                            Text("per year")
                                .font(.caption)
                                .foregroundColor(selectedPlan == "annual" ? .green.opacity(0.7) : .white.opacity(0.7))
                        }
                        .frame(maxWidth: .infinity) // 使文本居中显示
                        
                        // Save%标签放在右上角
                        Text("Save17%")
                            .font(.caption2)
                            .fontWeight(.bold)
                            .foregroundColor(.black)
                            .padding(.horizontal, 8)
                            .padding(.vertical, 4)
                            .background(Color.yellow)
                            .cornerRadius(10)
                            .offset(x: 1, y: -12) // 如需微调位置可修改这里的偏移量
                    }
                }
                .frame(maxWidth: .infinity)
                .padding(.vertical, 15)
                .background(selectedPlan == "annual" ? Color.white : Color.white.opacity(0.2))
                .cornerRadius(10)
                .overlay(
                    RoundedRectangle(cornerRadius: 10)
                        .stroke(selectedPlan == "annual" ? Color.green : Color.clear, lineWidth: 2)
                )
                .onTapGesture {
                    selectedPlan = "annual"
                }
            }

            // 订阅按钮 - 集成IAP功能
            Button(action: {
                Task {
                    await handleSubscription()
                }
            }) {
                HStack {
                    if isLoading {
                        ProgressView()
                            .progressViewStyle(CircularProgressViewStyle(tint: .black))
                            .scaleEffect(0.8)
                    }
                    Text(isLoading ? "Processing..." : userData.getSubscriptionButtonText())
                        .fontWeight(.semibold)
                }
                .frame(maxWidth: .infinity)
                .padding()
                .background(Color.yellow)
                .foregroundColor(.black)
                .cornerRadius(10)
            }
            .disabled(isLoading)

            Text("Cancel anytime")
                .font(.custom("Inter", size: 14))
                .fontWeight(.regular)
                .lineLimit(nil)
                .fixedSize(horizontal: false, vertical: true)
                .foregroundColor(Color(red: 1, green: 1, blue: 1).opacity(0.8))
                .frame(maxWidth: .infinity, alignment: .center)
                .lineSpacing(6) 
        }
        .padding()
        .background(
            LinearGradient(
                gradient: Gradient(colors: [
                    Color(red: 0.3, green: 0.69, blue: 0.31),
                    Color(red: 0.18, green: 0.49, blue: 0.2)
                ]),
                startPoint: .topLeading,
                endPoint: .bottomTrailing
            )
        ) // Background for the whole section
        .onAppear {
            Task {
                await subscriptionService.fetchSubscriptionProducts()
            }
        }
        .alert("Subscription Error", isPresented: $showAlert) {
            Button("OK", role: .cancel) { }
        } message: {
            Text(errorMessage ?? "Unknown error occurred")
        }
    }
    
    // 处理订阅购买
    @MainActor
    private func handleSubscription() async {
        isLoading = true
        errorMessage = nil
        
        do {
            // 获取对应的产品ID
            let productId: String
            if selectedPlan == "monthly" {
                guard let monthlyProduct = subscriptionService.getMonthlyProduct() else {
                    throw NSError(domain: "SubscriptionError", code: 1, userInfo: [NSLocalizedDescriptionKey: "Monthly product not found"])
                }
                productId = monthlyProduct.productId
            } else {
                guard let annualProduct = subscriptionService.getAnnualProduct() else {
                    throw NSError(domain: "SubscriptionError", code: 1, userInfo: [NSLocalizedDescriptionKey: "Annual product not found"])
                }
                productId = annualProduct.productId
            }
            
            print("📱 PremiumFeaturesSection: 开始购买 \(selectedPlan) 订阅，产品ID: \(productId)")
            
            // 使用StoreKitManager进行购买
            if #available(iOS 15.0, *) {
                // 首先加载产品
                await StoreKitManager.shared.loadProducts()
                
                // 查找对应的产品
                guard let product = StoreKitManager.shared.products.first(where: { $0.id == productId }) else {
                    throw NSError(domain: "SubscriptionError", code: 2, userInfo: [NSLocalizedDescriptionKey: "Product not found in App Store"])
                }
                
                // 发起购买
                let success = await StoreKitManager.shared.purchase(product)
                
                if success {
                    print("✅ PremiumFeaturesSection: 订阅购买成功")
                    // 刷新用户订阅状态
                    await userData.checkAndUpdateSubscriptionStatus()
                    
                    // 调用回调函数，让父视图处理刷新逻辑
                    onSubscriptionSuccess()
                } else {
                    throw NSError(domain: "SubscriptionError", code: 3, userInfo: [NSLocalizedDescriptionKey: "Purchase failed or was cancelled"])
                }
            } else {
                throw NSError(domain: "SubscriptionError", code: 4, userInfo: [NSLocalizedDescriptionKey: "iOS 15.0 or later required for subscriptions"])
            }
            
        } catch {
            print("❌ PremiumFeaturesSection: 订阅购买失败: \(error)")
            errorMessage = error.localizedDescription
            showAlert = true
        }
        
        isLoading = false
    }
}

// 新增：将底部导航栏提取为子视图
struct BottomNavBar: View {
    var body: some View {
        VStack(spacing: 0) {
             // 分割线 (可选)
            Divider()

            HStack(spacing: 0) {
                VStack(spacing: 5) {
                    Image(systemName: "house.fill")
                        .foregroundColor(.green)
                    Text("Home")
                        .font(.caption)
                        .foregroundColor(.green)
                }
                .frame(maxWidth: .infinity)

                VStack(spacing: 5) {
                    Image(systemName: "chart.xyaxis.line")
                        .foregroundColor(.secondary)
                    Text("Analysis")
                        .font(.caption)
                        .foregroundColor(.secondary)
                }
                .frame(maxWidth: .infinity)

                VStack(spacing: 5) {
                    Image(systemName: "person")
                        .foregroundColor(.secondary)
                    Text("Profile")
                        .font(.caption)
                        .foregroundColor(.secondary)
                }
                .frame(maxWidth: .infinity)
            }
            .padding(.top, 10)
            .padding(.bottom, 5) // Adjust padding as needed
            .background(.bar) // Use standard background material

            // 移除底部黑条，iOS通常使用系统指示器
            // Rectangle()
            //     .fill(Color.black)
            //     .frame(width: 40, height: 5)
            //     .cornerRadius(2.5)
            //     .padding(.vertical, 10)
        }
    }
}

// 相机视图 - 保留原来的实现以兼容
struct CameraView: View {
    @Binding var isPresented: Bool
    @Binding var showAnalyzing: Bool
    @Binding var selectedImage: UIImage?
    @State private var sourceType: UIImagePickerController.SourceType = .camera
    @State private var showImagePicker = false
    
    var body: some View {
        ZStack {
            Color.black.ignoresSafeArea()
            
            VStack(spacing: 0) {
                // 顶部标题
                Text("Position food in the frame")
                    .font(.headline)
                    .foregroundColor(.green)
                    .padding(.top, 20)
                
                Spacer()
                
                // 相机预览区域（使用模拟图片）
                ZStack {
                    Image("food_bowl")
                        .resizable()
                        .aspectRatio(contentMode: .fill)
                        .frame(width: UIScreen.main.bounds.width - 40, height: UIScreen.main.bounds.width - 40)
                        .clipShape(RoundedRectangle(cornerRadius: 15))
                        .overlay(
                            RoundedRectangle(cornerRadius: 15)
                                .stroke(Color.green, lineWidth: 2)
                        )
                    
                    // 网格线
                    GridLines()
                }
                
                // 提示文本
                Text("For best results, ensure good lighting")
                    .font(.subheadline)
                    .foregroundColor(.secondary)
                    .padding(.top, 20)
                
                Spacer()
                
                // 相机控制按钮
                HStack(spacing: 50) {
                    // 相册按钮
                    Button(action: {
                        sourceType = .photoLibrary
                        showImagePicker = true
                    }) {
                        Image(systemName: "photo")
                            .font(.system(size: 24))
                            .foregroundColor(.white)
                            .frame(width: 60, height: 60)
                    }
                    
                    // 拍照按钮
                    Button(action: {
                        // 模拟拍照，然后显示分析页面
                        DispatchQueue.main.asyncAfter(deadline: .now() + 0.5) {
                            // 使用示例图片作为已拍照的图片
                            selectedImage = UIImage(named: "food_bowl")
                            isPresented = false
                            showAnalyzing = true
                        }
                    }) {
                        Circle()
                            .fill(Color.green)
                            .frame(width: 70, height: 70)
                            .overlay(
                                Circle()
                                    .stroke(Color.white, lineWidth: 4)
                                    .frame(width: 60, height: 60)
                            )
                    }
                    
                    // 闪光灯按钮
                    Button(action: {}) {
                        Image(systemName: "bolt.fill")
                            .font(.system(size: 24))
                            .foregroundColor(.white)
                            .frame(width: 60, height: 60)
                    }
                }
                .padding(.bottom, 40)
            }
            .padding(.horizontal)
            
            // 关闭按钮
            VStack {
                HStack {
                    Button(action: {
                        isPresented = false
                    }) {
                        Image(systemName: "xmark")
                            .font(.title2)
                            .foregroundColor(.white)
                            .padding()
                    }
                    
                    Spacer()
                }
                
                Spacer()
            }
        }
        .sheet(isPresented: $showImagePicker) {
            if #available(iOS 14, *) {
                StandardPhotoPicker(selectedImage: $selectedImage, isPresented: $showImagePicker) {
                    print("📸 图片选择完成，准备显示分析页面")
                    isPresented = false
                    showAnalyzing = true
                }
            } else {
                UnsupportedPhotoPicker(isPresented: $showImagePicker) {
                    // 如果设备不支持，回调后什么都不做
                }
            }
        }
    }
}

// 网格线视图
struct GridLines: View {
    var body: some View {
        GeometryReader { geometry in
            ZStack {
                // 水平线
                ForEach(0..<4) { i in
                    let position = geometry.size.height / 3 * CGFloat(i)
                    if i > 0 && i < 3 {
                        Path { path in
                            path.move(to: CGPoint(x: 0, y: position))
                            path.addLine(to: CGPoint(x: geometry.size.width, y: position))
                        }
                        .stroke(Color.white, lineWidth: 1)
                    }
                }
                
                // 垂直线
                ForEach(0..<4) { i in
                    let position = geometry.size.width / 3 * CGFloat(i)
                    if i > 0 && i < 3 {
                        Path { path in
                            path.move(to: CGPoint(x: position, y: 0))
                            path.addLine(to: CGPoint(x: position, y: geometry.size.height))
                        }
                        .stroke(Color.white, lineWidth: 1)
                    }
                }
            }
        }
    }
}

// 图片选择器视图
struct ImagePickerView: View {
    @Binding var isPresented: Bool
    @Binding var showCamera: Bool
    @Binding var showPhotosPicker: Bool
    @Binding var selectedImage: UIImage?
    @State private var showPhotoLibrary = false
    @State private var showPermissionAlert = false
    @State private var alertTitle = "Photo Library Access"
    @State private var alertMessage = "Please allow FitScanAI to access your photo library to select a profile picture."
    
    var body: some View {
        VStack(spacing: 20) {
            HStack {
                Text("Choose Image Source")
                    .font(.title2)
                    .fontWeight(.bold)
                
                Spacer()
                
                Button(action: {
                    isPresented = false
                }) {
                    Image(systemName: "xmark")
                        .foregroundColor(.secondary)
                }
            }
            .padding(.bottom)
            
            Text("Select how you want to add your image")
                .foregroundColor(.secondary)
            
            Button(action: {
                isPresented = false
                showCamera = true
            }) {
                HStack {
                    Image(systemName: "camera.fill")
                        .font(.title2)
                        .foregroundColor(.green)
                        .frame(width: 40, height: 40)
                    
                    Text("Take Photo")
                        .fontWeight(.medium)
                        .foregroundColor(.primary)
                    
                    Spacer()
                }
                .padding()
                .frame(maxWidth: .infinity)
                .background(Color.green.opacity(0.1))
                .cornerRadius(10)
            }
            
            Button(action: {
                checkPhotoLibraryPermission()
            }) {
                HStack {
                    Image(systemName: "photo.fill")
                        .font(.title2)
                        .foregroundColor(.green)
                        .frame(width: 40, height: 40)
                    
                    Text("Choose from Gallery")
                        .fontWeight(.medium)
                        .foregroundColor(.primary)
                    
                    Spacer()
                }
                .padding()
                .frame(maxWidth: .infinity)
                .background(Color.green.opacity(0.1))
                .cornerRadius(10)
            }
        }
        .padding()
        .background(
            RoundedRectangle(cornerRadius: 20)
                .fill(Color.white)
        )
        .onAppear {
            // 统计
            XDTrackTool.shared.appear("图片选择弹窗")
        }
        .sheet(isPresented: $showPhotoLibrary) {
            if #available(iOS 14, *) {
                StandardPhotoPicker(selectedImage: $selectedImage, isPresented: $showPhotoLibrary) {
                    print("📸 图片库选择完成，准备显示分析页面")
                    isPresented = false
                    showPhotosPicker = true
                }
            } else {
                UnsupportedPhotoPicker(isPresented: $showPhotoLibrary) {
                    // 如果设备不支持，回调后什么都不做
                }
            }
        }
        .alert(isPresented: $showPermissionAlert) {
            Alert(
                title: Text(alertTitle),
                message: Text(alertMessage),
                primaryButton: .default(Text("Go to Settings")) {
                    if let url = URL(string: UIApplication.openSettingsURLString) {
                        UIApplication.shared.open(url)
                    }
                },
                secondaryButton: .cancel(Text("Cancel"))
            )
        }
    }
    
    // 检查相册权限 - 使用新的权限管理系统
    private func checkPhotoLibraryPermission() {
        print("📸 ContentView: Requesting photo library permission with user-friendly explanation")
        
        // 使用增强的权限管理器
        PhotoLibraryPermissionManager.shared.requestPhotoLibraryAccess { [self] status in
            DispatchQueue.main.async {
                switch status {
                case .authorized, .limited:
                    print("✅ ContentView: Photo library access granted, showing picker")
                    self.showPhotoLibrary = true
                default:
                    print("❌ ContentView: Photo library access denied")
                    // 权限被拒绝，不显示相册选择器
                    break
                }
            }
        }
    }
}

// 新增：使用PHPickerViewController的照片选择器，正确处理受限访问
@available(iOS 14, *)
struct LimitedPhotoPicker: UIViewControllerRepresentable {
    @Binding var selectedImage: UIImage?
    @Binding var isPresented: Bool
    var onImageSelected: () -> Void
    
    func makeUIViewController(context: Context) -> PHPickerViewController {
        var configuration = PHPickerConfiguration()
        configuration.filter = .images
        configuration.selectionLimit = 1
        
        let picker = PHPickerViewController(configuration: configuration)
        picker.delegate = context.coordinator
        return picker
    }
    
    func updateUIViewController(_ uiViewController: PHPickerViewController, context: Context) {}
    
    func makeCoordinator() -> Coordinator {
        Coordinator(self)
    }
    
    class Coordinator: NSObject, PHPickerViewControllerDelegate {
        let parent: LimitedPhotoPicker
        
        init(_ parent: LimitedPhotoPicker) {
            self.parent = parent
        }
        
        func picker(_ picker: PHPickerViewController, didFinishPicking results: [PHPickerResult]) {
            parent.isPresented = false
            
            guard let result = results.first else { return }
            
            result.itemProvider.loadObject(ofClass: UIImage.self) { [weak self] object, error in
                if let error = error {
                    print("Photo loading error: \(error.localizedDescription)")
                    return
                }
                
                guard let image = object as? UIImage else { return }
                
                DispatchQueue.main.async {
                    self?.parent.selectedImage = image
                    self?.parent.onImageSelected()
                }
            }
        }
    }
}

// 添加兼容iOS 13的图片选择器界面
struct UnsupportedPhotoPicker: View {
    @Binding var isPresented: Bool
    var onDismiss: () -> Void
    
    var body: some View {
        VStack(spacing: 20) {
            Image(systemName: "exclamationmark.triangle")
                .font(.system(size: 50))
                .foregroundColor(.orange)
                .padding()
            
            Text("Device Not Supported")
                .font(.title2)
                .fontWeight(.bold)
            
            Text("Limited photo access requires iOS 14 or later.\nPlease update your device to use this feature.")
                .multilineTextAlignment(.center)
                .padding()
            
            Button(action: {
                isPresented = false
                onDismiss()
            }) {
                Text("OK")
                    .frame(maxWidth: .infinity)
                    .padding()
                    .background(Color.green)
                    .foregroundColor(.white)
                    .cornerRadius(8)
            }
            .padding(.horizontal)
        }
        .padding()
        .background(Color(.systemBackground))
        .cornerRadius(12)
        .shadow(radius: 10)
        .frame(maxWidth: 320)
    }
}

// 分析中视图
struct AnalyzingView: View {
    @Binding var isPresented: Bool
    @Binding var showResult: Bool
    @Binding var selectedImage: UIImage?
    @EnvironmentObject var userData: UserData
    @State private var progress: Double = 0
    @State private var caloriesProgress: Double = 0
    @State private var waterProgress: Double = 0
    @State private var fatProgress: Double = 0
    // 添加从左到右加载效果的状态变量
    @State private var caloriesWidthProgress: Double = 0
    @State private var waterWidthProgress: Double = 0
    @State private var fatWidthProgress: Double = 0
    @State private var analysisText = "Analyzing..."
    @State private var rotation = 0.0
    @State private var circleScale: CGFloat = 0.8
    
    // 添加上传和识别状态变量
    @State private var isUploading = false
    @State private var imageId: String? = nil
    @State private var fileId: String? = nil
    @State private var fileLocation: String? = nil
    @State private var uploadError: String? = nil
    @State private var showError = false
    
    // 添加取消分析的状态变量
    @State private var isCancelled = false
    
    // 添加分析失败的提示弹窗状态
    @State private var showAnalysisFailedAlert = false
    @State private var analysisFailedMessage = ""
    
    // 添加屏幕尺寸检测
    private var isSmallScreen: Bool {
        UIScreen.main.bounds.height <= 812 // iPhone 12 mini 及更小屏幕
    }
    
    private var imageSize: CGFloat {
        let screenWidth = UIScreen.main.bounds.width
        let padding: CGFloat = isSmallScreen ? 60 : 40
        return min(screenWidth - padding, isSmallScreen ? 280 : 320)
    }
    
    var body: some View {
        GeometryReader { geometry in
            VStack(spacing: 0) {
                // 导航栏 - Safe Area适配布局
                HStack {
                    Button(action: {
                        print("🚫 Cancel button tapped - 开始执行取消操作")
                        
                        // 立即触发触觉反馈
                        let impactFeedback = UIImpactFeedbackGenerator(style: .medium)
                        impactFeedback.impactOccurred()
                        
                        // 立即设置取消标志
                        isCancelled = true
                        
                        // 清理数据
                        userData.currentFoodAnalysis = nil
                        selectedImage = nil
                        showResult = false
                        
                        // 直接关闭分析页面
                        isPresented = false
                        
                        // 发送通知确保ContentView也更新状态
                        NotificationCenter.default.post(name: Notification.Name("ForceCloseAnalyzing"), object: nil)
                        
                        print("✅ Cancel completed - 分析页面已关闭")
                    }) {
                        Text("Cancel")
                            .foregroundColor(.green)
                            .font(.system(size: 16, weight: .medium))
                            .frame(minWidth: 80, minHeight: 50) // 增大点击区域
                            .background(Color.clear) // 确保背景透明但可点击
                            .contentShape(Rectangle()) // 确保整个区域都可点击
                    }
                    .buttonStyle(PlainButtonStyle()) // 使用简单按钮样式，避免动画延迟
                    
                    Spacer()
                    
                    Text("Food Analysis")
                        .font(.headline)
                    
                    Spacer()
                    
                    // 空按钮占位，保持对称
                    Text("")
                        .frame(width: 80) // 与Cancel按钮宽度保持一致
                }
                .padding(.horizontal, 20)
                .padding(.top, geometry.safeAreaInsets.top) // 直接使用Safe Area，零间距
                .frame(height: geometry.safeAreaInsets.top + 25) // 导航栏总高度：Safe Area + 25px，减少10px更紧凑
                .background(Color.white)
                
                // 主要内容区域
                ScrollView {
                    VStack(spacing: isSmallScreen ? 15 : 20) {
                // 图片显示 - 调整位置和大小
                if let image = selectedImage {
                    // 检查是否是占位图片（空图片）
                    let isPlaceholder = image.size.width == 0 || image.size.height == 0
                    
                    if isPlaceholder {
                        // 显示加载占位符
                        ZStack {
                            RoundedRectangle(cornerRadius: isSmallScreen ? 12 : 15)
                                .fill(Color.gray.opacity(0.2))
                                .frame(width: UIScreen.main.bounds.width - 42, height: (UIScreen.main.bounds.width - 42) * 0.72)
                            
                            VStack(spacing: isSmallScreen ? 10 : 15) {
                                ProgressView()
                                    .progressViewStyle(CircularProgressViewStyle(tint: .green))
                                    .scaleEffect(1.2)
                                
                                Text("Loading image...")
                                    .font(isSmallScreen ? .body : .title3)
                                    .foregroundColor(.green)
                                    .fontWeight(.medium)
                            }
                        }
                        .padding(.horizontal, 21)
                        .padding(.top, 23)
                    } else {
                        // 显示真实图片
                        Image(uiImage: image)
                            .resizable()
                            .aspectRatio(contentMode: .fill)
                            .frame(width: UIScreen.main.bounds.width - 42, height: (UIScreen.main.bounds.width - 42) * 0.72) // 左右各21px margin，调整宽高比
                            .clipShape(RoundedRectangle(cornerRadius: isSmallScreen ? 12 : 15))
                            .overlay(
                                RoundedRectangle(cornerRadius: isSmallScreen ? 12 : 15)
                                    .stroke(Color.secondary.opacity(0.3), lineWidth: 1)
                            )
                            .overlay(
                                ZStack {
                                    // 半透明遮罩
                                    Color.black.opacity(0.5)
                                        .clipShape(RoundedRectangle(cornerRadius: isSmallScreen ? 12 : 15))
                                    
                                    // 分析动画
                                    VStack(spacing: isSmallScreen ? 10 : 15) {
                                        // 旋转的圆弧
                                        Circle()
                                            .trim(from: 0, to: 0.7)
                                            .stroke(Color.green, lineWidth: 3)
                                            .frame(width: isSmallScreen ? 80 : 100, height: isSmallScreen ? 80 : 100)
                                            .rotationEffect(Angle(degrees: rotation))
                                        
                                        Text(analysisText)
                                            .font(isSmallScreen ? .body : .title3)
                                            .foregroundColor(.green)
                                            .fontWeight(.medium)
                                    }
                                }
                            )
                            .padding(.horizontal, 21) // 左右各21px间距
                            .padding(.top, 23) // 距离导航栏23px
                    }
                }
                
                // 进度显示
                VStack(spacing: 5) {
                    Text("\(Int(progress * 100))%")
                        .font(isSmallScreen ? .title2 : .title)
                        .foregroundColor(.green)
                        .fontWeight(.bold)
                    
                    Text("Analysis in Progress")
                        .font(isSmallScreen ? .caption : .subheadline)
                        .foregroundColor(.secondary)
                }
                .padding(.top, isSmallScreen ? 10 : 15)
                
                // 营养数据进度 - 小屏幕优化
                VStack(spacing: isSmallScreen ? 10 : 15) {
                    // 卡路里
                    HStack {
                        HStack(spacing: isSmallScreen ? 10 : 15) {
                            Circle()
                                .fill(Color.red.opacity(0.2))
                                .frame(width: isSmallScreen ? 35 : 40, height: isSmallScreen ? 35 : 40)
                                .overlay(
                                    Image(systemName: "flame.fill")
                                        .foregroundColor(.red)
                                        .font(.system(size: isSmallScreen ? 14 : 16))
                                )
                            
                            VStack(alignment: .leading, spacing: 2) {
                                Text("342 kcal")
                                    .font(isSmallScreen ? .subheadline : .headline)
                                Text("Estimated Calories")
                                    .font(.caption)
                                    .foregroundColor(.secondary)
                            }
                        }
                        
                        Spacer()
                        
                        Text("\(Int(caloriesProgress * 100))%")
                            .foregroundColor(.green)
                            .font(isSmallScreen ? .caption : .subheadline)
                            .opacity(caloriesWidthProgress > 0.1 ? 1 : 0)
                            .animation(.easeInOut(duration: 0.3), value: caloriesWidthProgress)
                    }
                    
                    // 修改卡路里进度条，从左到右加载
                    GeometryReader { geometry in
                        ZStack(alignment: .leading) {
                            // 灰色背景
                            Rectangle()
                                .fill(Color.gray.opacity(0.2))
                                .frame(height: 4)
                                .cornerRadius(2)
                            
                            // 绿色进度条
                            Rectangle()
                                .fill(Color.green)
                                .frame(width: geometry.size.width * caloriesWidthProgress, height: 4)
                                .cornerRadius(2)
                        }
                    }
                    .frame(height: 4)
                    
                    // 水分含量
                    HStack {
                        HStack(spacing: isSmallScreen ? 10 : 15) {
                            Circle()
                                .fill(Color.blue.opacity(0.2))
                                .frame(width: isSmallScreen ? 35 : 40, height: isSmallScreen ? 35 : 40)
                                .overlay(
                                    Image(systemName: "drop.fill")
                                        .foregroundColor(.blue)
                                        .font(.system(size: isSmallScreen ? 14 : 16))
                                )
                            
                            VStack(alignment: .leading, spacing: 2) {
                                Text("65%")
                                    .font(isSmallScreen ? .subheadline : .headline)
                                Text("Water Content")
                                    .font(.caption)
                                    .foregroundColor(.secondary)
                            }
                        }
                        
                        Spacer()
                        
                        Text("\(Int(waterProgress * 100))%")
                            .foregroundColor(.blue)
                            .font(isSmallScreen ? .caption : .subheadline)
                            .opacity(waterWidthProgress > 0.1 ? 1 : 0)
                            .animation(.easeInOut(duration: 0.3), value: waterWidthProgress)
                    }
                    
                    // 修改水分进度条，从左到右加载
                    GeometryReader { geometry in
                        ZStack(alignment: .leading) {
                            // 灰色背景
                            Rectangle()
                                .fill(Color.gray.opacity(0.2))
                                .frame(height: 4)
                                .cornerRadius(2)
                            
                            // 蓝色进度条
                            Rectangle()
                                .fill(Color.blue)
                                .frame(width: geometry.size.width * waterWidthProgress, height: 4)
                                .cornerRadius(2)
                        }
                    }
                    .frame(height: 4)
                    
                    // 脂肪
                    HStack {
                        HStack(spacing: isSmallScreen ? 10 : 15) {
                            Circle()
                                .fill(Color.yellow.opacity(0.2))
                                .frame(width: isSmallScreen ? 35 : 40, height: isSmallScreen ? 35 : 40)
                                .overlay(
                                    Image(systemName: "circle.grid.cross.fill")
                                        .foregroundColor(.yellow)
                                        .font(.system(size: isSmallScreen ? 14 : 16))
                                )
                            
                            VStack(alignment: .leading, spacing: 2) {
                                Text("12g")
                                    .font(isSmallScreen ? .subheadline : .headline)
                                Text("Total Fat")
                                    .font(.caption)
                                    .foregroundColor(.secondary)
                            }
                        }
                        
                        Spacer()
                        
                        Text("\(Int(fatProgress * 100))%")
                            .foregroundColor(.yellow)
                            .font(isSmallScreen ? .caption : .subheadline)
                            .opacity(fatWidthProgress > 0.1 ? 1 : 0)
                            .animation(.easeInOut(duration: 0.3), value: fatWidthProgress)
                    }
                    
                    // 修改脂肪进度条，从左到右加载
                    GeometryReader { geometry in
                        ZStack(alignment: .leading) {
                            // 灰色背景
                            Rectangle()
                                .fill(Color.gray.opacity(0.2))
                                .frame(height: 4)
                                .cornerRadius(2)
                            
                            // 黄色进度条
                            Rectangle()
                                .fill(Color.yellow)
                                .frame(width: geometry.size.width * fatWidthProgress, height: 4)
                                .cornerRadius(2)
                        }
                    }
                    .frame(height: 4)
                }
                .padding(.horizontal, 21) // 与图片保持一致的左右间距
                .padding(.vertical, isSmallScreen ? 10 : 15)
                
                Text("Analyzing additional nutrients...")
                    .font(isSmallScreen ? .caption : .subheadline)
                    .foregroundColor(.secondary)
                
                Spacer()
                    }
                }
            }
        }
        .background(Color.white)
        .onAppear {
            // 统计
            XDTrackTool.shared.appear("食物分析页面")

            // 重置所有状态变量，确保每次进入页面时状态都是干净的
            print("🔄 AnalyzingView appeared - 重置所有状态")

            // 确保在主线程执行状态重置
            DispatchQueue.main.async {
                isCancelled = false
                progress = 0
                caloriesProgress = 0
                waterProgress = 0
                fatProgress = 0
                caloriesWidthProgress = 0
                waterWidthProgress = 0
                fatWidthProgress = 0
                analysisText = "Analyzing..."
                rotation = 0.0
                circleScale = 0.8
                isUploading = false
                imageId = nil
                fileId = nil
                fileLocation = nil
                uploadError = nil
                showError = false
                showAnalysisFailedAlert = false
                analysisFailedMessage = ""
                
                print("✅ AnalyzingView 状态重置完成")
                
                // 开始动画和上传
                startAnimations()
                uploadImageAndIdentify()
            }
        }
        .alert(isPresented: $showError) {
            Alert(
                title: Text("Analysis Error"),
                message: Text(uploadError ?? "Unable to analyze image, please try again"),
                dismissButton: .default(Text("OK")) {
                    // 关闭分析视图
                    isPresented = false
                }
            )
        }
        .alert("Analysis Result", isPresented: $showAnalysisFailedAlert) {
            Button("OK") {
                // 点击OK后退回到nutrition页面
                isPresented = false
            }
        } message: {
            Text(analysisFailedMessage)
        }
    }
    
    // 获取安全区域顶部间距的辅助函数
    private func getSafeAreaTop() -> CGFloat {
        if #available(iOS 15.0, *) {
            if let windowScene = UIApplication.shared.connectedScenes.first as? UIWindowScene,
               let window = windowScene.windows.first {
                return window.safeAreaInsets.top
            }
        } else {
            // iOS 15以下版本的兼容处理
            if let window = UIApplication.shared.windows.first {
                return window.safeAreaInsets.top
            }
        }
        return 20 // 默认值
    }
    
    // 开始所有动画
    func startAnimations() {
        // 旋转动画
        withAnimation(Animation.linear(duration: 2).repeatForever(autoreverses: false)) {
            rotation = 360
        }
        
        // 圆点缩放动画
        withAnimation(Animation.easeInOut(duration: 0.8).repeatForever(autoreverses: true)) {
            circleScale = 1.3
        }
        
        // 进度动画
        withAnimation(.easeInOut(duration: 1.2)) {
            progress = 0.78
        }
        
        // 从左到右加载卡路里进度条动画
        DispatchQueue.main.asyncAfter(deadline: .now() + 0.1) {
            withAnimation(.easeOut(duration: 1.0)) {
                caloriesWidthProgress = 0.78
            }
            
            withAnimation(.easeInOut(duration: 1.0).delay(0.3)) {
                caloriesProgress = 0.78
            }
        }
        
        // 从左到右加载水分进度条动画，更多延迟
        DispatchQueue.main.asyncAfter(deadline: .now() + 0.4) {
            withAnimation(.easeOut(duration: 0.8)) {
                waterWidthProgress = 0.65
            }
            
            withAnimation(.easeInOut(duration: 0.8).delay(0.3)) {
                waterProgress = 0.65
            }
        }
        
        // 从左到右加载脂肪进度条动画，更多延迟
        DispatchQueue.main.asyncAfter(deadline: .now() + 0.8) {
            withAnimation(.easeOut(duration: 0.6)) {
                fatWidthProgress = 0.45
            }
            
            withAnimation(.easeInOut(duration: 0.6).delay(0.3)) {
                fatProgress = 0.45
            }
        }
    }
    
    // 上传图片并识别
    private func uploadImageAndIdentify() {
        // 首先检查用户是否已经取消了分析
        if isCancelled {
            print("🚫 uploadImageAndIdentify: 用户已取消分析，不执行上传")
            return
        }
        
        guard let image = selectedImage else {
            uploadError = "No image selected"
            showError = true
            return
        }
        
        // 确保有认证令牌
        guard !userData.accessToken.isEmpty else {
            uploadError = "Invalid token, please login again"
            showError = true
            return
        }
        
        // 再次检查取消状态
        if isCancelled {
            print("🚫 uploadImageAndIdentify: 用户已取消分析，不开始上传")
            return
        }
        
        isUploading = true
        print("📤 开始上传图片...")
        
        // 上传图片
        ImageUploadService.shared.uploadImage(image: image, authToken: userData.accessToken) { result in
            DispatchQueue.main.async {
                // 检查用户是否已经取消了分析 - 在任何处理之前立即检查
                if self.isCancelled {
                    print("用户已取消分析，停止上传处理")
                    return
                }
                
                switch result {
                case .success(let response):
                    print("Image upload success: \(response)")
                    
                    // 再次检查取消状态
                    if self.isCancelled {
                        print("检测到用户已取消，不进行后续识别")
                        return
                    }
                    
                    // 保存图片相关信息
                    self.imageId = response.imageId
                    self.fileId = response.fileId
                    self.fileLocation = response.fileLocation
                    
                    // 更新状态
                    self.analysisText = "Identifying image..."
                    
                    // 识别图片
                    self.identifyFoodImage(
                        fileLocation: response.fileLocation,
                        fileId: response.fileId,
                        imageId: response.imageId
                    )
                    
                case .failure(let error):
                    // 检查用户是否已经取消了分析
                    if self.isCancelled {
                        print("用户已取消分析，不显示上传错误")
                        return
                    }
                    
                    // 检查是否是401错误
                    let nsError = error as NSError
                    if nsError.code == 401 {
                        print("🚨 图片上传遇到401错误，Token可能已过期")
                        // 发送Token过期通知
                        NotificationCenter.default.post(name: Notification.Name("TokenExpired"), object: nil)
                        return
                    }
                    
                    // 显示其他错误
                    self.uploadError = "Upload failed: \(error.localizedDescription)"
                    self.showError = true
                    self.isUploading = false
                    print("Image upload failed: \(error)")
                }
            }
        }
    }
    
    // 识别食物图片
    private func identifyFoodImage(fileLocation: String?, fileId: String?, imageId: String?) {
        // 首先检查用户是否已经取消了分析
        if isCancelled {
            print("🚫 identifyFoodImage: 用户已取消分析，不执行识别")
            return
        }
        
        // 确保有认证令牌
        guard !userData.accessToken.isEmpty else {
            uploadError = "Invalid token, please login again"
            showError = true
            return
        }
        
        // 再次检查取消状态
        if isCancelled {
            print("🚫 identifyFoodImage: 用户已取消分析，不开始识别")
            return
        }
        
        print("🔍 开始识别食物图片...")
        
        ImageUploadService.shared.identifyFoodImage(
            fileLocation: fileLocation,
            fileId: fileId,
            imageId: imageId,
            authToken: userData.accessToken
        ) { result in
            DispatchQueue.main.async {
                self.isUploading = false
                
                // 检查用户是否已经取消了分析 - 在任何处理之前立即检查
                if self.isCancelled {
                    print("用户已取消分析，停止所有后续处理")
                    return
                }
                
                switch result {
                case .success(let analysisResult):
                    print("Food recognition succeeded: \(analysisResult)")
                    
                    // 再次检查取消状态，确保不会在用户取消后显示结果
                    if self.isCancelled {
                        print("检测到用户已取消，不处理成功结果")
                        return
                    }
                    
                    // 检查是否是食物 - 需要先获取原始响应数据来检查is_food字段
                    // 由于当前的FoodAnalysisResult结构体没有包含is_food字段，我们需要修改API调用
                    // 暂时通过检查食物名称来判断
                    if analysisResult.foodName.isEmpty || 
                       analysisResult.foodName.lowercased().contains("unknown") ||
                       analysisResult.foodName.lowercased().contains("not food") ||
                       analysisResult.foodName.lowercased().contains("无法识别") ||
                       analysisResult.nutritionScore <= 0 {
                        
                        // 显示分析失败的提示
                        if analysisResult.foodName.lowercased().contains("not food") ||
                           analysisResult.foodName.lowercased().contains("无法识别") {
                            self.analysisFailedMessage = "The image does not appear to contain food. Please try taking a photo of food items."
                        } else {
                            self.analysisFailedMessage = "Unable to analyze this image. Please ensure the food is clearly visible and try again."
                        }
                        
                        self.showAnalysisFailedAlert = true
                        return
                    }
                    
                    // 检查营养成分是否有效
                    if analysisResult.calories <= 0 && 
                       analysisResult.protein <= 0 && 
                       analysisResult.carbs <= 0 && 
                       analysisResult.fat <= 0 {
                        
                        self.analysisFailedMessage = "Unable to determine nutritional information for this food. Please try a clearer photo."
                        self.showAnalysisFailedAlert = true
                        return
                    }
                    
                    // 如果分析结果有效，保存并显示结果页面
                    self.userData.currentFoodAnalysis = analysisResult
                    
                    // 3秒后切换到结果视图，给用户足够时间看到动画
                    DispatchQueue.main.asyncAfter(deadline: .now() + 1.0) {
                        // 最终检查用户是否取消了分析
                        if !self.isCancelled {
                            // 完成分析动画后，切换到结果视图
                            self.isPresented = false
                            self.showResult = true
                        } else {
                            print("用户已取消分析，不显示结果页面")
                        }
                    }
                    
                case .failure(let error):
                    print("Food recognition failed: \(error)")
                    
                    // 检查用户是否已经取消了分析
                    if self.isCancelled {
                        print("用户已取消分析，不显示错误提示")
                        return
                    }
                    
                    // 检查是否是401错误
                    let nsError = error as NSError
                    if nsError.code == 401 {
                        print("🚨 食物识别遇到401错误，Token可能已过期")
                        // 发送Token过期通知
                        NotificationCenter.default.post(name: Notification.Name("TokenExpired"), object: nil)
                        return
                    }
                    
                    // 根据错误类型显示不同的提示信息
                    let errorMessage = error.localizedDescription
                    
                    if errorMessage.contains("not food") || errorMessage.contains("非食物") {
                        self.analysisFailedMessage = "The image does not appear to contain food. Please try taking a photo of food items."
                    } else if errorMessage.contains("no result") || errorMessage.contains("无结果") || errorMessage.contains("empty") {
                        self.analysisFailedMessage = "Unable to analyze this image. Please ensure the food is clearly visible and try again."
                    } else if errorMessage.contains("network") || errorMessage.contains("网络") {
                        self.analysisFailedMessage = "Network connection error. Please check your internet connection and try again."
                    } else if errorMessage.contains("timeout") || errorMessage.contains("超时") {
                        self.analysisFailedMessage = "Request timeout. Please try again later."
                    } else {
                        self.analysisFailedMessage = "Analysis failed. Please try taking a clearer photo of the food."
                    }
                    
                    self.showAnalysisFailedAlert = true
                }
            }
        }
    }
}

// 结果视图
struct ResultView: View {
    @Binding var isPresented: Bool
    @Binding var selectedImage: UIImage?
    @EnvironmentObject var userData: UserData
    var addToFoodDiary: (String, Int, Int, Int, Int) -> Void
    
    // 存储识别结果的状态变量
    @State private var foodName: String = "Analyzing..."
    @State private var nutritionScore: Int = 0
    @State private var calories: Int = 0 
    @State private var protein: Int = 0
    @State private var carbs: Int = 0
    @State private var fat: Int = 0
    @State private var caloriePercentage: Int = 0
    @State private var recommendations: [String] = []
    @State private var water: Int = 0
    @State private var fiber: Int = 0
    @State private var sugar: Int = 0
    @State private var conversationId: String = "" // 新增：保存会话ID
    
    // 上传和识别状态变量
    @State private var isUploading = false
    @State private var isAnalyzing = true
    @State private var uploadError: String? = nil
    @State private var showError = false
    @State private var imageId: String? = nil
    @State private var fileId: String? = nil
    @State private var fileLocation: String? = nil
    
    var body: some View {
        VStack(spacing: 0) {
            // 导航栏
            HStack {
                Button(action: {
                    isPresented = false
                }) {
                    Image(systemName: "arrow.left")
                        .font(.title3)
                }
                
                Spacer()
                
                Text("Food Analysis")
                    .font(.headline)
                
                Spacer()
                
                // 空视图保持对称
                Image(systemName: "arrow.left")
                    .font(.title3)
                    .opacity(0)
            }
            .padding()
            
            if isAnalyzing {
                // 分析中...
                VStack(spacing: 20) {
                    Spacer()
                    
                    // 显示选中的图片
                    if let image = selectedImage {
                        Image(uiImage: image)
                            .resizable()
                            .aspectRatio(contentMode: .fill)
                            .frame(height: 200)
                            .clipped()
                    }
                    
                    ProgressView()
                        .progressViewStyle(CircularProgressViewStyle())
                        .scaleEffect(1.5)
                    
                    Text("Analyzing food image...")
                        .font(.headline)
                        .padding()
                    
                    Spacer()
                }
            } else {
                ScrollView {
                    VStack(spacing: 20) {
                        // 食物图片
                        if let image = selectedImage {
                            Image(uiImage: image)
                                .resizable()
                                .aspectRatio(contentMode: .fill)
                                .frame(height: 200)
                                .clipped()
                        }
                        
                        // 营养评分
                        HStack(spacing: 15) {
                            ZStack {
                                Circle()
                                    .trim(from: 0, to: Double(nutritionScore) / 100)
                                    .stroke(
                                        LinearGradient(
                                            gradient: Gradient(colors: [.green, .yellow, .orange]),
                                            startPoint: .leading,
                                            endPoint: .trailing
                                        ),
                                        style: StrokeStyle(lineWidth: 10, lineCap: .round)
                                    )
                                    .rotationEffect(.degrees(-90))
                                    .frame(width: 80, height: 80)
                                
                                VStack {
                                    Text("\(nutritionScore)")
                                        .font(.title2)
                                        .fontWeight(.bold)
                                    Text("/ 100")
                                        .font(.caption)
                                        .foregroundColor(.secondary)
                                }
                            }
                            
                            VStack(alignment: .leading) {
                                Text("Nutrition Score")
                                    .font(.headline)
                                // 将条件表达式拆分为变量
                                let nutritionMessage = nutritionScore >= 80 ? 
                                    "This meal has excellent nutritional value!" : 
                                    "This meal has good nutritional value."
                                Text(nutritionMessage)
                                    .font(.subheadline)
                                    .foregroundColor(.secondary)
                                    .fixedSize(horizontal: false, vertical: true)
                            }
                        }
                        .padding()
                        .background(Color.gray.opacity(0.1))
                        .cornerRadius(15)
                        .padding(.horizontal)
                        
                        // 添加到食物日记按钮
                        Button(action: {
                            uploadAndAddToFoodDiary()
                        }) {
                            HStack {
                                if isUploading {
                                    ProgressView()
                                        .progressViewStyle(CircularProgressViewStyle(tint: .white))
                                        .padding(.trailing, 5)
                                } else {
                                    Image(systemName: "plus")
                                }
                                
                                Text(isUploading ? "Uploading..." : "Today's Food Diary")
                                    .font(.headline)
                                    .foregroundColor(.white)
                            }
                           .padding()
                           .frame(maxWidth: .infinity)
                           .background(Color.green)
                           .foregroundColor(.white)
                           .cornerRadius(10)
                           .padding(.horizontal)
                        }
                        .disabled(isUploading)
                        
                        // 对每日目标的影响 - 仅当卡路里超过300时显示警告
                        if calories > 300 {
                            VStack(alignment: .leading, spacing: 10) {
                                Text("Impact on Daily Goals")
                                    .font(.headline)
                                    .padding(.horizontal)
                                
                                HStack {
                                    Image(systemName: "exclamationmark.triangle.fill")
                                        .foregroundColor(.orange)
                                    
                                    VStack(alignment: .leading) {
                                        Text("High in Calories")
                                            .font(.subheadline)
                                            .fontWeight(.semibold)
                                            .foregroundColor(.orange)
                                        
                                        Text("This meal contains \(caloriePercentage)% of your daily calorie allowance")
                                            .font(.caption)
                                            .foregroundColor(.secondary)
                                    }
                                }
                                .padding()
                                .frame(maxWidth: .infinity, alignment: .leading)
                                .background(Color.orange.opacity(0.1))
                                .cornerRadius(10)
                                .padding(.horizontal)
                            }
                        }
                        
                        // 建议 - 使用API返回的建议数据
                        if !recommendations.isEmpty {
                            VStack(alignment: .leading, spacing: 15) {
                                Text("Recommendations")
                                    .font(.headline)
                                    .padding(.horizontal)
                                
                                VStack(spacing: 12) {
                                    ForEach(recommendations, id: \.self) { recommendation in
                                        HStack(alignment: .top, spacing: 10) {
                                            Image(systemName: "checkmark.circle.fill")
                                                .foregroundColor(.green)
                                                .font(.system(size: 16))
                                            
                                            Text(recommendation)
                                                .font(.subheadline)
                                                .lineSpacing(4)
                                                .fixedSize(horizontal: false, vertical: true)
                                            
                                            Spacer()
                                        }
                                        .padding(.bottom, 6)
                                        
                                        if recommendation != recommendations.last {
                                            Divider()
                                                .padding(.leading, 26)
                                        }
                                    }
                                }
                                .padding()
                                .background(Color.green.opacity(0.1))
                                .cornerRadius(12)
                                .padding(.horizontal)
                            }
                            .padding(.vertical, 10)
                        }
                        
                        // 营养成分分析
                        VStack(alignment: .leading, spacing: 15) {
                            Text("Nutrition Breakdown")
                                .font(.headline)
                                .padding(.horizontal)
                            
                            // 饼图
                            VStack {
                                // 计算实际的百分比
                                let totalMacros = protein + carbs + fat
                                let proteinPercentage = totalMacros > 0 ? Double(protein) / Double(totalMacros) : 0
                                let carbsPercentage = totalMacros > 0 ? Double(carbs) / Double(totalMacros) : 0
                                let fatPercentage = totalMacros > 0 ? Double(fat) / Double(totalMacros) : 0
                                
                                // 计算每个部分在环形图中的起始和结束位置
                                let proteinEnd = proteinPercentage
                                let carbsEnd = proteinEnd + carbsPercentage
                                // fatEnd将是1.0 (100%)
                                
                                // 使用简单的环形图
                                ZStack {
                                    Circle()
                                        .trim(from: 0, to: CGFloat(proteinEnd))
                                        .stroke(Color.blue, lineWidth: 15)
                                        .rotationEffect(.degrees(-90))
                                    
                                    Circle()
                                        .trim(from: CGFloat(proteinEnd), to: CGFloat(carbsEnd))
                                        .stroke(Color.green, lineWidth: 15)
                                        .rotationEffect(.degrees(-90))
                                    
                                    Circle()
                                        .trim(from: CGFloat(carbsEnd), to: 1)
                                        .stroke(Color.yellow, lineWidth: 15)
                                        .rotationEffect(.degrees(-90))
                                }
                                .frame(width: 150, height: 150)
                                .padding(.vertical)
                                
                                // 营养素图例 - 改进版
                                VStack(spacing: 15) {
                                    // 计算百分比值
                                    let proteinPercent = Int(round(proteinPercentage * 100))
                                    let carbsPercent = Int(round(carbsPercentage * 100))
                                    let fatPercent = Int(round(fatPercentage * 100))
                                    
                                    // 蛋白质行
                                    HStack {
                                        Circle()
                                            .fill(Color.blue)
                                            .frame(width: 18, height: 18)
                                        
                                        Text("Protein")
                                            .font(.subheadline)
                                            .fontWeight(.medium)
                                        
                                        Spacer()
                                        
                                        VStack(alignment: .trailing) {
                                            Text("\(protein)g")
                                                .font(.subheadline)
                                                .fontWeight(.semibold)
                                            
                                            Text("\(proteinPercent)%")
                                                .font(.caption)
                                                .foregroundColor(.secondary)
                                        }
                                    }
                                    
                                    // 碳水行
                                    HStack {
                                        Circle()
                                            .fill(Color.green)
                                            .frame(width: 18, height: 18)
                                        
                                        Text("Carbs")
                                            .font(.subheadline)
                                            .fontWeight(.medium)
                                        
                                        Spacer()
                                        
                                        VStack(alignment: .trailing) {
                                            Text("\(carbs)g")
                                                .font(.subheadline)
                                                .fontWeight(.semibold)
                                            
                                            Text("\(carbsPercent)%")
                                                .font(.caption)
                                                .foregroundColor(.secondary)
                                        }
                                    }
                                    
                                    // 脂肪行
                                    HStack {
                                        Circle()
                                            .fill(Color.yellow)
                                            .frame(width: 18, height: 18)
                                        
                                        Text("Fat")
                                            .font(.subheadline)
                                            .fontWeight(.medium)
                                        
                                        Spacer()
                                        
                                        VStack(alignment: .trailing) {
                                            Text("\(fat)g")
                                                .font(.subheadline)
                                                .fontWeight(.semibold)
                                            
                                            Text("\(fatPercent)%")
                                                .font(.caption)
                                                .foregroundColor(.secondary)
                                        }
                                    }
                                }
                                .padding(.horizontal, 30)
                                .padding(.bottom)
                            }
                            .background(Color.gray.opacity(0.05))
                            .cornerRadius(15)
                            .padding(.horizontal)
                            
                            // 详细营养素信息
                            VStack(spacing: 15) {
                                // 卡路里
                                NutrientRow(
                                    icon: "flame.fill",
                                    iconColor: .red,
                                    name: "Calories",
                                    value: "\(calories) kcal"
                                )
                                
                                // 水分
                                NutrientRow(
                                    icon: "drop.fill",
                                    iconColor: .blue,
                                    name: "Water",
                                    value: "\(water)%"
                                )
                                
                                // 蛋白质
                                NutrientRow(
                                    icon: "bolt.fill",
                                    iconColor: .blue,
                                    name: "Protein",
                                    value: "\(protein)g"
                                )
                                
                                // 碳水
                                NutrientRow(
                                    icon: "leaf.fill",
                                    iconColor: .green,
                                    name: "Carbs",
                                    value: "\(carbs)g"
                                )
                                
                                // 纤维
                                NutrientRow(
                                    icon: "waveform.path",
                                    iconColor: .green,
                                    name: "Fiber",
                                    value: "\(fiber)g"
                                )
                                
                                // 糖
                                NutrientRow(
                                    icon: "cube.fill",
                                    iconColor: .pink,
                                    name: "Sugar",
                                    value: "\(sugar)g"
                                )
                            }
                            .padding()
                            .background(Color.gray.opacity(0.1))
                            .cornerRadius(15)
                            .padding(.horizontal)
                        }
                    }
                    .padding(.bottom, 30)
                }
            }
        }
        .background(Color.white)
        .onAppear {
            // 统计
            XDTrackTool.shared.appear("食物识别结果页面")

            // 默认开始分析状态
            isAnalyzing = true

            // 如果有传入的分析结果，直接使用
            if let analysisData = userData.currentFoodAnalysis {
                processAnalysisResult(analysisData)
                userData.currentFoodAnalysis = nil // 使用后清除
            } else {
                // 否则使用默认数据
                setDefaultFoodData()
            }
        }
        .alert(isPresented: $showError) {
            Alert(
                title: Text("Error"),
                message: Text(uploadError ?? "Unknown error occurred"),
                dismissButton: .default(Text("OK"))
            )
        }
    }
    
    // 处理接收到的分析结果
    private func processAnalysisResult(_ result: FoodAnalysisResult) {
        // 更新UI数据
        foodName = result.foodName
        nutritionScore = result.nutritionScore
        calories = result.calories
        protein = result.protein
        carbs = result.carbs
        fat = result.fat
        water = result.water
        fiber = result.fiber
        sugar = result.sugar
        caloriePercentage = result.caloriePercentage
        recommendations = result.recommendations
        conversationId = result.conversationId // 保存conversationId
        
        // 关闭加载状态
        isAnalyzing = false
    }
    

    
    // 设置默认食物数据（用于API调用失败时）
    private func setDefaultFoodData() {
        self.foodName = "Unknown food"
        self.nutritionScore = 70
        self.calories = 300
        self.protein = 20
        self.carbs = 10
        self.fat = 5
        self.water = 60
        self.fiber = 6
        self.sugar = 5
        self.caloriePercentage = 30
        self.conversationId = "" // 默认数据时conversationId为空
        self.recommendations = [
            "Recommendations unavailable. Please check your connection and retry.",
            "Make sure the food fills the frame and is well-lit.",
            "Try shooting near a window or under brighter lights."
        ]
    }
    
    // 获取最新食物记录并缓存ID
    private func fetchLatestFoodRecordAndCache() {
        // 获取今日食物记录
        let formatter = DateFormatter()
        formatter.dateFormat = "yyyyMMdd"
        let todayStr = formatter.string(from: Date())
        
        ImageUploadService.shared.getFoodRecords(dateStr: todayStr, authToken: userData.accessToken) { result in
            DispatchQueue.main.async {
                switch result {
                case .success(let records):
                    if let latestRecord = records.first {
                        // 缓存最新记录的ID
                        UserDefaultsManager.shared.saveLastFoodRecordId(latestRecord.id)
                        print("🆔 缓存最新食物记录ID: \(latestRecord.id)")
                    }
                case .failure(let error):
                    print("❌ 获取最新食物记录失败: \(error.localizedDescription)")
                }
            }
        }
    }
    
    // 上传图片并添加到食物日记的方法
    private func uploadAndAddToFoodDiary() {
        // 设置上传状态
        isUploading = true
        
        // 检查是否已登录
        if userData.accessToken.isEmpty {
            isUploading = false
            uploadError = "Please login to add food to diary"
            showError = true
            return
        }
        
        // 使用本地保存的conversationId
        let currentConversationId = conversationId
        
        // 打印会话ID，用于调试
        print("使用的conversationId: \(currentConversationId)")
        
        // 如果会话ID为空，说明识别失败，不能添加到食物日记
        if currentConversationId.isEmpty {
            print("会话ID为空，食物识别失败，无法添加到食物日记")
            isUploading = false
            uploadError = "Food recognition failed. Cannot add to diary without proper analysis."
            showError = true
            return
        }
        
        // 获取认证令牌
        let authToken = userData.accessToken
        
        // 调用创建食物记录接口
        ImageUploadService.shared.createDietRecord(conversationId: currentConversationId, authToken: authToken) { result in
            DispatchQueue.main.async {
                self.isUploading = false
                
                switch result {
                case .success(_):
                    print("成功创建食物记录到后端")
                    
                    // 获取最新的食物记录来获得记录ID
                    self.fetchLatestFoodRecordAndCache()
                    
                    // 延迟1秒后发送通知，确保服务器已处理完成
                    DispatchQueue.main.asyncAfter(deadline: .now() + 1.0) {
                        NotificationCenter.default.post(name: Notification.Name("APIFoodRecordAdded"), object: nil)
                    }
                    
                    // 关闭视图
                    self.isPresented = false
                    
                case .failure(let error):
                    print("创建食物记录失败: \(error.localizedDescription)")
                    
                    // API调用失败，显示错误信息
                    self.uploadError = "Failed to save food record: \(error.localizedDescription)"
                    self.showError = true
                }
            }
        }
    }
}

// 营养素行视图
struct NutrientRow: View {
    let icon: String
    let iconColor: Color
    let name: String
    let value: String
    
    var body: some View {
        HStack {
            Image(systemName: icon)
                .font(.system(size: 14)) // 统一图标大小为14
                .foregroundColor(.white)
                .frame(width: 32, height: 32) // 统一背景框大小为32x32
                .background(iconColor)
                .cornerRadius(8)
            
            Text(name)
                .fontWeight(.medium)
            
            Spacer()
            
            Text(value)
                .fontWeight(.semibold)
        }
    }
}


// 个人资料视图
struct ProfileView: View {
    @EnvironmentObject var userData: UserData
    @State private var showEditProfile = false
    @State private var showDeleteAccount = false
    
    // 添加网页显示状态
    @State private var showTermsOfService = false
    @State private var showPrivacyPolicy = false
    
    var body: some View {
        NavigationStack {
        ScrollView {
            VStack(spacing: 20) {
                // 用户头像和基本信息 - 根据访客模式显示不同内容
                HStack(spacing: 16) {
                    // 用户头像 - 左边
                    if userData.isGuestMode {
                        // 访客模式头像
                        Image(systemName: "person.circle.fill")
                            .resizable()
                            .scaledToFit()
                            .frame(width: 80, height: 80)
                            .foregroundColor(.gray)
                    } else {
                        // 正常用户头像
                        if let profileImage = userData.profileImage {
                            Image(uiImage: profileImage)
                                .resizable()
                                .scaledToFill()
                                .frame(width: 80, height: 80)
                                .clipShape(Circle())
                        } else {
                            Image(systemName: "person.circle.fill")
                                .resizable()
                                .scaledToFit()
                                .frame(width: 80, height: 80)
                                .foregroundColor(.gray)
                        }
                    }
                    
                    // 右边的文本信息
                    VStack(alignment: .leading, spacing: 8) {
                        if userData.isGuestMode {
                            // 访客模式显示
                            Text("Guest Mode")
                                .font(.title2)
                                .fontWeight(.bold)
                                .frame(maxWidth: .infinity, alignment: .leading)
                            
                            Text("Login to view personal data")
                                .foregroundColor(.secondary)
                                .font(.subheadline)
                                .frame(maxWidth: .infinity, alignment: .leading)
                        } else {
                            // 正常用户显示
                            // 用户名称 - 使用displayName，为空时不显示
                            if !userData.displayName.isEmpty {
                                Text(userData.displayName)
                                    .font(.title2)
                                    .fontWeight(.bold)
                                    .frame(maxWidth: .infinity, alignment: .leading)
                            }
                            
                            // 用户邮箱
                            Text(userData.email)
                                .foregroundColor(.secondary)
                                .font(.subheadline)
                                .frame(maxWidth: .infinity, alignment: .leading)
                            
                            // 编辑资料按钮
                            Button(action: {
                                showEditProfile = true
                            }) {
                                Text("Edit Profile")
                                    .foregroundColor(.green)
                                    .font(.system(size: 16, weight: .medium))
                            }
                            .frame(maxWidth: .infinity, alignment: .leading)
                        }
                    }
                    
                    Spacer()
                }
                .padding(.vertical)
                .padding(.horizontal)
                
                // 会员状态/登录提示 - 根据用户状态显示不同内容
                if userData.isGuestMode {
                    // 访客模式显示登录提示框
                    VStack(spacing: 16) {
                        Text("Login for Full Features")
                            .font(.system(size: 20, weight: .bold))
                            .foregroundColor(Color(red: 1, green: 1, blue: 1))
                            .frame(maxWidth: .infinity, alignment: .leading)
                        
                        Text("Login to view personal data and unlock premium features")
                            .font(.system(size: 16, weight: .regular))
                            .foregroundColor(Color(red: 1, green: 1, blue: 1))
                            .lineLimit(nil)
                            .frame(maxWidth: .infinity, alignment: .leading)
                        
                        HStack {
                            Spacer()
                            Button(action: {
                                userData.exitGuestMode()
                            }) {
                                Text("Login")
                                    .font(.system(size: 16, weight: .semibold)) // 字重600
                                    .foregroundColor(Color(red: 0.18, green: 0.49, blue: 0.2))
                                    .frame(width: 145, height: 48) // w145px, h48px
                                    .background(Color(red: 1, green: 1, blue: 1)) // 白色背景
                                    .cornerRadius(8) // 圆角8px
                            }
                            Spacer()
                        }
                    }
                    .padding(20)
                    .background(
                        LinearGradient(
                            gradient: Gradient(colors: [
                                Color(red: 0.3, green: 0.69, blue: 0.31),
                                Color(red: 0.18, green: 0.49, blue: 0.2)
                            ]),
                            startPoint: .top,
                            endPoint: .bottom
                        )
                    )
                    .cornerRadius(12)
                    .padding(.horizontal)
                } else if userData.isPremium {
                    // Premium会员状态显示
                    VStack(spacing: 15) {
                        HStack {
                            Image(systemName: "crown.fill")
                                .foregroundColor(.yellow)
                            Text("Membership Status")
                                .font(.headline)
                                .foregroundColor(.primary)
                            Spacer()
                        }
                        
                        Text("Premium Member")
                            .font(.title3)
                            .fontWeight(.bold)
                            .foregroundColor(.green)
                            .frame(maxWidth: .infinity, alignment: .leading)
                        
                        Text("Valid until \(userData.formatSubscriptionEndDate())")
                            .font(.subheadline)
                            .foregroundColor(.primary)
                            .frame(maxWidth: .infinity, alignment: .leading)
                        
                        // 说明显示累计订阅天数
                        Text("Total subscription: \(userData.getTotalSubscriptionDays()) days")
                            .font(.caption)
                            .foregroundColor(.secondary)
                            .frame(maxWidth: .infinity, alignment: .leading)
                            .padding(.bottom, 5)
                        
                        // 进度条 - 根据已经使用的时间比例来显示
                        ProgressView(value: userData.getSubscriptionProgress())
                            .progressViewStyle(LinearProgressViewStyle(tint: .green))
                            .padding(.vertical, 5)
                        
                        Text("\(userData.getRemainingSubscriptionDays()) days remaining")
                            .font(.caption)
                            .foregroundColor(.secondary)
                            .frame(maxWidth: .infinity, alignment: .leading)
                    }
                    .padding()
                    .background(Color(red: 0.96, green: 0.98, blue: 0.96))
                    .cornerRadius(15)
                    .shadow(color: Color.black.opacity(0.1), radius: 4, x: 0, y: 2)
                    .padding(.horizontal)
                } else {
                    // 仅当用户非订阅状态时显示订阅组件
                    ProfileSubscriptionSection()
                        .environmentObject(userData)
                        .padding(.horizontal)
                }
                
                // 设置部分 - 访客模式只显示Legal & Privacy
                if !userData.isGuestMode {
                    VStack(spacing: 0) {
                        Text("Settings")
                            .font(.headline)
                            .frame(maxWidth: .infinity, alignment: .leading)
                            .padding()
                        
                        Divider()
                        
                        // 通知设置
                            NavigationLink(destination: 
                                FullScreenNotificationsView()
                                    .environmentObject(userData)
                                    .navigationBarTitle("Notifications", displayMode: .inline)
                            ) {
                            HStack {
                                Image(systemName: "bell")
                                    .frame(width: 30)
                                    .foregroundColor(.primary)
                                
                                Text("Notifications")
                                    .foregroundColor(.primary)
                                
                                Spacer()
                                
                                Image(systemName: "chevron.right")
                                    .foregroundColor(.secondary)
                            }
                            .padding()
                        }
                        
                        Divider()
                        
                        // 单位设置
                            NavigationLink(destination: 
                                FullScreenUnitsView()
                                    .environmentObject(userData)
                                    .navigationBarTitle("Units", displayMode: .inline)
                            ) {
                            HStack {
                                Image(systemName: "ruler")
                                    .frame(width: 30)
                                    .foregroundColor(.primary)
                                
                                Text("Units (kg/lbs)")
                                    .foregroundColor(.primary)
                                
                                Spacer()
                                
                                Image(systemName: "chevron.right")
                                    .foregroundColor(.secondary)
                            }
                            .padding()
                        }
                        
                        Divider()
                        
                        // 密码管理
                            NavigationLink(destination: 
                                ChangePasswordView()
                                    .navigationBarTitle("Change Password", displayMode: .inline)
                            ) {
                            HStack {
                                Image(systemName: "key")
                                    .frame(width: 30)
                                    .foregroundColor(.primary)
                                
                                Text("Password Management")
                                    .foregroundColor(.primary)
                                
                                Spacer()
                                
                                Image(systemName: "chevron.right")
                                    .foregroundColor(.secondary)
                            }
                            .padding()
                        }
                    }
                    .background(Color(UIColor.systemBackground))
                    .cornerRadius(15)
                    .shadow(color: Color.black.opacity(0.1), radius: 4, x: 0, y: 2)
                    .padding(.horizontal)
                }
                
                // 法律与隐私部分
                VStack(spacing: 0) {
                    Text("Legal & Privacy")
                        .font(.headline)
                        .frame(maxWidth: .infinity, alignment: .leading)
                        .padding()
                    
                    Divider()
                    
                    // 服务条款
                        Button(action: {
                            showTermsOfService = true
                        }) {
                        HStack {
                            Image(systemName: "doc.text")
                                .frame(width: 30)
                                .foregroundColor(.primary)
                            
                            Text("Terms of Service")
                                .foregroundColor(.primary)
                            
                            Spacer()
                            
                            
                            Image(systemName: "chevron.right")
                                .foregroundColor(.secondary)
                        }
                        .padding()
                    }
                    .sheet(isPresented: $showTermsOfService) {
                        WebPageView(url: URL(string: "https://littlegrass.cc/app/fitscanai/terms.html")!, title: "Terms of Service")
                    }
                    
                    Divider()
                    
                    // 隐私政策
                        Button(action: {
                            showPrivacyPolicy = true
                        }) {
                        HStack {
                            Image(systemName: "shield")
                                .frame(width: 30)
                                .foregroundColor(.primary)
                            
                            Text("Privacy Policy")
                                .foregroundColor(.primary)
                            
                            Spacer()
                            
                            Image(systemName: "chevron.right")
                                .foregroundColor(.secondary)
                        }
                        .padding()
                    }
                    .sheet(isPresented: $showPrivacyPolicy) {
                        WebPageView(url: URL(string: "https://littlegrass.cc/app/fitscanai/privacy.html")!, title: "Privacy Policy")
                    }
                    
                    // 删除账户和退出登录 - 仅非访客模式显示
                    if !userData.isGuestMode {
                        Divider()
                        
                        // 删除账户
                        Button(action: {
                            showDeleteAccount = true
                        }) {
                            HStack {
                                Image(systemName: "trash")
                                    .frame(width: 30)
                                    .foregroundColor(.red)
                                
                                Text("Delete Account")
                                    .foregroundColor(.red)
                                
                                Spacer()
                                
                                Image(systemName: "chevron.right")
                                    .foregroundColor(.secondary)
                            }
                            .padding()
                        }
                        
                        Divider()
                        
                        // 退出登录
                        Button(action: {
                            // 使用userData的登出方法清除所有用户数据和token
                            userData.logout()
                            
                            // logout()方法会发送UserLoggedOut通知
                            // 该通知会被FitScanAIApp捕获并返回到登录页面
                            print("用户点击登出按钮，已发送登出通知")
                        }) {
                            HStack {
                                Image(systemName: "arrow.right.square")
                                    .frame(width: 30)
                                    .foregroundColor(.red)
                                
                                Text("Logout")
                                    .foregroundColor(.red)
                                
                                Spacer()
                                
                                Image(systemName: "chevron.right")
                                    .foregroundColor(.secondary)
                            }
                            .padding()
                        }
                    }
                    
                    // 添加版本号显示
                    if let version = Bundle.main.infoDictionary?["CFBundleShortVersionString"] as? String,
                       let build = Bundle.main.infoDictionary?["CFBundleVersion"] as? String {
                        Text("Version \(version) (\(build))")
                            .font(.footnote)
                            .foregroundColor(.secondary)
                            .frame(maxWidth: .infinity, alignment: .center)
                            .padding(.top, 8)
                            .padding(.bottom, 20)
                    }
                }
                .background(Color(UIColor.systemBackground))
                .cornerRadius(15)
                .shadow(color: Color.black.opacity(0.1), radius: 4, x: 0, y: 2)
                .padding(.horizontal)
                .padding(.bottom, 30)
                
                
            }
            .padding(.top)
        }
            .toolbarBackground(.visible, for: .navigationBar)
        }
        .sheet(isPresented: $showEditProfile) {
            EditProfileView()
                .environmentObject(userData)
        }
        .sheet(isPresented: $showDeleteAccount) {
            DeleteAccountView()
                .environmentObject(userData)
        }
        .onAppear {
            // 统计
            XDTrackTool.shared.appear("个人资料页面")

            // 从API获取用户信息
            if !userData.accessToken.isEmpty {
                print("ProfileView 正在尝试获取用户信息，当前用户昵称: \(userData.nickname)")
                UserService.shared.updateUserDataWithFetchedInfo(userData: userData)
            }
        }
        // 添加对ProfileUpdated通知的监听
        .onReceive(NotificationCenter.default.publisher(for: Notification.Name("ProfileUpdated"))) { _ in
            // 刷新用户资料数据
            UserService.shared.updateUserDataWithFetchedInfo(userData: userData)
            // 通知UI刷新
            userData.objectWillChange.send()
        }
    }
}

// 食物日记项目
struct FoodDiaryItem: View {
    let mealType: String
    let foodName: String
    let time: String
    let calories: Int
    let image: String
    
    var body: some View {
        HStack(spacing: 15) {
            // 食物图片
            Image(image)
                .resizable()
                .scaledToFill()
                .frame(width: 60, height: 60)
                .clipShape(RoundedRectangle(cornerRadius: 8))
            
            // 食物信息
            VStack(alignment: .leading, spacing: 5) {
                Text(mealType)
                    .font(.subheadline)
                    .foregroundColor(.secondary)
                
                Text(foodName)
                    .font(.headline)
                
                Text(time)
                    .font(.caption)
                    .foregroundColor(.secondary)
            }
            
            Spacer()
            
            // 卡路里信息
            VStack(alignment: .trailing) {
                Text("\(calories)")
                    .fontWeight(.medium)
                    .foregroundColor(.green)
                
                Text("kcal")
                    .font(.caption)
                    .foregroundColor(.secondary)
            }
        }
    }
}

// 编辑个人资料视图
struct EditProfileView: View {
    @Environment(\.presentationMode) var presentationMode
    @EnvironmentObject var userData: UserData
    @State private var nickname: String = ""
    @State private var profileImage: UIImage?
    @State private var isShowingImagePicker = false
    
    // 状态变量
    @State private var isUploading = false
    @State private var showAlert = false
    @State private var alertMessage = ""
    @State private var isSuccess = false
    
    // 添加权限相关状态变量
    @State private var showPermissionAlert = false
    @State private var alertTitle = "Photo Library Access"
    @State private var alertPermissionMessage = "Please allow FitScanAI to access your photo library to select a profile picture."
    
    // 计算属性：验证昵称是否有效
    private var isNicknameValid: Bool {
        return nickname.count >= 2 && nickname.count <= 30
    }
    
    // 计算属性：按钮是否应该被禁用
    private var isSaveButtonDisabled: Bool {
        return isUploading || !isNicknameValid
    }
    
    var body: some View {
        VStack(spacing: 0) {
            // 导航栏
            HStack {
                Text("Edit Profile")
                    .font(.title2)
                    .fontWeight(.bold)
                
                Spacer()
                
                Button(action: {
                    presentationMode.wrappedValue.dismiss()
                }) {
                    Image(systemName: "xmark")
                        .foregroundColor(.secondary)
                        .padding()
                }
            }
            .padding(.horizontal)
            .padding(.top)
            
            ScrollView {
                VStack(spacing: 30) {
                    // 头像选择
                    VStack {
                        ZStack {
                            if let image = profileImage {
                                Image(uiImage: image)
                                    .resizable()
                                    .scaledToFill()
                                    .frame(width: 100, height: 100)
                                    .clipShape(Circle())
                                    .overlay(Circle().stroke(Color.gray.opacity(0.3), lineWidth: 1))
                            } else {
                                Circle()
                                    .fill(Color.gray.opacity(0.2))
                                    .frame(width: 100, height: 100)
                                    .overlay(Image(systemName: "person.fill")
                                    .font(.system(size: 40))
                                        .foregroundColor(.gray))
                            }
                            
                            // 相机按钮
                            VStack {
                                Spacer()
                                HStack {
                                    Spacer()
                                    
                                    Button(action: {
                                        checkPhotoLibraryPermission()
                                    }) {
                                        Image(systemName: "camera.fill")
                                            .font(.system(size: 14))
                                            .foregroundColor(.white)
                                            .padding(8)
                                            .background(Color.green)
                                            .clipShape(Circle())
                                    }
                                }
                            }
                            .frame(width: 100, height: 100)
                        }
                        
                        Text("Change Avatar")
                            .foregroundColor(.secondary)
                            .font(.subheadline)
                            .padding(.top, 8)
                    }
                    .padding(.top, 20)
                    
                    // 昵称输入
                    VStack(alignment: .leading, spacing: 8) {
                        Text("Nickname")
                            .font(.headline)
                        
                        TextField("Enter your nickname", text: $nickname)
                            .padding()
                            .background(Color.gray.opacity(0.1))
                            .cornerRadius(8)
                            .overlay(
                                RoundedRectangle(cornerRadius: 8)
                                    .stroke(!nickname.isEmpty && !isNicknameValid ? Color.red : Color.clear, lineWidth: 1)
                            )
                        
                        HStack {
                            Text("2-30 characters")
                                .font(.caption)
                                .foregroundColor(!nickname.isEmpty && !isNicknameValid ? .red : .secondary)
                            
                            Spacer()
                            
                            Text("\(nickname.count)/30")
                                .font(.caption)
                                .foregroundColor(!nickname.isEmpty && !isNicknameValid ? .red : .secondary)
                        }
                    }
                    .padding(.horizontal)
                    
                    Spacer()
                    
                    // 保存按钮
                    Button(action: {
                        saveUserProfile()
                    }) {
                        if isUploading {
                            ProgressView()
                                .progressViewStyle(CircularProgressViewStyle(tint: .white))
                                .frame(maxWidth: .infinity)
                                .padding()
                                .background(Color.green.opacity(0.8))
                                .foregroundColor(.white)
                                .cornerRadius(10)
                        } else {
                        Text("Save Changes")
                            .fontWeight(.semibold)
                            .frame(maxWidth: .infinity)
                            .padding()
                            .background(isSaveButtonDisabled ? Color.gray.opacity(0.5) : Color.green)
                            .foregroundColor(.white)
                            .cornerRadius(10)
                    }
                    }
                    .disabled(isSaveButtonDisabled)
                    .padding(.horizontal)
                    .padding(.vertical, 20)
                }
            }
        }
        .background(Color(UIColor.systemBackground))
        .onAppear {
            // 统计
            XDTrackTool.shared.appear("编辑个人资料页面")

            // 初始化表单数据
            nickname = userData.nickname
            profileImage = userData.profileImage
        }
        .sheet(isPresented: $isShowingImagePicker) {
            if #available(iOS 14, *) {
                StandardPhotoPicker(selectedImage: $profileImage, isPresented: $isShowingImagePicker) {
                    // 不需要额外操作
                }
            } else {
                UnsupportedPhotoPicker(isPresented: $isShowingImagePicker) {
                    // 如果设备不支持，回调后什么都不做
                }
            }
        }
        .alert(isPresented: $showAlert) {
            Alert(
                title: Text(isSuccess ? "Success" : "Error"),
                message: Text(alertMessage),
                dismissButton: .default(Text("OK")) {
                    if isSuccess {
                        // 成功后关闭页面
                        presentationMode.wrappedValue.dismiss()
                    }
                }
            )
        }
        .alert(isPresented: $showPermissionAlert) {
            Alert(
                title: Text(alertTitle),
                message: Text(alertPermissionMessage),
                primaryButton: .default(Text("Go to Settings")) {
                    if let url = URL(string: UIApplication.openSettingsURLString) {
                        UIApplication.shared.open(url)
                    }
                },
                secondaryButton: .cancel(Text("Cancel"))
            )
        }
    }
    
    // 检查相册权限 - 使用新的权限管理系统（EditProfileView）
    private func checkPhotoLibraryPermission() {
        print("📸 EditProfileView: Requesting photo library permission with user-friendly explanation")
        
        // 使用增强的权限管理器
        PhotoLibraryPermissionManager.shared.requestPhotoLibraryAccess { status in
            DispatchQueue.main.async {
                switch status {
                case .authorized, .limited:
                    print("✅ EditProfileView: Photo library access granted, showing picker")
                    self.isShowingImagePicker = true
                default:
                    print("❌ EditProfileView: Photo library access denied")
                    // 权限被拒绝，不显示相册选择器
                    break
                }
            }
        }
    }
    
    // 保存用户资料的方法
    private func saveUserProfile() {
        // 双重验证昵称长度（虽然按钮已经有验证，但保留作为安全检查）
        if !isNicknameValid {
            alertMessage = "Nickname must be between 2-30 characters"
            isSuccess = false
            showAlert = true
            return
        }
        
        // 显示上传中状态
        isUploading = true
        
        // 1. 先更新昵称到后端
        UserService.shared.changeNickname(newNickname: nickname, userData: userData) { result in
            DispatchQueue.main.async {
                switch result {
                case .success(_):
                    print("昵称成功更新到后端")
                    
                    // 2. 如果有新头像，则上传头像
                    if let newImage = self.profileImage, newImage != self.userData.profileImage {
                        // 使用AvatarUploadService上传头像
                        AvatarUploadService.shared.updateUserAvatar(image: newImage, userData: self.userData) { success, message in
                            DispatchQueue.main.async {
                                // 更新上传状态
                                self.isUploading = false
                                
                                if success {
                                    // 成功后立即返回
                                    print("✅ 头像和昵称更新成功，立即返回个人页面")
                                    
                                    // 通知个人资料页面刷新
                                    NotificationCenter.default.post(name: Notification.Name("ProfileUpdated"), object: nil)
                                    
                                    // 立即关闭页面
                                    self.presentationMode.wrappedValue.dismiss()
                                } else {
                                    // 显示错误提示
                                    self.alertMessage = "Failed to update avatar"
                                    self.isSuccess = false
                                    self.showAlert = true
                                    
                                    // 检查是否是401错误
                                    if message.contains("登录凭证已过期") || message.contains("401") {
                                        self.alertMessage = "Your login session has expired. Please log in again."
                                        // 发送Token过期通知
                                        NotificationCenter.default.post(name: Notification.Name("TokenExpired"), object: nil)
                                    }
                                }
                            }
                        }
                    } else {
                        // 没有新头像，只更新了昵称
                        self.isUploading = false
                        
                        print("✅ 昵称更新成功，立即返回个人页面")
                        
                        // 通知个人资料页面刷新
                        NotificationCenter.default.post(name: Notification.Name("ProfileUpdated"), object: nil)
                        
                        // 立即关闭页面
                        self.presentationMode.wrappedValue.dismiss()
                    }
                    
                case .failure(let error):
                    // 昵称更新失败
                    self.isUploading = false
                    self.alertMessage = "Failed to update nickname: \(error.localizedDescription)"
                    self.isSuccess = false
                    self.showAlert = true
                    
                    // 检查是否是401错误
                    let nsError = error as NSError
                    if nsError.code == 401 {
                        self.alertMessage = "Your login session has expired. Please log in again."
                        // 发送Token过期通知
                        NotificationCenter.default.post(name: Notification.Name("TokenExpired"), object: nil)
                    }
                }
            }
        }
    }
}

// 高级功能项
struct PremiumFeatureItem: View {
    let icon: String
    let iconColor: Color
    let title: String
    let description: String
    
    var body: some View {
        HStack(spacing: 15) {
            // 图标
            Image(systemName: icon)
                .foregroundColor(.white)
                .frame(width: 24, height: 24) // 统一图标大小
                .padding(10)
                .background(iconColor)
                .cornerRadius(10)
            
            // 文本
            VStack(alignment: .leading, spacing: 3) {
                Text(title)
                    .font(.headline)
                
                Text(description)
                    .font(.subheadline)
                    .foregroundColor(.secondary)
            }
            
            Spacer()
        }
        .padding()
        .background(Color.gray.opacity(0.1))
        .cornerRadius(12)
    }
}

// 通知设置视图
struct NotificationsView: View {
    @Environment(\.presentationMode) var presentationMode
    @EnvironmentObject var userData: UserData
    
    var body: some View {
        NavigationView {
            List {
                // 日常体重提醒
                Section {
                    Toggle("Daily Weight Reminder", isOn: $userData.dailyReminderEnabled)
                        .toggleStyle(SwitchToggleStyle(tint: .green))
                        .onChange(of: userData.dailyReminderEnabled) { _, _ in
                            userData.saveSettings()
                            userData.scheduleNotifications()
                        }
                }
                
                // 提醒时间（固定）
                Section {
                    HStack {
                        Text("Reminder Time")
                        Spacer()
                        Text("07:30 AM / 10:30 PM")
                            .foregroundColor(.secondary)
                    }
                    .foregroundColor(.primary)
                }
                
                // 振动和声音选项
                Section {
                    Toggle("Vibration", isOn: $userData.vibrationEnabled)
                        .toggleStyle(SwitchToggleStyle(tint: .green))
                        .onChange(of: userData.vibrationEnabled) { _, _ in
                            userData.saveSettings()
                        }
                    
                    Toggle("Sound", isOn: $userData.soundEnabled)
                        .toggleStyle(SwitchToggleStyle(tint: .green))
                        .onChange(of: userData.soundEnabled) { _, _ in
                            userData.saveSettings()
                            userData.scheduleNotifications()
                        }
                }
                
                // 解释文本
                Section {
                    Text("Notifications help you maintain consistent weight tracking")
                        .font(.footnote)
                        .foregroundColor(.secondary)
                }
            }
            .listStyle(InsetGroupedListStyle())
            .navigationTitle("Notifications")
            .navigationBarTitleDisplayMode(.inline)
            .toolbar {
                ToolbarItem(placement: .navigationBarLeading) {
                    Button(action: {
                        presentationMode.wrappedValue.dismiss()
                    }) {
                        Image(systemName: "chevron.left")
                            .foregroundColor(.green)
                    }
                }
            }
        }
    }
}



// 单位设置视图
struct UnitsView: View {
    @Environment(\.presentationMode) var presentationMode
    @EnvironmentObject var userData: UserData
    
    var body: some View {
        NavigationView {
            List {
                // 体重单位
                Section(header: Text("Weight Unit")) {
                    // 公斤选项
                    HStack {
                        Text("KG (Kilograms)")
                        Spacer()
                        if userData.weightUnit == "kg" {
                            Image(systemName: "circle.fill")
                                .foregroundColor(.green)
                        } else {
                            Image(systemName: "circle")
                                .foregroundColor(.secondary)
                        }
                    }
                    .contentShape(Rectangle())
                    .onTapGesture {
                        if userData.weightUnit != "kg" {
                            userData.weightUnit = "kg"
                            userData.saveSettings()
                        }
                    }
                    
                    Text("1 kg = 2.205 lbs")
                        .font(.caption)
                        .foregroundColor(.secondary)
                    
                    // 磅选项
                    HStack {
                        Text("LBS (Pounds)")
                        Spacer()
                        if userData.weightUnit == "lbs" {
                            Image(systemName: "circle.fill")
                                .foregroundColor(.green)
                        } else {
                            Image(systemName: "circle")
                                .foregroundColor(.secondary)
                        }
                    }
                    .contentShape(Rectangle())
                    .onTapGesture {
                        if userData.weightUnit != "lbs" {
                            userData.weightUnit = "lbs"
                            userData.saveSettings()
                        }
                    }
                    
                    Text("1 lb = 0.454 kg")
                        .font(.caption)
                        .foregroundColor(.secondary)
                }
                
                // 身高单位
                Section(header: Text("Height Unit")) {
                    // 厘米选项
                    HStack {
                        Text("CM (Centimeters)")
                        Spacer()
                        if userData.heightUnit == "cm" {
                            Image(systemName: "circle.fill")
                                .foregroundColor(.green)
                        } else {
                            Image(systemName: "circle")
                                .foregroundColor(.secondary)
                        }
                    }
                    .contentShape(Rectangle())
                    .onTapGesture {
                        if userData.heightUnit != "cm" {
                            userData.heightUnit = "cm"
                            userData.saveSettings()
                        }
                    }
                    
                    // 英尺/英寸选项
                    HStack {
                        Text("FT (Feet/Inches)")
                        Spacer()
                        if userData.heightUnit == "ft" {
                            Image(systemName: "circle.fill")
                                .foregroundColor(.green)
                        } else {
                            Image(systemName: "circle")
                                .foregroundColor(.secondary)
                        }
                    }
                    .contentShape(Rectangle())
                    .onTapGesture {
                        if userData.heightUnit != "ft" {
                            userData.heightUnit = "ft"
                            userData.saveSettings()
                        }
                    }
                }
                
                // 提示信息
                Section {
                    HStack(spacing: 12) {
                        Image(systemName: "info.circle.fill")
                            .foregroundColor(.green)
                        
                        Text("Changes will apply to all future measurements")
                            .font(.footnote)
                            .foregroundColor(.secondary)
                    }
                    .padding(.vertical, 8)
                    .listRowBackground(Color.green.opacity(0.1))
                }
            }
            .listStyle(InsetGroupedListStyle())
            .navigationTitle("Units")
            .navigationBarTitleDisplayMode(.inline)
            .toolbar {
                ToolbarItem(placement: .navigationBarLeading) {
                    Button(action: {
                        presentationMode.wrappedValue.dismiss()
                    }) {
                        Image(systemName: "chevron.left")
                            .foregroundColor(.green)
                    }
                }
            }
        }
    }
}

// 食物日志详情视图
struct FoodJournalView: View {
    @EnvironmentObject var userData: UserData
    @Binding var isPresented: Bool
    @State private var selectedDate: Date = Date()
    @State private var apiRecords: [DietRecordResponse] = []
    @State private var isLoadingRecords: Bool = false
    @State private var lastLoadedDate: String = ""

    
    var body: some View {
        NavigationView {
        ZStack {
            VStack(spacing: 0) {
                // 头部导航栏
                HStack {
                    Button(action: {
                        isPresented = false
                    }) {
                        Image(systemName: "arrow.left")
                            .font(.title3)
                    }
                    
                    Spacer()
                    
                    Text("Food Journal")
                        .font(.headline)
                    
                    Spacer()
                    
                    // 空视图保持对称
                    Image(systemName: "arrow.left")
                        .font(.title3)
                        .opacity(0)
                }
                .padding()
                
                // 日期选择器
                VStack(spacing: 10) {
                    Text(monthYearString(from: selectedDate))
                        .font(.subheadline)
                        .foregroundColor(.secondary)
                    
                    // 日期选择器 - 横向滚动
                    ScrollView(.horizontal, showsIndicators: false) {
                        ScrollViewReader { scrollProxy in
                            HStack(spacing: 12) {
                                ForEach(-13..<1) { offset in // 显示从13天前到今天
                                    if let date = Calendar.current.date(byAdding: .day, value: offset, to: Date()) {
                                        DateCircle(date: date, isSelected: Calendar.current.isDate(date, inSameDayAs: selectedDate))
                                            .id(offset) // 添加id用于滚动定位
                                            .onTapGesture {
                                                // 确保用户不能选择未来日期
                                                if date <= Date() {
                                                    selectedDate = date
                                                    loadAPIRecordsForDate(date)
                                                } else {
                                                    print("不能选择未来日期: \(date)")
                                                }
                                            }
                                    }
                                }
                            }
                            .padding(.horizontal)
                            .onAppear {
                                // 在视图出现时自动滚动到今天（偏移量为0）
                                DispatchQueue.main.asyncAfter(deadline: .now() + 0.1) {
                                    withAnimation {
                                        scrollProxy.scrollTo(0, anchor: .center)
                                    }
                                }
                            }
                        }
                    }
                }
                .padding(.vertical)
                
                // 食物列表
                ScrollView {
                    VStack(spacing: 20) {
                        // 显示加载状态
                        if isLoadingRecords {
                            VStack(spacing: 15) {
                                ProgressView()
                                    .progressViewStyle(CircularProgressViewStyle())
                                
                                Text("Loading food records...")
                                    .font(.subheadline)
                                    .foregroundColor(.secondary)
                            }
                            .frame(maxWidth: .infinity)
                            .padding(.top, 40)
                        } else {
                            // API食物记录
                            if !apiRecords.isEmpty {
                                VStack(alignment: .leading, spacing: 15) {
                                    // 按餐点类型分组显示
                                    let groupedRecords = groupRecordsByMealTypeInJournal(apiRecords)
                                    
                                    ForEach(Array(groupedRecords.keys.sorted()), id: \.self) { mealType in
                                        if let records = groupedRecords[mealType] {
                                            VStack(alignment: .leading, spacing: 8) {
                                                // 餐点类型标题
                                                HStack {
                                                    Text(mealType)
                                                        .font(.subheadline)
                                                        .fontWeight(.semibold)
                                                        .foregroundColor(.secondary)
                                                        .padding(.horizontal, 16)
                                                        .padding(.top, 8)
                                                        .padding(.bottom, 4)
                                                    
                                                    Spacer()
                                                }
                                                
                                                // 该餐点的食物记录
                                                ForEach(records) { record in
                                                    NavigationLink(destination: 
                                                        DetailedFoodAnalysisView(
                                                            isPresented: .constant(true),
                                                            foodRecordId: record.id
                                                        )
                                                        .environmentObject(userData)
                                                        .navigationBarTitle("Food Analysis", displayMode: .inline)
                                                    ) {
                                                        APIFoodRecordJournalRow(record: record)
                                                    }
                                                    .buttonStyle(PlainButtonStyle())
                                                    .padding(.horizontal)
                                                    .padding(.vertical, 4)
                                                }
                                            }
                                        }
                                    }
                                }
                                // .background(Color(.systemGray6)) // 移除此处的背景色
                                .cornerRadius(10)
                                .padding(.horizontal)
                            }
                            
                            // 如果没有任何记录
                            if apiRecords.isEmpty {
                                VStack(spacing: 15) {
                                    Image(systemName: "fork.knife")
                                        .font(.system(size: 40))
                                        .foregroundColor(.secondary)
                                        .padding(.top, 40)
                                    
                                    Text("No Food Records")
                                        .font(.headline)
                                        .foregroundColor(.secondary)
                                    
                                    Text("No food recorded for this day")
                                        .font(.subheadline)
                                        .foregroundColor(.secondary.opacity(0.8))
                                        .padding(.bottom, 40)
                                }
                                .frame(maxWidth: .infinity)
                                .padding()
                            }
                            
                            // 总卡路里计数
                            if !apiRecords.isEmpty {
                                let totalCalories = apiRecords.reduce(0) { $0 + $1.calories }
                                
                                VStack(spacing: 5) {
                                    Text("Total")
                                        .font(.subheadline)
                                        .foregroundColor(.secondary)
                                    
                                    Text("\(totalCalories) kcal")
                                        .font(.title2)
                                        .fontWeight(.bold)
                                        .foregroundColor(.green)
                                }
                                .frame(maxWidth: .infinity)
                                .padding()
                                .background(Color.green.opacity(0.1))
                                .cornerRadius(10)
                                .padding(.horizontal)
                                .padding(.bottom, 100)
                            }
                        }
                    }
                }
            }
            .background(Color.white)
            .onAppear {
                // 统计
                XDTrackTool.shared.appear("食物日志页面")

                loadAPIRecordsForDate(selectedDate)
            }
            

            }
        }
    }
    
    // 按餐点类型分组食物记录（用于FoodJournalView）
    private func groupRecordsByMealTypeInJournal(_ records: [DietRecordResponse]) -> [String: [DietRecordResponse]] {
        var grouped: [String: [DietRecordResponse]] = [:]
        
        for record in records {
            let mealType = getMealTypeFromTimeStr(record.timeStr)
            if grouped[mealType] == nil {
                grouped[mealType] = []
            }
            grouped[mealType]?.append(record)
        }
        
        return grouped
    }
    
    // 根据时间字符串判断餐点类型（用于FoodJournalView）
    private func getMealTypeFromTimeStr(_ timeStr: String) -> String {
        // 解析时间字符串，timeStr格式为 "HH:MM AM/PM"
        let components = timeStr.components(separatedBy: " ")
        guard components.count >= 2 else { return "Meal" }
        
        let timeComponent = components[0]
        let ampmComponent = components[1]
        
        let timeComponents = timeComponent.split(separator: ":")
        guard let hourStr = timeComponents.first, let hour = Int(hourStr) else { return "Meal" }
        
        // 转换为24小时制
        var hour24 = hour
        if ampmComponent.uppercased() == "PM" && hour != 12 {
            hour24 += 12
        } else if ampmComponent.uppercased() == "AM" && hour == 12 {
            hour24 = 0
        }
        
        // 判断餐点类型 - 修改逻辑：凌晨0:00-4:59也算作早餐
        if (hour24 >= 0 && hour24 < 5) || (hour24 >= 5 && hour24 < 12) {
            return "Breakfast"
        } else if hour24 >= 12 && hour24 < 17 {
            return "Lunch"
        } else {
            return "Dinner"
        }
    }
    
    // 加载指定日期的API食物记录
    private func loadAPIRecordsForDate(_ date: Date) {
        // 格式化日期为API需要的格式 - 修改为yyyyMMdd格式
        let formatter = DateFormatter()
        formatter.dateFormat = "yyyyMMdd"
        let dateStr = formatter.string(from: date)
        
        // 避免重复加载相同日期的数据
        if dateStr == lastLoadedDate && !apiRecords.isEmpty {
            return
        }
        
        // 检查是否已登录
        guard !userData.accessToken.isEmpty else {
            print("用户未登录，跳过API食物记录加载")
            return
        }
        
        isLoadingRecords = true
        
        ImageUploadService.shared.getFoodRecords(dateStr: dateStr, authToken: userData.accessToken) { result in
            DispatchQueue.main.async {
                self.isLoadingRecords = false
                
                switch result {
                case .success(let records):
                    self.apiRecords = records
                    self.lastLoadedDate = dateStr
                    print("成功加载\(records.count)条API食物记录")
                    
                    // 预加载食物日记页面的图片
                    self.preloadFoodJournalImages(records)
                    
                case .failure(let error):
                    print("加载API食物记录失败: \(error.localizedDescription)")
                    self.apiRecords = []
                }
            }
        }
    }
    
    // 预加载食物日记页面的图片
    private func preloadFoodJournalImages(_ records: [DietRecordResponse]) {
        let imageUrls = records.compactMap { record in
            record.hasImage ? record.fileLocation : nil
        }.filter { !$0.isEmpty }
        
        if !imageUrls.isEmpty {
            print("🚀 开始预加载食物日记页面\(imageUrls.count)张图片")
            ImageCacheManager.shared.preloadImages(from: imageUrls)
        }
    }
    
    // 获取特定日期的本地食物记录
    private func getFoodEntries(for date: Date) -> [FoodEntry] {
        let calendar = Calendar.current
        let startOfDay = calendar.startOfDay(for: date)
        let endOfDay = calendar.date(byAdding: .day, value: 1, to: startOfDay)!
        
        return userData.foodEntries.filter { entry in
            entry.time >= startOfDay && entry.time < endOfDay
        }.sorted { $0.time < $1.time }
    }
    

    
    // 格式化日期为"月 年"格式
    private func monthYearString(from date: Date) -> String {
        let formatter = DateFormatter()
        formatter.dateFormat = "MMMM yyyy"
        return formatter.string(from: date)
    }
}

// 日期圆圈视图
struct DateCircle: View {
    let date: Date
    let isSelected: Bool
    
    var body: some View {
        VStack(spacing: 5) {
            // 日期数字
            Text("\(Calendar.current.component(.day, from: date))")
                .font(.system(size: 20, weight: .medium))
                .foregroundColor(isSelected ? .white : .primary)
            
            // 星期几
            Text(weekdayString(from: date))
                .font(.caption2)
                .foregroundColor(isSelected ? .white : .secondary)
        }
        .frame(width: 40, height: 70)
        .background(isSelected ? Color.green : Color.clear)
        .clipShape(RoundedRectangle(cornerRadius: 20))
    }
    
    // 获取星期几的简写
    private func weekdayString(from date: Date) -> String {
        let formatter = DateFormatter()
        formatter.dateFormat = "E"
        return formatter.string(from: date)
    }
}

// 日志食物条目行视图
struct JournalFoodEntryRow: View {
    let entry: FoodEntry
    
    var body: some View {
        HStack(spacing: 16) {
            // 食物图片
            if let imageData = entry.image, let uiImage = UIImage(data: imageData) {
                Image(uiImage: uiImage)
                    .resizable()
                    .scaledToFill()
                    .frame(width: 60, height: 60)
                    .cornerRadius(8)
            } else {
                ZStack {
                    Rectangle()
                        .fill(Color.gray.opacity(0.1))
                        .frame(width: 60, height: 60)
                        .cornerRadius(8)
                    
                    Image(systemName: "photo")
                        .foregroundColor(.gray)
                }
            }
            
            // 食物信息
            VStack(alignment: .leading, spacing: 4) {
                Text(entry.name)
                    .font(.headline)
                
                Text("\(formatTime(entry.time))")
                    .font(.caption)
                    .foregroundColor(.secondary)
            }
            
            Spacer()
            
            // 卡路里信息
            VStack(alignment: .trailing) {
                Text("\(entry.calories)")
                    .font(.headline)
                    .foregroundColor(.green)
                
                Text("kcal")
                    .font(.caption)
                    .foregroundColor(.secondary)
            }
        }
        .padding()
    }
    
    // 格式化时间
    func formatTime(_ date: Date) -> String {
        let formatter = DateFormatter()
        formatter.timeStyle = .short
        return formatter.string(from: date)
    }
}


// 健康目标提示卡片组件
struct NutritionHealthGoalsView: View {
    @EnvironmentObject var userData: UserData
    @State private var isCheckingPlan = true
    
    var body: some View {
        Group {
            if isCheckingPlan {
                // 检查计划状态时显示加载指示器
                ProgressView()
                    .onAppear {
                        // 检查用户是否有计划
                        UserPlanService.shared.checkUserHasPlan(userData: userData) { hasPlan in
                            DispatchQueue.main.async {
                                self.isCheckingPlan = false
                            }
                        }
                    }
            } else if !userData.hasPlan {
                // 用户没有计划时显示设置目标提示
                ZStack {
                    // 黄色背景
                    Rectangle()
                        .fill(Color.yellow.opacity(0.15))
                        .cornerRadius(12)
                    
                    VStack(alignment: .leading, spacing: 8) {
                        HStack(alignment: .top) {
                            // 警告图标
                            Image(systemName: "exclamationmark.triangle.fill")
                                .foregroundColor(.yellow)
                                .font(.title3)
                            
                            Text("Set Your Health Goals")
                                .font(.headline)
                            
                            Spacer()
                        }
                        
                        Text("Create personalized nutrition targets for better tracking")
                            .font(.subheadline)
                            .foregroundColor(.secondary)
                        
                        // 按钮
                        HStack {
                            Spacer()
                            Button(action: {
                                // 导航到Weight页面并显示体重编辑弹窗
                                navigateToWeightGoalEdit()
                            }) {
                                Text("Set Goals")
                                    .font(.headline)
                                    .foregroundColor(.white)
                                    .padding(.horizontal, 20)
                                    .padding(.vertical, 10)
                                    .background(Color.green)
                                    .cornerRadius(8)
                            }
                        }
                        .padding(.top, 10)
                    }
                    .padding()
                }
                .padding(.horizontal)
            }
            // 用户有计划时不显示任何内容
        }
    }
    
    // 导航到体重目标编辑页面
    private func navigateToWeightGoalEdit() {
        // 直接发送通知，显示目标体重编辑页面
        NotificationCenter.default.post(name: Notification.Name("DirectShowWeightGoalEdit"), object: nil)
    }
}

// 添加全屏版本的视图组件

// 全屏通知设置视图
struct FullScreenNotificationsView: View {
    @EnvironmentObject var userData: UserData
    
    var body: some View {
        List {
            // 日常体重提醒
            Section {
                Toggle("Daily Weight Reminder", isOn: $userData.dailyReminderEnabled)
                    .toggleStyle(SwitchToggleStyle(tint: .green))
                    .onChange(of: userData.dailyReminderEnabled) { _, _ in
                        userData.saveSettings()
                        userData.scheduleNotifications()
                    }
            }
            
            // 提醒时间（固定）
            Section {
                HStack {
                    Text("Reminder Time")
                    Spacer()
                                         Text("07:30 AM / 10:30 PM")
                        .foregroundColor(.secondary)
                }
                .foregroundColor(.primary)
            }
            
            // 振动和声音选项
            Section {
                Toggle("Vibration", isOn: $userData.vibrationEnabled)
                    .toggleStyle(SwitchToggleStyle(tint: .green))
                    .onChange(of: userData.vibrationEnabled) { _, _ in
                        userData.saveSettings()
                    }
                
                Toggle("Sound", isOn: $userData.soundEnabled)
                    .toggleStyle(SwitchToggleStyle(tint: .green))
                    .onChange(of: userData.soundEnabled) { _, _ in
                        userData.saveSettings()
                        userData.scheduleNotifications()
                    }
            }
            
            // 解释文本
            Section {
                Text("Notifications help you maintain consistent weight tracking")
                    .font(.footnote)
                    .foregroundColor(.secondary)
            }
        }
        .listStyle(InsetGroupedListStyle())
        .onAppear {
            // 统计
            XDTrackTool.shared.appear("通知设置页面")
        }
    }
}

// 全屏单位设置视图
struct FullScreenUnitsView: View {
    @EnvironmentObject var userData: UserData
    
    var body: some View {
        List {
            // 体重单位
            Section(header: Text("Weight Unit")) {
                // 公斤选项
                HStack {
                    Text("KG (Kilograms)")
                    Spacer()
                    if userData.weightUnit == "kg" {
                        Image(systemName: "circle.fill")
                            .foregroundColor(.green)
                    } else {
                        Image(systemName: "circle")
                            .foregroundColor(.secondary)
                    }
                }
                .contentShape(Rectangle())
                .onTapGesture {
                    if userData.weightUnit != "kg" {
                        userData.weightUnit = "kg"
                        userData.saveSettings()
                    }
                }
                
                Text("1 kg = 2.205 lbs")
                    .font(.caption)
                    .foregroundColor(.secondary)
                
                // 磅选项
                HStack {
                    Text("LBS (Pounds)")
                    Spacer()
                    if userData.weightUnit == "lbs" {
                        Image(systemName: "circle.fill")
                            .foregroundColor(.green)
                    } else {
                        Image(systemName: "circle")
                            .foregroundColor(.secondary)
                    }
                }
                .contentShape(Rectangle())
                .onTapGesture {
                    if userData.weightUnit != "lbs" {
                        userData.weightUnit = "lbs"
                        userData.saveSettings()
                    }
                }
                
                Text("1 lb = 0.454 kg")
                    .font(.caption)
                    .foregroundColor(.secondary)
            }
            
            // 身高单位
            Section(header: Text("Height Unit")) {
                // 厘米选项
                HStack {
                    Text("CM (Centimeters)")
                    Spacer()
                    if userData.heightUnit == "cm" {
                        Image(systemName: "circle.fill")
                            .foregroundColor(.green)
                    } else {
                        Image(systemName: "circle")
                            .foregroundColor(.secondary)
                    }
                }
                .contentShape(Rectangle())
                .onTapGesture {
                    if userData.heightUnit != "cm" {
                        userData.heightUnit = "cm"
                        userData.saveSettings()
                    }
                }
                
                // 英尺/英寸选项
                HStack {
                    Text("FT (Feet/Inches)")
                    Spacer()
                    if userData.heightUnit == "ft" {
                        Image(systemName: "circle.fill")
                            .foregroundColor(.green)
                    } else {
                        Image(systemName: "circle")
                            .foregroundColor(.secondary)
                    }
                }
                .contentShape(Rectangle())
                .onTapGesture {
                    if userData.heightUnit != "ft" {
                        userData.heightUnit = "ft"
                        userData.saveSettings()
                    }
                }
            }
            
            // 提示信息
            Section {
                HStack(spacing: 12) {
                    Image(systemName: "info.circle.fill")
                        .foregroundColor(.green)
                    
                    Text("Changes will apply to all future measurements")
                        .font(.footnote)
                        .foregroundColor(.secondary)
                }
                .padding(.vertical, 8)
                .listRowBackground(Color.green.opacity(0.1))
            }
        }
        .listStyle(InsetGroupedListStyle())
        .onAppear {
            // 统计
            XDTrackTool.shared.appear("单位设置页面")
        }
    }
}

// 全屏会员订阅视图
struct FullScreenPremiumView: View {
    @EnvironmentObject var userData: UserData
    @StateObject private var subscriptionService = SubscriptionService.shared
    @Environment(\.presentationMode) var presentationMode
    @State private var isProcessingSubscription = false
    @State private var showSubscriptionAlert = false
    @State private var alertMessage = ""
    @State private var selectedPlan: String = "monthly" // 默认选择月订阅
    @State private var selectedPlanIdentifier: String? = nil // Stores the Product ID of the selected plan
    @State private var isRestoringPurchases = false
    @State private var showRestoreSuccessAlert = false
    @State private var showRestoreFailedAlert = false
    @State private var showTermsOfService = false
    @State private var showPrivacyPolicy = false
    
    var body: some View {
        ScrollView {
            VStack(spacing: 20) {
                // 头部图片
                Image("food_bowl") // 使用食物图像，您可以替换为更适合的图像
                    .resizable()
                    .aspectRatio(contentMode: .fill)
                    .frame(height: 200)
                    .clipShape(RoundedRectangle(cornerRadius: 12))
                    .overlay(
                        LinearGradient(
                            gradient: Gradient(colors: [Color.black.opacity(0.5), Color.black.opacity(0)]),
                            startPoint: .bottom,
                            endPoint: .center
                        )
                    )
                
                VStack(alignment: .leading, spacing: 20) {
                    // 标题
                    Text("Unlock Premium Features")
                        .font(.title)
                        .fontWeight(.bold)
                    
                    Text("Track your nutrition journey with AI precision")
                        .foregroundColor(.secondary)
                    
                    // 功能列表
                    VStack(spacing: 15) {
                        PremiumFeatureItem(
                            icon: "camera.fill",
                            iconColor: .green,
                            title: "Unlimited Scans",
                            description: "Analyze as many foods as you want"
                        )
                        
                        PremiumFeatureItem(
                            icon: "chart.bar.fill",
                            iconColor: .green,
                            title: "Detailed Analysis",
                            description: "Get comprehensive nutrition insights"
                        )
                        
                        PremiumFeatureItem(
                            icon: "scale.3d",
                            iconColor: .green,
                            title: "Weight Tracking",
                            description: "Track your progress over time"
                        )
                        
                        PremiumFeatureItem(
                            icon: "text.bubble.fill",
                            iconColor: .green,
                            title: "Personalized Tips",
                            description: "Get customized nutrition advice"
                        )
                    }
                    
                    // 价格选项
                    VStack(spacing: 15) {
                        
                        // 月度订阅 - 始终显示
                        if let monthlyProduct = subscriptionService.getMonthlyProduct() {
                            Button(action: {
                                selectedPlanIdentifier = monthlyProduct.productId
                                selectedPlan = "monthly" // Keep for potential UI logic or remove if not used
                            }) {
                                HStack {
                                    VStack(alignment: .leading, spacing: 3) {
                                        Text(monthlyProduct.name)
                                            .font(.subheadline)
                                            .foregroundColor(.secondary)
                                        
                                        HStack(alignment: .firstTextBaseline) {
                                            Text("$\(String(format: "%.2f", monthlyProduct.price))")
                                                .font(.title2)
                                                .fontWeight(.bold)
                                                .foregroundColor(.green)
                                            
                                            Text("/month")
                                                .font(.subheadline)
                                                .foregroundColor(.secondary)
                                        }
                                    }
                                    
                                    Spacer()
                                }
                                .padding()
                                .background(selectedPlanIdentifier == monthlyProduct.productId ? Color.green.opacity(0.2) : Color(UIColor.systemGray6))
                                .cornerRadius(12)
                                .overlay(
                                    RoundedRectangle(cornerRadius: 12)
                                        .stroke(selectedPlanIdentifier == monthlyProduct.productId ? Color.green : Color.clear, lineWidth: 2)
                                )
                            }
                            .buttonStyle(PlainButtonStyle())
                        }
                        
                        // 年度订阅 - 始终显示
                        if let annualProduct = subscriptionService.getAnnualProduct() {
                            Button(action: {
                                selectedPlanIdentifier = annualProduct.productId
                                selectedPlan = "annual" // Keep for potential UI logic or remove if not used
                            }) {
                                ZStack {
                                    // 主要内容
                                    HStack {
                                        VStack(alignment: .leading, spacing: 3) {
                                            Text(annualProduct.name)
                                                .font(.subheadline)
                                                .foregroundColor(.secondary)
                                            
                                            HStack(alignment: .firstTextBaseline) {
                                                Text("$\(String(format: "%.2f", annualProduct.price))")
                                                    .font(.title2)
                                                    .fontWeight(.bold)
                                                    .foregroundColor(.green)
                                                
                                                Text("/year")
                                                    .font(.subheadline)
                                                    .foregroundColor(.secondary)
                                            }
                                        }
                                        
                                        Spacer()
                                        
                                        // Best Value标签
                                        VStack(spacing: 8) {
                                            Text("Save17%")
                                                .font(.caption2)
                                                .fontWeight(.bold)
                                                .foregroundColor(.black)
                                                .padding(.horizontal, 8)
                                                .padding(.vertical, 4)
                                                .background(Color.yellow)
                                                .cornerRadius(10)
                                            
                                            Text("Best Value")
                                                .font(.caption)
                                                .padding(.horizontal, 10)
                                                .padding(.vertical, 5)
                                                .background(Color.green)
                                                .foregroundColor(.white)
                                                .cornerRadius(15)
                                        }
                                    }
                                    .padding()
                                    .background(selectedPlanIdentifier == annualProduct.productId ? Color.green.opacity(0.2) : Color(UIColor.systemGray6))
                                    .cornerRadius(12)
                                    .overlay(
                                        RoundedRectangle(cornerRadius: 12)
                                            .stroke(selectedPlanIdentifier == annualProduct.productId ? Color.green : Color.clear, lineWidth: 2)
                                    )
                                }
                            }
                            .buttonStyle(PlainButtonStyle())
                        }
                    }
                    
                    // 订阅按钮 - 根据试用期动态显示文本
                    Button(action: {
                        if userData.isPremium {
                            // 用户已订阅，可以显示管理订阅的界面或提示
                            showSubscriptionAlert = true
                            alertMessage = "You are already a premium member. Manage your subscription in App Store settings."
                        } else if let planId = selectedPlanIdentifier {
                            let productToSubscribe: SubscriptionProduct?
                            if let monthly = subscriptionService.getMonthlyProduct(), monthly.productId == planId {
                                productToSubscribe = monthly
                            } else if let annual = subscriptionService.getAnnualProduct(), annual.productId == planId {
                                productToSubscribe = annual
                            } else {
                                productToSubscribe = nil
                            }

                            if let product = productToSubscribe {
                                processSubscription(product: product)
                            } else {
                                // 这不应该发生，如果 planId 是有效的
                                alertMessage = "Selected plan is currently unavailable. Please try again."
                                showSubscriptionAlert = true
                            }
                        } else {
                            // 没有选择计划
                            alertMessage = "Please select a monthly or annual plan first."
                            showSubscriptionAlert = true
                        }
                    }) {
                        if isProcessingSubscription {
                            ProgressView()
                                .progressViewStyle(CircularProgressViewStyle(tint: .white))
                                .frame(maxWidth: .infinity)
                                .padding()
                                .background(Color.green)
                                .cornerRadius(10)
                        } else {
                            // 根据产品试用期动态显示按钮文本
                            let buttonText = getSubscriptionButtonText()
                            Text(buttonText)
                                .fontWeight(.semibold)
                                .frame(maxWidth: .infinity)
                                .padding()
                                .background(Color.green)
                                .foregroundColor(.white)
                                .cornerRadius(10)
                        }
                    }
                    .disabled(isProcessingSubscription)
                    .padding(.vertical)
                    
                    // 取消提示
                    Text("Cancel anytime")
                        .font(.custom("Inter", size: 14))
                        .fontWeight(.regular)
                        .lineLimit(nil)
                        .fixedSize(horizontal: false, vertical: true)
                        .foregroundColor(Color(red: 0.29, green: 0.33, blue: 0.39))
                        .frame(maxWidth: .infinity, alignment: .center)
                        .lineSpacing(6) // 行高20px - 字号14px = 6px行间距
                    
                    // 协议链接组合：Restore Purchase | Terms of Service | Privacy Policy
                    HStack(spacing: 0) {
                        Button(action: {
                            Task {
                                isRestoringPurchases = true
                                do {
                                    let success = try await SubscriptionService.shared.restorePurchases()
                                    if success {
                                        showRestoreSuccessAlert = true
                                    } else {
                                        showRestoreFailedAlert = true
                                    }
                                } catch {
                                    showRestoreFailedAlert = true
                                }
                                isRestoringPurchases = false
                            }
                        }) {
                            Text(isRestoringPurchases ? "Restoring..." : "Restore Purchase")
                                .font(.custom("Inter", size: 10))
                                .fontWeight(.regular)
                                .foregroundColor(Color(red: 0.42, green: 0.45, blue: 0.5).opacity(0.7))
                        }
                        .disabled(isRestoringPurchases)
                        
                        Text(" | ")
                            .font(.custom("Inter", size: 10))
                            .fontWeight(.regular)
                            .foregroundColor(Color(red: 0.42, green: 0.45, blue: 0.5).opacity(0.7))
                        
                        Button(action: {
                            showTermsOfService = true
                        }) {
                            Text("Terms of Service")
                                .font(.custom("Inter", size: 10))
                                .fontWeight(.regular)
                                .foregroundColor(Color(red: 0.42, green: 0.45, blue: 0.5).opacity(0.7))
                        }
                        
                        Text(" | ")
                            .font(.custom("Inter", size: 10))
                            .fontWeight(.regular)
                            .foregroundColor(Color(red: 0.42, green: 0.45, blue: 0.5).opacity(0.7))
                        
                        Button(action: {
                            showPrivacyPolicy = true
                        }) {
                            Text("Privacy Policy")
                                .font(.custom("Inter", size: 10))
                                .fontWeight(.regular)
                                .foregroundColor(Color(red: 0.42, green: 0.45, blue: 0.5).opacity(0.7))
                        }
                    }
                    .frame(maxWidth: .infinity, alignment: .center)
                    .lineSpacing(6) // 行高16px - 字号10px = 6px行间距
                    .padding(.top, 10)
                }
                .padding()
            }
        }
        .onAppear {
            Task {
                await subscriptionService.fetchSubscriptionProducts()
                // 默认选择月订阅
                if let monthlyProduct = subscriptionService.getMonthlyProduct() {
                    selectedPlanIdentifier = monthlyProduct.productId
                    selectedPlan = "monthly"
                }
                
                // 检查试用期资格
                await userData.checkTrialEligibility()
            }
        }
        .alert(isPresented: $showSubscriptionAlert) {
            Alert(
                title: Text(selectedPlan.isEmpty ? "Subscription Notice" : "Subscription Confirmation"),
                message: Text(alertMessage),
                dismissButton: .default(Text("OK"))
            )
        }
        // 恢复购买成功提示 - 使用iOS自带Alert替代第三方弹窗
        .alert("Subscription Restored!", isPresented: $showRestoreSuccessAlert) {
            Button("OK") {
                // 刷新订阅状态
                Task {
                    // 延迟2秒等待服务器端同步订阅状态
                    try? await Task.sleep(nanoseconds: 2_000_000_000)
                    await userData.checkAndUpdateSubscriptionStatus()
                    // 恢复购买成功后也返回上一级
                    await MainActor.run {
                        presentationMode.wrappedValue.dismiss()
                    }
                }
            }
        } message: {
            Text("Your premium access has been successfully recovered.")
        }
        // 恢复购买失败提示 - 使用iOS自带Alert替代第三方弹窗
        .alert("No Purchases Found", isPresented: $showRestoreFailedAlert) {
            Button("Close", role: .cancel) { }
        } message: {
            Text("We couldn't find any available subscriptions or purchases linked to your account.\n• Ensure you're logged in with the original purchase account\n• App Store transactions may take 24 hours to synchronize")
        }
        .sheet(isPresented: $showTermsOfService) {
            NavigationView {
                WebPageView(url: URL(string: "https://littlegrass.cc/app/fitscanai/terms.html")!, title: "Terms of Service")
            }
        }
        .sheet(isPresented: $showPrivacyPolicy) {
            NavigationView {
                WebPageView(url: URL(string: "https://littlegrass.cc/app/fitscanai/privacy.html")!, title: "Privacy Policy")
            }
        }
    }
    
    // 根据产品试用期获取按钮文本
    private func getSubscriptionButtonText() -> String {
        return userData.getSubscriptionButtonText()
    }
    

    
    // 处理订阅
    func processSubscription(product: SubscriptionProduct) {
        print("🔄 ContentView: 开始处理订阅 - 产品: \(product.name), ID: \(product.productId)")
        isProcessingSubscription = true
        
        Task {
            if #available(iOS 15.0, *) {
                // 使用StoreKit 2
                let storeKitManager = StoreKitManager.shared
                
                print("📋 ContentView: 当前可用的StoreKit产品数量: \(storeKitManager.products.count)")
                for storeProduct in storeKitManager.products {
                    print("📋 ContentView: 可用产品 - ID: \(storeProduct.id), 名称: \(storeProduct.displayName)")
                }
                
                // 查找对应的StoreKit产品
                if let storeProduct = storeKitManager.products.first(where: { $0.id == product.productId }) {
                    print("✅ ContentView: 找到匹配的StoreKit产品: \(storeProduct.displayName)")
                    
                    // 直接尝试购买，不进行账号预检查
                    // 让StoreKit自己处理账号状态问题
                    let success = await storeKitManager.purchase(storeProduct)
                    
                    await MainActor.run {
            isProcessingSubscription = false
                        
                        if success {
                            print("✅ ContentView: 订阅成功")
                            alertMessage = "You have successfully subscribed to \(product.name) for $\(String(format: "%.2f", product.price))"
                            // 更新用户订阅状态 - 从API获取最新状态
                            Task {
                                // 延迟2秒等待服务器端同步订阅状态
                                try? await Task.sleep(nanoseconds: 2_000_000_000)
                                await userData.checkAndUpdateSubscriptionStatus()
                                // 订阅成功后返回上一级
                                await MainActor.run {
                                    presentationMode.wrappedValue.dismiss()
                                }
                            }
                        } else {
                            print("❌ ContentView: 订阅失败")
                            let errorMsg = subscriptionService.errorMessage ?? "Subscription failed, please try again"
                            print("❌ ContentView: 错误信息: \(errorMsg)")
                            
                            // 检查是否是账号问题
                            if errorMsg.contains("No active account") || errorMsg.contains("509") {
                                alertMessage = "Please sign in with your Apple ID in Settings > App Store to use subscription features."
                            } else {
                                alertMessage = errorMsg
                            }
                        }
                                    showSubscriptionAlert = true
                    }
                } else {
                    print("❌ ContentView: 未找到匹配的StoreKit产品")
                    print("❌ ContentView: 请求的产品ID: \(product.productId)")
                    print("❌ ContentView: 可用的产品ID: \(storeKitManager.products.map { $0.id })")
                    
                    // 尝试重新加载产品
                    print("🔄 ContentView: 尝试重新加载StoreKit产品...")
                    await storeKitManager.loadProducts()
                    
                    // 再次查找
                    if let storeProduct = storeKitManager.products.first(where: { $0.id == product.productId }) {
                        print("✅ ContentView: 重新加载后找到产品，开始购买...")
                        let success = await storeKitManager.purchase(storeProduct)
                        
                        await MainActor.run {
                            isProcessingSubscription = false
                            
                            if success {
                                alertMessage = "You have successfully subscribed to \(product.name) for $\(String(format: "%.2f", product.price))"
                                // 更新用户订阅状态 - 从API获取最新状态
                                Task {
                                    // 延迟2秒等待服务器端同步订阅状态
                                    try? await Task.sleep(nanoseconds: 2_000_000_000)
                                    await userData.checkAndUpdateSubscriptionStatus()
                                    // 订阅成功后返回上一级
                                    await MainActor.run {
                                        presentationMode.wrappedValue.dismiss()
                                    }
                                }
                            } else {
                                // Revert to using subscriptionService.errorMessage as storeKitManager.transactionError does not exist.
                                let errorMsg = subscriptionService.errorMessage ?? "Subscription failed, please try again"

                                // 检查是否是账号问题
                                if errorMsg.contains("No active account") || errorMsg.contains("509") {
                                    alertMessage = "Please sign in with your Apple ID in Settings > App Store to use subscription features."
                                } else {
                                    alertMessage = errorMsg
                                }
                            }
                            
                            showSubscriptionAlert = true
                        }
                    } else {
                        await MainActor.run {
                            isProcessingSubscription = false
                            alertMessage = "Product not available, please check your network connection and try again. If the problem persists, please contact support."
                            showSubscriptionAlert = true
                        }
                    }
                }
            } else {
                // iOS 15以下版本的处理
                await MainActor.run {
                    isProcessingSubscription = false
                    alertMessage = "iOS 15 or higher is required to use subscription features"
                    showSubscriptionAlert = true
                }
            }
        }
    }
}


// API主页食物记录行视图
struct APIHomeFoodRecordRow: View {
    let record: DietRecordResponse
    @State private var foodImage: UIImage?
    @State private var isLoadingImage = false
    
    var body: some View {
        HStack(spacing: 12) {
            // 食物图片
            if let image = foodImage {
                Image(uiImage: image)
                    .resizable()
                    .scaledToFill()
                    .frame(width: 60, height: 60)
                    .cornerRadius(8)
            } else if record.hasImage {
                ZStack {
                    Rectangle()
                        .fill(Color.gray.opacity(0.1))
                        .frame(width: 60, height: 60)
                        .cornerRadius(8)
                    
                    if isLoadingImage {
                        ProgressView()
                            .progressViewStyle(CircularProgressViewStyle())
                            .scaleEffect(0.7)
                    } else {
                        Image(systemName: "photo")
                            .foregroundColor(.gray)
                    }
                }
                .onAppear {
                    loadFoodImage()
                }
            } else {
                ZStack {
                    Rectangle()
                        .fill(Color.gray.opacity(0.1))
                        .frame(width: 60, height: 60)
                        .cornerRadius(8)
                    
                    Image(systemName: "fork.knife")
                        .foregroundColor(.gray)
                }
            }
            
            // 食物信息
            VStack(alignment: .leading, spacing: 4) {
                // 根据时间判断餐点类型
                let mealType = getMealType(from: record.displayTime)
                Text(mealType)
                    .font(.caption)
                    .foregroundColor(.secondary)
                
                Text(record.foodName)
                    .font(.headline)
                    .lineLimit(2)
                
                Text(record.displayTime)
                    .font(.caption)
                    .foregroundColor(.secondary)
            }
            
            Spacer()
            
            // 卡路里信息
            VStack(alignment: .trailing) {
                Text("\(record.calories)")
                    .font(.headline)
                    .foregroundColor(.green)
                
                Text(record.caloriesUnit)
                    .font(.caption)
                    .foregroundColor(.secondary)
            }
        }
        .padding(.horizontal, 16)
        .padding(.vertical, 12)
    }
    
    // 根据时间字符串判断餐点类型
    private func getMealType(from timeStr: String) -> String {
        // 尝试解析时间字符串，格式可能是 "HH:mm" 或 "yyyy-MM-dd HH:mm:ss"
        let timeComponents = timeStr.split(separator: " ")
        let time = timeComponents.last ?? timeComponents.first ?? ""
        
        let hourComponents = time.split(separator: ":")
        if let hourStr = hourComponents.first, let hour = Int(hourStr) {
            if hour < 12 {
                return "Breakfast"
            } else if hour < 17 {
                return "Lunch"
            } else {
                return "Dinner"
            }
        }
        
        return "Meal" // 默认值
    }
    
    // 加载食物图片
    private func loadFoodImage() {
        guard record.hasImage, let imageUrl = record.fileLocation, !imageUrl.isEmpty else {
            return
        }
        
        // 检查是否已经加载过图片
        if foodImage != nil {
            return
        }
        
        isLoadingImage = true
        
        print("开始加载主页食物图片: \(imageUrl)")
        
        // 对于第一次获取的大图，优先显示缩略图
        if ImageCacheManager.shared.getImage(for: imageUrl) == nil {
            // 第一次加载，使用快速缩略图
            ImageCacheManager.shared.loadThumbnailFirst(from: imageUrl) { image in
                DispatchQueue.main.async {
                    self.foodImage = image
                    self.isLoadingImage = false
                    if image != nil {
                        print("成功加载主页食物缩略图（超高压缩）")
                    } else {
                        print("加载主页食物缩略图失败")
                    }
                }
            }
        } else {
            // 已有缓存，直接使用优化的加载方法
            ImageCacheManager.shared.loadFoodDiaryImage(from: imageUrl, isListView: true) { image in
                DispatchQueue.main.async {
                    self.foodImage = image
                    self.isLoadingImage = false
                    if image != nil {
                        print("成功加载主页食物图片（缓存）")
                    } else {
                        print("加载主页食物图片失败")
                    }
                }
            }
        }
    }
}

// API食物记录行视图 (用于食物日记)
struct APIFoodRecordJournalRow: View {
    let record: DietRecordResponse
    @State private var foodImage: UIImage?
    @State private var isLoadingImage = false
    
    var body: some View {
        HStack(spacing: 16) {
            // 食物图片
            if let image = foodImage {
                Image(uiImage: image)
                    .resizable()
                    .scaledToFill()
                    .frame(width: 60, height: 60)
                    .cornerRadius(8)
            } else if record.hasImage {
                ZStack {
                    Rectangle()
                        .fill(Color.gray.opacity(0.1))
                        .frame(width: 60, height: 60)
                        .cornerRadius(8)
                    
                    if isLoadingImage {
                        ProgressView()
                            .progressViewStyle(CircularProgressViewStyle())
                            .scaleEffect(0.7)
                    } else {
                        Image(systemName: "photo")
                            .foregroundColor(.gray)
                    }
                }
                .onAppear {
                    loadFoodImage()
                }
            } else {
                ZStack {
                    Rectangle()
                        .fill(Color.gray.opacity(0.1))
                        .frame(width: 60, height: 60)
                        .cornerRadius(8)
                    
                    Image(systemName: "fork.knife")
                        .foregroundColor(.gray)
                }
            }
            
            // 食物信息
            VStack(alignment: .leading, spacing: 4) {
                Text(record.foodName)
                    .font(.headline)
                    .lineLimit(2)
                
                Text(record.displayTime)
                    .font(.caption)
                    .foregroundColor(.secondary)
            }
            
            Spacer()
            
            // 卡路里信息
            VStack(alignment: .trailing) {
                Text("\(record.calories)")
                    .font(.headline)
                    .foregroundColor(.green)
                
                Text(record.caloriesUnit)
                    .font(.caption)
                    .foregroundColor(.secondary)
            }
        }
        .padding()
        .background(Color.white)
        .cornerRadius(12)
        .shadow(color: .black.opacity(0.1), radius: 4, x: 0, y: 2)
    }
    
    // 加载食物图片
    private func loadFoodImage() {
        guard record.hasImage, let imageUrl = record.fileLocation, !imageUrl.isEmpty else {
            return
        }
        
        // 检查是否已经加载过图片
        if foodImage != nil {
            return
        }
        
        isLoadingImage = true
        
        print("开始加载食物日记图片: \(imageUrl)")
        
        // 对于第一次获取的大图，优先显示超压缩缩略图
        if ImageCacheManager.shared.getImage(for: imageUrl) == nil {
            // 第一次加载，使用快速缩略图
            ImageCacheManager.shared.loadThumbnailFirst(from: imageUrl) { image in
                DispatchQueue.main.async {
                    self.foodImage = image
                    self.isLoadingImage = false
                    if image != nil {
                        print("成功加载食物日记缩略图（超高压缩）")
                    } else {
                        print("加载食物日记缩略图失败")
                    }
                }
            }
        } else {
            // 已有缓存，直接使用优化的加载方法
            ImageCacheManager.shared.loadFoodDiaryImage(from: imageUrl, isListView: true) { image in
                DispatchQueue.main.async {
                    self.foodImage = image
                    self.isLoadingImage = false
                    if image != nil {
                        print("成功加载食物日记图片（缓存）")
                    } else {
                        print("加载食物日记图片失败")
                    }
                }
            }
        }
    }
}

#Preview {
    ContentView()
}

// 个人页面订阅组件
struct ProfileSubscriptionSection: View {
    @EnvironmentObject var userData: UserData
    @StateObject private var subscriptionService = SubscriptionService.shared
    @State private var selectedPlan: String = "annual" // 默认选择年度计划
    @State private var isLoading = false
    @State private var errorMessage: String?
    @State private var showAlert = false
    
    var body: some View {
        VStack(spacing: 20) {
            Text("Unlock Premium Features")
                .font(.title3)
                .fontWeight(.bold)
                .foregroundColor(.white)
                .frame(maxWidth: .infinity, alignment: .leading)
            
            // 添加跳转到完整订阅页面的功能
            NavigationLink(destination: 
                FullScreenPremiumView()
                    .environmentObject(userData)
                    .navigationBarTitle("Unlock Premium Features", displayMode: .inline)
            ) {
                Text("Get the most out of FitScanAI >")
                    .font(.custom("Inter", size: 16))
                    .fontWeight(.regular) // 字重400对应regular
                    .lineSpacing(8) // 行高24px - 字号16px = 8px行间距
                    .foregroundColor(.white.opacity(0.8))
                    .frame(maxWidth: .infinity, alignment: .leading)
            }
            
            HStack(spacing: 10) {
                // 月度计划
                VStack(spacing: 5) {
                    Text("Monthly Plan")
                        .font(.footnote)
                        .foregroundColor(selectedPlan == "monthly" ? .green : .white.opacity(0.9))
                    
                    Text("$5.99")
                        .font(.title)
                        .fontWeight(.bold)
                        .foregroundColor(selectedPlan == "monthly" ? .green : .white)
                    
                    Text("per month")
                        .font(.caption)
                        .foregroundColor(selectedPlan == "monthly" ? .green.opacity(0.7) : .white.opacity(0.7))
                }
                .frame(maxWidth: .infinity)
                .padding(.vertical, 15)
                .background(selectedPlan == "monthly" ? Color.white : Color.white.opacity(0.2))
                .cornerRadius(10)
                .overlay(
                    RoundedRectangle(cornerRadius: 10)
                        .stroke(selectedPlan == "monthly" ? Color.green : Color.clear, lineWidth: 2)
                )
                .onTapGesture {
                    selectedPlan = "monthly"
                }
                
                // 年度计划
                VStack(spacing: 5) {
                    Text("Annual Plan")
                        .font(.footnote)
                        .foregroundColor(selectedPlan == "annual" ? .green : .white.opacity(0.9))
                    
                    Text("$59.9")
                        .font(.title)
                        .fontWeight(.bold)
                        .foregroundColor(selectedPlan == "annual" ? .green : .white)
                    
                    Text("per year")
                        .font(.caption)
                        .foregroundColor(selectedPlan == "annual" ? .green.opacity(0.7) : .white.opacity(0.7))
                }
                .frame(maxWidth: .infinity)
                .padding(.vertical, 15)
                .background(selectedPlan == "annual" ? Color.white : Color.white.opacity(0.2))
                .cornerRadius(10)
                .overlay(
                    ZStack {
                        RoundedRectangle(cornerRadius: 10)
                            .stroke(selectedPlan == "annual" ? Color.green : Color.clear, lineWidth: 2)
                        
                        // Save17%标签到右上角 - 一直显示
                        VStack {
                            HStack {
                                Spacer()
                                Text("Save17%")
                                    .font(.caption2)
                                    .fontWeight(.bold)
                                    .foregroundColor(.black)
                                    .padding(.horizontal, 8)
                                    .padding(.vertical, 4)
                                    .background(Color.yellow)
                                    .cornerRadius(10)
                                    .offset(x: 1, y: 0)
                            }
                            Spacer()
                        }
                    }
                )
                .onTapGesture {
                    selectedPlan = "annual"
                }
            }
            
            // 订阅按钮 - 集成IAP功能
            Button(action: {
                Task {
                    await handleSubscription()
                }
            }) {
                HStack {
                    if isLoading {
                        ProgressView()
                            .progressViewStyle(CircularProgressViewStyle(tint: .black))
                            .scaleEffect(0.8)
                    }
                    Text(isLoading ? "Processing..." : userData.getSubscriptionButtonText())
                        .fontWeight(.semibold)
                }
                .frame(maxWidth: .infinity)
                .padding()
                .background(Color.yellow)
                .foregroundColor(.black)
                .cornerRadius(10)
            }
            .disabled(isLoading)
            
            Text("Cancel anytime")
                .font(.custom("Inter", size: 14))
                .fontWeight(.regular)
                .lineLimit(nil)
                .fixedSize(horizontal: false, vertical: true)
                .foregroundColor(Color(red: 1, green: 1, blue: 1).opacity(0.8))
                .frame(maxWidth: .infinity, alignment: .center)
                .lineSpacing(6) 
        }
        .padding()
        .background(
            LinearGradient(
                gradient: Gradient(colors: [
                    Color(red: 0.3, green: 0.69, blue: 0.31),
                    Color(red: 0.18, green: 0.49, blue: 0.2)
                ]),
                startPoint: .topLeading,
                endPoint: .bottomTrailing
            )
        )
        .cornerRadius(15)
        .onAppear {
            // 统计
            XDTrackTool.shared.appear("会员订阅页面")

            Task {
                await subscriptionService.fetchSubscriptionProducts()
            }
        }
        .alert("Subscription Error", isPresented: $showAlert) {
            Button("OK", role: .cancel) { }
        } message: {
            Text(errorMessage ?? "Unknown error occurred")
        }
    }
    
    // 处理订阅购买
    @MainActor
    private func handleSubscription() async {
        isLoading = true
        errorMessage = nil
        
        do {
            // 获取对应的产品ID
            let productId: String
            if selectedPlan == "monthly" {
                guard let monthlyProduct = subscriptionService.getMonthlyProduct() else {
                    throw NSError(domain: "SubscriptionError", code: 1, userInfo: [NSLocalizedDescriptionKey: "Monthly product not found"])
                }
                productId = monthlyProduct.productId
            } else {
                guard let annualProduct = subscriptionService.getAnnualProduct() else {
                    throw NSError(domain: "SubscriptionError", code: 1, userInfo: [NSLocalizedDescriptionKey: "Annual product not found"])
                }
                productId = annualProduct.productId
            }
            
            print("📱 ProfileSubscriptionSection: 开始购买 \(selectedPlan) 订阅，产品ID: \(productId)")
            
            // 使用StoreKitManager进行购买
            if #available(iOS 15.0, *) {
                // 首先加载产品
                await StoreKitManager.shared.loadProducts()
                
                // 查找对应的产品
                guard let product = StoreKitManager.shared.products.first(where: { $0.id == productId }) else {
                    throw NSError(domain: "SubscriptionError", code: 2, userInfo: [NSLocalizedDescriptionKey: "Product not found in App Store"])
                }
                
                // 发起购买
                let success = await StoreKitManager.shared.purchase(product)
                
                if success {
                    print("✅ ProfileSubscriptionSection: 订阅购买成功")
                    // 刷新用户订阅状态 - 从API获取最新状态
                    await userData.checkAndUpdateSubscriptionStatus()
                } else {
                    throw NSError(domain: "SubscriptionError", code: 3, userInfo: [NSLocalizedDescriptionKey: "Purchase failed or was cancelled"])
                }
            } else {
                throw NSError(domain: "SubscriptionError", code: 4, userInfo: [NSLocalizedDescriptionKey: "iOS 15.0 or later required for subscriptions"])
            }
            
        } catch {
            print("❌ ProfileSubscriptionSection: 订阅购买失败: \(error)")
            errorMessage = error.localizedDescription
            showAlert = true
        }
        
        isLoading = false
    }
}

// 根据小时数判断餐点类型的全局辅助函数
func getMealTypeFromHour(_ hour: Int) -> String {
    if (hour >= 0 && hour < 5) || (hour >= 5 && hour < 12) {
        return "Breakfast"
    } else if hour >= 12 && hour < 17 {
        return "Lunch"
    } else {
        return "Dinner"
    }
}


