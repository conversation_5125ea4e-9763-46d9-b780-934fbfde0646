import SwiftUI
import Combine
import UIKit
import WebKit
import Network

// 添加WebView组件（如果ContentView.swift中已有，可以删除这个重复定义）
struct LoginWebView: UIViewRepresentable {
    let url: URL
    @Binding var isLoading: Bool
    
    func makeUIView(context: Context) -> WKWebView {
        let configuration = WKWebViewConfiguration()
        configuration.allowsInlineMediaPlayback = true
        configuration.mediaTypesRequiringUserActionForPlayback = []
        
        let webView = WKWebView(frame: .zero, configuration: configuration)
        webView.navigationDelegate = context.coordinator
        webView.allowsBackForwardNavigationGestures = false
        webView.backgroundColor = UIColor.systemBackground
        
        // 设置用户代理
        webView.customUserAgent = "Mozilla/5.0 (iPhone; CPU iPhone OS 16_0 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/16.0 Mobile/15E148 Safari/604.1"
        
        return webView
    }
    
    func updateUIView(_ webView: WKWebView, context: Context) {
        // 检查是否已经加载了相同的URL，避免重复加载
        if webView.url != url {
            var request = URLRequest(url: url)
            request.timeoutInterval = 30.0
            request.cachePolicy = .returnCacheDataElseLoad
            webView.load(request)
        }
    }
    
    func makeCoordinator() -> Coordinator {
        Coordinator(self)
    }
    
    class Coordinator: NSObject, WKNavigationDelegate {
        let parent: LoginWebView
        private var retryCount = 0
        private let maxRetries = 2
        
        init(_ parent: LoginWebView) {
            self.parent = parent
        }
        
        func webView(_ webView: WKWebView, didStartProvisionalNavigation navigation: WKNavigation!) {
            DispatchQueue.main.async {
                self.parent.isLoading = true
            }
        }
        
        func webView(_ webView: WKWebView, didFinish navigation: WKNavigation!) {
            DispatchQueue.main.async {
                self.parent.isLoading = false
                self.retryCount = 0
            }
        }
        
        func webView(_ webView: WKWebView, didFail navigation: WKNavigation!, withError error: Error) {
            handleError(webView: webView, error: error)
        }
        
        func webView(_ webView: WKWebView, didFailProvisionalNavigation navigation: WKNavigation!, withError error: Error) {
            handleError(webView: webView, error: error)
        }
        
        private func handleError(webView: WKWebView, error: Error) {
            let nsError = error as NSError
            print("LoginWebView error: \(error.localizedDescription), code: \(nsError.code)")
            
            DispatchQueue.main.async {
                self.parent.isLoading = false
                
                // 对于-999错误，尝试自动重试
                if nsError.code == -999 && self.retryCount < self.maxRetries {
                    self.retryCount += 1
                    print("LoginWebView retrying load attempt \(self.retryCount)")
                    
                    DispatchQueue.main.asyncAfter(deadline: .now() + 1.5) {
                        var request = URLRequest(url: self.parent.url)
                        request.timeoutInterval = 30.0
                        request.cachePolicy = .returnCacheDataElseLoad
                        webView.load(request)
                    }
                }
            }
        }
        
        func webView(_ webView: WKWebView, decidePolicyFor navigationAction: WKNavigationAction, decisionHandler: @escaping (WKNavigationActionPolicy) -> Void) {
            decisionHandler(.allow)
        }
    }
}

// 网页视图组件
struct LoginWebPageView: View {
    let url: URL
    let title: String
    @Environment(\.presentationMode) var presentationMode
    @State private var isLoading = true
    
    var body: some View {
        NavigationView {
            ZStack {
                LoginWebView(url: url, isLoading: $isLoading)
                
                if isLoading {
                    ProgressView("Loading...")
                        .frame(maxWidth: .infinity, maxHeight: .infinity)
                        .background(Color.white)
                }
            }
            .navigationTitle(title)
            .navigationBarTitleDisplayMode(.inline)
            .navigationBarItems(
                leading: Button(action: {
                    presentationMode.wrappedValue.dismiss()
                }) {
                    Image(systemName: "chevron.left")
                        .foregroundColor(.green)
                }
            )
        }
    }
}

// 网络状态管理器
class NetworkStatusManager: ObservableObject {
    @Published var isNetworkAvailable = true
    @Published var hasNetworkPermission = true
    
    private let monitor = NWPathMonitor()
    private let queue = DispatchQueue(label: "NetworkMonitor")
    
    init() {
        startMonitoring()
    }
    
    private func startMonitoring() {
        monitor.pathUpdateHandler = { [weak self] path in
            DispatchQueue.main.async {
                self?.isNetworkAvailable = path.status == .satisfied
                print("📶 网络状态更新: \(path.status == .satisfied ? "可用" : "不可用")")
            }
        }
        monitor.start(queue: queue)
    }
    
    // 主动触发网络权限检查
    func triggerNetworkPermissionCheck() {
        print("🔍 主动触发网络权限检查...")
        
        // 发送一个轻量级的网络请求来触发权限对话框
        let url = URL(string: "https://fsai-auth.pickgoodspro.com/csrf-token")!
        var request = URLRequest(url: url)
        request.timeoutInterval = 5.0
        request.httpMethod = "HEAD" // 使用HEAD请求减少数据传输
        
        let task = URLSession.shared.dataTask(with: request) { [weak self] data, response, error in
            DispatchQueue.main.async {
                if let error = error {
                    let nsError = error as NSError
                    // 检查是否是网络权限相关的错误
                    if nsError.domain == NSURLErrorDomain {
                        switch nsError.code {
                        case NSURLErrorNotConnectedToInternet:
                            self?.hasNetworkPermission = false
                            print("❌ 网络权限被拒绝或网络不可用")
                        case NSURLErrorNetworkConnectionLost:
                            print("⚠️ 网络连接丢失")
                        default:
                            print("🔍 网络检查完成，错误: \(error.localizedDescription)")
                        }
                    }
                } else {
                    self?.hasNetworkPermission = true
                    print("✅ 网络权限检查完成，权限正常")
                }
            }
        }
        
        task.resume()
    }
    
    deinit {
        monitor.cancel()
    }
}

struct LoginView: View {
    @State private var email: String = ""
    @State private var password: String = ""

    @State private var isPasswordVisible: Bool = false
    @State private var showAlert: Bool = false
    @State private var alertTitle: String = ""
    @State private var alertMessage: String = ""
    @State private var isLoading: Bool = false

    
    // 登录按钮点击效果
    @State private var isSignInPressed: Bool = false
    @State private var isGooglePressed: Bool = false
    @State private var isApplePressed: Bool = false
    @State private var isSignUpPressed: Bool = false
    @State private var isForgotPasswordPressed: Bool = false
    
    @State private var navigateToHome: Bool = false
    @State private var navigateToSignUp: Bool = false
    
    // 改进的键盘高度监听和输入框焦点管理
    @State private var keyboardHeight: CGFloat = 0
    @FocusState private var focusedField: FocusedField?
    
    // 添加网页显示状态
    @State private var showTermsOfService = false
    @State private var showPrivacyPolicy = false
    
    // 网络状态管理
    @StateObject private var networkManager = NetworkStatusManager()
    @State private var showNetworkAlert = false
    
    @EnvironmentObject var userData: UserData
    
    // 定义焦点字段枚举
    enum FocusedField {
        case email, password
    }
    
    var body: some View {
        NavigationStack {
            ZStack {
                Color.white.edgesIgnoringSafeArea(.all)
                
                ScrollViewReader { proxy in
                    ScrollView(showsIndicators: false) {
                        VStack(spacing: 20) {
                            // 应用图标和标题
                            VStack(spacing: 8) {
                                // 使用新的AppLogo图片
                                Image("AppLogo")
                                    .resizable()
                                    .aspectRatio(contentMode: .fit)
                                        .frame(width: 80, height: 80)
                                        .cornerRadius(16)
                                
                                Text("FitScanAI")
                                    .font(.system(size: 32, weight: .bold))
                                    .foregroundColor(.black)
                                
                                Text("Your AI-Powered Health & Nutrition Partner")
                                    .font(.system(size: 16))
                                    .foregroundColor(.secondary)
                                    .multilineTextAlignment(.center)
                            }
                            .padding(.top, keyboardHeight > 0 ? 20 : 40)
                            .padding(.bottom, keyboardHeight > 0 ? 15 : 30)
                            
                            // Apple登录按钮
                            Button(action: {
                                isApplePressed = true
                                
                                // 添加登录延迟模拟效果
                                DispatchQueue.main.asyncAfter(deadline: .now() + 0.2) {
                                    isApplePressed = false
                                    
                                    // 调用Apple登录并传递userData实例
                                    AppleAuthService.shared.startSignInWithApple(userData: userData)
                                }
                            }) {
                                HStack {
                                    Image(systemName: "apple.logo")
                                        .resizable()
                                        .frame(width: 20, height: 24)
                                        .foregroundColor(.white)
                                        .padding(.trailing, 8)
                                        .padding(.vertical, 0) // 确保垂直对齐
                                    
                                    Text("Continue with Apple")
                                        .font(.system(size: 16, weight: .medium))
                                        .foregroundColor(.white)
                                }
                                .frame(maxWidth: .infinity)
                                .padding()
                                .background(isApplePressed ? Color.black.opacity(0.7) : .black)
                                .cornerRadius(10)
                            }
                            
                            // Google登录按钮
                            Button(action: {
                                isGooglePressed = true
                                
                                // 添加登录延迟模拟效果
                                DispatchQueue.main.asyncAfter(deadline: .now() + 0.2) {
                                    isGooglePressed = false
                                    
                                    // 调用Google登录并传递userData实例
                                    GoogleAuthService.shared.startSignInWithGoogle(userData: userData)
                                }
                            }) {
                                HStack {
                                    // Google官方SVG图标
                                    Image("google-logo")
                                        .resizable()
                                        .frame(width: 24, height: 24)
                                        .aspectRatio(contentMode: .fit)
                                        .padding(.trailing, 8)
                                        .padding(.vertical, 0) // 确保垂直对齐
                                    
                                    Text("Continue with Google")
                                        .font(.system(size: 16, weight: .medium))
                                        .foregroundColor(.primary)
                                }
                                .frame(maxWidth: .infinity)
                                .padding()
                                .background(isGooglePressed ? Color.gray.opacity(0.2) : Color.white)
                                .cornerRadius(10)
                                .overlay(
                                    RoundedRectangle(cornerRadius: 10)
                                        .stroke(Color.gray.opacity(0.3), lineWidth: 1)
                                )
                            }
                            
                            // 分隔线 - 屏幕自适应布局
                            HStack {
                                Rectangle()
                                    .frame(height: 1)
                                    .foregroundColor(.gray.opacity(0.3))
                                
                                Text("or")
                                    .font(.system(size: 14))
                                    .foregroundColor(.gray)
                                    .padding(.horizontal, 12)
                                    .fixedSize(horizontal: true, vertical: false) // 确保文本不会换行
                                
                                Rectangle()
                                    .frame(height: 1)
                                    .foregroundColor(.gray.opacity(0.3))
                            }
                            
                            // 邮箱输入框
                            VStack(spacing: 16) {
                                TextField("Email address", text: $email)
                                    .focused($focusedField, equals: .email)
                                    .padding()
                                    .background(Color(.systemGray6))
                                    .cornerRadius(8)
                                    .autocapitalization(.none)
                                    .keyboardType(.emailAddress)
                                    .textContentType(.emailAddress)
                                    .disableAutocorrection(true)
                                    .id("emailField")
                                    .onTapGesture {
                                        focusedField = .email
                                    }
                                
                                // 密码输入框
                                HStack {
                                    if isPasswordVisible {
                                        TextField("Password", text: $password)
                                            .focused($focusedField, equals: .password)
                                            .padding()
                                            .autocapitalization(.none)
                                            .textContentType(.password)
                                            .onTapGesture {
                                                focusedField = .password
                                            }
                                    } else {
                                        SecureField("Password", text: $password)
                                            .focused($focusedField, equals: .password)
                                            .padding()
                                            .autocapitalization(.none)
                                            .textContentType(.password)
                                            .onTapGesture {
                                                focusedField = .password
                                            }
                                    }
                                    
                                    // 显示/隐藏密码按钮
                                    Button(action: {
                                        isPasswordVisible.toggle()
                                    }) {
                                        Image(systemName: isPasswordVisible ? "eye.fill" : "eye.slash.fill")
                                            .foregroundColor(.gray)
                                            .padding(.trailing, 8)
                                    }
                                }
                                .background(Color(.systemGray6))
                                .cornerRadius(8)
                                .id("passwordField")
                                

                            }
                            
                            // 忘记密码按钮
                            HStack {
                                Spacer()
                                NavigationLink(destination: ResetPasswordView()) {
                                    Text("Forgot Password?")
                                        .font(.system(size: 14))
                                        .foregroundColor(isForgotPasswordPressed ? Color.green.opacity(0.7) : .green)
                                }
                                .simultaneousGesture(TapGesture().onEnded {
                                    isForgotPasswordPressed = true
                                    
                                    DispatchQueue.main.asyncAfter(deadline: .now() + 0.2) {
                                        isForgotPasswordPressed = false
                                    }
                                })
                            }
                            
                            // 登录按钮
                            Button(action: {
                                isSignInPressed = true
                                isLoading = true
                                
                                // 首先进行输入验证
                                if email.isEmpty || password.isEmpty {
                                    isLoading = false
                                    isSignInPressed = false
                                    alertTitle = "Input Error"
                                    alertMessage = "Email and password cannot be empty"
                                    showAlert = true
                                    return
                                }
                                
                                // 检查网络状态
                                if !networkManager.isNetworkAvailable {
                                    isLoading = false
                                    isSignInPressed = false
                                    alertTitle = "Network Error"
                                    alertMessage = "No internet connection available. Please check your network settings and try again."
                                    showAlert = true
                                    return
                                }
                                
                                // 调用登录API
                                NetworkService.shared.directLogin(
                                    email: email, 
                                    password: password
                                ) { result in
                                    self.isLoading = false
                                    self.isSignInPressed = false
                                    
                                    switch result {
                                    case .success(let loginData):
                                        // 登录成功，保存用户信息
                                        userData.saveLoginState(
                                            userId: loginData.userId,
                                            userNickname: loginData.userInfo?.nickname ?? loginData.userName ?? loginData.userInfo?.name ?? "User", // 优先使用nickname，其次是userName，再次是name
                                            email: self.email, // 使用用户输入的邮箱
                                            accessToken: loginData.accessToken,
                                            refreshToken: loginData.refreshToken,
                                            expiresIn: loginData.expiresIn
                                        )
                                        
                                        print("登录成功，用户名: \(loginData.userName ?? loginData.userInfo?.name ?? "未知用户名")")
                                        
                                        // 登录成功后，调用API获取用户完整信息
                                        if loginData.accessToken != nil {
                                            UserService.shared.updateUserDataWithFetchedInfo(userData: userData)
                                        }
                                        
                                        // 发送登录成功通知
                                        NotificationCenter.default.post(name: Notification.Name("UserLoggedIn"), object: nil)
                                        
                                    case .failure(let error):
                                        // 登录失败 - 改进错误处理逻辑
                                        self.alertTitle = "Login Failed"
                                        
                                        // 根据错误类型提供更友好的错误信息
                                        let nsError = error as NSError
                                        
                                        // 根据错误类型提供更友好的错误信息
                                        switch nsError.domain {
                                            case "AuthorizationError":
                                                self.alertMessage = "Invalid email or password. Please check your credentials and try again."
                                            case "APIError":
                                                self.alertMessage = "Login failed. Please verify your account information."
                                            default:
                                                if nsError.localizedDescription.contains("Forbidden") || 
                                                   nsError.localizedDescription.contains("禁止") ||
                                                   nsError.localizedDescription.contains("401") ||
                                                   nsError.localizedDescription.contains("unauthorized") {
                                                    self.alertMessage = "Invalid email or password. Please check your credentials and try again."
                                                } else if nsError.localizedDescription.contains("timeout") || 
                                                         nsError.localizedDescription.contains("timed out") ||
                                                         nsError.localizedDescription.contains("超时") {
                                                    self.alertMessage = "Connection timeout. Please check your network connection and try again."
                                                } else if nsError.localizedDescription.contains("Internet connection") ||
                                                          nsError.localizedDescription.contains("网络连接") ||
                                                          nsError.localizedDescription.contains("network") ||
                                                          nsError.code == NSURLErrorNotConnectedToInternet ||
                                                          nsError.code == NSURLErrorNetworkConnectionLost {
                                                    self.alertMessage = "No internet connection. Please check your network settings and ensure the app has network access permission."
                                                } else if nsError.localizedDescription.contains("password") ||
                                                          nsError.localizedDescription.contains("密码") ||
                                                          nsError.localizedDescription.contains("email") ||
                                                          nsError.localizedDescription.contains("邮箱") {
                                                    self.alertMessage = "Invalid email or password. Please check your credentials and try again."
                                                } else if nsError.localizedDescription.contains("account") ||
                                                          nsError.localizedDescription.contains("账号") ||
                                                          nsError.localizedDescription.contains("locked") ||
                                                          nsError.localizedDescription.contains("disabled") {
                                                    self.alertMessage = "Account issue detected. Your account may be locked or disabled. Please contact support."
                                                } else {
                                                    self.alertMessage = "Login failed. Please check your email and password, then try again."
                                                }
                                            }
                                        
                                        self.showAlert = true
                                        print("登录失败: \(error)")
                                    }
                                }
                            }) {
                                ZStack {
                                    Text("Sign In")
                                        .font(.system(size: 16, weight: .medium))
                                        .foregroundColor(.white)
                                        .opacity(isLoading ? 0 : 1)
                                    
                                    if isLoading {
                                        ProgressView()
                                            .progressViewStyle(CircularProgressViewStyle(tint: .white))
                                    }
                                }
                                .frame(maxWidth: .infinity)
                                .padding()
                                .background(isSignInPressed ? Color.green.opacity(0.7) : .green)
                                .cornerRadius(10)
                            }
                            
                            // Continue as Guest 按钮
                            Button(action: {
                                // 设置访客模式
                                userData.setGuestMode()
                                
                                // 发送访客登录成功通知（确保在主线程执行）
                                DispatchQueue.main.async {
                                    NotificationCenter.default.post(name: Notification.Name("GuestLoggedIn"), object: nil)
                                }
                            }) {
                                Text("Continue as Guest")
                                    .font(.system(size: 16, weight: .medium))
                                    .foregroundColor(.green)
                                    .frame(maxWidth: .infinity)
                                    .padding()
                                    .background(Color.white)
                                    .cornerRadius(10)
                                    .overlay(
                                        RoundedRectangle(cornerRadius: 10)
                                            .stroke(Color.green, lineWidth: 1)
                                    )
                            }
                            .padding(.top, 12)
                            
                            // 注册链接
                            HStack(spacing: 4) {
                                Text("Don't have an account?")
                                    .font(.system(size: 14))
                                    .foregroundColor(.secondary)
                                
                                NavigationLink(destination: SignUpView()) {
                                    Text("Sign Up")
                                        .font(.system(size: 14, weight: .medium))
                                        .foregroundColor(isSignUpPressed ? Color.green.opacity(0.7) : .green)
                                }
                                .simultaneousGesture(TapGesture().onEnded {
                                    isSignUpPressed = true
                                    
                                    DispatchQueue.main.asyncAfter(deadline: .now() + 0.2) {
                                        isSignUpPressed = false
                                    }
                                })
                            }
                            .padding(.top, 5)
                            
                            // 隐私政策和服务条款
                            VStack(spacing: 0) {
                                HStack(spacing: 4) {
                                    Text("By continuing, you agree to our")
                                        .font(.system(size: 12))
                                        .foregroundColor(.secondary)
                                    
                                    Button(action: {
                                        showTermsOfService = true
                                    }) {
                                        Text("Terms of Service")
                                            .font(.system(size: 12, weight: .medium))
                                            .foregroundColor(.green)
                                    }
                                }
                                
                                HStack(spacing: 4) {
                                    Text("and")
                                        .font(.system(size: 12))
                                        .foregroundColor(.secondary)
                                    
                                    Button(action: {
                                        showPrivacyPolicy = true
                                    }) {
                                        Text("Privacy Policy")
                                            .font(.system(size: 12, weight: .medium))
                                            .foregroundColor(.green)
                                    }
                                }
                            }
                            .padding(.top, 30)
                            .padding(.bottom, 50)
                        }
                        .padding(.horizontal, 24)
                    }
                    .onChange(of: focusedField) { oldValue, newValue in
                        // 当焦点改变时，自动滚动到对应的输入框
                        if let field = newValue {
                            withAnimation(.easeInOut(duration: 0.5)) {
                                switch field {
                                case .email:
                                    proxy.scrollTo("emailField", anchor: .center)
                                case .password:
                                    proxy.scrollTo("passwordField", anchor: .center)
                                }
                            }
                        }
                    }
                }
                .contentShape(Rectangle())
                .onTapGesture {
                    focusedField = nil
                    UIApplication.shared.sendAction(#selector(UIResponder.resignFirstResponder), to: nil, from: nil, for: nil)
                }
            }
            .alert(isPresented: $showAlert) {
                Alert(
                    title: Text(alertTitle),
                    message: Text(alertMessage),
                    dismissButton: .default(Text("OK"))
                )
            }
            .navigationBarHidden(true)
            .onAppear {
                // 统计
                XDTrackTool.shared.appear("登录页面")

                // 主动触发网络权限检查，确保用户在登录前就授权网络访问
                DispatchQueue.main.asyncAfter(deadline: .now() + 0.5) {
                    networkManager.triggerNetworkPermissionCheck()
                }
                
                // 键盘显示/隐藏监听
                NotificationCenter.default.addObserver(forName: UIResponder.keyboardWillShowNotification, object: nil, queue: .main) { notification in
                    if let keyboardSize = (notification.userInfo?[UIResponder.keyboardFrameEndUserInfoKey] as? NSValue)?.cgRectValue {
                        withAnimation(.easeOut(duration: 0.3)) {
                            self.keyboardHeight = keyboardSize.height
                        }
                    }
                }
                
                NotificationCenter.default.addObserver(forName: UIResponder.keyboardWillHideNotification, object: nil, queue: .main) { _ in
                    withAnimation(.easeOut(duration: 0.3)) {
                        self.keyboardHeight = 0
                    }
                }
            }
            .onDisappear {
                // 清理通知监听
                NotificationCenter.default.removeObserver(self, name: UIResponder.keyboardWillShowNotification, object: nil)
                NotificationCenter.default.removeObserver(self, name: UIResponder.keyboardWillHideNotification, object: nil)
            }
        }
        .sheet(isPresented: $showTermsOfService) {
            LoginWebPageView(url: URL(string: "https://littlegrass.cc/app/fitscanai/terms.html")!, title: "Terms of Service")
        }
        .sheet(isPresented: $showPrivacyPolicy) {
            LoginWebPageView(url: URL(string: "https://littlegrass.cc/app/fitscanai/privacy.html")!, title: "Privacy Policy")
        }
    }
    

}

struct LoginView_Previews: PreviewProvider {
    static var previews: some View {
        LoginView()
    }
}
