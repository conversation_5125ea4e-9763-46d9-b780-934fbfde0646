import SwiftUI

struct ResetPasswordView: View {
    @Environment(\.presentationMode) var presentationMode
    
    // 用户输入数据
    @State private var email: String = ""
    @State private var verificationCode: String = ""
    @State private var newPassword: String = ""
    @State private var confirmPassword: String = ""
    @State private var isNewPasswordVisible: Bool = false
    @State private var isConfirmPasswordVisible: Bool = false
    
    // 按钮状态
    @State private var isSendCodePressed: Bool = false
    @State private var isResetPasswordPressed: Bool = false
    @State private var isSignInPressed: Bool = false
    
    // 计时器相关
    @State private var countdownTime: Int = 60
    @State private var isCountingDown: Bool = false
    @State private var timer: Timer? = nil
    @State private var isSendingCode: Bool = false  // 防止重复点击
    
    // 密码要求验证
    @State private var hasEightChars: Bool = false
    @State private var hasNumbers: Bool = false
    @State private var hasUppercase: Bool = false
    @State private var hasLowercase: Bool = false
    @State private var hasSpecialChars: Bool = false
    
    // 加载状态和提示
    @State private var isLoading: Bool = false
    @State private var showAlert: Bool = false
    @State private var alertTitle: String = ""
    @State private var alertMessage: String = ""
    
    // 密码匹配状态
    @State private var passwordsMatch: Bool = false
    
    var body: some View {
        ZStack {
            Color.white.edgesIgnoringSafeArea(.all)
            
            ScrollView {
                VStack(alignment: .center, spacing: 24) {
                    // 标题部分
                    Text("Reset Password")
                        .font(.system(size: 24, weight: .bold))
                        .foregroundColor(.black)
                        .padding(.top, 16)
                    
                    // 锁图标
                    ZStack {
                        Circle()
                            .fill(Color.green.opacity(0.15))
                            .frame(width: 80, height: 80)
                        
                        Image(systemName: "lock.fill")
                            .resizable()
                            .aspectRatio(contentMode: .fit)
                            .frame(width: 32, height: 32)
                            .foregroundColor(.green)
                    }
                    .padding(.top, 8)
                    
                    // 忘记密码提示
                    VStack(spacing: 8) {
                        Text("Forgot your password?")
                            .font(.system(size: 24, weight: .bold))
                            .foregroundColor(.black)
                        
                        Text("Don't worry! Enter your email and we'll help you reset it.")
                            .font(.system(size: 16))
                            .foregroundColor(.secondary)
                            .multilineTextAlignment(.center)
                            .padding(.horizontal)
                    }
                    .padding(.bottom, 16)
                    
                    // 邮箱输入
                    VStack(alignment: .leading, spacing: 8) {
                        Text("Email Address")
                            .font(.system(size: 16, weight: .medium))
                            .foregroundColor(.black)
                        
                        HStack {
                            Image(systemName: "envelope")
                                .foregroundColor(.gray)
                                .padding(.leading, 12)
                            
                            TextField("Enter your email", text: $email)
                                .keyboardType(.emailAddress)
                                .autocapitalization(.none)
                                .padding(.vertical, 12)
                                .adaptiveKeyboard()
                        }
                        .background(Color(.systemGray6))
                        .cornerRadius(8)
                    }
                    .padding(.horizontal, 24)
                    
                    // 验证码输入
                    VStack(alignment: .leading, spacing: 8) {
                        Text("Email Verification Code")
                            .font(.system(size: 16, weight: .medium))
                            .foregroundColor(.black)
                        
                        HStack {
                            TextField("Enter verification code", text: $verificationCode)
                                .keyboardType(.numberPad)
                                .padding(.vertical, 12)
                                .padding(.leading, 12)
                                .adaptiveKeyboard()
                            
                            Spacer()
                            
                            Button(action: {
                                // 点击发送验证码按钮
                                if !isCountingDown && !isSendingCode {
                                    sendVerificationCode()
                                }
                            }) {
                                Text(isCountingDown ? "\(countdownTime)s" : (isSendingCode ? "Sending..." : "Send Code"))
                                    .font(.system(size: 16, weight: .medium))
                                    .foregroundColor(.white)
                                    .padding(.horizontal, 16)
                                    .padding(.vertical, 8)
                                    .background(isCountingDown ? Color.green.opacity(0.7) : Color.green)
                                    .cornerRadius(8)
                            }
                            .disabled(isCountingDown || email.isEmpty || isSendingCode)
                            .padding(.trailing, 8)
                        }
                        .background(Color(.systemGray6))
                        .cornerRadius(8)
                    }
                    .padding(.horizontal, 24)
                    
                    // 新密码输入
                    VStack(alignment: .leading, spacing: 8) {
                        Text("New Password")
                            .font(.system(size: 16, weight: .medium))
                            .foregroundColor(.black)
                        
                        HStack {
                            if isNewPasswordVisible {
                                TextField("Enter your password", text: $newPassword)
                                    .autocapitalization(.none)
                                    .onChange(of: newPassword) { _, newValue in
                                        validatePassword(newValue)
                                        checkPasswordsMatch()
                                    }
                                    .padding(.vertical, 12)
                                    .padding(.leading, 12)
                                    .adaptiveKeyboard()
                            } else {
                                SecureField("Enter your password", text: $newPassword)
                                    .autocapitalization(.none)
                                    .onChange(of: newPassword) { _, newValue in
                                        validatePassword(newValue)
                                        checkPasswordsMatch()
                                    }
                                    .padding(.vertical, 12)
                                    .padding(.leading, 12)
                                    .adaptiveKeyboard()
                            }
                            
                            Button(action: {
                                isNewPasswordVisible.toggle()
                            }) {
                                Image(systemName: isNewPasswordVisible ? "eye.fill" : "eye.slash.fill")
                                    .foregroundColor(.gray)
                            }
                            .padding(.trailing, 12)
                        }
                        .background(Color(.systemGray6))
                        .cornerRadius(8)
                    }
                    .padding(.horizontal, 24)
                    
                    // 密码强度指示器
                    VStack(alignment: .leading, spacing: 8) {
                        HStack(spacing: 4) {
                            ForEach(0..<5, id: \.self) { index in
                                Rectangle()
                                    .fill(passwordStrengthColor(for: index))
                                    .frame(height: 6)
                                    .cornerRadius(3)
                            }
                        }
                        
                        Text(passwordStrengthText)
                            .font(.system(size: 14, weight: .medium))
                            .foregroundColor(passwordStrengthTextColor)
                    }
                    .padding(.horizontal, 24)
                    
                    // 密码要求提示
                    VStack(alignment: .leading, spacing: 12) {
                        Text("Password Requirements")
                            .font(.system(size: 16, weight: .medium))
                            .foregroundColor(.black)
                            .padding(.bottom, 4)
                        
                        // 至少6个字符
                        HStack {
                            Image(systemName: hasEightChars ? "checkmark.circle.fill" : "xmark.circle")
                                .foregroundColor(hasEightChars ? .green : .gray)
                            
                            Text("At least 6 characters")
                                .font(.system(size: 14))
                                .foregroundColor(hasEightChars ? .green : .gray)
                            
                            Spacer()
                        }
                        
                        // 包含数字
                        HStack {
                            Image(systemName: hasNumbers ? "checkmark.circle.fill" : "xmark.circle")
                                .foregroundColor(hasNumbers ? .green : .gray)
                            
                            Text("Include numbers")
                                .font(.system(size: 14))
                                .foregroundColor(hasNumbers ? .green : .gray)
                            
                            Spacer()
                        }
                        
                        // 包含大写字母
                        HStack {
                            Image(systemName: hasUppercase ? "checkmark.circle.fill" : "xmark.circle")
                                .foregroundColor(hasUppercase ? .green : .gray)
                            
                            Text("Include uppercase letters")
                                .font(.system(size: 14))
                                .foregroundColor(hasUppercase ? .green : .gray)
                            
                            Spacer()
                        }
                        
                        // 包含小写字母
                        HStack {
                            Image(systemName: hasLowercase ? "checkmark.circle.fill" : "xmark.circle")
                                .foregroundColor(hasLowercase ? .green : .gray)
                            
                            Text("Include lowercase letters")
                                .font(.system(size: 14))
                                .foregroundColor(hasLowercase ? .green : .gray)
                            
                            Spacer()
                        }
                        
                        // 包含特殊字符
                        HStack {
                            Image(systemName: hasSpecialChars ? "checkmark.circle.fill" : "xmark.circle")
                                .foregroundColor(hasSpecialChars ? .green : .gray)
                            
                            Text("Include special characters")
                                .font(.system(size: 14))
                                .foregroundColor(hasSpecialChars ? .green : .gray)
                            
                            Spacer()
                        }
                    }
                    .padding()
                    .frame(maxWidth: .infinity)
                    .background(Color(.systemGray6).opacity(0.5))
                    .cornerRadius(8)
                    .padding(.horizontal, 24)
                    
                    // 确认密码
                    VStack(alignment: .leading, spacing: 8) {
                        Text("Confirm Password")
                            .font(.system(size: 16, weight: .medium))
                            .foregroundColor(.black)
                        
                        HStack {
                            if isConfirmPasswordVisible {
                                TextField("Re-enter your password", text: $confirmPassword)
                                    .autocapitalization(.none)
                                    .onChange(of: confirmPassword) { _, _ in
                                        checkPasswordsMatch()
                                    }
                                    .padding(.vertical, 12)
                                    .padding(.leading, 12)
                                    .adaptiveKeyboard()
                            } else {
                                SecureField("Re-enter your password", text: $confirmPassword)
                                    .autocapitalization(.none)
                                    .onChange(of: confirmPassword) { _, _ in
                                        checkPasswordsMatch()
                                    }
                                    .padding(.vertical, 12)
                                    .padding(.leading, 12)
                                    .adaptiveKeyboard()
                            }
                            
                            Button(action: {
                                isConfirmPasswordVisible.toggle()
                            }) {
                                Image(systemName: isConfirmPasswordVisible ? "eye.fill" : "eye.slash.fill")
                                    .foregroundColor(.gray)
                            }
                            .padding(.trailing, 12)
                        }
                        .background(Color(.systemGray6))
                        .cornerRadius(8)
                    }
                    .padding(.horizontal, 24)
                    
                    // 密码匹配提示
                    if !confirmPassword.isEmpty {
                        HStack {
                            Image(systemName: passwordsMatch ? "checkmark.circle.fill" : "xmark.circle.fill")
                                .foregroundColor(passwordsMatch ? .green : .red)
                            
                            Text(passwordsMatch ? "Passwords match" : "Passwords don't match")
                                .font(.system(size: 14))
                                .foregroundColor(passwordsMatch ? .green : .red)
                            
                            Spacer()
                        }
                        .padding(.horizontal, 24)
                    }
                    
                    // 重置密码按钮
                    Button(action: {
                        resetPassword()
                    }) {
                        ZStack {
                            Text("Reset Password")
                                .font(.system(size: 16, weight: .medium))
                                .foregroundColor(.white)
                                .opacity(isLoading ? 0 : 1)
                            
                            if isLoading {
                                ProgressView()
                                    .progressViewStyle(CircularProgressViewStyle(tint: .white))
                            }
                        }
                        .frame(maxWidth: .infinity)
                        .padding()
                        .background(isAllRequirementsMet() ? Color.green : Color.gray)
                        .cornerRadius(10)
                    }
                    .disabled(!isAllRequirementsMet() || isLoading)
                    .padding(.horizontal, 24)
                    .padding(.top, 16)
                    
                    // 返回登录
                    HStack(spacing: 4) {
                        Text("Remember your password?")
                            .font(.system(size: 14))
                            .foregroundColor(.secondary)
                        
                        Button(action: {
                            isSignInPressed = true
                            
                            DispatchQueue.main.asyncAfter(deadline: .now() + 0.2) {
                                isSignInPressed = false
                                presentationMode.wrappedValue.dismiss()
                            }
                        }) {
                            Text("Sign In")
                                .font(.system(size: 14, weight: .medium))
                                .foregroundColor(isSignInPressed ? Color.green.opacity(0.7) : .green)
                        }
                    }
                    .padding(.bottom, 32)
                }
                .frame(maxWidth: .infinity)
                .contentShape(Rectangle())
                .onTapGesture {
                    UIApplication.shared.sendAction(#selector(UIResponder.resignFirstResponder), to: nil, from: nil, for: nil)
                }
            }
        }
        .navigationBarBackButtonHidden(true)
        .navigationBarItems(leading: Button(action: {
            presentationMode.wrappedValue.dismiss()
        }) {
            Image(systemName: "chevron.left")
                .foregroundColor(.black)
        })
        .alert(isPresented: $showAlert) {
            Alert(
                title: Text(alertTitle),
                message: Text(alertMessage),
                dismissButton: .default(Text("OK"))
            )
        }
        .onAppear {
            // 统计
            XDTrackTool.shared.appear("重置密码页面")
        }
        .onDisappear {
            // 确保在视图消失时停止计时器
            stopTimer()
        }
    }
    
    // 验证密码是否符合要求
    private func validatePassword(_ password: String) {
        // 至少6个字符
        hasEightChars = password.count >= 6
        
        // 包含数字
        hasNumbers = password.contains(where: { $0.isNumber })
        
        // 包含大写字母
        hasUppercase = password.contains(where: { $0.isUppercase })
        
        // 包含小写字母
        hasLowercase = password.contains(where: { $0.isLowercase })
        
        // 包含特殊字符
        let specialCharacters = CharacterSet(charactersIn: "`!@#$%^&*?()-+_=;':\",./<>")
        hasSpecialChars = password.contains { char in
            guard let scalar = String(char).unicodeScalars.first else { return false }
            return specialCharacters.contains(scalar)
        }
    }
    
    // 检查两次输入的密码是否匹配
    private func checkPasswordsMatch() {
        passwordsMatch = !confirmPassword.isEmpty && newPassword == confirmPassword
    }
    
    // 检查是否所有密码要求都已满足
    private func isAllRequirementsMet() -> Bool {
        return !email.isEmpty && 
               !verificationCode.isEmpty &&
               hasEightChars && 
               hasNumbers && 
               hasUppercase && 
               hasLowercase &&
               hasSpecialChars &&
               passwordsMatch
    }
    
    // 发送验证码
    private func sendVerificationCode() {
        // 防止重复点击
        if isSendingCode {
            return
        }
        
        // 验证邮箱格式
        if !isValidEmail(email) {
            alertTitle = "Invalid Email"
            alertMessage = "Please enter a valid email address"
            showAlert = true
            return
        }
        
        isLoading = true
        isSendingCode = true
        isSendCodePressed = true
        
        DispatchQueue.main.asyncAfter(deadline: .now() + 0.2) {
            self.isSendCodePressed = false
        }
        
        // 在发送验证码前强制重置会话状态，确保获取新的CSRF token和JSESSIONID
        // 这对于退出登录后的忘记密码功能非常重要
        print("🔄 忘记密码页面发送验证码前重置会话状态")
        print("📧 准备为邮箱发送忘记密码验证码: \(self.email)")
        
        // 先重置会话状态，等待完成后再发送验证码
        NetworkService.shared.resetSessionState { success in
            if success {
                print("✅ 会话状态重置完成，开始发送忘记密码验证码")
            } else {
                print("⚠️ 会话状态重置失败，仍尝试发送忘记密码验证码")
            }
            
            // 调用忘记密码验证码API
            NetworkService.shared.sendForgotPasswordCode(email: self.email) { result in
            self.isLoading = false
            self.isSendingCode = false
            
            switch result {
            case .success(let response):
                print("忘记密码验证码发送成功: \(response)")
                
                // 开始倒计时
                self.startCountdown()
                
            case .failure(let error):
                // 处理错误
                self.alertTitle = "Verification Code Error"
                
                let nsError = error as NSError
                
                if nsError.localizedDescription.contains("不存在") || nsError.localizedDescription.contains("not exist") {
                    self.alertMessage = "This email is not registered. Please check the email address."
                } else if nsError.localizedDescription.contains("频繁") || nsError.localizedDescription.contains("frequent") {
                    self.alertMessage = "You're requesting codes too frequently. Please try again later."
                } else if nsError.localizedDescription.contains("格式") {
                    self.alertMessage = "Invalid email format. Please check and try again."
                } else if nsError.code == 429 {
                    self.alertMessage = "Too many attempts. Please try again later."
                } else if nsError.code == 400 {
                    self.alertMessage = "Invalid request. Please check your information and try again."
                } else if nsError.localizedDescription.contains("network") || nsError.localizedDescription.contains("网络") {
                    self.alertMessage = "Network connection error. Please check your internet connection."
                } else if nsError.localizedDescription.contains("timeout") || nsError.localizedDescription.contains("超时") {
                    self.alertMessage = "Request timeout. Please try again later."
                } else if nsError.localizedDescription.contains("server") || nsError.localizedDescription.contains("服务器") {
                    self.alertMessage = "Server error. Please try again later."
                } else {
                    self.alertMessage = "Failed to send verification code. Please try again later."
                }
                
                self.showAlert = true
                print("忘记密码验证码发送失败: \(error)")
            }
        }
        }
    }
    
    // 开始倒计时
    private func startCountdown() {
        isCountingDown = true
        countdownTime = 60
        
        timer = Timer.scheduledTimer(withTimeInterval: 1, repeats: true) { _ in
            if countdownTime > 0 {
                countdownTime -= 1
            } else {
                stopTimer()
                isCountingDown = false
            }
        }
    }
    
    // 停止计时器
    private func stopTimer() {
        timer?.invalidate()
        timer = nil
    }
    
    // 重置密码
    private func resetPassword() {
        // 验证输入
        if email.isEmpty || verificationCode.isEmpty || newPassword.isEmpty || confirmPassword.isEmpty {
            alertTitle = "Input Error"
            alertMessage = "Please fill in all fields"
            showAlert = true
            return
        }
        
        // 验证邮箱格式
        if !isValidEmail(email) {
            alertTitle = "Invalid Email"
            alertMessage = "Please enter a valid email address"
            showAlert = true
            return
        }
        
        // 验证密码是否满足所有要求
        if !hasEightChars || !hasNumbers || !hasUppercase || !hasLowercase || !hasSpecialChars {
            alertTitle = "Weak Password"
            alertMessage = "Your password does not meet all requirements. Please choose a stronger password."
            showAlert = true
            return
        }
        
        // 验证两次密码输入是否一致
        if !passwordsMatch {
            alertTitle = "Password Mismatch"
            alertMessage = "The passwords you entered do not match. Please try again."
            showAlert = true
            return
        }
        
        // 开始加载
        isLoading = true
        isResetPasswordPressed = true
        
        DispatchQueue.main.asyncAfter(deadline: .now() + 0.2) {
            self.isResetPasswordPressed = false
        }
        
        // 调用API重置密码
        NetworkService.shared.resetForgotPassword(email: email, code: verificationCode, newPassword: newPassword) { result in
            self.isLoading = false
            
            switch result {
            case .success:
                // 重置成功
                self.alertTitle = "Password Reset Successful"
                self.alertMessage = "Your password has been reset successfully. You can now log in with your new password."
                self.showAlert = true
                
                // 3秒后返回登录页面
                DispatchQueue.main.asyncAfter(deadline: .now() + 3) {
                    self.presentationMode.wrappedValue.dismiss()
                }
                
            case .failure(let error):
                // 处理错误
                self.alertTitle = "Password Reset Failed"
                
                let nsError = error as NSError
                
                if nsError.localizedDescription.contains("验证码") || nsError.localizedDescription.contains("verification code") {
                    self.alertMessage = "Invalid verification code. Please check and try again."
                } else if nsError.localizedDescription.contains("过期") || nsError.localizedDescription.contains("expired") {
                    self.alertMessage = "Verification code has expired. Please request a new code."
                } else if nsError.localizedDescription.contains("密码") || nsError.localizedDescription.contains("password") {
                    if nsError.localizedDescription.contains("弱") || nsError.localizedDescription.contains("weak") {
                        self.alertMessage = "Please use a stronger password."
                    } else if nsError.localizedDescription.contains("不匹配") || nsError.localizedDescription.contains("match") {
                        self.alertMessage = "Password confirmation doesn't match."
                    } else {
                        self.alertMessage = "Password doesn't meet the security requirements."
                    }
                } else if nsError.code == 400 {
                    self.alertMessage = "Invalid request. Please check your information and try again."
                } else if nsError.code == 401 || nsError.code == 403 {
                    self.alertMessage = "Authorization error. Please try again later."
                } else if nsError.code == 404 {
                    self.alertMessage = "User not found. This email is not registered."
                } else if nsError.code == 429 {
                    self.alertMessage = "Too many attempts. Please try again later."
                } else if nsError.localizedDescription.contains("network") || nsError.localizedDescription.contains("网络") {
                    self.alertMessage = "Network connection error. Please check your internet connection."
                } else if nsError.localizedDescription.contains("timeout") || nsError.localizedDescription.contains("超时") {
                    self.alertMessage = "Request timeout. Please try again later."
                } else if nsError.localizedDescription.contains("server") || nsError.localizedDescription.contains("服务器") {
                    self.alertMessage = "Server error. Please try again later."
                } else {
                    self.alertMessage = "Password reset failed. Please try again later."
                }
                
                self.showAlert = true
                print("重置密码失败: \(error)")
            }
        }
    }
    
    // 验证邮箱格式
    private func isValidEmail(_ email: String) -> Bool {
        let emailRegEx = "[A-Z0-9a-z._%+-]+@[A-Za-z0-9.-]+\\.[A-Za-z]{2,64}"
        let emailPred = NSPredicate(format:"SELF MATCHES %@", emailRegEx)
        return emailPred.evaluate(with: email)
    }
    
    // 计算密码强度颜色
    private func passwordStrengthColor(for index: Int) -> Color {
        let metRequirements = [hasEightChars, hasNumbers, hasUppercase, hasLowercase, hasSpecialChars].filter { $0 }.count
        
        if index < metRequirements {
            switch metRequirements {
            case 1:
                return .red
            case 2, 3:
                return .orange
            case 4:
                return .yellow
            case 5:
                return .green
            default:
                return Color(.systemGray5)
            }
        } else {
            return Color(.systemGray5)
        }
    }
    
    // 密码强度文本
    private var passwordStrengthText: String {
        let metRequirements = [hasEightChars, hasNumbers, hasUppercase, hasLowercase, hasSpecialChars].filter { $0 }.count
        
        switch metRequirements {
        case 0:
            return ""
        case 1:
            return "Weak password"
        case 2, 3:
            return "Fair password"
        case 4:
            return "Good password"
        case 5:
            return "Strong password"
        default:
            return ""
        }
    }
    
    // 密码强度文本颜色
    private var passwordStrengthTextColor: Color {
        let metRequirements = [hasEightChars, hasNumbers, hasUppercase, hasLowercase, hasSpecialChars].filter { $0 }.count
        
        switch metRequirements {
        case 1:
            return .red
        case 2, 3:
            return .orange
        case 4:
            return .yellow
        case 5:
            return .green
        default:
            return .clear
        }
    }
}

struct ResetPasswordView_Previews: PreviewProvider {
    static var previews: some View {
        ResetPasswordView()
    }
} 