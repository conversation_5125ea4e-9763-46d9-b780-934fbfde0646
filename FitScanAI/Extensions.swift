//
//  Extensions.swift
//  NutriScan
//
//  Created by 陈亚宁 on 2025/4/9.
//

import Foundation
import SwiftUI
import UIKit
import ObjectiveC

// 文本输入限制扩展
extension String {
    func limitedTo999_99() -> String {
        let filtered = self.filter { "0123456789.".contains($0) }
        
        // 如果包含小数点
        if filtered.contains(".") {
            let parts = filtered.components(separatedBy: ".")
            if parts.count > 2 {
                // 多个小数点，只保留第一个
                return parts[0] + "." + parts[1]
            }
            
            let integerPart = parts[0]
            let decimalPart = parts.count > 1 ? parts[1] : ""
            
            // 限制整数部分最多3位
            let limitedInteger = String(integerPart.prefix(3))
            
            // 限制小数部分最多2位
            let limitedDecimal = String(decimalPart.prefix(2))
            
            // 检查整数部分是否超过999
            if let intValue = Int(limitedInteger), intValue > 999 {
                return "999.99"
            }
            
            // 如果整数部分是999，并且小数部分超过99，则限制为999.99
            if let intValue = Int(limitedInteger), intValue == 999 {
                if let decValue = Int(limitedDecimal), decValue > 99 {
                    return "999.99"
                }
            }
            
            // 构建结果，确保小数点后有内容时才显示小数点
            if decimalPart.isEmpty && filtered.hasSuffix(".") {
                // 用户刚输入小数点，保留小数点
                return limitedInteger + "."
            } else if limitedDecimal.isEmpty {
                // 没有小数部分，不显示小数点
                return limitedInteger
            } else {
                // 有小数部分，显示完整数字
                return limitedInteger + "." + limitedDecimal
            }
        } else {
            // 没有小数点，限制最多3位
            let limitedInteger = String(filtered.prefix(3))
            
            // 检查是否超过999
            if let intValue = Int(limitedInteger), intValue > 999 {
                return "999"
            }
            
            return limitedInteger
        }
    }
}

// 扩展View，添加隐藏键盘的功能
extension View {
    func hideKeyboard() {
        UIApplication.shared.sendAction(#selector(UIResponder.resignFirstResponder), to: nil, from: nil, for: nil)
    }
    
    // 修改键盘行为，防止约束冲突
    func adaptiveKeyboard() -> some View {
        return self
            .onAppear {
                // 修复键盘相关约束
                fixKeyboardConstraints()
            }
    }
}

// 修复键盘约束相关问题
private func fixKeyboardConstraints() {
    DispatchQueue.main.async {
        // 获取活动窗口场景
        if #available(iOS 15.0, *) {
            // 使用UIWindowScene.windows
            guard let scene = UIApplication.shared.connectedScenes.first as? UIWindowScene,
                  let keyWindow = scene.windows.first(where: { $0.isKeyWindow }) else { 
                return 
            }
            findAndFixConstraints(in: keyWindow)
        } else {
            // 使用旧方法兼容iOS 15.0以下版本
            if let keyWindow = UIApplication.shared.windows.first(where: { $0.isKeyWindow }) {
                findAndFixConstraints(in: keyWindow)
            }
        }
    }
}

// 递归查找并修复输入助手视图的约束
private func findAndFixConstraints(in view: UIView) {
    // 通过类名检查视图
    let className = NSStringFromClass(type(of: view))
    
    // 寻找与约束冲突相关的视图
    if className.contains("SystemInputAssistant") {
        // 查找并移除可能引起冲突的约束
        var constraintToRemove: NSLayoutConstraint? = nil
        
        for constraint in view.constraints {
            if constraint.identifier == "assistantHeight" || 
               (constraint.firstAttribute == .height && constraint.relation == .equal) {
                constraintToRemove = constraint
                break
            }
        }
        
        // 移除冲突约束
        if let constraint = constraintToRemove {
            view.removeConstraint(constraint)
            
            // 添加自定义约束
            let newHeightConstraint = NSLayoutConstraint(
                item: view,
                attribute: .height,
                relatedBy: .equal,
                toItem: nil,
                attribute: .notAnAttribute,
                multiplier: 1.0,
                constant: 0
            )
            newHeightConstraint.priority = .required
            newHeightConstraint.identifier = "customHeight"
            view.addConstraint(newHeightConstraint)
        }
    }
    
    // 递归处理所有子视图
    for subview in view.subviews {
        findAndFixConstraints(in: subview)
    }
}

// 禁用输入助手栏的扩展
@available(iOS 15.0, *)
extension UITextInputAssistantItem {
    static var allowsHidingShortcuts: Bool {
        get {
            return objc_getAssociatedObject(UITextInputAssistantItem.self, UnsafeRawPointer(bitPattern: ObjectIdentifier(AssociatedKeys.self).hashValue)!) as? Bool ?? false
        }
        set {
            objc_setAssociatedObject(UITextInputAssistantItem.self, UnsafeRawPointer(bitPattern: ObjectIdentifier(AssociatedKeys.self).hashValue)!, newValue, .OBJC_ASSOCIATION_RETAIN_NONATOMIC)
            
            // 当设置为true时，尝试修复SystemInputAssistantView的高度约束
            if newValue {
                // 寻找并修复SystemInputAssistantView的约束
                DispatchQueue.main.async {
                    if #available(iOS 15.0, *) {
                        // 使用UIWindowScene.windows
                        for scene in UIApplication.shared.connectedScenes {
                            if let windowScene = scene as? UIWindowScene {
                                for window in windowScene.windows {
                                    fixInputAssistantConstraints(in: window)
                                }
                            }
                        }
                    } else {
                        // 兼容iOS 15以下版本
                        for window in UIApplication.shared.windows {
                            fixInputAssistantConstraints(in: window)
                        }
                    }
                }
            }
        }
    }
}

private struct AssociatedKeys {
    static var allowsHidingShortcuts = "allowsHidingShortcuts"
}

// 递归遍历视图层次结构，查找并修复输入助手视图的约束
private func fixInputAssistantConstraints(in view: UIView) {
    // 检查当前视图是否是SystemInputAssistantView
    let className = NSStringFromClass(type(of: view))
    if className.contains("SystemInputAssistantView") {
        // 找到"assistantHeight"约束并移除
        for constraint in view.constraints {
            if constraint.identifier == "assistantHeight" {
                view.removeConstraint(constraint)
                
                // 添加一个新的高度约束，设置为0以防止冲突
                let newConstraint = NSLayoutConstraint(
                    item: view,
                    attribute: .height,
                    relatedBy: .equal,
                    toItem: nil,
                    attribute: .notAnAttribute,
                    multiplier: 1.0,
                    constant: 0
                )
                newConstraint.identifier = "fixedAssistantHeight"
                newConstraint.priority = .defaultHigh
                view.addConstraint(newConstraint)
                break
            }
        }
    }
    
    // 递归检查子视图
    for subview in view.subviews {
        fixInputAssistantConstraints(in: subview)
    }
}