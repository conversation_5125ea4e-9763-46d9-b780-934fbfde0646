import Foundation
import UIKit

/**
 * AvatarUploadService - 用户头像上传服务
 * 
 * 这个服务负责处理用户头像的上传功能，使用multipart/form-data格式
 * 将用户选择的头像图片上传到服务器，并处理返回的结果。
 * 
 * 主要功能:
 * 1. 将UIImage转换为JPEG数据
 * 2. 创建和配置multipart/form-data格式的HTTP请求
 * 3. 添加认证token到请求头
 * 4. 处理服务器响应，解析返回的URL
 * 5. 更新用户数据中的头像
 * 
 * API接口:
 * - URL: https://us-file-test.techrightcloud.cn/file-api/upload
 * - 请求方法: POST
 * - 内容类型: multipart/form-data
 * - 认证方式: Bearer token
 * - 参数: file (文件数据)
 */
class AvatarUploadService {
    // 单例模式
    static let shared = AvatarUploadService()
    
    private init() {}
    
    // 上传头像的方法
    func uploadAvatar(image: UIImage, authToken: String, completion: @escaping (Result<[String: Any], Error>) -> Void) {
        // 将图片转换为Data，压缩质量为0.7
        guard let imageData = image.jpegData(compressionQuality: 0.7) else {
            completion(.failure(NSError(domain: "AvatarUploadError", code: 0, userInfo: [NSLocalizedDescriptionKey: "无法将图片转换为数据"])))
            return
        }
        
        print("图片转换完成，大小: \(imageData.count) 字节")
        
        // 打印认证令牌信息（只显示前10个字符，保护安全）
        let tokenPreview = authToken.isEmpty ? "空" : 
            (authToken.count > 10 ? "\(authToken.prefix(10))..." : authToken)
        print("上传头像使用的认证令牌: \(tokenPreview), 长度: \(authToken.count)")
        
        // 检查认证令牌是否为空
        if authToken.isEmpty {
            print("警告: 认证令牌为空，API请求可能会失败")
        }
        
        // 创建URL - 根据接口文档使用完整URL
        // 注意: 基础URL应该由后端开发提供，这里我们尝试使用接口文档中的路径
        let baseUrlString = "https://fsai.pickgoodspro.com/file-api"
        let pathString = "/mall/fs-upload"
        let fullUrlString = baseUrlString + pathString
        
        guard let url = URL(string: fullUrlString) else {
            print("错误: 无效的URL - \(fullUrlString)")
            completion(.failure(NSError(domain: "AvatarUploadError", code: 1, userInfo: [NSLocalizedDescriptionKey: "无效的URL"])))
            return
        }
        
        print("准备上传头像到URL: \(url.absoluteString)")
        
        // 创建边界字符串
        let boundary = "Boundary-\(UUID().uuidString)"
        print("使用边界字符串: \(boundary)")
        
        // 创建请求
        var request = URLRequest(url: url)
        request.httpMethod = "POST"
        request.setValue("multipart/form-data; boundary=\(boundary)", forHTTPHeaderField: "Content-Type")
        
        // 添加认证token - 确保格式正确
        let authHeaderValue = "Bearer \(authToken)"
        request.setValue(authHeaderValue, forHTTPHeaderField: "Authorization")
        
        // 验证Authorization头格式
        print("Authorization头格式检查: \(authHeaderValue.hasPrefix("Bearer ") ? "正确" : "错误")")
        print("Authorization头格式: Bearer + [token]")
        
        // 打印所有请求头，用于调试
        print("请求头:")
        request.allHTTPHeaderFields?.forEach { key, value in
            // 如果是Authorization头，只显示部分内容
            if key == "Authorization" {
                print("  \(key): Bearer ***令牌已隐藏***")
            } else {
                print("  \(key): \(value)")
            }
        }
        
        // 构建multipart表单数据
        let httpBody = NSMutableData()
        
        // 根据接口文档，文件参数名称应为"file"
        print("构建multipart表单数据，文件字段名: file")
        
        // 添加文件数据
        let fileData = convertFileData(fieldName: "file", 
                                      fileName: "avatar.jpg", 
                                      mimeType: "image/jpeg", 
                                      fileData: imageData, 
                                      using: boundary)
        httpBody.append(fileData)
        print("文件数据大小: \(fileData.count) 字节")
        
        // 添加结束边界
        let endBoundaryData = "--\(boundary)--".data(using: .utf8)!
        httpBody.append(endBoundaryData)
        print("添加结束边界: \(endBoundaryData.count) 字节")
        
        // 设置请求体
        request.httpBody = httpBody as Data
        
        print("最终请求体大小: \(httpBody.length) 字节")
        
        // 为调试目的，打印请求体的前100字节(如果有)
        if httpBody.length > 0 {
            let previewLength = min(100, httpBody.length)
            let previewData = httpBody.subdata(with: NSRange(location: 0, length: previewLength))
            if let previewString = String(data: previewData, encoding: .utf8) {
                print("请求体前100字节预览: \(previewString)")
            } else {
                print("请求体前100字节预览: [二进制数据]")
            }
        }
        
        // 创建URLSession任务
        let task = URLSession.shared.dataTask(with: request) { data, response, error in
            // 处理错误
            if let error = error {
                print("网络请求错误: \(error.localizedDescription)")
                DispatchQueue.main.async {
                    completion(.failure(error))
                }
                return
            }
            
            // 检查响应状态码
            if let httpResponse = response as? HTTPURLResponse {
                print("头像上传API HTTP状态码: \(httpResponse.statusCode)")
                
                // 打印响应头，用于调试
                print("响应头:")
                httpResponse.allHeaderFields.forEach { key, value in
                    print("  \(key): \(value)")
                }
                
                if !(200...299).contains(httpResponse.statusCode) {
                    let errorMessage = "Server returned error code: \(httpResponse.statusCode)"
                    print("\(errorMessage)")
                    
                    // 对于401错误，给出更详细的提示
                    if httpResponse.statusCode == 401 {
                        print("401 error: Unauthorized. Please check if the authentication token is valid and properly formatted.")
                    }
                    
                    DispatchQueue.main.async {
                        completion(.failure(NSError(domain: "AvatarUploadError", code: httpResponse.statusCode, userInfo: [NSLocalizedDescriptionKey: errorMessage])))
                    }
                    return
                }
            }
            
            // 解析响应数据
            guard let data = data else {
                print("No data returned")
                DispatchQueue.main.async {
                    completion(.failure(NSError(domain: "AvatarUploadError", code: 3, userInfo: [NSLocalizedDescriptionKey: "No data returned"])))
                }
                return
            }
            
            // 打印响应数据
            if let responseString = String(data: data, encoding: .utf8) {
                print("响应数据: \(responseString)")
            }
            
            do {
                // 尝试解析返回的JSON数据
                if let json = try JSONSerialization.jsonObject(with: data, options: []) as? [String: Any] {
                    print("解析JSON成功: \(json)")
                    
                    // 提取所有响应数据
                    var responseParams: [String: Any] = [
                        "type": "image/jpeg",  // 默认值
                        "filename": "avatar.jpg",  // 默认值
                        "originName": "avatar.jpg"  // 默认值
                    ]
                    
                    // 根据接口文档的返回格式尝试不同的解析方式
                    if let status = json["status"] as? Int,
                       status == 200,
                       let result = json["result"] as? [String: Any] {
                        // 将响应中的所有参数合并到responseParams
                        for (key, value) in result {
                            responseParams[key] = value
                        }
                        
                        print("成功提取服务器返回的所有参数")
                        // 确保路径存在
                        if let url = result["url"] as? String {
                            responseParams["path"] = url
                            print("设置path参数: \(url)")
                        }
                    } else {
                        // 尝试直接使用响应的顶级参数
                        for (key, value) in json {
                            responseParams[key] = value
                        }
                        
                        // 如果有location字段，用它作为path
                        if let location = json["location"] as? String {
                            responseParams["path"] = location
                            print("使用location字段作为path参数: \(location)")
                        }
                    }
                    
                    // 检查是否有path字段
                    if let path = responseParams["path"] as? String, !path.isEmpty {
                        print("成功获取头像URL: \(path)")
                        DispatchQueue.main.async {
                            completion(.success(responseParams))
                        }
                    } else {
                        // 没有找到有效的URL
                        let responseString = String(data: data, encoding: .utf8) ?? "Unable to parse response data"
                        print("No valid URL found in server response: \(responseString)")
                        DispatchQueue.main.async {
                            completion(.failure(NSError(domain: "AvatarUploadError", code: 4, userInfo: [NSLocalizedDescriptionKey: "No valid URL found in server response: \(responseString)"])))
                        }
                    }
                } else {
                    let responseString = String(data: data, encoding: .utf8) ?? "Unable to parse response data"
                    print("Unable to parse as JSON: \(responseString)")
                    DispatchQueue.main.async {
                        completion(.failure(NSError(domain: "AvatarUploadError", code: 5, userInfo: [NSLocalizedDescriptionKey: "Invalid JSON response: \(responseString)"])))
                    }
                }
            } catch {
                // JSON解析异常
                print("JSON解析异常: \(error.localizedDescription)")
                DispatchQueue.main.async {
                    completion(.failure(error))
                }
            }
        }
        
        // 启动任务
        print("开始上传头像...")
        task.resume()
    }
    
    // 将文件数据转换为multipart格式
    private func convertFileData(fieldName: String, fileName: String, mimeType: String, fileData: Data, using boundary: String) -> Data {
        print("构建文件数据部分 - 字段名: \(fieldName), 文件名: \(fileName), MIME类型: \(mimeType)")
        
        let data = NSMutableData()
        
        // 添加边界和Content-Disposition
        let boundaryPrefix = "--\(boundary)\r\n"
        data.append(boundaryPrefix.data(using: .utf8)!)
        print("添加边界: \(boundaryPrefix)")
        
        // 添加Content-Disposition头
        let disposition = "Content-Disposition: form-data; name=\"\(fieldName)\"; filename=\"\(fileName)\"\r\n"
        data.append(disposition.data(using: .utf8)!)
        print("添加Content-Disposition: \(disposition)")
        
        // 添加Content-Type头
        let contentType = "Content-Type: \(mimeType)\r\n\r\n"
        data.append(contentType.data(using: .utf8)!)
        print("添加Content-Type: \(contentType)")
        
        // 添加文件数据
        data.append(fileData)
        print("添加文件数据: \(fileData.count) 字节")
        
        // 添加结尾的换行符
        data.append("\r\n".data(using: .utf8)!)
        
        return data as Data
    }
    
    // 保存头像URL到用户数据
    func saveAvatarUrl(url: String, userData: UserData) {
        // 保存URL到UserData
        // 如果需要，这里可以更新用户数据中的头像URL
        
        print("尝试保存和下载头像URL: \(url)")
        
        // 检查URL是否有效
        if URL(string: url) == nil {
            print("❌ 下载用户头像失败: unsupported URL: \(url)")
            
            // 尝试进行URL转义
            if let escapedUrl = url.addingPercentEncoding(withAllowedCharacters: .urlQueryAllowed),
               let imageUrl = URL(string: escapedUrl) {
                print("✅ URL转义成功: \(escapedUrl)")
                
                URLSession.shared.dataTask(with: imageUrl) { data, response, error in
                    if let error = error {
                        print("❌ 下载用户头像失败(使用转义URL): \(error.localizedDescription)")
                        return
                    }
                    
                    if let data = data, let image = UIImage(data: data) {
                        DispatchQueue.main.async {
                            // 通过通知更新头像，不保存到UserDefaults
                            NotificationCenter.default.post(
                                name: Notification.Name("UserAvatarUpdated"),
                                object: nil,
                                userInfo: ["avatarImage": image]
                            )
                            print("✅ 成功下载并设置用户头像(使用转义URL)")
                        }
                    } else {
                        print("❌ 下载的头像数据无效或无法转换为图像")
                    }
                }.resume()
            } else {
                print("❌ URL转义后仍然无效: \(url)")
            }
        } else if let imageUrl = URL(string: url) {
            print("开始下载头像: \(imageUrl.absoluteString)")
            
            URLSession.shared.dataTask(with: imageUrl) { data, response, error in
                if let error = error {
                    print("❌ 下载用户头像失败: \(error.localizedDescription)")
                    return
                }
                
                if let data = data, let image = UIImage(data: data) {
                    DispatchQueue.main.async {
                        // 通过通知更新头像，不保存到UserDefaults
                        NotificationCenter.default.post(
                            name: Notification.Name("UserAvatarUpdated"),
                            object: nil,
                            userInfo: ["avatarImage": image]
                        )
                        print("✅ 成功下载并设置用户头像")
                    }
                } else {
                    print("❌ 下载的头像数据无效或无法转换为图像")
                }
            }.resume()
        }
    }
    
    // 将头像URL提交到用户资料API
    func submitAvatarToProfile(params: [String: Any], accessToken: String, completion: @escaping (Result<Bool, Error>) -> Void) {
        // 创建请求URL
        let urlString = "https://fsai.pickgoodspro.com/auth-resource-api/ns/oauth/avatar"
        guard let url = URL(string: urlString) else {
            print("❌ 无效的头像更新API URL")
            completion(.failure(NSError(domain: "AvatarUploadError", code: 1001, userInfo: [NSLocalizedDescriptionKey: "无效的头像更新API URL"])))
            return
        }
        
        // 创建请求
        var request = URLRequest(url: url, timeoutInterval: 30.0)
        request.httpMethod = "POST"
        request.addValue("application/json", forHTTPHeaderField: "Content-Type")
        request.addValue("Bearer \(accessToken)", forHTTPHeaderField: "Authorization")
        
        // 确保必要的字段存在
        var requestParams = params
        
        // 如果没有提供context字段，添加默认值
        if requestParams["context"] == nil || (requestParams["context"] as? String)?.isEmpty == true {
            requestParams["context"] = "user_avatar" 
        }
        
        // 如果没有type字段，设置默认值
        if requestParams["type"] == nil || (requestParams["type"] as? String)?.isEmpty == true {
            requestParams["type"] = "image/jpeg"
        }
        
        // 确保有合适的filename和originName
        if requestParams["filename"] == nil || (requestParams["filename"] as? String)?.isEmpty == true {
            requestParams["filename"] = "avatar.jpg"
        }
        
        if requestParams["originName"] == nil || (requestParams["originName"] as? String)?.isEmpty == true {
            requestParams["originName"] = "avatar.jpg"
        }
        
        do {
            // 转换为JSON数据
            let jsonData = try JSONSerialization.data(withJSONObject: requestParams)
            request.httpBody = jsonData
            
            // 打印请求信息
            print("🌐 提交头像URL到用户资料API [POST] \(urlString)")
            print("🔑 请求头: \(request.allHTTPHeaderFields ?? [:])")
            if let bodyString = String(data: jsonData, encoding: .utf8) {
                print("📦 请求体: \(bodyString)")
            }
            
            // 发送请求
            let task = URLSession.shared.dataTask(with: request) { data, response, error in
                // 检查是否有错误
                if let error = error {
                    print("❌ 提交头像URL失败: \(error.localizedDescription)")
                    DispatchQueue.main.async {
                        completion(.failure(error))
                    }
                    return
                }
                
                // 检查HTTP响应状态码
                if let httpResponse = response as? HTTPURLResponse {
                    print("📥 提交头像URL API状态码: \(httpResponse.statusCode)")
                    
                    // 打印响应数据
                    if let data = data, let responseString = String(data: data, encoding: .utf8) {
                        print("📄 提交头像URL响应内容: \(responseString)")
                    }
                    
                    if (200...299).contains(httpResponse.statusCode) {
                        // 成功提交头像URL
                        DispatchQueue.main.async {
                            print("✅ 成功提交头像URL到用户资料API")
                            completion(.success(true))
                        }
                    } else {
                        // 提交头像URL失败
                        var errorMessage = "服务器返回错误：状态码 \(httpResponse.statusCode)"
                        
                        if let data = data, let responseString = String(data: data, encoding: .utf8) {
                            errorMessage += ", 响应内容: \(responseString)"
                            print("❌ 提交头像URL API错误响应: \(responseString)")
                        }
                        
                        let error = NSError(domain: "AvatarUploadError", code: httpResponse.statusCode, userInfo: [NSLocalizedDescriptionKey: errorMessage])
                        DispatchQueue.main.async {
                            completion(.failure(error))
                        }
                    }
                } else {
                    let error = NSError(domain: "AvatarUploadError", code: 1002, userInfo: [NSLocalizedDescriptionKey: "无法获取HTTP响应"])
                    DispatchQueue.main.async {
                        completion(.failure(error))
                    }
                }
            }
            
            // 启动任务
            task.resume()
            
        } catch {
            print("❌ 创建JSON请求体失败: \(error.localizedDescription)")
            completion(.failure(error))
        }
    }
}

// 添加用于更新用户头像的功能
extension AvatarUploadService {
    // 通过调用此方法，可以在用户点击保存按钮时上传头像
    func updateUserAvatar(image: UIImage, userData: UserData, completion: @escaping (Bool, String) -> Void) {
        // 详细检查accessToken
        let token = userData.accessToken.trimmingCharacters(in: .whitespacesAndNewlines)
        
        if token.isEmpty {
            print("Error: UserData accessToken is empty")
            completion(false, "Not logged in or login credentials have expired, please log in again")
            return
        }
        
        // 检查token格式
        if !token.hasPrefix("ey") && !token.contains(".") {
            print("警告: accessToken格式可能不是有效的JWT格式")
        }
        
        print("用户登录状态检查: ID=\(userData.id), Email=\(userData.email), Token长度=\(token.count)")
        
        // 上传头像到文件服务器
        uploadAvatar(image: image, authToken: token) { [weak self] result in
            guard let self = self else { return }
            
            switch result {
            case .success(let responseParams):
                print("头像上传到文件服务器成功，收到完整的响应参数")
                
                // 提取URL，用于显示和保存到用户数据
                var avatarUrl = ""
                // 创建一个可变的副本
                var mutableParams = responseParams
                
                if let path = responseParams["path"] as? String {
                    avatarUrl = path
                } else if let url = responseParams["url"] as? String {
                    avatarUrl = url
                    // 确保path参数存在
                    mutableParams["path"] = url
                }
                
                // 提交可变的参数副本到用户资料API
                self.submitAvatarToProfile(params: mutableParams, accessToken: token) { profileResult in
                    switch profileResult {
                    case .success(_):
                        print("头像URL成功提交到用户资料API")
                        
                        // 更新本地用户数据
                        if !avatarUrl.isEmpty {
                            self.saveAvatarUrl(url: avatarUrl, userData: userData)
                        }
                        completion(true, "Avatar uploaded and updated successfully")
                        
                    case .failure(let error):
                        let errorCode = (error as NSError).code
                        var errorMessage = "Failed to submit avatar to user profile: \(error.localizedDescription)"
                        
                        // 针对特定错误码提供更友好的错误消息
                        if errorCode == 401 {
                            errorMessage = "Login credentials have expired, please log in again"
                        } else if errorCode == 403 {
                            errorMessage = "You don't have permission to perform this operation"
                        }
                        
                        print("Failed to submit avatar to user profile: \(errorMessage)")
                        completion(false, errorMessage)
                    }
                }
                
            case .failure(let error):
                let errorCode = (error as NSError).code
                var errorMessage = "Avatar upload failed: \(error.localizedDescription)"
                
                // 针对特定错误码提供更友好的错误消息
                if errorCode == 401 {
                    errorMessage = "Login credentials have expired, please log in again"
                    print("401 error: User token is invalid or format is incorrect")
                } else if errorCode == 403 {
                    errorMessage = "You don't have permission to perform this operation"
                } else if errorCode == 413 {
                    errorMessage = "Image is too large, please select a smaller image"
                }
                
                print("Avatar upload failed: \(errorMessage)")
                completion(false, errorMessage)
            }
        }
    }
}

// 添加一个诊断方法
func checkAuthStatus(userData: UserData) -> String {
    var report = "===== 身份验证状态检查 =====\n"
    
    // 检查基本信息
    report += "用户ID: \(userData.id.isEmpty ? "空" : userData.id)\n"
    report += "用户邮箱: \(userData.email.isEmpty ? "空" : userData.email)\n"
    
    // 检查令牌
    let token = userData.accessToken.trimmingCharacters(in: .whitespacesAndNewlines)
    
    if token.isEmpty {
        report += "访问令牌: 空（未登录）\n"
        report += "诊断结果: 用户未登录，需要先登录才能上传头像\n"
    } else {
        let tokenPreview = token.count > 15 ? 
            "\(token.prefix(10))...\(token.suffix(5))" : token
        
        report += "访问令牌: \(tokenPreview) (长度: \(token.count))\n"
        
        // 检查token格式
        if token.hasPrefix("ey") && token.contains(".") {
            report += "令牌格式: 似乎是有效的JWT格式\n"
            
            // 尝试分析JWT的过期时间
            let components = token.components(separatedBy: ".")
            if components.count >= 2 {
                let base64String = components[1].padding(toLength: ((components[1].count + 3) / 4) * 4, withPad: "=", startingAt: 0)
                if let payloadData = Data(base64Encoded: base64String),
                   let payload = try? JSONSerialization.jsonObject(with: payloadData, options: []) as? [String: Any] {
                    
                    if let exp = payload["exp"] as? TimeInterval {
                        let expirationDate = Date(timeIntervalSince1970: exp)
                        let isExpired = expirationDate < Date()
                        
                        report += "令牌过期时间: \(expirationDate)\n"
                        report += "令牌状态: \(isExpired ? "已过期" : "有效")\n"
                        
                        if isExpired {
                            report += "诊断结果: 令牌已过期，需要重新登录\n"
                        } else {
                            report += "诊断结果: 令牌有效，应该可以正常使用\n"
                        }
                    } else {
                        report += "诊断结果: 无法解析令牌的过期时间\n"
                    }
                } else {
                    report += "诊断结果: 令牌结构有效，但无法解析内容\n"
                }
            } else {
                report += "诊断结果: 令牌格式异常，不包含足够的部分\n"
            }
        } else {
            report += "令牌格式: 不是标准JWT格式，可能无效\n"
            report += "诊断结果: 令牌格式可能不正确，建议重新登录\n"
        }
    }
    
    report += "===========================\n"
    print(report)
    return report
} 