//
//  DetailedFoodAnalysisView.swift
//  FitScanAI
//
//  Created by AI Assistant
//

import SwiftUI

// 详细食物分析视图
struct DetailedFoodAnalysisView: View {
    @Binding var isPresented: Bool
    let foodRecordId: Int
    @EnvironmentObject var userData: UserData
    
    // 存储详细数据的状态变量
    @State private var detailedRecord: DetailedFoodRecord?
    @State private var foodImage: UIImage?
    @State private var isLoading = true
    @State private var errorMessage: String?
    @State private var showError = false
    @State private var isLoadingImage = false
    @State private var actualFoodRecordId: Int = 0
    @State private var hasTriedLoading = false
    
    @Environment(\.presentationMode) var presentationMode
    
    var body: some View {
        VStack(spacing: 0) {
            
            if isLoading {
                // 加载中状态
                VStack(spacing: 20) {
                    Spacer()
                    
                    ProgressView()
                        .progressViewStyle(CircularProgressViewStyle())
                        .scaleEffect(1.5)
                    
                    Text("Loading food analysis...")
                        .font(.headline)
                        .padding()
                    
                    Spacer()
                }
            } else if let record = detailedRecord {
                // 显示详细分析数据
                ScrollView {
                    VStack(spacing: 20) {
                        // 食物图片
                        if let image = foodImage {
                            Image(uiImage: image)
                                .resizable()
                                .aspectRatio(contentMode: .fill)
                                .frame(height: 200)
                                .clipped()
                        } else if record.hasImage {
                            // 显示加载中的图片占位符
                            ZStack {
                                Rectangle()
                                    .fill(Color.gray.opacity(0.1))
                                    .frame(height: 200)
                                
                                if isLoadingImage {
                                    ProgressView()
                                        .progressViewStyle(CircularProgressViewStyle())
                                } else {
                                    VStack(spacing: 10) {
                                        Image(systemName: "photo")
                                            .font(.system(size: 40))
                                            .foregroundColor(.gray)
                                        
                                        Text("Image not available")
                                            .font(.caption)
                                            .foregroundColor(.gray)
                                    }
                                }
                            }
                        } else {
                            // 无图片时的占位符
                            ZStack {
                                Rectangle()
                                    .fill(Color.gray.opacity(0.1))
                                    .frame(height: 200)
                                
                                VStack(spacing: 10) {
                                    Image(systemName: "fork.knife")
                                        .font(.system(size: 40))
                                        .foregroundColor(.gray)
                                    
                                    Text("No image available")
                                        .font(.caption)
                                        .foregroundColor(.gray)
                                }
                            }
                        }
                        
                        // 营养评分
                        HStack(spacing: 15) {
                            ZStack {
                                Circle()
                                    .trim(from: 0, to: Double(record.displayScore) / 100)
                                    .stroke(
                                        LinearGradient(
                                            gradient: Gradient(colors: [.green, .yellow, .orange]),
                                            startPoint: .leading,
                                            endPoint: .trailing
                                        ),
                                        style: StrokeStyle(lineWidth: 10, lineCap: .round)
                                    )
                                    .rotationEffect(.degrees(-90))
                                    .frame(width: 80, height: 80)
                                
                                VStack {
                                    Text("\(record.displayScore)")
                                        .font(.title2)
                                        .fontWeight(.bold)
                                    Text("/ 100")
                                        .font(.caption)
                                        .foregroundColor(.secondary)
                                }
                            }
                            
                            VStack(alignment: .leading) {
                                Text("Nutrition Score")
                                    .font(.headline)
                                
                                let nutritionMessage = record.displayScore >= 80 ? 
                                    "This meal has excellent nutritional value!" : 
                                    "This meal has good nutritional value."
                                Text(nutritionMessage)
                                    .font(.subheadline)
                                    .foregroundColor(.secondary)
                                    .fixedSize(horizontal: false, vertical: true)
                            }
                        }
                        .padding()
                        .background(Color.gray.opacity(0.1))
                        .cornerRadius(15)
                        .padding(.horizontal)
                        
                        // 食物名称
                        Text(record.foodName)
                            .font(.title2)
                            .fontWeight(.bold)
                            .padding(.horizontal)
                        
                        // 对每日目标的影响 - 仅当卡路里超过300时显示警告
                        if record.calories > 300 {
                            VStack(alignment: .leading, spacing: 10) {
                                Text("Impact on Daily Goals")
                                    .font(.headline)
                                    .padding(.horizontal)
                                
                                HStack {
                                    Image(systemName: "exclamationmark.triangle.fill")
                                        .foregroundColor(.orange)
                                    
                                    VStack(alignment: .leading) {
                                        Text("High in Calories")
                                            .font(.subheadline)
                                            .fontWeight(.semibold)
                                            .foregroundColor(.orange)
                                        
                                        Text("This meal contains \(record.calories) calories")
                                            .font(.caption)
                                            .foregroundColor(.secondary)
                                    }
                                }
                                .padding()
                                .frame(maxWidth: .infinity, alignment: .leading)
                                .background(Color.orange.opacity(0.1))
                                .cornerRadius(10)
                                .padding(.horizontal)
                            }
                        }
                        
                        // 建议 - 使用API返回的建议数据
                        let recommendations = record.parsedHealthAdvice
                        if !recommendations.isEmpty {
                            VStack(alignment: .leading, spacing: 15) {
                                Text("Recommendations")
                                    .font(.headline)
                                    .padding(.horizontal)
                                
                                VStack(spacing: 12) {
                                    ForEach(recommendations, id: \.self) { recommendation in
                                        HStack(alignment: .top, spacing: 10) {
                                            Image(systemName: "checkmark.circle.fill")
                                                .foregroundColor(.green)
                                                .font(.system(size: 16))
                                            
                                            Text(recommendation)
                                                .font(.subheadline)
                                                .lineSpacing(4)
                                                .fixedSize(horizontal: false, vertical: true)
                                            
                                            Spacer()
                                        }
                                        .padding(.bottom, 6)
                                        
                                        if recommendation != recommendations.last {
                                            Divider()
                                                .padding(.leading, 26)
                                        }
                                    }
                                }
                                .padding()
                                .background(Color.green.opacity(0.1))
                                .cornerRadius(12)
                                .padding(.horizontal)
                            }
                            .padding(.vertical, 10)
                        }
                        
                        // 营养成分分析
                        VStack(alignment: .leading, spacing: 15) {
                            Text("Nutrition Breakdown")
                                .font(.headline)
                                .padding(.horizontal)
                            
                            // 饼图
                            VStack {
                                // 计算实际的百分比
                                let totalMacros = record.protein + record.carbs + record.fat
                                let proteinPercentage = totalMacros > 0 ? Double(record.protein) / Double(totalMacros) : 0
                                let carbsPercentage = totalMacros > 0 ? Double(record.carbs) / Double(totalMacros) : 0
                                let fatPercentage = totalMacros > 0 ? Double(record.fat) / Double(totalMacros) : 0
                                
                                // 计算每个部分在环形图中的起始和结束位置
                                let proteinEnd = proteinPercentage
                                let carbsEnd = proteinEnd + carbsPercentage
                                // fatEnd将是1.0 (100%)
                                
                                // 使用简单的环形图
                                ZStack {
                                    Circle()
                                        .trim(from: 0, to: CGFloat(proteinEnd))
                                        .stroke(Color.blue, lineWidth: 15)
                                        .rotationEffect(.degrees(-90))
                                    
                                    Circle()
                                        .trim(from: CGFloat(proteinEnd), to: CGFloat(carbsEnd))
                                        .stroke(Color.green, lineWidth: 15)
                                        .rotationEffect(.degrees(-90))
                                    
                                    Circle()
                                        .trim(from: CGFloat(carbsEnd), to: 1)
                                        .stroke(Color.yellow, lineWidth: 15)
                                        .rotationEffect(.degrees(-90))
                                }
                                .frame(width: 150, height: 150)
                                .padding(.vertical)
                                
                                // 营养素图例 - 改进版
                                VStack(spacing: 15) {
                                    // 计算百分比值
                                    let proteinPercent = Int(round(proteinPercentage * 100))
                                    let carbsPercent = Int(round(carbsPercentage * 100))
                                    let fatPercent = Int(round(fatPercentage * 100))
                                    
                                    // 蛋白质行
                                    HStack {
                                        Circle()
                                            .fill(Color.blue)
                                            .frame(width: 18, height: 18)
                                        
                                        Text("Protein")
                                            .font(.subheadline)
                                            .fontWeight(.medium)
                                        
                                        Spacer()
                                        
                                        VStack(alignment: .trailing) {
                                            Text("\(record.protein)g")
                                                .font(.subheadline)
                                                .fontWeight(.semibold)
                                            
                                            Text("\(proteinPercent)%")
                                                .font(.caption)
                                                .foregroundColor(.secondary)
                                        }
                                    }
                                    
                                    // 碳水行
                                    HStack {
                                        Circle()
                                            .fill(Color.green)
                                            .frame(width: 18, height: 18)
                                        
                                        Text("Carbs")
                                            .font(.subheadline)
                                            .fontWeight(.medium)
                                        
                                        Spacer()
                                        
                                        VStack(alignment: .trailing) {
                                            Text("\(record.carbs)g")
                                                .font(.subheadline)
                                                .fontWeight(.semibold)
                                            
                                            Text("\(carbsPercent)%")
                                                .font(.caption)
                                                .foregroundColor(.secondary)
                                        }
                                    }
                                    
                                    // 脂肪行
                                    HStack {
                                        Circle()
                                            .fill(Color.yellow)
                                            .frame(width: 18, height: 18)
                                        
                                        Text("Fat")
                                            .font(.subheadline)
                                            .fontWeight(.medium)
                                        
                                        Spacer()
                                        
                                        VStack(alignment: .trailing) {
                                            Text("\(record.fat)g")
                                                .font(.subheadline)
                                                .fontWeight(.semibold)
                                            
                                            Text("\(fatPercent)%")
                                                .font(.caption)
                                                .foregroundColor(.secondary)
                                        }
                                    }
                                }
                                .padding(.horizontal, 30)
                                .padding(.bottom)
                            }
                            .background(Color.gray.opacity(0.1))
                            .cornerRadius(15)
                            .padding(.horizontal)
                            
                            // 详细营养素信息
                            VStack(spacing: 15) {
                                // 卡路里
                                DetailedNutrientRow(
                                    icon: "flame.fill",
                                    iconColor: .red,
                                    name: "Calories",
                                    value: "\(record.calories) kcal"
                                )
                                
                                // 水分
                                DetailedNutrientRow(
                                    icon: "drop.fill",
                                    iconColor: .blue,
                                    name: "Water",
                                    value: "\(record.water)%"
                                )
                                
                                // 蛋白质
                                DetailedNutrientRow(
                                    icon: "bolt.fill",
                                    iconColor: .blue,
                                    name: "Protein",
                                    value: "\(record.protein)g"
                                )
                                
                                // 碳水
                                DetailedNutrientRow(
                                    icon: "leaf.fill",
                                    iconColor: .green,
                                    name: "Carbs",
                                    value: "\(record.carbs)g"
                                )
                                
                                // 纤维
                                DetailedNutrientRow(
                                    icon: "waveform.path",
                                    iconColor: .green,
                                    name: "Fiber",
                                    value: "\(record.fiber)g"
                                )
                                
                                // 糖
                                DetailedNutrientRow(
                                    icon: "cube.fill",
                                    iconColor: .pink,
                                    name: "Sugar",
                                    value: "\(record.sugar)g"
                                )
                            }
                            .padding()
                            .background(Color.gray.opacity(0.1))
                            .cornerRadius(15)
                            .padding(.horizontal)
                        }
                        .padding(.vertical, 10)
                        
                        // 分析原因 (如果有的话) - 移到与Nutrition Breakdown并列的位置
                        if !record.analysisReason.isEmpty {
                            VStack(alignment: .leading, spacing: 15) {
                                Text("Analysis Details")
                                    .font(.headline)
                                    .padding(.horizontal)
                                
                                VStack(alignment: .leading, spacing: 8) {
                                    Text(record.analysisReason)
                                        .font(.subheadline)
                                        .foregroundColor(.secondary)
                                        .multilineTextAlignment(.leading)
                                }
                                .padding()
                                .frame(maxWidth: .infinity, alignment: .leading)
                                .background(Color.gray.opacity(0.1))
                                .cornerRadius(15)
                                .padding(.horizontal)
                            }
                            .padding(.vertical, 10)
                        }
                    }
                    .padding(.bottom, 30)
                }
            } else {
                // 错误状态
                VStack(spacing: 20) {
                    Spacer()
                    
                    Image(systemName: "exclamationmark.triangle")
                        .font(.system(size: 50))
                        .foregroundColor(.orange)
                    
                    Text("Failed to load food analysis")
                        .font(.headline)
                    
                    if let error = errorMessage {
                        Text(error)
                            .font(.subheadline)
                            .foregroundColor(.secondary)
                            .multilineTextAlignment(.center)
                            .padding(.horizontal)
                    }
                    
                    Button("Retry") {
                        loadDetailedFoodRecord()
                    }
                    .padding()
                    .background(Color.green)
                    .foregroundColor(.white)
                    .cornerRadius(10)
                    
                    Spacer()
                }
            }
        }
        .background(Color.white)
        .navigationBarTitleDisplayMode(.inline)
        .onAppear {
            // 统计
            XDTrackTool.shared.appear("食物详情页面")

            print("DetailedFoodAnalysisView onAppear - 接收到的foodRecordId: \(foodRecordId)")

            // 防止重复加载
            guard !hasTriedLoading else { return }
            hasTriedLoading = true
            
            // 优先使用传入的ID，如果为0则尝试使用缓存的ID
            if foodRecordId > 0 {
                actualFoodRecordId = foodRecordId
                loadDetailedFoodRecord()
            } else {
                print("DetailedFoodAnalysisView - foodRecordId为0，尝试使用缓存的ID")
                
                // 尝试从缓存获取最新的食物记录ID
                let cachedId = UserDefaultsManager.shared.getLastFoodRecordId()
                
                if cachedId > 0 {
                    print("DetailedFoodAnalysisView - 使用缓存的foodRecordId: \(cachedId)")
                    actualFoodRecordId = cachedId
                    loadDetailedFoodRecord()
                } else {
                    print("DetailedFoodAnalysisView - 缓存中也没有有效ID，等待sheet完全显示")
                    // 使用较长的延迟确保sheet完全显示
                    DispatchQueue.main.asyncAfter(deadline: .now() + 0.5) {
                        // 重新检查传入的ID
                        if self.foodRecordId > 0 {
                            self.actualFoodRecordId = self.foodRecordId
                            self.loadDetailedFoodRecord()
                        } else {
                            // 再次尝试缓存
                            let retryCachedId = UserDefaultsManager.shared.getLastFoodRecordId()
                            if retryCachedId > 0 {
                                self.actualFoodRecordId = retryCachedId
                                self.loadDetailedFoodRecord()
                            } else {
                                self.errorMessage = "Invalid food record ID: \(self.foodRecordId)"
                                self.isLoading = false
                                print("DetailedFoodAnalysisView - 延迟后仍然无法获取有效的foodRecordId")
                            }
                        }
                    }
                }
            }
        }
        .alert(isPresented: $showError) {
            Alert(
                title: Text("Load Error"),
                message: Text(errorMessage ?? "Unknown error occurred"),
                dismissButton: .default(Text("OK"))
            )
        }
    }
    
    // 加载详细食物记录
    private func loadDetailedFoodRecord() {
        // 检查是否已登录
        guard !userData.accessToken.isEmpty else {
            errorMessage = "Please login to view food details"
            isLoading = false
            return
        }
        
        isLoading = true
        errorMessage = nil
        
        print("DetailedFoodAnalysisView - 开始加载详细食物记录，recordId: \(actualFoodRecordId)")
        
        ImageUploadService.shared.getDetailedFoodRecord(recordId: actualFoodRecordId, authToken: userData.accessToken) { result in
            DispatchQueue.main.async {
                self.isLoading = false
                
                switch result {
                case .success(let record):
                    print("DetailedFoodAnalysisView - 成功加载详细食物记录，ID: \(record.id), 食物名称: \(record.foodName)")
                    self.detailedRecord = record
                    
                    // 加载图片（如果有的话）
                    if record.hasImage, let imageUrl = record.fileLocation {
                        self.loadFoodImage(from: imageUrl)
                    }
                    
                case .failure(let error):
                    print("DetailedFoodAnalysisView - 加载详细食物记录失败，recordId: \(self.actualFoodRecordId), 错误: \(error)")
                    self.errorMessage = error.localizedDescription
                }
            }
        }
    }
    
    // 加载食物图片
    private func loadFoodImage(from url: String) {
        isLoadingImage = true
        
        print("开始加载详细食物分析图片: \(url)")
        
        // 对于第一次获取的大图，优先显示超压缩缩略图，然后加载高质量版本
        if ImageCacheManager.shared.getImage(for: url) == nil {
            // 第一次加载，使用快速缩略图策略
            ImageCacheManager.shared.loadThumbnailFirst(from: url) { image in
                DispatchQueue.main.async {
                    self.foodImage = image
                    self.isLoadingImage = false
                    if image != nil {
                        print("成功加载详细分析缩略图（超高压缩），稍后将加载高质量版本")
                    } else {
                        print("加载详细分析缩略图失败")
                    }
                }
            }
        } else {
            // 已有缓存，直接使用中等压缩加载
            ImageCacheManager.shared.loadFoodDiaryImage(from: url, isListView: false) { image in
                DispatchQueue.main.async {
                    self.foodImage = image
                    self.isLoadingImage = false
                    if image != nil {
                        print("成功加载详细分析图片（中等压缩）")
                    } else {
                        print("加载详细分析图片失败")
                    }
                }
            }
        }
    }
}

// 详细营养素行视图
struct DetailedNutrientRow: View {
    let icon: String
    let iconColor: Color
    let name: String
    let value: String
    
    var body: some View {
        HStack {
            Image(systemName: icon)
                .font(.system(size: 14)) // 统一图标大小为14
                .foregroundColor(.white)
                .frame(width: 32, height: 32) // 统一背景框大小为32x32
                .background(iconColor)
                .cornerRadius(8)
            
            Text(name)
                .fontWeight(.medium)
            
            Spacer()
            
            Text(value)
                .fontWeight(.semibold)
        }
    }
} 