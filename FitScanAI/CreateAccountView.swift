import SwiftUI
import UIKit
import WebKit
import Network

// 添加WebView组件
struct CreateAccountWebView: UIViewRepresentable {
    let url: URL
    @Binding var isLoading: Bool
    
    func makeUIView(context: Context) -> WKWebView {
        let configuration = WKWebViewConfiguration()
        configuration.allowsInlineMediaPlayback = true
        configuration.mediaTypesRequiringUserActionForPlayback = []
        
        let webView = WKWebView(frame: .zero, configuration: configuration)
        webView.navigationDelegate = context.coordinator
        webView.allowsBackForwardNavigationGestures = false
        webView.backgroundColor = UIColor.systemBackground
        
        // 设置用户代理
        webView.customUserAgent = "Mozilla/5.0 (iPhone; CPU iPhone OS 16_0 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/16.0 Mobile/15E148 Safari/604.1"
        
        return webView
    }
    
    func updateUIView(_ webView: WKWebView, context: Context) {
        // 检查是否已经加载了相同的URL，避免重复加载
        if webView.url != url {
            var request = URLRequest(url: url)
            request.timeoutInterval = 30.0
            request.cachePolicy = .returnCacheDataElseLoad
            webView.load(request)
        }
    }
    
    func makeCoordinator() -> Coordinator {
        Coordinator(self)
    }
    
    class Coordinator: NSObject, WKNavigationDelegate {
        let parent: CreateAccountWebView
        private var retryCount = 0
        private let maxRetries = 2
        
        init(_ parent: CreateAccountWebView) {
            self.parent = parent
        }
        
        func webView(_ webView: WKWebView, didStartProvisionalNavigation navigation: WKNavigation!) {
            DispatchQueue.main.async {
                self.parent.isLoading = true
            }
        }
        
        func webView(_ webView: WKWebView, didFinish navigation: WKNavigation!) {
            DispatchQueue.main.async {
                self.parent.isLoading = false
                self.retryCount = 0
            }
        }
        
        func webView(_ webView: WKWebView, didFail navigation: WKNavigation!, withError error: Error) {
            handleError(webView: webView, error: error)
        }
        
        func webView(_ webView: WKWebView, didFailProvisionalNavigation navigation: WKNavigation!, withError error: Error) {
            handleError(webView: webView, error: error)
        }
        
        private func handleError(webView: WKWebView, error: Error) {
            let nsError = error as NSError
            print("CreateAccountWebView error: \(error.localizedDescription), code: \(nsError.code)")
            
            DispatchQueue.main.async {
                self.parent.isLoading = false
                
                // 对于-999错误，尝试自动重试
                if nsError.code == -999 && self.retryCount < self.maxRetries {
                    self.retryCount += 1
                    print("CreateAccountWebView retrying load attempt \(self.retryCount)")
                    
                    DispatchQueue.main.asyncAfter(deadline: .now() + 1.5) {
                        var request = URLRequest(url: self.parent.url)
                        request.timeoutInterval = 30.0
                        request.cachePolicy = .returnCacheDataElseLoad
                        webView.load(request)
                    }
                }
            }
        }
        
        func webView(_ webView: WKWebView, decidePolicyFor navigationAction: WKNavigationAction, decisionHandler: @escaping (WKNavigationActionPolicy) -> Void) {
            decisionHandler(.allow)
        }
    }
}

// 网页视图组件
struct CreateAccountWebPageView: View {
    let url: URL
    let title: String
    @Environment(\.presentationMode) var presentationMode
    @State private var isLoading = true
    
    var body: some View {
        NavigationView {
            ZStack {
                CreateAccountWebView(url: url, isLoading: $isLoading)
                
                if isLoading {
                    ProgressView("Loading...")
                        .frame(maxWidth: .infinity, maxHeight: .infinity)
                        .background(Color.white)
                }
            }
            .navigationTitle(title)
            .navigationBarTitleDisplayMode(.inline)
            .navigationBarItems(
                leading: Button(action: {
                    presentationMode.wrappedValue.dismiss()
                }) {
                    Image(systemName: "chevron.left")
                        .foregroundColor(.green)
                }
            )
        }
    }
}

// 网络状态管理器
class CreateAccountNetworkManager: ObservableObject {
    @Published var isNetworkAvailable: Bool = true
    @Published var connectionType: String = "Unknown"
    
    private let monitor = NWPathMonitor()
    private let queue = DispatchQueue(label: "NetworkMonitor")
    
    init() {
        startMonitoring()
    }
    
    private func startMonitoring() {
        monitor.pathUpdateHandler = { [weak self] path in
            DispatchQueue.main.async {
                self?.isNetworkAvailable = path.status == .satisfied
                
                if path.usesInterfaceType(.wifi) {
                    self?.connectionType = "WiFi"
                } else if path.usesInterfaceType(.cellular) {
                    self?.connectionType = "Cellular"
                } else {
                    self?.connectionType = "Unknown"
                }
                
                print("CreateAccount网络状态: \(self?.isNetworkAvailable == true ? "已连接" : "未连接") - \(self?.connectionType ?? "Unknown")")
            }
        }
        
        monitor.start(queue: queue)
    }
    
    func triggerNetworkPermissionCheck() {
        // 触发网络权限检查
        let url = URL(string: "https://www.apple.com")!
        let task = URLSession.shared.dataTask(with: url) { _, _, _ in }
        task.resume()
    }
    
    deinit {
        monitor.cancel()
    }
}

struct CreateAccountView: View {
    // 当从引导进入注册页时，禁用返回
    var disableBackNavigation: Bool = false
    @Environment(\.presentationMode) var presentationMode
    @EnvironmentObject private var userData: UserData
    
    // 用户输入数据
    @State private var fullName: String = ""
    @State private var email: String = ""
    @State private var verificationCode: String = ""
    @State private var password: String = ""
    @State private var isPasswordVisible: Bool = false
    
    // 按钮状态
    @State private var isGetCodePressed: Bool = false
    @State private var isCreateAccountPressed: Bool = false
    @State private var isApplePressed: Bool = false
    @State private var isGooglePressed: Bool = false
    @State private var isTermsPressed: Bool = false
    @State private var isPolicyPressed: Bool = false
    
    // 计时器相关
    @State private var countdownTime: Int = 60
    @State private var isCountingDown: Bool = false
    @State private var timer: Timer? = nil
    @State private var isSendingCode: Bool = false  // 与SignUpView一致，防重复发送
    
    // 密码要求验证
    @State private var hasEightChars: Bool = false
    @State private var hasNumbers: Bool = false
    @State private var hasUppercase: Bool = false
    @State private var hasLowercase: Bool = false
    @State private var hasSpecialChars: Bool = false
    
    // 加载状态和提示
    @State private var isLoading: Bool = false
    @State private var showAlert: Bool = false
    @State private var alertTitle: String = ""
    @State private var alertMessage: String = ""
    
    // 添加网页显示状态
    @State private var showTermsOfService = false
    @State private var showPrivacyPolicy = false
    
    // 网络状态管理
    @StateObject private var networkManager = CreateAccountNetworkManager()
    @State private var showNetworkAlert = false
    
    var body: some View {
        ZStack {
            Color.white.edgesIgnoringSafeArea(.all)
            
            ScrollView {
                VStack(alignment: .leading, spacing: 24) {
                    // 标题部分
                    VStack(alignment: .leading, spacing: 8) {
                        Text("Create Account")
                            .font(.system(size: 32, weight: .bold))
                            .foregroundColor(.black)
                        
                        Text("You're almost there! Complete your account setup to access all premium features and start your journey.")
                            .font(.system(size: 16))
                            .foregroundColor(.secondary)
                    }
                    .padding(.top, 40)
                    
                    // 新增的特性说明组件
                    VStack(alignment: .leading, spacing: 12) {
                            Text("Why create an account?")
                            .font(.system(size: 16, weight: .semibold))
                            .foregroundColor(.black)
                        
                        VStack(alignment: .leading, spacing: 8) {
                            HStack(spacing: 8) {
                                Image(systemName: "checkmark.circle.fill")
                                    .foregroundColor(.green)
                                    .font(.system(size: 16))
                                
                                Text("Save your preferences and settings")
                                    .font(.system(size: 14))
                                    .foregroundColor(.black)
                                
                                Spacer()
                            }
                            
                            HStack(spacing: 8) {
                                Image(systemName: "checkmark.circle.fill")
                                    .foregroundColor(.green)
                                    .font(.system(size: 16))
                                
                                Text("Access exclusive content and features")
                                    .font(.system(size: 14))
                                    .foregroundColor(.black)
                                
                                Spacer()
                            }
                            
                            HStack(spacing: 8) {
                                Image(systemName: "checkmark.circle.fill")
                                    .foregroundColor(.green)
                                    .font(.system(size: 16))
                                
                                Text("Get personalized recommendations")
                                    .font(.system(size: 14))
                                    .foregroundColor(.black)
                                
                                Spacer()
                            }
                        }
                    }
                    .padding(16)
                    .frame(maxWidth: .infinity)
                    .background(Color(red: 0.92, green: 0.97, blue: 0.93))
                    .cornerRadius(12)
                    
                    // 全名输入
                    VStack(alignment: .leading, spacing: 8) {
                        Text("Full Name")
                            .font(.system(size: 16, weight: .medium))
                            .foregroundColor(.black)
                        
                        HStack {
                            Image(systemName: "person")
                                .foregroundColor(.gray)
                            
                            TextField("Enter your full name", text: $fullName)
                                .multilineTextAlignment(.leading)
                                .adaptiveKeyboard()
                        }
                        .padding()
                        .background(Color(.systemGray6))
                        .cornerRadius(8)
                    }
                    
                    // 邮箱输入
                    VStack(alignment: .leading, spacing: 8) {
                        Text("Email Address")
                            .font(.system(size: 16, weight: .medium))
                            .foregroundColor(.black)
                        
                        HStack {
                            Image(systemName: "envelope")
                                .foregroundColor(.gray)
                            
                            TextField("Enter your email address", text: $email)
                                .keyboardType(.emailAddress)
                                .autocapitalization(.none)
                                .multilineTextAlignment(.leading)
                                .adaptiveKeyboard()
                        }
                        .padding()
                        .background(Color(.systemGray6))
                        .cornerRadius(8)
                    }
                    
                    // 验证码输入
                    VStack(alignment: .leading, spacing: 8) {
                        Text("Verification Code")
                            .font(.system(size: 16, weight: .medium))
                            .foregroundColor(.black)
                        
                        HStack {
                            TextField("Enter code", text: $verificationCode)
                                .keyboardType(.numberPad)
                                .padding(.vertical)
                                .multilineTextAlignment(.leading)
                                .adaptiveKeyboard()
                            
                            Button(action: {
                                // 点击获取验证码按钮
                                if !isCountingDown && !isSendingCode {
                                    sendVerificationCode()
                                }
                            }) {
                                Text(isCountingDown ? "\(countdownTime)s" : (isSendingCode ? "Sending..." : "Get Code"))
                                    .font(.system(size: 16, weight: .medium))
                                    .foregroundColor(.white)
                                    .padding(.horizontal, 16)
                                    .padding(.vertical, 8)
                                    .background(isCountingDown ? Color.green : Color.green)
                                    .cornerRadius(8)
                            }
                            .disabled(isCountingDown || email.isEmpty || isSendingCode)
                        }
                        .padding(.horizontal)
                        .background(Color(.systemGray6))
                        .cornerRadius(8)
                    }
                    
                    // 密码输入
                    VStack(alignment: .leading, spacing: 8) {
                        Text("Password")
                            .font(.system(size: 16, weight: .medium))
                            .foregroundColor(.black)
                        
                        HStack {
                            Image(systemName: "lock")
                                .foregroundColor(.gray)
                            
                            if isPasswordVisible {
                                TextField("Create a password", text: $password)
                                    .autocapitalization(.none)
                                    .onChange(of: password) { _, newValue in
                                        validatePassword(newValue)
                                    }
                                    .multilineTextAlignment(.leading)
                                    .adaptiveKeyboard()
                            } else {
                                SecureField("Create a password", text: $password)
                                    .autocapitalization(.none)
                                    .onChange(of: password) { _, newValue in
                                        validatePassword(newValue)
                                    }
                                    .multilineTextAlignment(.leading)
                                    .adaptiveKeyboard()
                            }
                            
                            Button(action: {
                                isPasswordVisible.toggle()
                            }) {
                                Image(systemName: isPasswordVisible ? "eye.fill" : "eye.slash.fill")
                                    .foregroundColor(.gray)
                            }
                        }
                        .padding()
                        .background(Color(.systemGray6))
                        .cornerRadius(8)
                    }
                    
                    // 密码强度指示器
                    VStack(alignment: .leading, spacing: 8) {
                        HStack(spacing: 4) {
                            ForEach(0..<5, id: \.self) { index in
                                Rectangle()
                                    .fill(passwordStrengthColor(for: index))
                                    .frame(height: 6)
                                    .cornerRadius(3)
                            }
                        }
                        
                        Text(passwordStrengthText)
                            .font(.system(size: 14, weight: .medium))
                            .foregroundColor(passwordStrengthTextColor)
                    }
                    
                    // 密码要求提示 - 自适应布局
                    VStack(alignment: .leading, spacing: adaptiveSpacing()) {
                        Text("Password Requirements")
                            .font(.system(size: 16, weight: .medium))
                            .foregroundColor(.black)
                            .padding(.bottom, 4)
                        
                        // 使用LazyVGrid来适应不同屏幕尺寸
                        if UIScreen.main.bounds.height < 700 { // iPhone 12 mini等小屏设备
                            // 小屏设备使用紧凑布局
                            VStack(alignment: .leading, spacing: 8) {
                                // 第一行：字符数量和数字
                                HStack(spacing: 12) {
                                    passwordRequirementItem(
                                        isValid: hasEightChars,
                                         text: "6+ chars"
                                    )
                                    
                                    passwordRequirementItem(
                                        isValid: hasNumbers,
                                         text: "Numbers"
                                    )
                        }
                        
                                // 第二行：大写字母和小写字母
                                HStack(spacing: 12) {
                                    passwordRequirementItem(
                                        isValid: hasUppercase,
                                         text: "Uppercase"
                                    )
                                    
                                    passwordRequirementItem(
                                        isValid: hasLowercase,
                                         text: "Lowercase"
                                    )
                        }
                        
                                // 第三行：特殊字符
                                HStack(spacing: 12) {
                                    passwordRequirementItem(
                                        isValid: hasSpecialChars,
                                         text: "Special chars"
                                    )
                                    
                                    Spacer()
                        }
                            }
                        } else {
                            // 大屏设备使用标准布局
                            VStack(alignment: .leading, spacing: 12) {
                                passwordRequirementItem(
                                    isValid: hasEightChars,
                                    text: "At least 6 characters"
                                )
                                
                                passwordRequirementItem(
                                    isValid: hasNumbers,
                                    text: "Include numbers"
                                )
                                
                                passwordRequirementItem(
                                    isValid: hasUppercase,
                                    text: "Include uppercase letters"
                                )
                                
                                passwordRequirementItem(
                                    isValid: hasLowercase,
                                    text: "Include lowercase letters"
                                )
                                
                                passwordRequirementItem(
                                    isValid: hasSpecialChars,
                                    text: "Include special characters"
                                )
                            }
                        }
                    }
                    .padding(adaptivePadding())
                    .frame(maxWidth: .infinity)
                    .background(Color(.systemGray6).opacity(0.5))
                    .cornerRadius(8)
                    
                    // 创建账户按钮
                    Button(action: {
                        withAnimation {
                            isCreateAccountPressed = false
                        }
                        // 创建账户逻辑
                        createAccount()
                    }) {
                        Text("Create Account")
                            .font(.system(size: 18, weight: .semibold))
                            .foregroundColor(.white)
                            .frame(maxWidth: .infinity)
                            .padding()
                            .background(
                                isLoading || !isAllRequirementsMet() ? 
                                Color.gray.opacity(0.6) : 
                                (isCreateAccountPressed ? Color.green.opacity(0.8) : Color.green)
                            )
                            .cornerRadius(10)
                    }
                    .disabled(isLoading || !isAllRequirementsMet())
                    
                    // 添加社交登录选项
                    VStack(spacing: 16) {
                        // 分隔线 - 屏幕自适应布局
                        HStack {
                            Rectangle()
                                .frame(height: 1)
                                .foregroundColor(.gray.opacity(0.3))
                            
                            Text("or continue with")
                                .font(.system(size: 14))
                                .foregroundColor(.gray)
                                .padding(.horizontal, 12)
                                .fixedSize(horizontal: true, vertical: false) // 确保文本不会换行
                            
                            Rectangle()
                                .frame(height: 1)
                                .foregroundColor(.gray.opacity(0.3))
                        }
                        .padding(.vertical, 8)
                        
                        // 社交登录按钮
                        HStack(spacing: 16) {
                            // Apple登录按钮
                            Button(action: {
                                isApplePressed = true
                                
                                // 添加延迟以显示按钮按下效果
                                DispatchQueue.main.asyncAfter(deadline: .now() + 0.2) {
                                    isApplePressed = false
                                    
                                    // 保存当前输入的邮箱，可用于与苹果登录返回的邮箱进行关联
                                    if !email.isEmpty {
                                        UserDefaults.standard.set(email, forKey: "tempSignUpEmail")
                                        print("🍎 已保存注册邮箱：\(email)")
                                    }
                                    
                                    print("🍎 开始苹果登录流程...")
                                    
                                    // 调用Apple登录并传递userData实例，不发送通知
                                    AppleAuthService.shared.startSignInWithApple(userData: userData, shouldSendNotification: false) { success in
                                        DispatchQueue.main.async {
                                            if success {
                                                print("🍎 Apple登录成功，开始绑定引导信息并进入主页")
                                                // 标记需要展示第二个欢迎页（来源：CreateAccountView）
                                                UserDefaults.standard.set(true, forKey: "showSecondWelcome")
                                                self.bindPendingOnboardingDataToAccount { _ in
                                                    // 关闭CreateAccountView
                                                    self.presentationMode.wrappedValue.dismiss()
                                                    // 发送通知直接进入主页
                                                    NotificationCenter.default.post(name: Notification.Name("WelcomeGetStarted"), object: nil)
                                                }
                                            } else {
                                                print("🍎 Apple登录失败")
                                            }
                                        }
                                    }
                                }
                            }) {
                                HStack {
                                    Image(systemName: "apple.logo")
                                        .resizable()
                                        .frame(width: 20, height: 24)
                                        .foregroundColor(.white)
                                    
                                    Text("Apple")
                                        .font(.system(size: 16, weight: .medium))
                                        .foregroundColor(.white)
                                }
                                .frame(maxWidth: .infinity)
                                .padding(.vertical, 12)
                                .background(isApplePressed ? Color.black.opacity(0.7) : .black)
                                .cornerRadius(10)
                            }
                            
                            // Google登录按钮
                            Button(action: {
                                isGooglePressed = true
                                
                                // 添加登录延迟模拟效果
                                DispatchQueue.main.asyncAfter(deadline: .now() + 0.2) {
                                    isGooglePressed = false
                                    
                                    // 保存当前输入的邮箱，可用于与谷歌登录返回的邮箱进行关联
                                    if !email.isEmpty {
                                        UserDefaults.standard.set(email, forKey: "tempSignUpEmail")
                                        print("🌐 已保存注册邮箱：\(email)")
                                    }
                                    
                                    print("🌐 开始谷歌登录流程...")
                                    
                                    // 调用Google登录并传递userData实例，不发送通知
                                    GoogleAuthService.shared.startSignInWithGoogle(userData: userData, shouldSendNotification: false) { success in
                                        DispatchQueue.main.async {
                                            if success {
                                                print("🌐 Google登录成功，开始绑定引导信息并进入主页")
                                                // 标记需要展示第二个欢迎页（来源：CreateAccountView）
                                                UserDefaults.standard.set(true, forKey: "showSecondWelcome")
                                                self.bindPendingOnboardingDataToAccount { _ in
                                                    // 关闭CreateAccountView
                                                    self.presentationMode.wrappedValue.dismiss()
                                                    // 发送通知直接进入主页
                                                    NotificationCenter.default.post(name: Notification.Name("WelcomeGetStarted"), object: nil)
                                                }
                                            } else {
                                                print("🌐 Google登录失败")
                                            }
                                        }
                                    }
                                }
                            }) {
                                HStack {
                                    Image("google-logo")
                                        .resizable()
                                        .frame(width: 24, height: 24)
                                    
                                    Text("Google")
                                        .font(.system(size: 16, weight: .medium))
                                        .foregroundColor(.primary)
                                }
                                .frame(maxWidth: .infinity)
                                .padding(.vertical, 12)
                                .background(isGooglePressed ? Color.gray.opacity(0.2) : Color.white)
                                .cornerRadius(10)
                                .overlay(
                                    RoundedRectangle(cornerRadius: 10)
                                        .stroke(Color.gray.opacity(0.3), lineWidth: 1)
                                )
                            }
                        }
                    }
                    .padding(.top, 16)
                    
                    // 服务条款和隐私政策 - 与登录页面一致的布局
                    HStack {
                        Spacer()
                        
                        VStack(spacing: 8) {
                            // 主要文本
                            Text("By creating an account, you agree to our")
                                .font(.system(size: adaptiveTermsFontSize()))
                                .foregroundColor(.secondary)
                                .multilineTextAlignment(.center)
                            
                            // 可点击的链接按钮 - 与登录页面一致
                            HStack(spacing: 8) {
                                Button(action: {
                                    isTermsPressed = true
                                    DispatchQueue.main.asyncAfter(deadline: .now() + 0.2) {
                                        isTermsPressed = false
                                        showTermsOfService = true
                                    }
                                }) {
                                    Text("Terms of Service")
                                        .font(.system(size: adaptiveTermsFontSize()))
                                        .foregroundColor(isTermsPressed ? Color.green.opacity(0.7) : .green)
                                }
                                
                                Text("and")
                                    .font(.system(size: adaptiveTermsFontSize()))
                                    .foregroundColor(.secondary)
                                
                                Button(action: {
                                    isPolicyPressed = true
                                    DispatchQueue.main.asyncAfter(deadline: .now() + 0.2) {
                                        isPolicyPressed = false
                                        showPrivacyPolicy = true
                                    }
                                }) {
                                    Text("Privacy Policy")
                                        .font(.system(size: adaptiveTermsFontSize()))
                                        .foregroundColor(isPolicyPressed ? Color.green.opacity(0.7) : .green)
                                }
                            }
                        }
                        
                        Spacer()
                    }
                    
                    Spacer()
                        .frame(height: 24)
                }
                .padding(.horizontal, 24)
                .frame(maxWidth: .infinity)
                .contentShape(Rectangle())
                .onTapGesture {
                    UIApplication.shared.sendAction(#selector(UIResponder.resignFirstResponder), to: nil, from: nil, for: nil)
                }
            }
        }
        .navigationBarBackButtonHidden(true)
        .navigationBarItems(leading:
            Group {
                if !disableBackNavigation {
                    Button(action: {
                        presentationMode.wrappedValue.dismiss()
                    }) {
                        Image(systemName: "chevron.left")
                            .foregroundColor(.black)
                    }
                }
            }
        )
        .alert(isPresented: $showAlert) {
            Alert(
                title: Text(alertTitle),
                message: Text(alertMessage),
                dismissButton: .default(Text("OK"))
            )
        }
        .onDisappear {
            // 确保在视图消失时停止计时器
            stopTimer()
        }
        .sheet(isPresented: $showTermsOfService) {
            CreateAccountWebPageView(url: URL(string: "https://littlegrass.cc/app/fitscanai/terms.html")!, title: "Terms of Service")
        }
        .sheet(isPresented: $showPrivacyPolicy) {
            CreateAccountWebPageView(url: URL(string: "https://littlegrass.cc/app/fitscanai/privacy.html")!, title: "Privacy Policy")
        }
        .onAppear {
            // 统计
            XDTrackTool.shared.appear("创建账户页面")

            // 主动触发网络权限检查，确保用户在注册前就授权网络访问
            DispatchQueue.main.asyncAfter(deadline: .now() + 0.5) {
                networkManager.triggerNetworkPermissionCheck()
            }
        }
    }
    
    // 验证密码是否符合要求
    private func validatePassword(_ password: String) {
        // 至少6个字符
        hasEightChars = password.count >= 6
        
        // 包含数字
        hasNumbers = password.contains(where: { $0.isNumber })
        
        // 包含大写字母
        hasUppercase = password.contains(where: { $0.isUppercase })
        
        // 包含小写字母
        hasLowercase = password.contains(where: { $0.isLowercase })
        
        // 包含特殊字符
        let specialCharacters = CharacterSet(charactersIn: "`!@#$%^&*?()-+_=;':\",./<>")
        hasSpecialChars = password.contains { char in
            guard let scalar = String(char).unicodeScalars.first else { return false }
            return specialCharacters.contains(scalar)
        }
    }
    
    // 检查是否所有密码要求都已满足
    private func isAllRequirementsMet() -> Bool {
        return !fullName.isEmpty && 
               !email.isEmpty && 
               !verificationCode.isEmpty &&
               hasEightChars && 
               hasNumbers && 
               hasUppercase && 
               hasLowercase &&
               hasSpecialChars
    }
    
    // 发送验证码
    private func sendVerificationCode() {
        // 防重复
        if isSendingCode { return }
        // 验证邮箱格式
        if !isValidEmail(email) {
            alertTitle = "Invalid Email"
            alertMessage = "Please enter a valid email address"
            showAlert = true
            return
        }
        
        // 检查网络状态（SignUpView未做此拦截，但保留更友好的提示，不影响一致性）
        if !networkManager.isNetworkAvailable {
            alertTitle = "Network Error"
            alertMessage = "No internet connection available. Please check your network settings and try again."
            showAlert = true
            return
        }
        
        isLoading = true
        isSendingCode = true
        isGetCodePressed = true
        
        DispatchQueue.main.asyncAfter(deadline: .now() + 0.2) {
            self.isGetCodePressed = false
        }
        
        // 在发送验证码前强制重置会话状态，确保获取新的CSRF token和JSESSIONID
        // 这对于退出登录后的新用户注册非常重要
        print("🔄 注册页面发送验证码前重置会话状态")
        print("📧 准备为邮箱发送验证码: \(self.email)")
        
        // 先重置会话状态，等待完成后再发送验证码
        NetworkService.shared.resetSessionState { success in
            
            if success {
                print("✅ 会话状态重置完成，开始发送验证码")
            } else {
                print("⚠️ 会话状态重置失败，仍尝试发送验证码")
            }
            
            // 调用API发送验证码
            NetworkService.shared.sendVerificationCode(email: self.email) { result in
            self.isLoading = false
            self.isSendingCode = false
            
            switch result {
            case .success(let response):
                print("验证码发送成功: \(response)")
                
                // 开始倒计时
                self.startCountdown()
                
            case .failure(let error):
                // 处理错误
                self.alertTitle = "Verification Code Error"
                
                let nsError = error as NSError
                
                if nsError.localizedDescription.contains("已注册") || nsError.localizedDescription.contains("already registered") {
                    self.alertMessage = "This email is already registered. Please use another email."
                } else if nsError.localizedDescription.contains("验证码") || nsError.localizedDescription.contains("verification code") {
                    self.alertMessage = "Invalid verification code. Please check and try again."
                } else if nsError.localizedDescription.contains("密码") || nsError.localizedDescription.contains("password") {
                    if nsError.localizedDescription.contains("弱") || nsError.localizedDescription.contains("weak") {
                        self.alertMessage = "Please use a stronger password."
                    } else if nsError.localizedDescription.contains("不匹配") || nsError.localizedDescription.contains("match") {
                        self.alertMessage = "Password confirmation doesn't match."
                    } else {
                        self.alertMessage = "Password doesn't meet the security requirements."
                    }
                } else if nsError.code == 400 {
                    self.alertMessage = "Invalid request. Please check your information and try again."
                } else if nsError.code == 401 || nsError.code == 403 {
                    self.alertMessage = "Authorization error. Please try again later."
                } else if nsError.code == 404 {
                    self.alertMessage = "The service is currently unavailable. Please try again later."
                } else if nsError.code == 409 {
                    self.alertMessage = "This email address is already in use."
                } else if nsError.code == 429 {
                    self.alertMessage = "Too many attempts. Please try again later."
                } else if nsError.localizedDescription.contains("network") || nsError.localizedDescription.contains("网络") {
                    self.alertMessage = "Network connection error. Please check your internet connection."
                } else if nsError.localizedDescription.contains("timeout") || nsError.localizedDescription.contains("超时") {
                    self.alertMessage = "Request timeout. Please try again later."
                } else if nsError.localizedDescription.contains("server") || nsError.localizedDescription.contains("服务器") {
                    self.alertMessage = "Server error. Please try again later."
                } else {
                    self.alertMessage = "Registration failed. Please try again later."
                }
                
                self.showAlert = true
                print("验证码发送失败: \(error)")
            }
        }
        }
    }
    
    // 开始倒计时
    private func startCountdown() {
        isCountingDown = true
        countdownTime = 60
        
        timer = Timer.scheduledTimer(withTimeInterval: 1, repeats: true) { _ in
            if countdownTime > 0 {
                countdownTime -= 1
            } else {
                stopTimer()
                isCountingDown = false
            }
        }
    }
    
    // 停止计时器
    private func stopTimer() {
        timer?.invalidate()
        timer = nil
    }
    
    // 注册账号
    private func createAccount() {
        // 验证输入
        if fullName.isEmpty || email.isEmpty || verificationCode.isEmpty || password.isEmpty {
            alertTitle = "Input Error"
            alertMessage = "Please fill in all fields"
            showAlert = true
            return
        }
        
        // 验证邮箱格式
        if !isValidEmail(email) {
            alertTitle = "Invalid Email"
            alertMessage = "Please enter a valid email address"
            showAlert = true
            return
        }
        
        // 检查网络状态
        if !networkManager.isNetworkAvailable {
            alertTitle = "Network Error"
            alertMessage = "No internet connection available. Please check your network settings and try again."
            showAlert = true
            return
        }
        
        // 验证密码是否满足所有要求
        if !hasEightChars || !hasNumbers || !hasUppercase || !hasLowercase || !hasSpecialChars {
            alertTitle = "Weak Password"
            alertMessage = "Your password does not meet all requirements. Please choose a stronger password."
            showAlert = true
            return
        }
        
        // 开始加载
        isLoading = true
        isCreateAccountPressed = true
        
        DispatchQueue.main.asyncAfter(deadline: .now() + 0.2) {
            self.isCreateAccountPressed = false
        }
        
        // 第一步：注册用户
        NetworkService.shared.registerUser(nickname: fullName, email: email, password: password, code: verificationCode) { result in
            switch result {
            case .success(let registerResponse):
                print("✅ 注册成功: \(registerResponse)")
                
                // 第二步：使用邮箱和密码登录
                NetworkService.shared.directLogin(email: self.email, password: self.password) { loginResult in
            self.isLoading = false
            
                    switch loginResult {
            case .success(let loginData):
                        print("✅ 登录成功，获取到token")
                
                DispatchQueue.main.async {
                    // 保存登录状态
                    self.userData.saveLoginState(
                        userId: loginData.userId,
                        userNickname: self.fullName, // 使用用户输入的全名作为昵称
                        email: self.email,
                        accessToken: loginData.accessToken,
                        refreshToken: loginData.refreshToken,
                        expiresIn: loginData.expiresIn
                    )
                    // 仅从CreateAccount页面登录成功时显示第二个欢迎页
                    UserDefaults.standard.set(true, forKey: "showSecondWelcome")
                    // 绑定引导信息到当前用户（查询→创建/更新）
                    self.bindPendingOnboardingDataToAccount { _ in
                        // 绑定完成后进入主页
                        // 关闭CreateAccountView
                        self.presentationMode.wrappedValue.dismiss()
                        // 发送通知直接进入主页
                        NotificationCenter.default.post(name: Notification.Name("WelcomeGetStarted"), object: nil)
                    }
                        }
                        
                    case .failure(let loginError):
                        print("❌ 注册成功但登录失败: \(loginError.localizedDescription)")
                        
                        DispatchQueue.main.async {
                            self.alertTitle = "Login Failed"
                            self.alertMessage = "Account created successfully, but login failed. Please try to log in manually."
                            self.showAlert = true
                        }
                    }
                }
                
            case .failure(let error):
                self.isLoading = false
                
                // 注册失败
                print("❌ 注册失败: \(error.localizedDescription)")
                
                DispatchQueue.main.async {
                    let nsError = error as NSError
                    
                    // 特别处理账号已注册的情况
                    if nsError.code == 409 || nsError.localizedDescription.contains("already registered") {
                        self.alertTitle = "Email Already Registered"
                        self.alertMessage = "This email address is already registered. Please use a different email or try logging in."
                        self.showAlert = true
                        return
                    }
                    
                    // 其他错误情况
                    self.alertTitle = "Registration Failed"
                    
                    if nsError.localizedDescription.contains("验证码") || nsError.localizedDescription.contains("verification code") {
                        self.alertMessage = "Invalid verification code. Please check and try again."
                    } else if nsError.localizedDescription.contains("密码") || nsError.localizedDescription.contains("password") {
                        if nsError.localizedDescription.contains("弱") || nsError.localizedDescription.contains("weak") {
                            self.alertMessage = "Please use a stronger password."
                        } else if nsError.localizedDescription.contains("不匹配") || nsError.localizedDescription.contains("match") {
                            self.alertMessage = "Password confirmation doesn't match."
                        } else {
                            self.alertMessage = "Password doesn't meet the security requirements."
                        }
                    } else if nsError.code == 400 {
                        self.alertMessage = "Invalid request. Please check your information and try again."
                    } else if nsError.code == 401 || nsError.code == 403 {
                        self.alertMessage = "Authorization error. Please try again later."
                    } else if nsError.code == 404 {
                        self.alertMessage = "The service is currently unavailable. Please try again later."
                    } else if nsError.code == 429 {
                        self.alertMessage = "Too many attempts. Please try again later."
                    } else if nsError.localizedDescription.contains("network") || nsError.localizedDescription.contains("网络") {
                        self.alertMessage = "Network connection error. Please check your internet connection."
                    } else if nsError.localizedDescription.contains("timeout") || nsError.localizedDescription.contains("超时") {
                        self.alertMessage = "Request timeout. Please try again later."
                    } else if nsError.localizedDescription.contains("server") || nsError.localizedDescription.contains("服务器") {
                        self.alertMessage = "Server error. Please try again later."
                    } else {
                        self.alertMessage = "Registration failed. Please try again later."
                    }
                    
                    self.showAlert = true
                    print("注册失败: \(error)")
                }
            }
        }
    }
    
    // 验证邮箱格式
    private func isValidEmail(_ email: String) -> Bool {
        let emailRegEx = "[A-Z0-9a-z._%+-]+@[A-Za-z0-9.-]+\\.[A-Za-z]{2,64}"
        let emailPred = NSPredicate(format:"SELF MATCHES %@", emailRegEx)
        return emailPred.evaluate(with: email)
    }
    
    // 计算密码强度颜色
    private func passwordStrengthColor(for index: Int) -> Color {
        let metRequirements = [hasEightChars, hasNumbers, hasUppercase, hasLowercase, hasSpecialChars].filter { $0 }.count
        
        if index < metRequirements {
            switch metRequirements {
            case 1:
                return .red
            case 2, 3:
                return .orange
            case 4:
                return .yellow
            case 5:
                return .green
            default:
                return Color(.systemGray5)
            }
        } else {
            return Color(.systemGray5)
        }
    }
    
    // 密码强度文本
    private var passwordStrengthText: String {
        let metRequirements = [hasEightChars, hasNumbers, hasUppercase, hasLowercase, hasSpecialChars].filter { $0 }.count
        
        switch metRequirements {
        case 0:
            return ""
        case 1:
            return "Weak password"
        case 2, 3:
            return "Fair password"
        case 4:
            return "Good password"
        case 5:
            return "Strong password"
        default:
            return ""
        }
    }
    
    // 密码强度文本颜色
    private var passwordStrengthTextColor: Color {
        let metRequirements = [hasEightChars, hasNumbers, hasUppercase, hasLowercase, hasSpecialChars].filter { $0 }.count
        
        switch metRequirements {
        case 1:
            return .red
        case 2, 3:
            return .orange
        case 4:
            return .yellow
        case 5:
            return .green
        default:
            return .clear
        }
    }
    
    // 自适应间距
    private func adaptiveSpacing() -> CGFloat {
        if UIScreen.main.bounds.height < 700 {
            return 8
        } else {
            return 12
        }
    }
    
    // 自适应填充
    private func adaptivePadding() -> EdgeInsets {
        if UIScreen.main.bounds.height < 700 {
            return EdgeInsets(top: 8, leading: 8, bottom: 8, trailing: 8)
        } else {
            return EdgeInsets(top: 12, leading: 12, bottom: 12, trailing: 12)
        }
    }
    
    // 密码要求项
    private func passwordRequirementItem(isValid: Bool, text: String) -> some View {
        HStack(spacing: 4) {
            Image(systemName: isValid ? "checkmark.circle.fill" : "xmark.circle")
                .foregroundColor(isValid ? .green : .gray)
            
            Text(text)
                .font(.system(size: 14))
                .foregroundColor(isValid ? .green : .gray)
            
            Spacer()
        }
    }
    
    // 自适应条款间距
    private func adaptiveTermsSpacing() -> CGFloat {
        if UIScreen.main.bounds.height < 700 {
            return 8
        } else {
            return 12
        }
    }
    
    // 自适应条款字体大小
    private func adaptiveTermsFontSize() -> CGFloat {
        if UIScreen.main.bounds.height < 700 {
            return 12
        } else {
            return 16
        }
    }
}

// MARK: - 引导数据绑定逻辑
extension CreateAccountView {
    private func bindPendingOnboardingDataToAccount(completion: @escaping (Bool) -> Void) {
        // 检查是否有待绑定标记
        let hasPending = UserDefaults.standard.bool(forKey: "pendingOnboardingBinding")
        guard hasPending else { completion(true); return }
        
        // 需要有效token
        guard !userData.accessToken.isEmpty else { completion(false); return }
        
        // 从userData读取已暂存的引导信息
        let targetWeeks = userData.goalTimelineWeeks
        let age = userData.age
        let gender = userData.gender
        var targetWeight = userData.goalWeight
        var currentWeight = userData.currentManagementWeight
        var height = userData.height
        
        // 将数值转换为与用户单位一致，以配合服务内的单位转换逻辑
        if userData.weightUnit == "lbs" {
            targetWeight = targetWeight * 2.205
            currentWeight = currentWeight * 2.205
        }
        if userData.heightUnit == "ft" {
            height = height / 30.48
        }
        
        // 通过“查询→创建/更新”合并到账号
        UserPlanService.shared.createOrUpdateUserPlan(
            userData: userData,
            targetWeight: targetWeight,
            targetWeeks: max(1, targetWeeks),
            age: max(1, age),
            height: max(1.0, height),
            currentWeight: max(0.1, currentWeight),
            gender: gender.isEmpty ? "Other" : gender
        ) { result in
            DispatchQueue.main.async {
                switch result {
                case .success(_):
                    print("✅ 已将引导信息成功绑定到账号")
                    UserDefaults.standard.set(false, forKey: "pendingOnboardingBinding")
                    completion(true)
                case .failure(let error):
                    print("⚠️ 绑定引导信息失败: \(error.localizedDescription)")
                    // 即使失败也不阻塞进入主页
                    UserDefaults.standard.set(false, forKey: "pendingOnboardingBinding")
                    completion(false)
                }
            }
        }
    }
}

struct CreateAccountView_Previews: PreviewProvider {
    static var previews: some View {
        CreateAccountView()
    }
} 
