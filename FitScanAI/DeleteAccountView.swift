import SwiftUI

struct DeleteAccountView: View {
    @Environment(\.dismiss) private var dismiss
    @EnvironmentObject var userData: UserData
    @State private var password: String = ""
    @State private var selectedReason: String = "Select a reason"
    @State private var isLoading: Bool = false
    @State private var showAlert: Bool = false
    @State private var alertMessage: String = ""
    
    // 删除账户的可选理由（改为英文）
    private let reasons = [
        "Select a reason",
        "I found a better alternative",
        "App features don't meet my needs",
        "I no longer need this type of app",
        "App performance or stability issues",
        "Privacy concerns",
        "App is too complicated to use",
        "Other reason"
    ]
    
    var body: some View {
        ZStack(alignment: .bottom) {
            // 半透明黑色背景覆盖全屏
            Color.black.opacity(0.4)
                .ignoresSafeArea()
                .onTapGesture {
                    dismiss()
                }
            
            // 弹窗内容
            VStack(spacing: 16) {
                // 顶部小横条，可以用来拖动
                Capsule()
                    .fill(Color.gray.opacity(0.5))
                    .frame(width: 40, height: 5)
                    .padding(.top, 10)
                
                // 标题
                Text("Delete Account")
                    .font(.title3.bold())
                    .foregroundColor(.red)
                    .frame(maxWidth: .infinity, alignment: .leading)
                    .padding(.horizontal)
                    .padding(.top, 10)
                
                // 警告信息
                warningCard
                
                // 理由选择
                reasonSelectionSection
                
                // 密码确认
                passwordSection
                
                Spacer(minLength: 20)
                
                // 删除按钮
                deleteButton
                
                // 取消按钮
                cancelButton
                    .padding(.bottom, 20)
            }
            .padding(.horizontal)
            .frame(maxWidth: .infinity)
            .background(
                // 白色背景带圆角
                Color(UIColor.systemBackground)
                    .cornerRadius(20, corners: [.topLeft, .topRight])
                    .shadow(color: Color.black.opacity(0.1), radius: 10, x: 0, y: -5)
            )
            .edgesIgnoringSafeArea(.bottom)
        }
        .alert(isPresented: $showAlert) {
            Alert(
                title: Text("Account Operation"),
                message: Text(alertMessage),
                dismissButton: .default(Text("OK"))
            )
        }
        .onAppear {
            // 统计
            XDTrackTool.shared.appear("删除账户页面")
        }
    }
    
    // 警告卡片
    private var warningCard: some View {
        VStack(alignment: .leading, spacing: 8) {
            Text("Warning")
                .font(.headline)
                .foregroundColor(.primary)
            
            Text("This action cannot be undone. All your data will be permanently deleted.")
                .font(.subheadline)
                .foregroundColor(.secondary)
                .fixedSize(horizontal: false, vertical: true)
        }
        .padding()
        .frame(maxWidth: .infinity, alignment: .leading)
        .background(Color.red.opacity(0.1))
        .cornerRadius(10)
        .padding(.horizontal)
    }
    
    // 理由选择部分
    private var reasonSelectionSection: some View {
        VStack(alignment: .leading, spacing: 10) {
            Text("Why are you leaving?")
                .font(.headline)
                .foregroundColor(.primary)
                .padding(.horizontal)
            
            Menu {
                ForEach(reasons.filter { $0 != "Select a reason" }, id: \.self) { reason in
                    Button(action: {
                        selectedReason = reason
                    }) {
                        Text(reason)
                    }
                }
            } label: {
                HStack {
                    Text(selectedReason)
                        .foregroundColor(selectedReason == "Select a reason" ? .secondary : .primary)
                    Spacer()
                    Image(systemName: "chevron.down")
                        .foregroundColor(.secondary)
                        .font(.system(size: 14))
                }
                .padding()
                .background(Color(UIColor.secondarySystemBackground))
                .cornerRadius(10)
                .overlay(
                    RoundedRectangle(cornerRadius: 10)
                        .stroke(Color.gray.opacity(0.3), lineWidth: 1)
                )
            }
            .padding(.horizontal)
        }
    }
    
    // 密码确认部分
    private var passwordSection: some View {
        VStack(alignment: .leading, spacing: 10) {
            Text("Confirm your password")
                .font(.headline)
                .foregroundColor(.primary)
                .padding(.horizontal)
                .padding(.top, 5)
            
            SecureField("Enter your password", text: $password)
                .padding()
                .background(Color(UIColor.secondarySystemBackground))
                .cornerRadius(10)
                .overlay(
                    RoundedRectangle(cornerRadius: 10)
                        .stroke(Color.gray.opacity(0.3), lineWidth: 1)
                )
                .padding(.horizontal)
        }
    }
    
    // 删除按钮
    private var deleteButton: some View {
        Button(action: {
            deleteAccount()
        }) {
            Group {
                if isLoading {
                    ProgressView()
                        .progressViewStyle(CircularProgressViewStyle(tint: .white))
                } else {
                    Text("Delete Account")
                        .fontWeight(.semibold)
                }
            }
            .frame(maxWidth: .infinity)
            .padding(.vertical, 14)
            .background(Color(red: 0.95, green: 0.25, blue: 0.25))
            .foregroundColor(.white)
            .cornerRadius(10)
        }
        .padding(.horizontal)
        .disabled(password.isEmpty || selectedReason == "Select a reason" || isLoading)
        .opacity((password.isEmpty || selectedReason == "Select a reason" || isLoading) ? 0.6 : 1)
    }
    
    // 取消按钮
    private var cancelButton: some View {
        Button(action: {
            dismiss()
        }) {
            Text("Cancel")
                .fontWeight(.medium)
                .frame(maxWidth: .infinity)
                .padding(.vertical, 14)
                .foregroundColor(Color.gray)
        }
    }
    
    // 删除账户逻辑
    private func deleteAccount() {
        guard !password.isEmpty, selectedReason != "Select a reason" else { return }
        
        isLoading = true
        
        // 调用UserService的注销账户方法
        UserService.shared.deleteAccount(
            password: password,
            reason: selectedReason,
            userData: userData
        ) { result in
            DispatchQueue.main.async {
                self.isLoading = false
                
                switch result {
                case .success(_):
                    // 注销成功
                    self.alertMessage = "Your account has been successfully deleted. Thank you for using our service."
                    self.showAlert = true
                    
                    // 确保发送登出通知，让应用回到登录页面
                    NotificationCenter.default.post(name: Notification.Name("UserLoggedOut"), object: nil)
                    
                case .failure(let error):
                    // 注销失败
                    if let nsError = error as NSError? {
                        // 解析后端返回的具体错误信息
                        if let errorMessage = self.parseBackendErrorMessage(from: nsError) {
                            self.alertMessage = errorMessage
                        } else {
                            // 根据HTTP状态码提供友好的错误提示
                            switch nsError.code {
                            case 400:
                                self.alertMessage = "Invalid request. Please check your password and try again."
                            case 401:
                                self.alertMessage = "Authentication failed. The password you entered is incorrect."
                            case 403:
                                self.alertMessage = "Access denied. You don't have permission to delete this account."
                            case 404:
                                self.alertMessage = "Account not found. Please contact support."
                            case 409:
                                self.alertMessage = "Account deletion conflict. Please try again later."
                            case 422:
                                self.alertMessage = "Invalid account data. Please contact support."
                            case 429:
                                self.alertMessage = "Too many deletion attempts. Please wait a few minutes before trying again."
                            case 500:
                                self.alertMessage = "Server error occurred. Please try again later or contact support."
                            case 503:
                                self.alertMessage = "Service temporarily unavailable. Please try again later."
                            default:
                                if nsError.localizedDescription.contains("password") || nsError.localizedDescription.contains("密码") {
                                    self.alertMessage = "The password you entered is incorrect. Please try again."
                                } else if nsError.localizedDescription.contains("network") || nsError.localizedDescription.contains("connection") {
                                    self.alertMessage = "Network connection error. Please check your internet connection and try again."
                                } else {
                                    self.alertMessage = "Account deletion failed. Please try again later."
                                }
                            }
                        }
                    } else {
                        self.alertMessage = "Account deletion failed. Please try again later."
                    }
                    self.showAlert = true
                }
            }
        }
    }
    
    // 解析后端返回的错误信息
    private func parseBackendErrorMessage(from error: NSError) -> String? {
        let errorDescription = error.localizedDescription
        
        // 尝试从错误描述中提取JSON格式的错误信息
        if let data = errorDescription.data(using: .utf8),
           let json = try? JSONSerialization.jsonObject(with: data) as? [String: Any] {
            
            // 检查常见的错误字段
            if let message = json["message"] as? String {
                return translateErrorMessage(message)
            } else if let error = json["error"] as? String {
                return translateErrorMessage(error)
            } else if let detail = json["detail"] as? String {
                return translateErrorMessage(detail)
            }
        }
        
        // 尝试从错误描述中提取包含"响应内容"的部分
        if errorDescription.contains("响应内容:") {
            let components = errorDescription.components(separatedBy: "响应内容:")
            if components.count > 1 {
                let responseContent = components[1].trimmingCharacters(in: .whitespacesAndNewlines)
                if let data = responseContent.data(using: .utf8),
                   let json = try? JSONSerialization.jsonObject(with: data) as? [String: Any] {
                    
                    if let message = json["message"] as? String {
                        return translateErrorMessage(message)
                    } else if let error = json["error"] as? String {
                        return translateErrorMessage(error)
                    }
                }
            }
        }
        
        return nil
    }
    
    // 翻译错误信息为用户友好的英文提示
    private func translateErrorMessage(_ message: String) -> String {
        let lowercaseMessage = message.lowercased()
        
        if lowercaseMessage.contains("password") || lowercaseMessage.contains("密码") {
            return "The password you entered is incorrect. Please try again."
        } else if lowercaseMessage.contains("unauthorized") || lowercaseMessage.contains("未授权") {
            return "Authentication failed. Please log in again."
        } else if lowercaseMessage.contains("forbidden") || lowercaseMessage.contains("禁止") {
            return "Access denied. You don't have permission to delete this account."
        } else if lowercaseMessage.contains("not found") || lowercaseMessage.contains("未找到") {
            return "Account not found. Please contact support."
        } else if lowercaseMessage.contains("too many") || lowercaseMessage.contains("频繁") {
            return "Too many attempts. Please wait a few minutes before trying again."
        } else if lowercaseMessage.contains("server error") || lowercaseMessage.contains("服务器错误") {
            return "Server error occurred. Please try again later."
        } else if lowercaseMessage.contains("conflict") || lowercaseMessage.contains("冲突") {
            return "Account deletion conflict. Please try again later."
        } else {
            // 如果无法识别具体错误，返回原始消息（如果是英文）或通用提示
            if message.range(of: "[a-zA-Z]", options: .regularExpression) != nil {
                return message
            } else {
                return "Account deletion failed. Please try again later."
            }
        }
    }
}

// 扩展View来支持指定角落的圆角
extension View {
    func cornerRadius(_ radius: CGFloat, corners: UIRectCorner) -> some View {
        clipShape(RoundedCorner(radius: radius, corners: corners))
    }
}

// 自定义形状来创建特定角落的圆角
struct RoundedCorner: Shape {
    var radius: CGFloat = .infinity
    var corners: UIRectCorner = .allCorners

    func path(in rect: CGRect) -> Path {
        let path = UIBezierPath(roundedRect: rect, byRoundingCorners: corners, cornerRadii: CGSize(width: radius, height: radius))
        return Path(path.cgPath)
    }
}

#Preview {
    DeleteAccountView()
        .environmentObject(UserData())
} 