import SwiftUI
import Charts
import WebKit

// 运动记录模型
struct ExerciseRecord: Identifiable, Codable {
    var id = UUID()
    var type: String        // 运动类型，如"Running"、"Cycling"等
    var duration: Int       // 运动时长，单位为分钟
    var intensity: String   // 运动强度："Light"、"Moderate"或"Intense"
    var calories: Int       // 消耗的卡路里
    var date: Date          // 运动日期时间
    var timeString: String  // 格式化后的时间字符串，用于显示（如"8:00"，来自API的createdTime）
    var createdAt: Date = Date() // 添加时间戳，用于排序
    
    // 添加CodingKeys以确保UUID正确编码解码
    enum CodingKeys: String, CodingKey {
        case id, type, duration, intensity, calories, date, timeString, createdAt
    }
    
    // 自定义初始化方法确保id被正确解码
    init(from decoder: Decoder) throws {
        let container = try decoder.container(keyedBy: CodingKeys.self)
        id = try container.decode(UUID.self, forKey: .id)
        type = try container.decode(String.self, forKey: .type)
        duration = try container.decode(Int.self, forKey: .duration)
        intensity = try container.decode(String.self, forKey: .intensity)
        calories = try container.decode(Int.self, forKey: .calories)
        date = try container.decode(Date.self, forKey: .date)
        timeString = try container.decode(String.self, forKey: .timeString)
        createdAt = try container.decodeIfPresent(Date.self, forKey: .createdAt) ?? date // 兼容旧数据
    }
    
    // 普通初始化方法
    init(type: String, duration: Int, intensity: String, calories: Int, date: Date, timeString: String, id: UUID = UUID(), createdAt: Date = Date()) {
        self.id = id
        self.type = type
        self.duration = duration
        self.intensity = intensity
        self.calories = calories
        self.date = date
        self.timeString = timeString
        self.createdAt = createdAt
    }
}

// 辅助扩展：为ExerciseAPIService添加WeightView页面需要的方法
extension ExerciseAPIService {
    // 获取某一天的运动记录
    func fetchExerciseRecords(userData: UserData, date: Date, completion: @escaping ([ExerciseRecord]) -> Void) {
        // 调用API获取指定日期的运动记录
        getUserExerciseRecords(userData: userData, date: date) { result in
            switch result {
            case .success(let records):
                print("✅ [Weight] 成功获取\(records.count)条当日运动记录")
                // 使用userData中更新后的本地记录
                
                // 获取今天的日期范围
                let calendar = Calendar.current
                let startOfDay = calendar.startOfDay(for: date)
                let endOfDay = calendar.date(byAdding: .day, value: 1, to: startOfDay)!
                
                // 过滤当天的记录
                let todayRecords = userData.exerciseRecords.filter { record in
                    let recordDate = record.date
                    return recordDate >= startOfDay && recordDate < endOfDay
                }
                
                // 确保按创建时间排序
                let sortedRecords = todayRecords.sorted(by: { $0.createdAt > $1.createdAt })
                
                completion(sortedRecords)
                
            case .failure(let error):
                print("❌ [Weight] 获取运动记录失败: \(error.localizedDescription)")
                
                // 失败时尝试从本地获取
                let calendar = Calendar.current
                let startOfDay = calendar.startOfDay(for: date)
                let endOfDay = calendar.date(byAdding: .day, value: 1, to: startOfDay)!
                
                // 过滤当天的记录
                let todayRecords = userData.exerciseRecords.filter { record in
                    let recordDate = record.date
                    return recordDate >= startOfDay && recordDate < endOfDay
                }
                
                // 确保按创建时间排序
                let sortedRecords = todayRecords.sorted(by: { $0.createdAt > $1.createdAt })
                
                completion(sortedRecords)
            }
        }
    }
    
    // 获取用户的每日卡路里目标
    func fetchDailyCalorieTarget(userData: UserData, completion: @escaping (Int) -> Void) {
        // 检查用户是否设置了目标体重和时间
        guard userData.hasPlan,
              userData.goalTimelineWeeks > 0,
              userData.morningWeight > 0,
              userData.goalWeight > 0 else {
            // 如果用户未设置目标，返回默认值或已保存的值
            let goalValue = userData.dailyCalorieBurnGoal > 0 ? userData.dailyCalorieBurnGoal : 1200
            completion(goalValue)
            return
        }
        
        // 计算每日所需燃烧的卡路里
        // 这里使用一个简单的公式：每减少1kg体重需要燃烧约7700卡路里
        let currentWeight = userData.morningWeight
        let targetWeight = userData.goalWeight
        let weightDifference = abs(currentWeight - targetWeight) // 体重差值(kg)
        let totalCaloriesToBurn = weightDifference * 7700.0 // 总共需要燃烧的卡路里
        
        // 安全检查：确保goalTimelineWeeks大于0
        let weeks = max(1, userData.goalTimelineWeeks) // 确保至少为1周
        let daysToGoal = Double(weeks) * 7.0 // 目标天数
        
        // 计算每日需要燃烧的卡路里
        let dailyCaloriesBurnDouble = totalCaloriesToBurn / daysToGoal
        
        // 检查计算结果是否有效
        var dailyCalorieBurnGoal: Int
        if dailyCaloriesBurnDouble.isNaN || dailyCaloriesBurnDouble.isInfinite || dailyCaloriesBurnDouble < 500 {
            dailyCalorieBurnGoal = 1200 // 默认值
        } else {
            dailyCalorieBurnGoal = Int(dailyCaloriesBurnDouble)
            
            // 添加合理的上限，防止计算结果过大
            if dailyCalorieBurnGoal > 10000 {
                dailyCalorieBurnGoal = 2500 // 合理的大值
            }
        }
        
        // 将计算结果保存到userData
        userData.dailyCalorieBurnGoal = dailyCalorieBurnGoal
        userData.saveSettings()
        
        completion(dailyCalorieBurnGoal)
    }
    
    // 获取一周内的运动记录
    func fetchWeeklyExerciseRecords(userData: UserData, startDate: Date, endDate: Date, completion: @escaping ([ExerciseRecord], Int, Int) -> Void) {
        // 调用API获取所有运动记录（实际项目中可能需要分页或限制日期范围）
        getUserExerciseRecords(userData: userData, date: nil) { result in
            switch result {
            case .success(_):
                print("✅ [Weight] 成功获取周运动记录")
                
                // 从userData过滤指定日期范围内的记录
                let weeklyRecords = userData.exerciseRecords.filter { record in
                    return record.date >= startDate && record.date < endDate
                }
                
                // 计算本周已燃烧的卡路里总量
                let totalCaloriesBurned = weeklyRecords.reduce(0) { $0 + $1.calories }
                
                // 获取每日目标并计算周目标
                let dailyTarget = userData.dailyCalorieBurnGoal
                let weeklyTarget = dailyTarget * 7
                
                completion(weeklyRecords, totalCaloriesBurned, weeklyTarget)
                
            case .failure(let error):
                print("❌ [Weight] 获取周运动记录失败: \(error.localizedDescription)")
                
                // 失败时使用本地数据
                // 从userData过滤指定日期范围内的记录
                let weeklyRecords = userData.exerciseRecords.filter { record in
                    return record.date >= startDate && record.date < endDate
                }
                
                // 计算本周已燃烧的卡路里总量
                let totalCaloriesBurned = weeklyRecords.reduce(0) { $0 + $1.calories }
                
                // 获取每日目标并计算周目标
                let dailyTarget = userData.dailyCalorieBurnGoal
                let weeklyTarget = dailyTarget * 7
                
                completion(weeklyRecords, totalCaloriesBurned, weeklyTarget)
            }
        }
    }
}

struct WeightView: View {
    @EnvironmentObject var userData: UserData
    @StateObject private var planAdviceService = PlanAdviceService.shared
    @State private var showGoalEditView = false
    @State private var showWeightInputPopup = false
    @State private var showAddExercise = false
    @State private var showExerciseDetail = false
    @State private var selectedWeightTime: WeightView.WeightTime = .morning
    @State private var isLoading = false
    @State private var selectedDate: Date = Date()
    @State private var dailyWeightRecords: [WeightRecord] = []
    @State private var showHealthInfoSheet = false
    
    private func convertToDisplayUnit(kgValue: Double) -> Double {
        if userData.weightUnit == "lbs" {
            return kgValue * 2.20462
        }
        return kgValue
    }
    
    private var morningWeightString: String {
        let value = convertToDisplayUnit(kgValue: userData.morningWeight)
        return String(format: "%.1f", value)
    }
    
    private var eveningWeightString: String {
        let value = convertToDisplayUnit(kgValue: userData.eveningWeight)
        return String(format: "%.1f", value)
    }
    
    private var goalWeightString: String {
        let value = convertToDisplayUnit(kgValue: userData.goalWeight)
        return String(format: "%.1f", value)
    }
    
    private var weightToGoString: String {
        let value = convertToDisplayUnit(kgValue: userData.weightToGoal())
        return String(format: "%.1f", value)
    }
    
    private var weightUnit: String {
        return userData.weightUnit
    }
    
    enum WeightTime {
        case morning
        case evening
    }
    
    var body: some View {
        ZStack {
            if userData.isGuestMode {
                // 访客模式显示特殊UI
                GuestWeightView()
                    .environmentObject(userData)
            } else {
                ScrollView {
                    WeightViewContent(
                        showGoalEditView: $showGoalEditView,
                        showWeightInputPopup: $showWeightInputPopup,
                        showAddExercise: $showAddExercise,
                        showExerciseDetail: $showExerciseDetail,
                        selectedWeightTime: $selectedWeightTime,
                        isLoading: $isLoading,
                        selectedDate: $selectedDate,
                        morningWeightString: morningWeightString,
                        eveningWeightString: eveningWeightString,
                        goalWeightString: goalWeightString,
                        weightToGoString: weightToGoString,
                        weightUnit: weightUnit,
                        calculateProgress: calculateProgress,
                        showDailyWeightRecordView: showDailyWeightRecordView,
                        planAdviceService: planAdviceService,
                        showHealthInfoSheet: $showHealthInfoSheet
                    )
                    .environmentObject(userData)
                }
            }
            
            // 各种弹出层
            if showWeightInputPopup {
                WeightInputPopup(
                    isShowing: $showWeightInputPopup,
                    weightTime: selectedWeightTime,
                    initialWeight: selectedWeightTime == .morning ? morningWeightString : eveningWeightString,
                    initialUnit: weightUnit
                ) { weight, unit in
                    // 保存体重数据
                    saveWeightData(
                        weight: weight,
                        unit: unit,
                        isMorning: selectedWeightTime == .morning
                    )
                }
            }
        }
        .overlay(
            Group {
                if showAddExercise {
                    AddExerciseView(isShowing: $showAddExercise, selectedDate: Date(), onSave: { record in
                        // 通知其他页面刷新 - 确保在主线程上发送通知
                        DispatchQueue.main.async {
                            NotificationCenter.default.post(name: NSNotification.Name("RefreshExerciseData"), object: nil)
                            NotificationCenter.default.post(name: Notification.Name("ExerciseDataUpdated"), object: nil)
                        }
                    })
                    .environmentObject(userData)
                }
            }
        )
        .onAppear {
            // 统计
            XDTrackTool.shared.appear("体重页面")

            loadWeightData()

            // 加载AI建议数据
            planAdviceService.loadAllAdvices()

            // 🔍 检查并更新订阅状态，确保显示正确的UI
            Task {
                await userData.checkAndUpdateSubscriptionStatus()
                print("🔍 [WeightView] onAppear - 订阅状态检查完毕: isPremium=\(userData.isPremium)")
            }
            
            // 添加ShowExerciseHistory通知监听
            NotificationCenter.default.addObserver(forName: Notification.Name("ShowExerciseHistory"), object: nil, queue: .main) { _ in
                self.showExerciseDetail = true
            }
            
            // 🆕 添加订阅状态更新通知监听，确保UI及时响应状态变化
            NotificationCenter.default.addObserver(forName: Notification.Name("SubscriptionStatusUpdated"), object: nil, queue: .main) { notification in
                if let userInfo = notification.userInfo,
                   let isPremium = userInfo["isPremium"] as? Bool {
                    print("🔍 [WeightView] 收到订阅状态更新通知: isPremium=\(isPremium)")
                    // 由于userData是@EnvironmentObject，状态已经自动更新，这里只是记录日志
                }
            }
        }
        .onDisappear {
            // 移除通知监听
            NotificationCenter.default.removeObserver(self, name: Notification.Name("ShowExerciseHistory"), object: nil)
            NotificationCenter.default.removeObserver(self, name: Notification.Name("SubscriptionStatusUpdated"), object: nil)
        }
        // 修改通知响应，从显示弹窗改为发送导航通知
        .onReceive(NotificationCenter.default.publisher(for: Notification.Name("ShowWeightGoalEdit"))) { _ in
            // 发送导航通知，使用统一的导航方式
            NotificationCenter.default.post(name: Notification.Name("DirectShowWeightGoalEdit"), object: nil)
        }
        .sheet(isPresented: $showHealthInfoSheet) {
            WebPageView(url: URL(string: "https://fsai.pickgoodspro.com/fitScanAiDoc.html")!, title: "Authoritative Sources of Health Information")
        }
    }
    
    // 保存体重数据到"后端"
    private func saveWeightData(date: Date = Date(), weight: Double, unit: String, isMorning: Bool) {
        isLoading = true
        print("开始保存体重数据: 体重=\(weight) \(unit), 日期=\(date), 是早晨=\(isMorning)")
        
        // 转换到kg（如果后端总是使用kg存储）
        var weightInKg = weight
        if unit == "lbs" {
            weightInKg = userData.convertToKg(weight)
            print("转换体重从lbs到kg: \(weight)lbs -> \(weightInKg)kg")
        }
        
        // 将isMorning转换为timeType
        let timeType = isMorning ? "AM" : "PM"
        
        // 使用当前日期格式化为YYYYMMDD格式
        let dateFormatter = DateFormatter()
        dateFormatter.dateFormat = "yyyyMMdd"
        // 修改：使用selectedDate而不是当前日期，确保使用选择的日期创建记录
        let dateStr = dateFormatter.string(from: selectedDate)
        
        // 调用WeightRecordService创建体重记录
        WeightRecordService.shared.createWeightRecord(weight: weightInKg, timeType: timeType, dateStr: dateStr, userData: userData) { result in
            DispatchQueue.main.async {
                self.isLoading = false
                
                switch result {
                case .success(_):
                    print("成功保存体重记录到API")
                    
                // 更新本地数据
                if isMorning {
                        self.userData.morningWeight = weightInKg
                        print("更新早晨体重为: \(weightInKg)kg")
                        // 发送早晨体重更新通知
                        NotificationCenter.default.post(name: Notification.Name("MorningWeightUpdated"), object: nil)
                } else {
                        self.userData.eveningWeight = weightInKg
                        print("更新晚上体重为: \(weightInKg)kg")
                        // 发送晚上体重更新通知
                        NotificationCenter.default.post(name: Notification.Name("EveningWeightUpdated"), object: nil)
                }
                
                    // 重新加载体重数据以确保显示最新数据
                self.loadWeightData()
                
                // 确保在主线程上发送通知
                DispatchQueue.main.async {
                    // 发送通知，通知体重数据已更新，用于刷新Recent Changes组件和PlanView中的进度
                    NotificationCenter.default.post(name: Notification.Name("WeightDataUpdated"), object: nil)
                    print("已发送WeightDataUpdated通知")
                }
                    
                case .failure(let error):
                    print("创建体重记录失败: \(error.localizedDescription)")
                    // 可以在这里添加错误处理逻辑，例如显示错误提示
                }
            }
        }
    }
    
    // 从"后端"加载体重数据
    private func loadWeightData() {
        isLoading = true
        print("开始从API加载体重数据...")
        
        // 使用当前日期格式化为YYYYMMDD格式
        let dateFormatter = DateFormatter()
        dateFormatter.dateFormat = "yyyyMMdd"
        
        // 获取当前日期
        let currentDate = Date()
        _ = dateFormatter.string(from: currentDate)
        
        // 计算前30天的日期用于获取历史数据
        let calendar = Calendar.current
        let startDate = calendar.date(byAdding: .day, value: -30, to: currentDate) ?? currentDate
        
        // 使用日期范围获取30天的数据，确保折线图有完整数据
        WeightRecordService.shared.getUserWeightRecordsForDateRange(startDate: startDate, endDate: currentDate, userData: userData) { result in
            DispatchQueue.main.async {
                self.isLoading = false
                
                switch result {
                case .success(let apiRecords):
                    print("成功从API获取\(apiRecords.count)条体重记录")
                    
                    // 记录API返回的原始数据，方便调试
                    for (index, record) in apiRecords.enumerated() {
                        print("API原始记录[\(index)]: id=\(record.id ?? 0), 日期字符串=\(record.dateStr ?? "nil"), 创建时间=\(record.createdTime ?? "nil"), 体重=\(record.weight)kg, 类型=\(record.timeType)")
                    }
                    
                    // 将API返回的记录转换为应用内部使用的WeightRecord结构
                    var weightRecords: [WeightRecord] = []
                    
                    for record in apiRecords {
                        if let date = record.getDate() {
                            // 根据timeType确定是早晨还是晚上的记录
                            let isMorning = record.timeType == "AM"
                            
                            // 检查体重值是否合理
                            if record.weight <= 0 {
                                print("警告: 记录体重值异常 (\(record.weight)kg)，已跳过")
                                continue
                            }
                            
                            // 创建内部使用的WeightRecord
                            let weightRecord = WeightRecord(date: date, weight: record.weight, isMorning: isMorning)
                            weightRecords.append(weightRecord)
                            
                            print("转换记录: 日期=\(date), 体重=\(record.weight)kg, 是早晨=\(isMorning), 来源日期字段=\(record.dateStr ?? record.createdTime ?? "未知")")
                        } else {
                            print("警告: 记录缺少有效日期或无法解析 (dateStr=\(record.dateStr ?? "nil"), createdTime=\(record.createdTime ?? "nil"))，已跳过")
                        }
                    }
                    
                    // 如果没有有效记录，显示警告
                    if weightRecords.isEmpty && !apiRecords.isEmpty {
                        print("警告: API返回了\(apiRecords.count)条记录，但没有一条可以成功转换为WeightRecord")
                    }
                    
                    // 过滤出所选日期的记录
                    let calendar = Calendar.current
                    self.dailyWeightRecords = weightRecords.filter { record in
                        return calendar.isDate(record.date, inSameDayAs: self.selectedDate)
                    }
                    
                    print("找到\(self.dailyWeightRecords.count)条与所选日期\(self.selectedDate)匹配的记录")
                    
                    // 关键修复：更新userData的weightHistory数组
                    self.userData.weightHistory = weightRecords
                    
                    print("已更新userData.weightHistory，现在有\(self.userData.weightHistory.count)条记录")
                    
                    // 打印今日记录，检查数据是否有效
                    let today = calendar.startOfDay(for: Date())
                    let todayRecords = weightRecords.filter { calendar.isDate($0.date, inSameDayAs: today) }
                    print("今日记录数量: \(todayRecords.count)")
                    
                    // 对当天记录按日期时间排序，确保获取最新的记录
                    let sortedTodayRecords = todayRecords.sorted(by: { $0.date > $1.date })
                    
                    // 更新早晨和晚上体重 - 如果今天没有记录则设置为0
                    if let morningRecord = sortedTodayRecords.first(where: { $0.isMorning }) {
                        self.userData.morningWeight = morningRecord.weight
                        print("更新今日早晨体重: \(morningRecord.weight)kg")
                    } else {
                        // 如果今天没有早晨记录，设置为0表示未打卡
                        self.userData.morningWeight = 0.0
                        print("今日无早晨体重记录，设置为0")
                    }
                    
                    // 更新今日晚上体重
                    if let eveningRecord = sortedTodayRecords.first(where: { !$0.isMorning }) {
                        self.userData.eveningWeight = eveningRecord.weight
                        print("更新今日晚上体重: \(eveningRecord.weight)kg")
                    } else {
                        // 如果今天没有晚上记录，设置为0表示未打卡
                        self.userData.eveningWeight = 0.0
                        print("今日无晚上体重记录，设置为0")
                    }
                    
                    // 保存更新后的数据
                    self.userData.saveSettings()
                    print("已保存用户数据")
                    
                    // 强制刷新视图 - 通过userData触发更新
                    self.userData.objectWillChange.send()
                    
                case .failure(let error):
                    print("加载体重记录失败: \(error.localizedDescription)")
                    // 可以在这里添加错误处理逻辑，例如显示错误提示
                }
            }
        }
    }
    
    // 计算进度条的值
    private func calculateProgress() -> Double {
        let currentWeight = userData.currentManagementWeight
        let startWeight = userData.startWeight
        let progress = (startWeight - currentWeight) / userData.weightToGoal()
        return min(max(progress, 0), 1) // 确保值在0-1之间
    }
    
    // 显示每日体重记录详情视图
    private func showDailyWeightRecordView() {
        selectedDate = Date() // 默认显示今天
        // 不再需要设置 showDailyRecordView = true，因为我们使用 NavigationLink 了
    }
    
    // 计算营养目标值
    private func calculateNutritionGoals(currentWeight: Double, goalWeight: Double, completion: @escaping (Int, Int) -> Void) {
        // 根据用户信息计算每日卡路里目标
        // 使用基础代谢率(BMR)计算公式：Mifflin-St Jeor方程
        let age = userData.age
        let height = userData.height // cm
        let gender = userData.gender
        let weightInKg = currentWeight
        
        // BMR计算 - 基础代谢率
        var bmr: Double = 0
        if gender == "Male" {
            bmr = (10 * weightInKg) + (6.25 * height) - (5 * Double(age)) + 5
        } else {
            bmr = (10 * weightInKg) + (6.25 * height) - (5 * Double(age)) - 161
        }
        
        // 根据是减重还是增重调整卡路里
        let isWeightLoss = goalWeight < currentWeight
        
        // 默认活动水平乘数 - 中等活动水平
        let activityMultiplier = 1.55
        
        // 总能量需求
        var dailyCalories = Int(bmr * activityMultiplier)
        
        // 如果是减重，减少15-20%的卡路里；如果是增重，增加15%的卡路里
        if isWeightLoss {
            dailyCalories = Int(Double(dailyCalories) * 0.85) // 减少15%
        } else if goalWeight > currentWeight {
            dailyCalories = Int(Double(dailyCalories) * 1.15) // 增加15%
        }
        
        // 根据每日卡路里计算蛋白质目标
        // 每公斤体重1.5-2克蛋白质是一个常见的健身建议
        let proteinPerKg = isWeightLoss ? 2.0 : 1.6 // 减重时更多蛋白质帮助保持肌肉
        let dailyProtein = Int(weightInKg * proteinPerKg)
        
        // 模拟API请求延迟
        DispatchQueue.main.asyncAfter(deadline: .now() + 0.5) {
            completion(dailyCalories, dailyProtein)
        }
    }
}

// 将WeightView的内容拆分为子视图以减轻编译器负担
struct WeightViewContent: View {
    @EnvironmentObject var userData: UserData
    @Binding var showGoalEditView: Bool
    @Binding var showWeightInputPopup: Bool
    @Binding var showAddExercise: Bool
    @Binding var showExerciseDetail: Bool
    @Binding var selectedWeightTime: WeightView.WeightTime
    @Binding var isLoading: Bool
    @Binding var selectedDate: Date
    
    let morningWeightString: String
    let eveningWeightString: String
    let goalWeightString: String
    let weightToGoString: String
    let weightUnit: String
    let calculateProgress: () -> Double
    let showDailyWeightRecordView: () -> Void
    let planAdviceService: PlanAdviceService
    @Binding var showHealthInfoSheet: Bool
    
    var body: some View {
        VStack(alignment: .leading, spacing: adaptiveSpacing()) {
            // 标题 - 适配小屏幕
            VStack(alignment: .leading, spacing: 5) {
                Text("Weight Management")
                    .font(adaptiveTitleFont())
                    .fontWeight(.bold)
                
                Text("Track your progress daily")
                    .font(adaptiveSubheadlineFont())
                    .foregroundColor(.secondary)
            }
            .padding(.horizontal, adaptiveHorizontalPadding())
            
            // 早晨和晚上体重记录
            UnifiedWeightEditSection(
                morningWeightString: morningWeightString,
                eveningWeightString: eveningWeightString,
                weightUnit: weightUnit,
                onMorningEdit: {
                    selectedWeightTime = .morning
                    showWeightInputPopup = true
                },
                onEveningEdit: {
                    selectedWeightTime = .evening
                    showWeightInputPopup = true
                },
                style: .detailed
            )
            
            // 目标体重区域
            GoalWeightSection(
                goalWeightString: goalWeightString,
                weightToGoString: weightToGoString,
                weightUnit: weightUnit,
                progress: calculateProgress()
            )
            
            // 添加今日运动组件
            TodayExerciseView(
                showAddExercise: $showAddExercise,
                showExerciseDetail: $showExerciseDetail,
                planAdviceService: planAdviceService
            )
            .environmentObject(userData)
            
            // 日建议区域 - 移动到30天趋势图之前
            AIRecommendationsSection(
                recommendations: planAdviceService.getDailyAdviceTexts(),
                exerciseBurnGoal: Double(planAdviceService.getSafeDailyExerciseBurnGoal()),
                userData: userData
            )
            
            // 添加30天体重趋势图 - 移动到AI建议之后
            WeightTrendSection(
                isLoading: isLoading,
                userData: userData,
                onMoreTap: showDailyWeightRecordView
            )
            
            // 最近4天体重变化组件
            RecentWeightChangesSection(
                userData: userData
            )
            
            // 底部间距
            Spacer().frame(height: adaptiveBottomSpacing())
            
            // 添加底部文本和下划线
            VStack(spacing: 8) {
                HStack(spacing: 0) {
                    Text("View ")
                        .font(.system(size: 14, weight: .light))
                        .foregroundColor(Color(red: 0.42, green: 0.45, blue: 0.5))
                    
                    Text("Authoritative Sources of Health Information")
                        .font(.system(size: 14, weight: .light))
                        .foregroundColor(Color(red: 0.42, green: 0.45, blue: 0.5))
                        .underline()
                        .onTapGesture {
                            showHealthInfoSheet = true
                        }
                }
                .frame(maxWidth: .infinity)
                .multilineTextAlignment(.center)
            }
            .padding(.bottom, 20)
        }
    }
    
    // 适配函数
    private func adaptiveSpacing() -> CGFloat {
        if UIScreen.main.bounds.height < 700 {
            return 15 // iPhone 12 mini等小屏
        } else {
            return 20 // 大屏
        }
    }
    
    private func adaptiveTitleFont() -> Font {
        if UIScreen.main.bounds.height < 700 {
            return .title2 // 小屏使用稍小的标题
        } else {
            return .title // 大屏使用标准标题
        }
    }
    
    private func adaptiveSubheadlineFont() -> Font {
        if UIScreen.main.bounds.height < 700 {
            return .caption // 小屏使用更小的副标题
        } else {
            return .subheadline // 大屏使用标准副标题
        }
    }
    
    private func adaptiveHorizontalPadding() -> CGFloat {
        if UIScreen.main.bounds.width < 375 {
            return 12 // iPhone 12 mini等小屏
        } else {
            return 16 // 大屏
        }
    }
    
    private func adaptiveBottomSpacing() -> CGFloat {
        if UIScreen.main.bounds.height < 700 {
            return 15 // 小屏减少底部间距
        } else {
            return 20 // 大屏保持标准间距
        }
    }
}

// 统一的早晨和晚上体重记录组件框架
struct UnifiedWeightEditSection: View {
    let morningWeightString: String
    let eveningWeightString: String
    let weightUnit: String
    let onMorningEdit: () -> Void
    let onEveningEdit: () -> Void
    let style: WeightEditStyle
    
    enum WeightEditStyle {
        case compact  // Weight页面使用的紧凑样式
        case detailed // 详细折线图页面使用的详细样式
    }
    
    var body: some View {
        VStack(spacing: 0) {
            // 早晨体重
            HStack {
                // 图标和标签
                HStack(spacing: iconSpacing) {
                    Image(systemName: "sun.max.fill")
                        .foregroundColor(.yellow)
                        .font(iconFont)
                    VStack(alignment: .leading, spacing: 2) {
                        Text("Morning Weight (Fasting)")
                            .font(labelFont)
                            .lineLimit(1)
                            .minimumScaleFactor(0.8)
                        
                        Text("\(morningWeightString) \(weightUnit)")
                            .font(weightFont)
                            .fontWeight(.bold)
                        
                        Text("7:30 AM")
                            .font(timeFont)
                            .foregroundColor(.secondary)
                    }
                }
                
                Spacer()
                
                // 编辑按钮
                Button(action: onMorningEdit) {
                    Image(systemName: "pencil.circle")
                        .foregroundColor(.green)
                        .font(buttonFont)
                }
            }
            .padding(cardPadding)
            
            // 分割线
            Divider()
                .padding(.horizontal, cardPadding.leading)
            
            // 晚上体重
            HStack {
                // 图标和标签
                HStack(spacing: iconSpacing) {
                    Image(systemName: "moon.fill")
                        .foregroundColor(.blue)
                        .font(iconFont)
                    VStack(alignment: .leading, spacing: 2) {
                        Text("Evening Weight (Before bed)")
                            .font(labelFont)
                            .lineLimit(1)
                            .minimumScaleFactor(0.8)
                        
                        Text("\(eveningWeightString) \(weightUnit)")
                            .font(weightFont)
                            .fontWeight(.bold)
                        
                        Text("10:30 PM")
                            .font(timeFont)
                            .foregroundColor(.secondary)
                    }
                }
                
                Spacer()
                
                // 编辑按钮
                Button(action: onEveningEdit) {
                    Image(systemName: "pencil.circle")
                        .foregroundColor(.green)
                        .font(buttonFont)
                }
            }
            .padding(cardPadding)
        }
        .background(Color.white)
        .cornerRadius(12)
        .shadow(color: Color.black.opacity(0.1), radius: 4, x: 0, y: 2)
        .padding(.horizontal, horizontalPadding)
    }
    
    // 根据样式计算适配值
    private var iconSpacing: CGFloat {
        switch style {
        case .compact:
            return UIScreen.main.bounds.width < 375 ? 8 : 12
        case .detailed:
            return 12
        }
    }
    
    private var iconFont: Font {
        switch style {
        case .compact:
            return UIScreen.main.bounds.height < 700 ? .system(size: 18) : .system(size: 22)
        case .detailed:
            return .system(size: 22)
        }
    }
    
    private var labelFont: Font {
        switch style {
        case .compact:
            return UIScreen.main.bounds.width < 375 ? .caption : .subheadline
        case .detailed:
            return .subheadline
        }
    }
    
    private var weightFont: Font {
        switch style {
        case .compact:
            return UIScreen.main.bounds.height < 700 ? .title2 : .title
        case .detailed:
            return .title
        }
    }
    
    private var timeFont: Font {
        switch style {
        case .compact:
            return UIScreen.main.bounds.height < 700 ? .caption2 : .caption
        case .detailed:
            return .caption
        }
    }
    
    private var buttonFont: Font {
        switch style {
        case .compact:
            return UIScreen.main.bounds.height < 700 ? .title3 : .title2
        case .detailed:
            return .title2
        }
    }
    
    private var cardPadding: EdgeInsets {
        switch style {
        case .compact:
            return UIScreen.main.bounds.height < 700 ? 
                EdgeInsets(top: 12, leading: 12, bottom: 12, trailing: 12) :
                EdgeInsets(top: 16, leading: 16, bottom: 16, trailing: 16)
        case .detailed:
            return EdgeInsets(top: 16, leading: 16, bottom: 16, trailing: 16)
        }
    }
    
    private var horizontalPadding: CGFloat {
        switch style {
        case .compact:
            return UIScreen.main.bounds.width < 375 ? 12 : 16
        case .detailed:
            return 16
        }
    }
}

// 目标体重部分
struct GoalWeightSection: View {
    @EnvironmentObject var userData: UserData
    let goalWeightString: String
    let weightToGoString: String
    let weightUnit: String
    let progress: Double
    
    var body: some View {
        NavigationLink(destination: GoalWeightFullScreenView().environmentObject(userData)) {
            VStack(alignment: .leading, spacing: 12) {
                HStack {
                    Text("Goal Weight")
                        .font(.headline)
                        .foregroundColor(.white)
                    
                    Spacer()
                    
                    Image(systemName: "pencil")
                        .foregroundColor(.white)
                }
                
                Text("\(goalWeightString) \(weightUnit)")
                    .font(.system(size: 36, weight: .bold))
                    .foregroundColor(.white)
                
                Text("\(weightToGoString) \(weightUnit) to go")
                    .font(.subheadline)
                    .foregroundColor(.white.opacity(0.8))
                
                ProgressView(value: progress)
                    .progressViewStyle(LinearProgressViewStyle(tint: .white))
            }
            .padding()
            .background(
                LinearGradient(gradient: Gradient(colors: [.green, .green.opacity(0.7)]), startPoint: .leading, endPoint: .trailing)
            )
            .cornerRadius(15)
            .shadow(color: Color.black.opacity(0.1), radius: 5, x: 0, y: 3)
            .padding(.horizontal)
        }
    }
}

// 体重趋势图部分
struct WeightTrendSection: View {
    let isLoading: Bool
    let userData: UserData
    let onMoreTap: () -> Void
    
    var body: some View {
        // 根据订阅状态显示不同的UI
        if userData.isPremium {
            // Premium用户显示完整的30天体重趋势图
            VStack(alignment: .leading, spacing: 15) {
                HStack {
                    Text("30-Day Weight Trend")
                        .font(.headline)
                    
                    Spacer()
                    
                    NavigationLink(destination: DailyWeightRecordFullScreenView().environmentObject(userData)) {
                        HStack(spacing: 5) {
                            Text("more")
                                .font(.subheadline)
                                .foregroundColor(.secondary)
                            
                            Image(systemName: "chevron.right")
                                .font(.caption)
                                .foregroundColor(.secondary)
                        }
                    }
                }
                .padding(.horizontal)
                
                if isLoading {
                    HStack {
                        Spacer()
                        ProgressView()
                            .progressViewStyle(CircularProgressViewStyle())
                        Spacer()
                    }
                    .padding()
                    .frame(height: 250)
                    .background(Color.white)
                    .cornerRadius(12)
                    .shadow(color: Color.black.opacity(0.1), radius: 4, x: 0, y: 2)
                    .padding(.horizontal)
                } else {
                    WeightTrendChart(userData: userData)
                        .frame(height: 250)
                        .padding(.vertical, 10)
                        .padding(.leading, 5) // 向左偏移留出更多空间给右侧
                        .padding(.trailing, 15) // 右侧留出更多空间显示30标签
                        .background(Color.white)
                        .cornerRadius(12)
                        .shadow(color: Color.black.opacity(0.1), radius: 4, x: 0, y: 2)
                        .padding(.horizontal)
                }
            }
        } else {
            // 非Premium用户显示锁定的30天图表UI
            Weight30DayChartPremiumView()
                .environmentObject(userData)
        }
    }
}

// 日建议部分
struct AIRecommendationsSection: View {
    let recommendations: [String]
    let exerciseBurnGoal: Double
    @ObservedObject var userData: UserData
    
    var body: some View {
        // 根据订阅状态显示不同的UI
        if userData.isPremium {
            // Premium用户显示完整的日建议
            VStack(alignment: .leading, spacing: 15) {
                Text("Today's Recommendations")
                    .font(.headline)
                    .padding(.horizontal)
                
                VStack(alignment: .leading, spacing: 12) {
                    // 🆕 检查是否有建议数据
                    if recommendations.isEmpty {
                        // 无数据时显示提示文本（与Plan页面周建议组件设计一致）
                        Text("No Today's recommendations available")
                            .font(.subheadline)
                            .foregroundColor(.secondary)
                            .frame(maxWidth: .infinity, alignment: .center)
                            .padding()
                    } else {
                        // 显示日建议文本
                        ForEach(recommendations, id: \.self) { recommendation in
                            HStack(alignment: .top, spacing: 10) {
                                Image(systemName: "checkmark.circle.fill")
                                    .foregroundColor(.green)
                                    .font(.system(size: 16))
                                    .frame(width: 16, height: 16)
                                
                                Text(recommendation)
                                    .font(.subheadline)
                                    .fixedSize(horizontal: false, vertical: true)
                                    .frame(maxWidth: .infinity, alignment: .leading)
                            }
                            .frame(maxWidth: .infinity, alignment: .leading)
                        }
                    }
                }
                .padding()
                .frame(maxWidth: .infinity, alignment: .leading)
                .background(Color.white)
                .cornerRadius(12)
                .shadow(color: Color.black.opacity(0.1), radius: 4, x: 0, y: 2)
                .padding(.horizontal)
            }
        } else {
            // 非Premium用户显示锁定的日建议UI
            WeightAIRecommendationsPremiumView()
                .environmentObject(userData)
        }
    }
}

// 体重输入弹窗
struct WeightInputPopup: View {
    @EnvironmentObject var userData: UserData
    @Binding var isShowing: Bool
    let weightTime: WeightView.WeightTime
    @State private var weightText: String
    @State private var selectedUnit: String
    @State private var number = ""
    
    let onSave: (Double, String) -> Void
    
    init(isShowing: Binding<Bool>, weightTime: WeightView.WeightTime, initialWeight: String, initialUnit: String, onSave: @escaping (Double, String) -> Void) {
        self._isShowing = isShowing
        self.weightTime = weightTime
        // 如果初始值是"0.0"，改为"0"以便用户可以直接开始输入
        let adjustedInitialWeight = initialWeight == "0.0" ? "0" : initialWeight
        self._weightText = State(initialValue: adjustedInitialWeight)
        self._selectedUnit = State(initialValue: initialUnit)
        self.onSave = onSave
    }
    
    var body: some View {
        ZStack {
            // 半透明背景
            Color.black.opacity(0.4)
                .edgesIgnoringSafeArea(.all)
                .onTapGesture {
                    isShowing = false
                }
            
            // 弹窗内容
            VStack(spacing: 20) {
                // 标题
                HStack {
                    Text("Enter Weight")
                        .font(.headline)
                    
                    Spacer()
                    
                    Button(action: {
                        isShowing = false
                    }) {
                        Image(systemName: "xmark")
                            .foregroundColor(.secondary)
                    }
                }
                
                // 重量显示区域
                HStack(alignment: .firstTextBaseline) {
                    Spacer()
                    
                    Text(weightText)
                        .font(.system(size: 48, weight: .bold))
                    
                    // 单位选择
                    Menu {
                        Button("kg") {
                            if selectedUnit != "kg" {
                                // 如果当前是lbs，转换显示的数值
                                if let weight = Double(weightText) {
                                    let kgWeight = weight / 2.20462
                                    weightText = String(format: "%.1f", kgWeight)
                                }
                                selectedUnit = "kg"
                                // 同步到UserData
                                userData.weightUnit = "kg"
                                userData.saveSettings()
                            }
                        }
                        
                        Button("lbs") {
                            if selectedUnit != "lbs" {
                                // 如果当前是kg，转换显示的数值
                                if let weight = Double(weightText) {
                                    let lbsWeight = weight * 2.20462
                                    weightText = String(format: "%.1f", lbsWeight)
                                }
                                selectedUnit = "lbs"
                                // 同步到UserData
                                userData.weightUnit = "lbs"
                                userData.saveSettings()
                            }
                        }
                    } label: {
                        HStack {
                            Text(selectedUnit)
                                .font(.title2)
                                .foregroundColor(.green)
                            
                            Image(systemName: "chevron.down")
                                .foregroundColor(.green)
                        }
                    }
                    
                    Spacer()
                }
                .padding(.vertical, 20)
                
                // 数字键盘
                VStack(spacing: 15) {
                    HStack(spacing: 30) {
                        numberButton("1")
                        numberButton("2")
                        numberButton("3")
                    }
                    .frame(height: 60)
                    
                    HStack(spacing: 30) {
                        numberButton("4")
                        numberButton("5")
                        numberButton("6")
                    }
                    .frame(height: 60)
                    
                    HStack(spacing: 30) {
                        numberButton("7")
                        numberButton("8")
                        numberButton("9")
                    }
                    .frame(height: 60)
                    
                    HStack(spacing: 30) {
                        numberButton(".")
                        numberButton("0")
                        
                        // 删除按钮
                        Button(action: {
                            if !weightText.isEmpty {
                                weightText.removeLast()
                                if weightText.isEmpty {
                                    weightText = "0"
                                }
                            }
                        }) {
                            Image(systemName: "delete.left")
                                .font(.title)
                                .frame(width: 60, height: 60)
                                .foregroundColor(.primary)
                        }
                    }
                    .frame(height: 60)
                }
                
                // 确认按钮
                Button(action: {
                    if let weight = Double(weightText) {
                        // 确保全局单位设置已更新
                        userData.weightUnit = selectedUnit
                        userData.saveSettings()
                        
                        // 通知单位变更
                        onSave(weight, selectedUnit)
                        isShowing = false
                    }
                }) {
                    Text("Confirm")
                        .font(.headline)
                        .foregroundColor(.white)
                        .frame(maxWidth: .infinity)
                        .padding()
                        .background(Color.green)
                        .cornerRadius(10)
                }
            }
            .padding()
            .background(Color(UIColor.systemBackground))
            .cornerRadius(16)
            .padding(30)
        }
    }
    
    // 数字按钮
    func numberButton(_ number: String) -> some View {
        Button(action: {
            if number == "." {
                // 添加小数点的逻辑需要和其他键盘保持一致
                if !weightText.contains(".") {
                    // 检查整数部分是否不超过3位
                    let parts = weightText.split(separator: ".")
                    if parts.count <= 1 && (parts.isEmpty || parts[0].count <= 3) {
                        weightText += number
                    }
                }
            } else {
                // 当值为"0.0"时，如果输入数字，清空并重新开始
                if weightText == "0.0" && number.rangeOfCharacter(from: CharacterSet.decimalDigits) != nil {
                    weightText = number
                    return
                }
                
                // 如果当前只有"0"，则替换它
                if weightText == "0" {
                    weightText = number
                } else {
                    // 修复的输入限制逻辑：小数点前3位，小数点后1位
                    let parts = weightText.components(separatedBy: ".")
                    
                    if parts.count == 1 {
                        // 还没有小数点，检查整数部分是否已达到3位
                        if parts[0].count < 3 {
                            weightText += number
                        }
                    } else if parts.count == 2 {
                        // 已有小数点，检查小数部分是否已达到1位
                        if parts[1].count < 1 {
                            weightText += number
                        }
                    } else if parts.count == 0 {
                        // 空值情况
                        weightText += number
                    }
                }
            }
        }) {
            Text(number)
                .font(.title)
                .frame(width: 60, height: 60)
                .foregroundColor(.primary)
        }
    }
}

// 30天体重趋势图表组件
struct WeightTrendChart: View {
    @ObservedObject var userData: UserData
    @State private var refreshTrigger = UUID() // 添加刷新触发器
    
    // 表示一个体重数据点
    struct WeightDataPoint: Identifiable {
        var id = UUID()
        var day: Int
        var weight: Double
        var isMorning: Bool
        var date: Date
    }
    
    // 将数据点分组为连续的序列，以避免在缺少数据的地方连接折线
    private func groupConsecutiveData(_ data: [WeightDataPoint]) -> [[WeightDataPoint]] {
        guard !data.isEmpty else { return [] }
        
        var result: [[WeightDataPoint]] = []
        var currentGroup: [WeightDataPoint] = [data[0]]
        
        for i in 1..<data.count {
            // 如果当前点和前一个点是连续的（天数差为1），则添加到当前组
            if data[i].day - data[i-1].day == 1 {
                currentGroup.append(data[i])
            } else {
                // 否则，开始一个新组
                result.append(currentGroup)
                currentGroup = [data[i]]
            }
        }
        
        // 添加最后一组
        if !currentGroup.isEmpty {
            result.append(currentGroup)
        }
        
        return result
    }
    
    // 处理数据并准备图表数据点
    private var chartData: [WeightDataPoint] {
        // 使用refreshTrigger强制刷新计算，但不产生实际影响
        _ = refreshTrigger
        
        // 直接从userData.weightHistory获取数据，不依赖getFilteredWeightHistory
        let allHistory = userData.weightHistory
        
        // 如果没有记录，返回空数组
        if allHistory.isEmpty {
            print("WeightTrendChart: userData.weightHistory为空，没有体重记录")
            return []
        }
        
        // 获取过去30天的数据范围
        let calendar = Calendar.current
        let today = Date()
        let thirtyDaysAgo = calendar.date(byAdding: .day, value: -30, to: today) ?? today
        
        // 只保留过去30天内的记录
        let recentHistory = allHistory.filter { record in
            return record.date >= thirtyDaysAgo && record.date <= today
        }
        
        print("WeightTrendChart: 历史记录总数=\(allHistory.count), 最近30天记录数=\(recentHistory.count)")
        
        // BUG修复点: 不再因最近30天无数据而使用所有记录中最近的记录，因为这可能导致显示非选定日期范围的数据。
        // 如果DailyWeightRecordFullScreenView也依赖此chartData，它应该自己处理特定日期的数据获取。
        // 对于30天趋势图，如果最近30天没有数据，就应该显示无数据。
        let historyToUse = recentHistory
        
        // 如果 historyToUse 为空 (即最近30天没有数据)，直接返回空数组，由视图显示"无数据"。
        if historyToUse.isEmpty {
            print("WeightTrendChart: 最近30天没有记录 (historyToUse is empty)，将返回空数据点数组。")
            return []
        }
        
        // 按日期降序排序，最新的记录在前面
        let sortedHistory = historyToUse.sorted { $0.date > $1.date }
        
        // 日志输出记录情况
        print("WeightTrendChart: 处理记录前的总记录数: \(sortedHistory.count)")
        
        // 按日期分组，确保每天只保留最新的早晨和晚上记录
        var latestMorningRecordsByDay: [Date: WeightRecord] = [:]
        var latestEveningRecordsByDay: [Date: WeightRecord] = [:]
        
        for record in sortedHistory {
            if record.weight <= 0 {
                print("WeightTrendChart: 跳过无效的体重记录 \(record.weight)kg")
                continue
            }
            
            // 获取该记录的日期（只保留年月日）
            let recordDay = calendar.startOfDay(for: record.date)
            
            if record.isMorning {
                // 检查是否已有该日期的早晨记录
                if let existingRecord = latestMorningRecordsByDay[recordDay] {
                    // 如果已有记录，比较时间，保留最新的
                    if record.date > existingRecord.date {
                        print("WeightTrendChart: 更新\(recordDay)的早晨记录，新值: \(record.weight)kg")
                        latestMorningRecordsByDay[recordDay] = record
                    }
                } else {
                    // 如果该日期没有记录，直接添加
                    print("WeightTrendChart: 添加\(recordDay)的早晨记录: \(record.weight)kg")
                    latestMorningRecordsByDay[recordDay] = record
                }
            } else {
                // 晚上记录同理
                if let existingRecord = latestEveningRecordsByDay[recordDay] {
                    if record.date > existingRecord.date {
                        print("WeightTrendChart: 更新\(recordDay)的晚上记录，新值: \(record.weight)kg")
                        latestEveningRecordsByDay[recordDay] = record
            }
                } else {
                    // 如果该日期没有记录，直接添加
                    print("WeightTrendChart: 添加\(recordDay)的晚上记录: \(record.weight)kg")
                    latestEveningRecordsByDay[recordDay] = record
                }
            }
        }
        
        // 将字典值转换为数组
        let morningRecords = Array(latestMorningRecordsByDay.values)
        let eveningRecords = Array(latestEveningRecordsByDay.values)
        
        print("WeightTrendChart: 按日期去重后 - 早晨记录数量=\(morningRecords.count), 晚上记录数量=\(eveningRecords.count)")
        
        // 创建数据点
        var dataPoints: [WeightDataPoint] = []
        
        // 生成过去30天的日期数组
        var dates: [Date] = []
        let todayStart = calendar.startOfDay(for: today)
        
        // 生成从今天开始往过去30天的日期序列
        for i in 0..<30 {
            if let dayDate = calendar.date(byAdding: .day, value: -i, to: todayStart) {
                dates.append(dayDate)
            }
        }
        
        // 创建日期到日期索引(1-30)的映射，今天是1，30天前是30
        var dateToDay: [Date: Int] = [:]
        for (index, date) in dates.enumerated() {
            // 今天映射到1，依次往后
            dateToDay[date] = index + 1
            }
            
        print("WeightTrendChart: 生成了\(dates.count)个日期点作为X轴")
        
        // 添加早晨记录
        for record in morningRecords {
            let recordDate = calendar.startOfDay(for: record.date)
            if let day = dateToDay[recordDate] {
                let weight = userData.getWeightValue(record.weight)
                
                print("WeightTrendChart: 添加早晨数据点 day=\(day), weight=\(weight), date=\(record.date)")
                    dataPoints.append(
                        WeightDataPoint(
                            day: day,
                            weight: weight,
                            isMorning: true,
                            date: record.date
                        )
                    )
            }
        }
        
        // 添加晚上记录
        for record in eveningRecords {
            let recordDate = calendar.startOfDay(for: record.date)
            if let day = dateToDay[recordDate] {
                let weight = userData.getWeightValue(record.weight)
                
                print("WeightTrendChart: 添加晚上数据点 day=\(day), weight=\(weight), date=\(record.date)")
                    dataPoints.append(
                        WeightDataPoint(
                            day: day,
                            weight: weight,
                            isMorning: false,
                            date: record.date
                        )
                    )
            }
        }
        
        // 确保数据点按天排序
        let sortedPoints = dataPoints.sorted { $0.day < $1.day }
        print("WeightTrendChart: 最终生成\(sortedPoints.count)个数据点用于绘图")
        
        // BUG修复：移除当 sortedPoints 为空时，回退到显示当天体重的逻辑。
        // 如果 filtered/processed history 为空，chartData 应该就是空的，
        // 视图的 .body 部分应该处理 chartData.isEmpty 的情况并显示 "No Data"。
        // 这可以防止在选择没有数据的特定日期时，错误地显示了当天的数据。
        /*
        if sortedPoints.isEmpty && (userData.morningWeight > 0 || userData.eveningWeight > 0) {
            print("WeightTrendChart: 没有有效的数据点，添加当前体重作为单点 (此逻辑已移除)")
            // ... 原回退逻辑代码 ...
            return currentPoints
        }
        */
        
        return sortedPoints
    }
    
    // 获取Y轴的最小值和最大值
    private var yAxisRange: (min: Double, max: Double) {
        if chartData.isEmpty {
            // 默认范围围绕当前体重
            let baseWeight = userData.getWeightValue(userData.morningWeight)
            return (baseWeight - 3, baseWeight + 3)
        }
        
        let weights = chartData.map { $0.weight }
        
        // 如果只有一个点，创建一个合理的范围
        if weights.count == 1, let singleWeight = weights.first {
            return (singleWeight - 3, singleWeight + 3)
        }
        
        let minWeight = weights.min() ?? userData.getWeightValue(userData.morningWeight) - 3
        let maxWeight = weights.max() ?? userData.getWeightValue(userData.morningWeight) + 3
        
        // 计算范围并添加一些边距
        let range = maxWeight - minWeight
        let padding = max(1.0, range * 0.1) // 至少1单位或范围的10%
        
        return (minWeight - padding, maxWeight + padding)
    }
    
    var body: some View {
        VStack(alignment: .leading, spacing: 10) {
            if chartData.isEmpty {
                // 当没有数据时显示提示信息
                VStack(spacing: 15) {
                    Image(systemName: "chart.line.downtrend.xyaxis")
                        .font(.system(size: 40))
                        .foregroundColor(.secondary)
                        .padding(.top, 20)
                    
                    Text("No Weight Data Available")
                        .font(.headline)
                        .foregroundColor(.secondary)
                    
                    Text("Start recording your morning and evening weight to see your 30-day trend")
                        .font(.subheadline)
                        .foregroundColor(.secondary)
                        .multilineTextAlignment(.center)
                        .padding(.horizontal)
                        .padding(.bottom, 20)
                }
                .frame(maxWidth: .infinity, minHeight: 200)
            } else {
                Chart {
                    // 绘制早晨体重折线 - 使用分组方法
                    let morningData = chartData.filter { $0.isMorning }.sorted { $0.day < $1.day }
                    if !morningData.isEmpty {
                        // 按照连续的数据段分组绘制
                        let morningGroups = groupConsecutiveData(morningData)
                        ForEach(Array(morningGroups.enumerated()), id: \.0) { idx, group in
                            ForEach(group) { dataPoint in
                            LineMark(
                                x: .value("Day", dataPoint.day),
                                y: .value("Weight", dataPoint.weight)
                            )
                            .foregroundStyle(by: .value("Time", "Morning"))
                            .lineStyle(StrokeStyle(lineWidth: 2))
                            .interpolationMethod(.catmullRom)
                            }
                        }
                        
                        // 绘制数据点
                        ForEach(morningData) { dataPoint in
                            PointMark(
                                x: .value("Day", dataPoint.day),
                                y: .value("Weight", dataPoint.weight)
                            )
                            .foregroundStyle(by: .value("Time", "Morning"))
                            .symbolSize(20)
                            .annotation(position: .top) {
                                // 显示所有数据点的注释，不再只限于特定日期
                                    Text(String(format: "%.1f", dataPoint.weight))
                                        .font(.caption2)
                                        .foregroundColor(.green)
                            }
                        }
                    }
                    
                    // 绘制晚上体重折线 - 分组绘制避免连接不连续的点
                    let eveningData = chartData.filter { !$0.isMorning }.sorted { $0.day < $1.day }
                    if !eveningData.isEmpty {
                        // 按照连续的数据段分组绘制
                        let eveningGroups = groupConsecutiveData(eveningData)
                        ForEach(Array(eveningGroups.enumerated()), id: \.0) { idx, group in
                            ForEach(group) { dataPoint in
                            LineMark(
                                x: .value("Day", dataPoint.day),
                                y: .value("Weight", dataPoint.weight)
                            )
                            .foregroundStyle(by: .value("Time", "Evening"))
                            .lineStyle(StrokeStyle(lineWidth: 2))
                            .interpolationMethod(.catmullRom)
                            }
                        }
                        
                        // 绘制数据点
                        ForEach(eveningData) { dataPoint in
                            PointMark(
                                x: .value("Day", dataPoint.day),
                                y: .value("Weight", dataPoint.weight)
                            )
                            .foregroundStyle(by: .value("Time", "Evening"))
                            .symbolSize(20)
                            .annotation(position: .top) {
                                // 显示所有数据点的注释，不再只限于特定日期
                                    Text(String(format: "%.1f", dataPoint.weight))
                                        .font(.caption2)
                                        .foregroundColor(Color(red: 0.98, green: 0.85, blue: 0.35))
                            }
                        }
                    }
                }
                .chartForegroundStyleScale([
    "Morning": Color.green,
    "Evening": Color(red: 0.98, green: 0.85, blue: 0.35)
])
.chartXScale(domain: 1...31) // 扩展X轴范围到31，确保30标签能显示
.chartYScale(domain: yAxisRange.min...yAxisRange.max)
.chartXAxis {
    AxisMarks(values: [1, 5, 10, 15, 20, 25, 30]) { value in
        if let day = value.as(Int.self) {
            AxisValueLabel {
                // 确保始终显示标签，特别是30
                Text("\(day)")
                    .font(.caption)
                    .foregroundColor(.secondary)
                    .fixedSize() // 确保文本不被截断
            }
            .foregroundStyle(.secondary)
            AxisGridLine()
            AxisTick()
        }
    }
}
.chartYAxis {
    AxisMarks(position: .leading) { value in
        AxisValueLabel()
        AxisGridLine()
        AxisTick()
    }
}
.frame(minWidth: 0, maxWidth: .infinity, minHeight: 200) // 确保图表有足够宽度显示所有标签
.padding(.horizontal, 4) // 减少内边距但保留一些空间
.padding(.trailing, 16) // 右侧留出足够空间显示30标签

// 图例
HStack(spacing: 20) {
    Spacer()
    
    Text("Weight in \(userData.weightUnit)")
        .font(.caption)
        .foregroundColor(.secondary)
}
            }
        }
        .onReceive(NotificationCenter.default.publisher(for: Notification.Name("WeightDataUpdated"))) { _ in
            print("WeightTrendChart: 收到体重数据更新通知，刷新图表...")
            self.refreshTrigger = UUID() // 刷新触发器更新将强制视图重新计算
        }
    }
}

// 目标体重编辑视图
struct WeightGoalEditView: View {
    @Environment(\.presentationMode) var presentationMode
    @EnvironmentObject var userData: UserData
    
    @State private var goalWeight: Double = 0
    @State private var timelineWeeks: Double = 12
    @State private var gender: String = "Male"
    @State private var currentWeight: String = ""
    @State private var age: String = ""
    @State private var height: String = ""
    @State private var showWeightGoalEditPopup: Bool = false
    @State private var isLoading: Bool = false
    @State private var showErrorAlert: Bool = false
    @State private var errorMessage: String = ""
    
    var body: some View {
        VStack(spacing: 0) {
            // 标题栏
            HStack {
                Text("Goal Weight ment")
                    .font(.headline)
                
                Spacer()
                
                Button(action: {
                    presentationMode.wrappedValue.dismiss()
                }) {
                    Image(systemName: "xmark")
                        .foregroundColor(.primary)
                }
            }
            .padding()
            
            ScrollView {
                VStack(alignment: .leading, spacing: 25) {
                    // 体重目标
                    VStack(alignment: .leading, spacing: 10) {
                        Text("Weight Goal")
                            .font(.headline)
                        
                        HStack {
                            Spacer()
                            
                            Text("\(String(format: "%.1f", goalWeight)) \(userData.weightUnit)")
                                .font(.system(size: 32, weight: .bold))
                                .foregroundColor(.green)
                            
                            Spacer()
                            
                            Button(action: {
                                showWeightGoalEditPopup = true
                            }) {
                                Image(systemName: "pencil")
                                    .foregroundColor(.green)
                                    .padding()
                                    .background(Color(UIColor.secondarySystemBackground))
                                    .cornerRadius(10)
                            }
                        }
                        
                        Text("Set your target weight")
                            .font(.caption)
                            .foregroundColor(.secondary)
                    }
                    .padding(.horizontal)
                    
                    // 目标时间线
                    VStack(alignment: .leading, spacing: 10) {
                        Text("Your Goal Timeline")
                            .font(.headline)
                        
                        Slider(value: $timelineWeeks, in: 4...24, step: 1)
                            .accentColor(.green)
                        
                        HStack {
                            Spacer()
                            Text("\(Int(timelineWeeks)) weeks")
                                .font(.subheadline)
                                .foregroundColor(.secondary)
                        }
                    }
                    .padding(.horizontal)
                    
                    // 个人详情
                    VStack(alignment: .leading, spacing: 15) {
                        Text("Personal Details")
                            .font(.headline)
                        
                        // 性别选择
                        VStack(alignment: .leading, spacing: 8) {
                            Text("Gender")
                                .font(.subheadline)
                            
                            HStack(spacing: 20) {
                                // 男性
                                HStack {
                                    Circle()
                                        .fill(gender == "Male" ? Color.green : Color.gray.opacity(0.2))
                                        .frame(width: 20, height: 20)
                                        .overlay(
                                            gender == "Male" ?
                                            Circle().stroke(Color.green, lineWidth: 2) : nil
                                        )
                                    
                                    Text("Male")
                                        .foregroundColor(.primary)
                                }
                                .onTapGesture {
                                    gender = "Male"
                                }
                                
                                // 女性
                                HStack {
                                    Circle()
                                        .fill(gender == "Female" ? Color.green : Color.gray.opacity(0.2))
                                        .frame(width: 20, height: 20)
                                        .overlay(
                                            gender == "Female" ?
                                            Circle().stroke(Color.green, lineWidth: 2) : nil
                                        )
                                    
                                    Text("Female")
                                        .foregroundColor(.primary)
                                }
                                .onTapGesture {
                                    gender = "Female"
                                }
                                
                                // 其他
                                HStack {
                                    Circle()
                                        .fill(gender == "Other" ? Color.green : Color.gray.opacity(0.2))
                                        .frame(width: 20, height: 20)
                                        .overlay(
                                            gender == "Other" ?
                                            Circle().stroke(Color.green, lineWidth: 2) : nil
                                        )
                                    
                                    Text("Other")
                                        .foregroundColor(.primary)
                                }
                                .onTapGesture {
                                    gender = "Other"
                                }
                            }
                        }
                        
                        // 当前体重
                        VStack(alignment: .leading, spacing: 8) {
                            Text("Current Weight")
                                .font(.subheadline)
                            
                            HStack {
                                TextField("Enter weight", text: $currentWeight)
                                    .keyboardType(.decimalPad)
                                    .padding()
                                    .background(Color(UIColor.secondarySystemBackground))
                                    .cornerRadius(8)
                                    .onChange(of: currentWeight) { oldValue, newValue in
                                        currentWeight = newValue.limitedTo999_99()
                                    }
                                
                                Text(userData.weightUnit)
                                    .foregroundColor(.secondary)
                                    .padding(.leading, 8)
                            }
                        }
                        
                        // 年龄
                        VStack(alignment: .leading, spacing: 8) {
                            Text("Age")
                                .font(.subheadline)
                            
                            TextField("28", text: $age)
                                .keyboardType(.numberPad)
                                .padding()
                                .background(Color(UIColor.secondarySystemBackground))
                                .cornerRadius(8)
                                .onChange(of: age) { oldValue, newValue in
                                    // 年龄只允许整数，但也使用相同的限制逻辑
                                    let filtered = newValue.filter { "0123456789".contains($0) }
                                    let limited = String(filtered.prefix(3))
                                    if let intValue = Int(limited), intValue > 999 {
                                        age = "999"
                                    } else {
                                        age = limited
                                    }
                                }
                        }
                        
                        // 身高
                        VStack(alignment: .leading, spacing: 8) {
                            Text("height")
                                .font(.subheadline)
                            
                            HStack {
                                TextField("188", text: $height)
                                    .keyboardType(.decimalPad)
                                    .padding()
                                    .background(Color(UIColor.secondarySystemBackground))
                                    .cornerRadius(8)
                                    .onChange(of: height) { oldValue, newValue in
                                        height = newValue.limitedTo999_99()
                                    }
                                
                                Text(userData.heightUnit)
                                    .foregroundColor(.secondary)
                                    .padding(.leading, 8)
                            }
                        }
                    }
                    .padding(.horizontal)
                    
                    // 提示信息
                    HStack(spacing: 12) {
                        Image(systemName: "info.circle")
                            .foregroundColor(.green)
                        
                        Text("These details help us provide more accurate weight management recommendations")
                            .font(.caption)
                            .foregroundColor(.secondary)
                            .fixedSize(horizontal: false, vertical: true)
                            .frame(maxWidth: .infinity, alignment: .leading)
                            .lineLimit(nil) // 允许多行
                            .multilineTextAlignment(.leading) // 确保多行文本左对齐
                    }
                    .padding()
                    .frame(maxWidth: .infinity) // 确保HStack铺满整个宽度
                    .background(Color.green.opacity(0.1))
                    .cornerRadius(8)
                    .padding(.horizontal)
                    
                    // 保存按钮
                    Button(action: {
                        saveChanges() // 注意：dismiss已移至API回调中处理
                    }) {
                        Text("Save Changes")
                            .fontWeight(.medium)
                            .foregroundColor(.white)
                            .frame(maxWidth: .infinity)
                            .padding()
                            .background(Color.green)
                            .cornerRadius(10)
                    }
                    .padding()
                }
                .padding(.vertical)
            }
        }
        .onAppear {
            // 初始化表单数据
            initializeForm()
        }
        .overlay(
            ZStack {
                if showWeightGoalEditPopup {
                    WeightGoalInputPopup(
                        isShowing: $showWeightGoalEditPopup,
                        initialWeight: String(format: "%.1f", goalWeight),
                        initialUnit: userData.weightUnit,
                        onSave: { weight, unit in
                            if unit == userData.weightUnit {
                                goalWeight = weight
                            } else {
                                // 单位转换
                                if unit == "kg" && userData.weightUnit == "lbs" {
                                    goalWeight = weight * 2.20462
                                } else if unit == "lbs" && userData.weightUnit == "kg" {
                                    goalWeight = weight / 2.20462
                                } else {
                                    goalWeight = weight
                                }
                            }
                        }
                    )
                }
                
                // 移除了加载状态指示器，提升用户体验
            }
        )
        .alert(isPresented: $showErrorAlert) {
            Alert(
                title: Text("Error"),
                message: Text(errorMessage),
                dismissButton: .default(Text("OK"))
            )
        }
    }
    
    // 初始化表单数据
    private func initializeForm() {
        // 首先尝试从API获取最新计划
        if !userData.accessToken.isEmpty {
            fetchLatestPlanData()
        } else {
            // 如果没有访问令牌，回退到本地数据
            loadFromLocalData()
        }
    }
    
    // 获取最新计划数据
    private func fetchLatestPlanData() {
        print("正在获取最新计划数据...")
        UserPlanService.shared.getLatestPlan(userData: userData) { result in
            DispatchQueue.main.async {
                switch result {
                case .success(let planResponse):
                    print("成功获取最新计划数据：")
                    print("- 目标体重: \(planResponse.targetWeight)kg")
                    print("- 目标周数: \(planResponse.targetWeeks)周")
                    
                    // 设置目标体重
                    let weightValue = self.userData.getWeightValue(planResponse.targetWeight)
                    self.goalWeight = weightValue
                    
                    // 设置目标周期
                    self.timelineWeeks = Double(planResponse.targetWeeks)
                    
                    // 设置性别
                    if let apiGender = planResponse.gender {
                        switch apiGender {
                        case "BOY":
                            self.gender = "Male"
                        case "GIRL":
                            self.gender = "Female"
                        case "NONE":
                            self.gender = "Other"
                        default:
                            self.gender = "Other"
                        }
                    }
                    
                    // 设置当前体重
                    if let currentWeight = planResponse.currentWeight {
                        let currentWeightValue = self.userData.getWeightValue(currentWeight)
                        self.currentWeight = String(format: "%.1f", currentWeightValue)
                    }
                    
                    // 设置年龄
                    if let age = planResponse.age {
                        self.age = "\(age)"
                    }
                    
                    // 设置身高
                    if let height = planResponse.height {
                        let heightValue = self.userData.heightUnit == "cm" ? height : height / 2.54
                        self.height = String(format: "%.1f", heightValue)
                    }
                    
                case .failure(let error):
                    print("获取最新计划失败，使用本地数据: \(error.localizedDescription)")
                    self.loadFromLocalData()
                }
            }
        }
    }
    
    // 从本地数据加载
    private func loadFromLocalData() {
        // 加载目标体重 - 仅当用户有计划时
        if userData.hasPlan {
            let weightValue = userData.getWeightValue(userData.goalWeight)
            goalWeight = weightValue
        } else {
            // 用户没有计划，不设置默认值
            goalWeight = 0
        }
        
        // 加载目标周期 - 仅当用户有计划时
        if userData.hasPlan {
            timelineWeeks = Double(userData.goalTimelineWeeks)
        } else {
            // 用户没有计划，不设置默认值
            timelineWeeks = 12
        }
        
        // 加载性别 - 仅当用户有计划时
        if userData.hasPlan {
            gender = userData.gender
        } else {
            // 用户没有计划，不设置默认值
            gender = "Male"
        }
        
        // 加载当前体重 - 仅当用户有计划时
        if userData.hasPlan {
            let currentWeightValue = userData.getWeightValue(userData.initialWeight)
            currentWeight = String(format: "%.1f", currentWeightValue)
        } else {
            // 用户没有计划，不设置默认值
            currentWeight = ""
        }
        
        // 加载年龄 - 仅当用户有计划时
        if userData.hasPlan {
            age = "\(userData.age)"
        } else {
            // 用户没有计划，不设置默认值
            age = ""
        }
        
        // 加载身高 - 仅当用户有计划时
        if userData.hasPlan {
            let heightValue = userData.heightUnit == "cm" ? userData.height : userData.height / 2.54
            height = String(format: "%.1f", heightValue)
        } else {
            // 用户没有计划，不设置默认值
            height = ""
        }
    }
    
    // 保存更改
    private func saveChanges() {
        // 确认输入是有效的
        guard let currentWeightValue = Double(currentWeight),
              let ageValue = Int(age),
              let heightValue = Double(height) else {
            return
        }
        
        // 计算目标天数 (周数 * 7)
        _ = Int(timelineWeeks) * 7
        
        // 使用通用的创建或更新方法，它会根据情况自动选择正确的API端点
        UserPlanService.shared.createOrUpdateUserPlan(
                userData: userData, 
                targetWeight: goalWeight, 
                targetWeeks: Int(timelineWeeks), 
                age: ageValue, 
                height: heightValue, 
                currentWeight: currentWeightValue, 
                gender: gender
            ) { result in
                DispatchQueue.main.async {
                    switch result {
                    case .success(_):
                        print("用户计划创建/更新成功")
                        
                        // 设置用户已有计划
                        self.userData.hasPlan = true
                        self.userData.checkPlanStatus()
                        
                        // 发送通知，通知体重数据已更新，用于刷新PlanView中的进度
                        NotificationCenter.default.post(name: Notification.Name("WeightDataUpdated"), object: nil)
                        
                        // 额外发送通知，通知PlanView刷新
                        NotificationCenter.default.post(name: Notification.Name("RefreshPlanView"), object: nil)
                        
                        // 发送通知，告知目标体重已更新
                        NotificationCenter.default.post(name: Notification.Name("WeightGoalUpdated"), object: nil)
                        
                        // 退出页面
                        self.presentationMode.wrappedValue.dismiss()
                        
                    case .failure(let error):
                        print("❌ [WeightView] 计划创建/更新失败: \(error.localizedDescription)")
                        // 显示错误信息
                        self.errorMessage = "Unable to create or update plan: \(error.localizedDescription)"
                        self.showErrorAlert = true
                    }
                }
        }
    }
    
    // 注意：已移除本地保存逻辑，完全依赖后端API
}



#Preview {
    WeightView()
        .environmentObject(UserData())
}

// 日历日期选择器视图
struct DatePickerView: View {
    @Binding var isShowing: Bool
    @Binding var selectedDate: Date
    let userCreationDate: Date
    @State private var tempSelectedDate: Date
    
    init(isShowing: Binding<Bool>, selectedDate: Binding<Date>, userCreationDate: Date) {
        self._isShowing = isShowing
        self._selectedDate = selectedDate
        self.userCreationDate = userCreationDate
        self._tempSelectedDate = State(initialValue: selectedDate.wrappedValue)
    }
    
    var body: some View {
        ZStack {
            // 半透明背景
            Color.black.opacity(0.4)
                .edgesIgnoringSafeArea(.all)
                .onTapGesture {
                    isShowing = false
                }
            
            // 日历选择器
            VStack(spacing: 20) {
                // 标题
                HStack {
                    Text("Select Date")
                        .font(.headline)
                    
                    Spacer()
                    
                    Button(action: {
                        isShowing = false
                    }) {
                        Image(systemName: "xmark")
                            .foregroundColor(.secondary)
                    }
                }
                
                // 日历
                DatePicker(
                    "",
                    selection: $tempSelectedDate,
                    in: userCreationDate...Date(),
                    displayedComponents: [.date]
                )
                .datePickerStyle(GraphicalDatePickerStyle())
                .labelsHidden()
                .accentColor(.green)
                .tint(.green)
                
                // 按钮
                HStack {
                    // 取消按钮
                    Button(action: {
                        isShowing = false
                    }) {
                        Text("Cancel")
                            .frame(maxWidth: .infinity)
                            .padding()
                            .background(Color(UIColor.systemBackground))
                            .foregroundColor(.primary)
                            .cornerRadius(10)
                            .overlay(
                                RoundedRectangle(cornerRadius: 10)
                                    .stroke(Color.gray.opacity(0.2), lineWidth: 1)
                            )
                    }
                    
                    // 确认按钮
                    Button(action: {
                        selectedDate = tempSelectedDate
                        isShowing = false
                    }) {
                        Text("Confirm")
                            .frame(maxWidth: .infinity)
                            .padding()
                            .background(Color.green)
                            .foregroundColor(.white)
                            .cornerRadius(10)
                    }
                }
            }
            .padding()
            .background(Color(UIColor.systemBackground))
            .cornerRadius(16)
            .padding(.horizontal, 30)
        }
    }
}

// 每日体重记录API
class DailyWeightAPI {
    static let shared = DailyWeightAPI()
    
    private init() {}
    
    // 获取指定日期的体重记录
    func fetchDailyWeightRecords(date: Date, completion: @escaping ([LocalWeightRecord]) -> Void) {
        // 模拟网络请求延迟
        DispatchQueue.main.asyncAfter(deadline: .now() + 0.5) {
            // 在实际应用中，这里会从后端获取数据
            let calendar = Calendar.current
            let startOfDay = calendar.startOfDay(for: date)
            
            // 模拟数据 - 生成特定日期的早晨和晚上体重
            let seed = Double(startOfDay.timeIntervalSince1970 / 86400) // 使用日期作为随机种子
            let morningWeight = 65.0 + sin(seed * 0.1) * 3.0 // 生成波动的体重
            let eveningWeight = morningWeight + Double.random(in: 0.2...0.8) // 晚上略重
            
            var records: [LocalWeightRecord] = []
            
            // 添加早晨记录
            var morningDate = startOfDay
            morningDate = calendar.date(byAdding: .hour, value: 7, to: morningDate) ?? morningDate
            morningDate = calendar.date(byAdding: .minute, value: 30, to: morningDate) ?? morningDate
            records.append(LocalWeightRecord(date: morningDate, weight: morningWeight, isMorning: true))
            
            // 添加晚上记录
            var eveningDate = startOfDay
            eveningDate = calendar.date(byAdding: .hour, value: 22, to: eveningDate) ?? eveningDate
            eveningDate = calendar.date(byAdding: .minute, value: 30, to: eveningDate) ?? eveningDate
            records.append(LocalWeightRecord(date: eveningDate, weight: eveningWeight, isMorning: false))
            
            completion(records)
        }
    }
    
    // 保存体重记录
    func saveWeightRecord(date: Date, weight: Double, isMorning: Bool, unit: String, completion: @escaping (Bool) -> Void) {
        // 模拟网络请求延迟
        DispatchQueue.main.asyncAfter(deadline: .now() + 0.5) {
            // 在实际应用中，这里会发送网络请求到后端
            print("保存体重记录: \(weight) \(unit), 日期: \(date), 早晨: \(isMorning)")
            
            // 模拟成功响应
            completion(true)
        }
    }
}

// 今日运动视图组件
struct TodayExerciseView: View {
    @EnvironmentObject var userData: UserData
    @Binding var showAddExercise: Bool
    @Binding var showExerciseDetail: Bool
    @State private var todayExercises: [ExerciseRecord] = []
    @State private var totalCalories: Int = 0
    @State private var calorieGoal: Int = 1200
    @State private var isLoading = false
    // 添加变量来跟踪更新
    @State private var lastRefreshTime = Date()
    // 添加变量来跟踪用户计划状态
    @State private var isCheckingPlan = true
    @State private var userHasPlan = false
    
    // 添加planAdviceService参数
    let planAdviceService: PlanAdviceService
    
    var body: some View {
        VStack(alignment: .leading, spacing: 15) {
            HStack {
                Text("Today's Exercise")
                    .font(.headline)
                
                Spacer()
                
                if !todayExercises.isEmpty && !isCheckingPlan && userHasPlan {
                    NavigationLink(destination: ExerciseFullScreenView().environmentObject(userData)) {
                        HStack(spacing: 5) {
                            Text("more")
                                .font(.subheadline)
                                .foregroundColor(.secondary)
                            
                            Image(systemName: "chevron.right")
                                .font(.caption)
                                .foregroundColor(.secondary)
                        }
                    }
                }
            }
            .padding(.horizontal)
            
            if isCheckingPlan {
                // 加载中状态
                HStack {
                    Spacer()
                    ProgressView()
                    Spacer()
                }
                .padding()
                .background(Color.white)
                .cornerRadius(12)
                .shadow(color: Color.black.opacity(0.1), radius: 4, x: 0, y: 2)
                .padding(.horizontal)
            } else if !userHasPlan {
                // 用户未设置目标时显示占位界面
                VStack(spacing: adaptiveNoGoalSpacing()) {
                    // 显示问号和燃烧卡路里文本
                    HStack(alignment: .center, spacing: adaptiveNoGoalTextSpacing()) {
                        VStack(alignment: .leading, spacing: 4) {
                            HStack(alignment: .firstTextBaseline, spacing: 4) {
                                Text("? ?")
                                    .font(adaptiveNoGoalTitleFont())
                                    .foregroundColor(.gray)
                                
                                Text("/ ? ?")
                                    .font(adaptiveNoGoalSubtitleFont())
                                    .foregroundColor(.gray)
                            }
                            
                            Text("calories burned")
                                .font(adaptiveNoGoalCaptionFont())
                                .foregroundColor(.secondary)
                        }
                        
                        Spacer()
                        
                        // 问号圆圈
                        ZStack {
                            Circle()
                                .stroke(Color.gray.opacity(0.2), lineWidth: 4)
                                .frame(width: adaptiveCircleSize(), height: adaptiveCircleSize())
                            
                            Text("???")
                                .font(adaptiveCircleTextFont())
                                .fontWeight(.medium)
                                .foregroundColor(.gray)
                        }
                    }
                    
                    // 分隔线
                    Divider()
                        .padding(.horizontal, 0)
                    
                    // 显示三个分离的运动卡片
                    HStack(spacing: adaptiveCardSpacing()) {
                        exerciseCardView(iconName: "figure.walk", title: "Walking")
                        exerciseCardView(iconName: "figure.pool.swim", title: "Swimming")
                        exerciseCardView(iconName: "bicycle", title: "Cycling")
                    }
                    .padding(.vertical, adaptiveCardVerticalPadding())
                    
                    // Set Goal按钮
                    Button(action: {
                        navigateToGoalSetting()
                    }) {
                        Text("Set Goal")
                            .font(adaptiveButtonFont())
                            .foregroundColor(.white)
                            .frame(maxWidth: .infinity)
                            .padding(.vertical, adaptiveButtonVerticalPadding())
                            .background(Color.green)
                            .cornerRadius(25)
                    }
                    .padding(.top, 5)
                }
                .padding(adaptiveContainerPadding())
                .background(Color.white)
                .cornerRadius(12)
                .shadow(color: Color.black.opacity(0.1), radius: 4, x: 0, y: 2)
                .padding(.horizontal, adaptiveHorizontalPadding())
            } else if !userData.isPremium {
                // 非Premium用户显示Premium限制界面
                TodayExercisePremiumView()
                    .environmentObject(userData)
            } else if isLoading {
                HStack {
                    Spacer()
                    ProgressView()
                        .progressViewStyle(CircularProgressViewStyle())
                    Spacer()
                }
                .padding()
                .background(Color.white)
                .cornerRadius(12)
                .shadow(color: Color.black.opacity(0.1), radius: 4, x: 0, y: 2)
                .padding(.horizontal)
            } else if todayExercises.isEmpty {
                // 没有运动记录时显示提示
                VStack(spacing: 15) {
                    Image(systemName: "figure.walk")
                        .font(.system(size: 40))
                        .foregroundColor(.secondary)
                    
                    Text("No Exercise Recorded Today")
                        .font(.subheadline)
                        .foregroundColor(.secondary)
                    
                    Button(action: {
                        showAddExercise = true
                    }) {
                        Text("Add Exercise")
                            .font(.headline)
                            .foregroundColor(.white)
                            .padding(.horizontal, 30)
                            .padding(.vertical, 10)
                            .background(Color.green)
                            .cornerRadius(25)
                    }
                    .padding(.top, 10)
                }
                .frame(maxWidth: .infinity)
                .padding(.vertical, 20)
                .background(Color.white)
                .cornerRadius(12)
                .shadow(color: Color.black.opacity(0.1), radius: 4, x: 0, y: 2)
                .padding(.horizontal)
            } else {
                // 有运动记录时显示记录和圆环
                VStack(spacing: 12) {
                    // 卡路里和进度环
                    HStack(alignment: .firstTextBaseline)  {
                        VStack(alignment: .leading, spacing: 5) {
                            HStack(alignment: .firstTextBaseline, spacing: 4) {
                                Text("\(totalCalories)")
                                    .font(.system(size: 28, weight: .bold))
                                Text("/ \(calorieGoal)")
                                    .foregroundColor(.secondary)
                            }
                            Text("calories burned")
                                .font(.caption)
                                .foregroundColor(.secondary)
                        }
                        
                        Spacer()
                        
                        // 进度环
                        ZStack {
                            Circle()
                                .stroke(lineWidth: 8)
                                .opacity(0.3)
                                .foregroundColor(Color.green)
                            
                            Circle()
                                .trim(from: 0.0, to: calculateProgress(current: totalCalories, goal: calorieGoal))
                                .stroke(style: StrokeStyle(lineWidth: 8, lineCap: .round, lineJoin: .round))
                                .foregroundColor(Color.green)
                                .rotationEffect(Angle(degrees: 270.0))
                                .animation(.linear, value: totalCalories)
                            
                            Text(formatProgressPercentage(current: totalCalories, goal: calorieGoal))
                                .font(.system(.title3, design: .rounded))
                                .fontWeight(.bold)
                                .foregroundColor(.green)
                        }
                        .frame(width: 70, height: 70)
                    }
                    .padding(.horizontal)
                    .padding(.top, 10)
                    
                    Divider()
                        .padding(.horizontal)
                    
                    // 显示最近的两条运动记录
                    ForEach(Array(todayExercises.prefix(2).enumerated()), id: \.1.id) { index, exercise in
                        HStack(spacing: 15) {
                            // 运动图标
                            ZStack {
                                Circle()
                                    .fill(Color.green.opacity(0.2))
                                    .frame(width: 40, height: 40)
                                
                                Image(systemName: iconForExerciseType(exercise.type))
                                    .foregroundColor(.green)
                            }
                            
                            VStack(alignment: .leading, spacing: 3) {
                                Text(exercise.type)
                                    .font(.subheadline)
                                
                                HStack {
                                    Image(systemName: "clock")
                                        .font(.caption2)
                                        .foregroundColor(.secondary)
                                    
                                    Text("\(exercise.duration) min")
                                        .font(.caption)
                                        .foregroundColor(.secondary)
                                }
                            }
                            
                            Spacer()
                            
                            VStack(alignment: .trailing, spacing: 3) {
                                Text("\(exercise.calories) kcal")
                                    .font(.headline)
                                    .foregroundColor(.green)
                                
                                // 使用API返回的时间或默认时间
                                Text(exercise.timeString)
                                    .font(.caption)
                                    .foregroundColor(.secondary)
                            }
                        }
                        .padding(.horizontal)
                        .padding(.vertical, 8)
                        
                        // 添加分割线，除了最后一个项目
                        if index < todayExercises.prefix(2).count - 1 {
                            Divider()
                                .padding(.horizontal)
                        }
                    }
                    
                    // 添加运动按钮
                    Button(action: {
                        showAddExercise = true
                    }) {
                        HStack {
                            Spacer()
                            
                            Image(systemName: "plus")
                                .foregroundColor(.white)
                            
                            Text("Add Exercise")
                                .font(.headline)
                                .foregroundColor(.white)
                            
                            Spacer()
                        }
                        .padding(.vertical, 12)
                        .background(Color.green)
                        .foregroundColor(.white)
                        .cornerRadius(20)
                        .padding(.horizontal)
                        .padding(.top, 5)
                        .padding(.bottom, 10)
                    }
                }
                .background(Color.white)
                .cornerRadius(12)
                .shadow(color: Color.black.opacity(0.1), radius: 4, x: 0, y: 2)
                .padding(.horizontal)
            }
        }
        .onAppear {
            // 检查用户是否有计划，基于API响应而不是本地状态
            isCheckingPlan = true
            UserPlanService.shared.checkUserHasPlan(userData: userData) { hasPlan in
                DispatchQueue.main.async {
                    self.userHasPlan = hasPlan
                    self.isCheckingPlan = false
                    
                    // 如果有计划，再加载运动数据
                    if hasPlan {
                        loadTodayExercises()
                    }
                    
                    print("TodayExerciseView: API返回用户计划状态: hasPlan=\(hasPlan)")
                }
            }
            
            // 添加通知监听
            NotificationCenter.default.addObserver(forName: NSNotification.Name("RefreshExerciseData"), object: nil, queue: .main) { _ in
                self.lastRefreshTime = Date()
            }
            
            // 添加Plan页面发出的通知监听
            NotificationCenter.default.addObserver(forName: Notification.Name("ExerciseDataUpdated"), object: nil, queue: .main) { _ in
                self.lastRefreshTime = Date()
            }
            
            // 添加WeightGoalUpdated通知监听
            NotificationCenter.default.addObserver(forName: Notification.Name("WeightGoalUpdated"), object: nil, queue: .main) { _ in
                // 当用户设置或更新目标体重时，重新从API检查计划状态
                self.isCheckingPlan = true
                UserPlanService.shared.checkUserHasPlan(userData: userData) { hasPlan in
                    DispatchQueue.main.async {
                        self.userHasPlan = hasPlan
                        self.isCheckingPlan = false
                        
                        // 如果有计划，加载运动数据
                        if hasPlan {
                            self.loadTodayExercises()
                        }
                        
                        print("TodayExerciseView: Weight目标更新通知已处理, API返回hasPlan=\(hasPlan)")
                    }
                }
            }
        }
        .onDisappear {
            // 移除通知监听
            NotificationCenter.default.removeObserver(self, name: NSNotification.Name("RefreshExerciseData"), object: nil)
            NotificationCenter.default.removeObserver(self, name: Notification.Name("ExerciseDataUpdated"), object: nil)
            NotificationCenter.default.removeObserver(self, name: Notification.Name("WeightGoalUpdated"), object: nil)
        }
        .onChange(of: lastRefreshTime) { _, _ in
            // 当收到更新通知时，先检查用户是否有计划
            isCheckingPlan = true
            UserPlanService.shared.checkUserHasPlan(userData: userData) { hasPlan in
                DispatchQueue.main.async {
                    self.userHasPlan = hasPlan
                    self.isCheckingPlan = false
                    
                    // 如果有计划，再加载运动数据
                    if hasPlan {
                        loadTodayExercises()
                    }
                }
            }
        }
    }
    
    // 小屏幕适配函数
    private func adaptiveNoGoalSpacing() -> CGFloat {
        if UIScreen.main.bounds.height < 700 {
            return 12 // iPhone 12 mini等小屏
        } else {
            return 15 // 大屏
        }
    }
    
    private func adaptiveNoGoalTitleFont() -> Font {
        if UIScreen.main.bounds.height < 700 {
            return .system(size: 24, weight: .bold) // 小屏减小字体
        } else {
            return .system(size: 28, weight: .bold) // 大屏标准字体
        }
    }
    
    private func adaptiveNoGoalSubtitleFont() -> Font {
        if UIScreen.main.bounds.height < 700 {
            return .system(size: 16, weight: .medium) // 小屏
        } else {
            return .system(size: 18, weight: .medium) // 大屏
        }
    }
    
    private func adaptiveCircleSize() -> CGFloat {
        if UIScreen.main.bounds.height < 700 {
            return 45 // 小屏稍小的圆圈
        } else {
            return 50 // 大屏标准大小
        }
    }
    
    private func adaptiveCircleTextFont() -> Font {
        if UIScreen.main.bounds.height < 700 {
            return .system(.caption2, design: .rounded) // 小屏更小字体
        } else {
            return .system(.caption, design: .rounded) // 大屏标准字体
        }
    }
    
    private func adaptiveNoGoalCaptionFont() -> Font {
        if UIScreen.main.bounds.height < 700 {
            return .caption2 // 小屏更小字体
        } else {
            return .caption // 大屏标准字体
        }
    }
    
    private func adaptiveCardSpacing() -> CGFloat {
        if UIScreen.main.bounds.width < 375 {
            return 6 // iPhone 12 mini等小屏减少间距
        } else {
            return 8 // 大屏标准间距
        }
    }
    
    private func adaptiveCardVerticalPadding() -> CGFloat {
        if UIScreen.main.bounds.height < 700 {
            return 6 // 小屏减少内边距
        } else {
            return 8 // 大屏标准内边距
        }
    }
    
    private func adaptiveButtonFont() -> Font {
        if UIScreen.main.bounds.height < 700 {
            return .subheadline // 小屏使用较小字体
        } else {
            return .headline // 大屏使用标准字体
        }
    }
    
    private func adaptiveButtonVerticalPadding() -> CGFloat {
        if UIScreen.main.bounds.height < 700 {
            return 10 // 小屏减少按钮内边距
        } else {
            return 12 // 大屏标准内边距
        }
    }
    
    private func adaptiveContainerPadding() -> EdgeInsets {
        if UIScreen.main.bounds.height < 700 || UIScreen.main.bounds.width < 375 {
            return EdgeInsets(top: 12, leading: 12, bottom: 12, trailing: 12) // 小屏减少内边距
        } else {
            return EdgeInsets(top: 16, leading: 16, bottom: 16, trailing: 16) // 大屏标准内边距
        }
    }
    
    private func adaptiveHorizontalPadding() -> CGFloat {
        if UIScreen.main.bounds.width < 375 {
            return 12 // iPhone 12 mini等小屏
        } else {
            return 16 // 大屏
        }
    }
    
    // 新增：无计划时文本间距适配
    private func adaptiveNoGoalTextSpacing() -> CGFloat {
        if UIScreen.main.bounds.width < 375 {
            return 8 // iPhone 12 mini等小屏减少间距
        } else {
            return 12 // 大屏标准间距
        }
    }
    
    // 帮助创建运动图标的辅助视图
    private func exerciseIconView(iconName: String, title: String) -> some View {
        VStack(spacing: 5) {
            Image(systemName: iconName)
                .font(.system(size: 24))
                .foregroundColor(.gray)
                .frame(width: 40, height: 40)
            
            Text(title)
                .font(.caption)
                .foregroundColor(.gray)
        }
    }
    
    // 导航到目标设置页面
    private func navigateToGoalSetting() {
        // 发送通知让ContentView显示GoalWeightDetailView
        NotificationCenter.default.post(name: Notification.Name("DirectShowWeightGoalEdit"), object: nil)
    }
    
    // 根据运动类型返回对应的图标名称
    private func iconForExerciseType(_ type: String) -> String {
        switch type {
        case "Running":
            return "figure.run"
        case "Walking":
            return "figure.walk"
        case "Cycling":
            return "bicycle"
        case "Swimming":
            return "figure.pool.swim"
        case "Weight Training":
            return "dumbbell"
        case "Gym":
            return "dumbbell"
        case "Yoga":
            return "figure.yoga"
        case "Jump Rope":
            return "figure.jumprope"
        case "Basketball":
            return "basketball"
        case "Climbing":
            return "mountain.2"
        default:
            return "figure.walk"
        }
    }
    
    // 计算进度值（0.0-1.0）
    private func calculateProgress(current: Int, goal: Int) -> CGFloat {
        return CGFloat(min(Double(current) / Double(max(1, goal)), 1.0))
    }
    
    // 格式化进度百分比为文本
    private func formatProgressPercentage(current: Int, goal: Int) -> String {
        let percentage = Int(calculateProgress(current: current, goal: goal) * 100)
        return "\(percentage)%"
    }
    
    // 加载今日运动数据
    private func loadTodayExercises() {
        isLoading = true
        
        // 使用planAdviceService获取日燃脂目标（周数据/7）
        self.calorieGoal = planAdviceService.getSafeDailyExerciseBurnGoal()
        print("🏃‍♂️ [TodayExercise] 日燃脂目标已设为: \(self.calorieGoal) (从周数据除以7获得)")
        
        // 直接从userData获取今日运动记录，避免使用ExerciseAPI可能导致的数据不同步
        let calendar = Calendar.current
        let today = calendar.startOfDay(for: Date())
        let tomorrow = calendar.date(byAdding: .day, value: 1, to: today)!
        
        // 过滤今天的运动记录
        let todayRecords = self.userData.exerciseRecords.filter { record in
            let recordDate = record.date
            return recordDate >= today && recordDate < tomorrow
        }
        
        // 确保按创建时间降序排序，最新添加的运动会显示在前面
        self.todayExercises = todayRecords.sorted(by: { $0.createdAt > $1.createdAt })
        self.totalCalories = todayRecords.reduce(0) { $0 + $1.calories }
        self.isLoading = false
        
        print("🏃‍♂️ [TodayExercise] 今日运动统计: \(self.totalCalories)/\(self.calorieGoal) 卡路里, \(todayRecords.count)个运动")
    }
    
    // 创建运动卡片的辅助视图
    private func exerciseCardView(iconName: String, title: String) -> some View {
        VStack(alignment: .center, spacing: adaptiveCardInternalSpacing()) {
            Image(systemName: iconName)
                .font(adaptiveCardIconFont())
                .foregroundColor(.gray)
                .frame(height: adaptiveCardIconHeight())
            
            Text(title)
                .font(adaptiveCardTitleFont())
                .foregroundColor(.gray)
                .lineLimit(1)
                .frame(height: adaptiveCardTitleHeight())
        }
        .padding(.vertical, adaptiveCardInternalVerticalPadding())
        .frame(width: adaptiveCardWidth(), height: adaptiveCardHeight())
        .background(Color.gray.opacity(0.08))
        .cornerRadius(12)
    }
    
    // 卡片适配函数
    private func adaptiveCardInternalSpacing() -> CGFloat {
        if UIScreen.main.bounds.height < 700 {
            return 8 // 小屏减少内部间距
        } else {
            return 10 // 大屏标准间距
        }
    }
    
    private func adaptiveCardIconFont() -> Font {
        if UIScreen.main.bounds.height < 700 {
            return .system(size: 20) // 小屏较小图标
        } else {
            return .system(size: 24) // 大屏标准图标
        }
    }
    
    private func adaptiveCardIconHeight() -> CGFloat {
        if UIScreen.main.bounds.height < 700 {
            return 25 // 小屏较小高度
        } else {
            return 30 // 大屏标准高度
        }
    }
    
    private func adaptiveCardTitleFont() -> Font {
        if UIScreen.main.bounds.width < 375 {
            return .caption2 // 小屏更小字体
        } else {
            return .caption // 大屏标准字体
        }
    }
    
    private func adaptiveCardTitleHeight() -> CGFloat {
        return 15 // 固定高度
    }
    
    private func adaptiveCardInternalVerticalPadding() -> CGFloat {
        if UIScreen.main.bounds.height < 700 {
            return 10 // 小屏减少内边距
        } else {
            return 12 // 大屏标准内边距
        }
    }
    
    private func adaptiveCardWidth() -> CGFloat {
        if UIScreen.main.bounds.width < 375 {
            return 95 // iPhone 12 mini等小屏减少宽度
        } else {
            return 105 // 大屏标准宽度
        }
    }
    
    private func adaptiveCardHeight() -> CGFloat {
        if UIScreen.main.bounds.height < 700 {
            return 65 // 小屏减少高度
        } else {
            return 75 // 大屏标准高度
        }
    }
}

// 添加运动弹窗
struct AddExerciseView: View {
    @EnvironmentObject var userData: UserData
    @Binding var isShowing: Bool
    @State private var selectedExerciseType: String = "Running"
    @State private var hours: Int = 0
    @State private var minutes: Int = 0
    @State private var selectedIntensity: String = "Moderate"
    @State private var isLoading = false
    // 添加可选的日期参数，如果不提供则使用当前日期
    var selectedDate: Date?
    
    var onSave: ((ExerciseRecord) -> Void)?
    
    // 运动选项
    private let exerciseTypes = [
        "Running", "Cycling", "Swimming", 
        "Walking", "Gym", "Yoga",
        "Jump Rope", "Basketball", "Climbing"
    ]
    
    // 强度选项
    private let intensityLevels = ["Light", "Moderate", "Intense"]
    
    var body: some View {
        ZStack {
            // 半透明背景
            Color.black.opacity(0.4)
                .edgesIgnoringSafeArea(.all)
                .onTapGesture {
                    isShowing = false
                }
            
            // 弹窗内容
            VStack(spacing: 20) {
                // 标题和关闭按钮
                HStack {
                    Text("Add Exercise")
                        .font(.headline)
                    
                    Spacer()
                    
                    Button(action: {
                        isShowing = false
                    }) {
                        Image(systemName: "xmark")
                            .foregroundColor(.secondary)
                    }
                }
                .padding(.bottom, 10)
                
                ScrollView {
                    VStack(spacing: 25) {
                        // 运动类型选择
                        VStack(alignment: .leading, spacing: 10) {
                            Text("Exercise Type")
                                .font(.subheadline)
                                .foregroundColor(Color(red: 0.2, green: 0.2, blue: 0.2))
                            
                            LazyVGrid(columns: [GridItem(.flexible()), GridItem(.flexible()), GridItem(.flexible())], spacing: 15) {
                                ForEach(exerciseTypes, id: \.self) { type in
                                    Button(action: {
                                        selectedExerciseType = type
                                    }) {
                                        VStack(spacing: 10) {
                                            ZStack {
                                                Circle()
                                                    .fill(selectedExerciseType == type ? Color.green.opacity(0.3) : Color(UIColor.secondarySystemBackground))
                                                    .frame(width: 60, height: 60)
                                                
                                                Image(systemName: iconForExerciseType(type))
                                                    .font(.system(size: 24))
                                                    .foregroundColor(selectedExerciseType == type ? .green : .gray)
                                            }
                                            
                                            Text(type)
                                                .font(.caption)
                                                .foregroundColor(selectedExerciseType == type ? .green : .primary)
                                        }
                                    }
                                    .buttonStyle(PlainButtonStyle())
                                }
                            }
                        }
                        
                        // 持续时间
                        VStack(alignment: .leading, spacing: 10) {
                            Text("Duration")
                                .font(.subheadline)
                                .foregroundColor(Color(red: 0.2, green: 0.2, blue: 0.2))
                            
                            HStack(spacing: 20) {
                                // 小时选择
                                VStack {
                                    HStack {
                                        Spacer()
                                        
                                        Button(action: {
                                            if hours < 12 {
                                                hours += 1
                                            }
                                        }) {
                                            Image(systemName: "chevron.up")
                                                .foregroundColor(.green)
                                                .padding(10)
                                                .background(Color(UIColor.secondarySystemBackground))
                                                .cornerRadius(8)
                                        }
                                        
                                        Spacer()
                                    }
                                    
                                    Text("\(String(format: "%02d", hours))")
                                        .font(.title)
                                        .fontWeight(.medium)
                                        .frame(minWidth: 60)
                                    
                                    HStack {
                                        Spacer()
                                        
                                        Button(action: {
                                            if hours > 0 {
                                                hours -= 1
                                            }
                                        }) {
                                            Image(systemName: "chevron.down")
                                                .foregroundColor(.green)
                                                .padding(10)
                                                .background(Color(UIColor.secondarySystemBackground))
                                                .cornerRadius(8)
                                        }
                                        
                                        Spacer()
                                    }
                                    
                                    Text("hours")
                                        .font(.caption)
                                        .foregroundColor(.secondary)
                                }
                                
                                // 分钟选择
                                VStack {
                                    HStack {
                                        Spacer()
                                        
                                        Button(action: {
                                            if minutes < 59 {
                                                minutes += 1
                                            } else {
                                                minutes = 0
                                                if hours < 12 {
                                                    hours += 1
                                                }
                                            }
                                        }) {
                                            Image(systemName: "chevron.up")
                                                .foregroundColor(.green)
                                                .padding(10)
                                                .background(Color(UIColor.secondarySystemBackground))
                                                .cornerRadius(8)
                                        }
                                        
                                        Spacer()
                                    }
                                    
                                    Text("\(String(format: "%02d", minutes))")
                                        .font(.title)
                                        .fontWeight(.medium)
                                        .frame(minWidth: 60)
                                    
                                    HStack {
                                        Spacer()
                                        
                                        Button(action: {
                                            if minutes > 0 {
                                                minutes -= 1
                                            } else if hours > 0 {
                                                minutes = 59
                                                hours -= 1
                                            }
                                        }) {
                                            Image(systemName: "chevron.down")
                                                .foregroundColor(.green)
                                                .padding(10)
                                                .background(Color(UIColor.secondarySystemBackground))
                                                .cornerRadius(8)
                                        }
                                        
                                        Spacer()
                                    }
                                    
                                    Text("minutes")
                                        .font(.caption)
                                        .foregroundColor(.secondary)
                                }
                            }
                            .padding(.vertical, 10)
                        }
                        
                        // 强度选择
                        VStack(alignment: .leading, spacing: 10) {
                            HStack {
                                Text("Intensity")
                                    .font(.subheadline)
                                    .foregroundColor(Color(red: 0.2, green: 0.2, blue: 0.2))
                                Spacer()
                            }
                            HStack(spacing: 15) {
                                Spacer()
                                ForEach(intensityLevels, id: \.self) { intensity in
                                    Button(action: {
                                        selectedIntensity = intensity
                                    }) {
                                        Text(intensity)
                                            .font(.subheadline)
                                            .frame(minWidth: 60, minHeight: 30)
                                            .padding(.horizontal, 16)
                                            .padding(.vertical, 10)
                                            .background(selectedIntensity == intensity ? Color.green.opacity(0.2) : Color(UIColor.secondarySystemBackground))
                                            .foregroundColor(selectedIntensity == intensity ? .green : .primary)
                                            .cornerRadius(25)
                                            .overlay(
                                                RoundedRectangle(cornerRadius: 25)
                                                    .stroke(selectedIntensity == intensity ? Color.green : Color.clear, lineWidth: 1)
                                            )
                                    }
                                    .buttonStyle(PlainButtonStyle())
                                }
                                Spacer()
                            }
                        }
                    }
                }
                
                // 保存按钮
                Button(action: {
                    saveExercise()
                }) {
                    if isLoading {
                        ProgressView()
                            .progressViewStyle(CircularProgressViewStyle(tint: .white))
                            .frame(maxWidth: .infinity)
                            .padding()
                            .background(Color.green)
                            .cornerRadius(10)
                    } else {
                        Text("Save")
                            .font(.headline)
                            .foregroundColor(.white)
                            .frame(maxWidth: .infinity)
                            .padding()
                            .background(isFormValid ? Color.green : Color.green.opacity(0.5))
                            .cornerRadius(10)
                    }
                }
                .disabled(!isFormValid || isLoading)
            }
            .padding()
            .background(Color(UIColor.systemBackground))
            .cornerRadius(16)
            .shadow(radius: 10)
            .padding(.horizontal, 20)
        }
        .onAppear {
            // 统计
            XDTrackTool.shared.appear("添加运动页面")
        }
    }
    
    // 表单验证
    private var isFormValid: Bool {
        return (hours > 0 || minutes > 0) && !selectedExerciseType.isEmpty && !selectedIntensity.isEmpty
    }
    
    // 根据运动类型返回对应的图标名称
    private func iconForExerciseType(_ type: String) -> String {
        switch type {
        case "Running":
            return "figure.run"
        case "Walking":
            return "figure.walk"
        case "Cycling":
            return "bicycle"
        case "Swimming":
            return "figure.pool.swim"
        case "Weight Training":
            return "dumbbell"
        case "Gym":
            return "dumbbell"
        case "Yoga":
            return "figure.yoga"
        case "Jump Rope":
            return "figure.jumprope"
        case "Basketball":
            return "basketball"
        case "Climbing":
            return "mountain.2"
        default:
            return "figure.walk"
        }
    }
    
    // 保存运动记录
    private func saveExercise() {
        if !isFormValid {
            return
        }
        
        isLoading = true
        
        // 计算总分钟数
        let totalMinutes = hours * 60 + minutes
        
        // 使用传入的日期或默认为当前日期
        let dateToUse = selectedDate ?? Date()
        
        // 调用API创建运动记录
        ExerciseAPIService.shared.createExerciseRecord(
            userData: userData,
            exerciseItem: selectedExerciseType,
            intensity: selectedIntensity,
            hours: hours,
            minutes: minutes,
            date: dateToUse
        ) { result in
            self.isLoading = false
            
            switch result {
            case .success(_):
                print("✅ 成功创建运动记录，日期：\(dateToUse)")
                
                // 构建本地记录对象（用于回调）
                let totalCalories = ExerciseAPIService.shared.calculateCalories(
                    type: self.selectedExerciseType,
            duration: totalMinutes,
                    intensity: self.selectedIntensity
                )
                
                let record = ExerciseRecord(
                    type: self.selectedExerciseType,
                    duration: totalMinutes,
                    intensity: self.selectedIntensity,
                    calories: totalCalories,
                    date: dateToUse,
                    timeString: "12:00",
                    id: UUID(),
                    createdAt: Date()
                )
                
                // 调用回调（如果有）
                if let onSave = self.onSave {
                    onSave(record)
                }
                
            case .failure(let error):
                print("❌ 创建运动记录失败: \(error.localizedDescription)")
                
                // 构建本地记录对象（用于回调）
                let totalCalories = ExerciseAPIService.shared.calculateCalories(
                    type: self.selectedExerciseType,
                    duration: totalMinutes,
                    intensity: self.selectedIntensity
                )
                
                let record = ExerciseRecord(
                    type: self.selectedExerciseType,
                    duration: totalMinutes,
                    intensity: self.selectedIntensity,
                    calories: totalCalories,
                    date: dateToUse,
                    timeString: "12:00",
                    id: UUID(),
                    createdAt: Date()
                )
                
                // 即使API失败，也调用回调以确保良好的用户体验
                if let onSave = self.onSave {
                onSave(record)
                }
            }
            
            // 关闭弹窗
            self.isShowing = false
        }
    }
}

// 运动记录详情视图
// 体重目标输入弹窗
struct WeightGoalInputPopup: View {
    @EnvironmentObject var userData: UserData
    @Binding var isShowing: Bool
    @State private var weightText: String
    @State private var selectedUnit: String
    
    let onSave: (Double, String) -> Void
    
    init(isShowing: Binding<Bool>, initialWeight: String, initialUnit: String, onSave: @escaping (Double, String) -> Void) {
        self._isShowing = isShowing
        self._weightText = State(initialValue: initialWeight)
        self._selectedUnit = State(initialValue: initialUnit)
        self.onSave = onSave
    }
    
    var body: some View {
        ZStack {
            // 半透明背景
            Color.black.opacity(0.4)
                .edgesIgnoringSafeArea(.all)
                .onTapGesture {
                    isShowing = false
                }
            
            // 弹窗内容
            VStack(spacing: 20) {
                // 标题
                HStack {
                    Text("Edit Goal Weight")
                        .font(.headline)
                    
                    Spacer()
                    
                    Button(action: {
                        isShowing = false
                    }) {
                        Image(systemName: "xmark")
                            .foregroundColor(.secondary)
                    }
                }
                
                // 重量显示区域
                HStack(alignment: .firstTextBaseline) {
                    Spacer()
                    
                    Text(weightText)
                        .font(.system(size: 48, weight: .bold))
                    
                    // 单位选择
                    Menu {
                        Button("kg") {
                            if selectedUnit != "kg" {
                                // 如果当前是lbs，转换显示的数值
                                if let weight = Double(weightText) {
                                    let kgWeight = weight / 2.20462
                                    weightText = String(format: "%.1f", kgWeight)
                                }
                                selectedUnit = "kg"
                                // 同步更新到用户数据
                                userData.weightUnit = "kg"
                            }
                        }
                        
                        Button("lbs") {
                            if selectedUnit != "lbs" {
                                // 如果当前是kg，转换显示的数值
                                if let weight = Double(weightText) {
                                    let lbsWeight = weight * 2.20462
                                    weightText = String(format: "%.1f", lbsWeight)
                                }
                                selectedUnit = "lbs"
                                // 同步更新到用户数据
                                userData.weightUnit = "lbs"
                            }
                        }
                    } label: {
                        HStack {
                            Text(selectedUnit)
                                .font(.title2)
                                .foregroundColor(.green)
                            
                            Image(systemName: "chevron.down")
                                .foregroundColor(.green)
                        }
                    }
                    
                    Spacer()
                }
                .padding(.vertical, 20)
                
                // 数字键盘
                VStack(spacing: 15) {
                    HStack(spacing: 30) {
                        numberButton("1")
                        numberButton("2")
                        numberButton("3")
                    }
                    
                    HStack(spacing: 30) {
                        numberButton("4")
                        numberButton("5")
                        numberButton("6")
                    }
                    
                    HStack(spacing: 30) {
                        numberButton("7")
                        numberButton("8")
                        numberButton("9")
                    }
                    
                    HStack(spacing: 30) {
                        numberButton(".")
                        numberButton("0")
                        
                        // 删除按钮
                        Button(action: {
                            if !weightText.isEmpty {
                                weightText.removeLast()
                                if weightText.isEmpty {
                                    weightText = "0"
                                }
                            }
                        }) {
                            Image(systemName: "delete.left")
                                .font(.title)
                                .frame(width: 60, height: 60)
                                .foregroundColor(.primary)
                        }
                    }
                }
                
                // 确认按钮
                Button(action: {
                    if let weight = Double(weightText) {
                        // 保存单位到UserData
                        userData.weightUnit = selectedUnit
                        userData.saveSettings()
                        
                        // 触发回调函数
                        onSave(weight, selectedUnit)
                        isShowing = false
                    }
                }) {
                    Text("Confirm")
                        .font(.headline)
                        .foregroundColor(.white)
                        .frame(maxWidth: .infinity)
                        .padding()
                        .background(Color.green)
                        .cornerRadius(10)
                }
            }
            .padding()
            .background(Color(UIColor.systemBackground))
            .cornerRadius(16)
            .padding(30)
        }
    }
    
    // 数字按钮
    func numberButton(_ number: String) -> some View {
        Button(action: {
            if number == "." {
                // 添加小数点的逻辑需要和其他键盘保持一致
                if !weightText.contains(".") {
                    // 检查整数部分是否不超过3位
                    let parts = weightText.components(separatedBy: ".")
                    if parts.count <= 1 && (parts.isEmpty || parts[0].count <= 3) {
                        weightText += number
                    }
                }
            } else {
                // 当值为"0.0"时，如果输入数字，清空并重新开始
                if weightText == "0.0" && number.rangeOfCharacter(from: CharacterSet.decimalDigits) != nil {
                    weightText = number
                    return
                }
                
                // 如果当前只有"0"，则替换它
                if weightText == "0" {
                    weightText = number
                } else {
                    // 修复的输入限制逻辑：小数点前3位，小数点后1位
                    let parts = weightText.components(separatedBy: ".")
                    
                    if parts.count == 1 {
                        // 还没有小数点，检查整数部分是否已达到3位
                        if parts[0].count < 3 {
                            weightText += number
                        }
                    } else if parts.count == 2 {
                        // 已有小数点，检查小数部分是否已达到1位
                        if parts[1].count < 1 {
                            weightText += number
                        }
                    } else if parts.count == 0 {
                        // 空值情况
                        weightText += number
                    }
                }
            }
        }) {
            Text(number)
                .font(.title)
                .frame(width: 60, height: 60)
                .foregroundColor(.primary)
        }
    }
}

struct RecentWeightChangesSection: View {
    let userData: UserData
    @State private var recentChanges: [(id: UUID, day: String, change: String, isGain: Bool)] = []
    @State private var refreshTrigger = UUID() // 添加刷新触发器
    
    var body: some View {
        VStack(alignment: .leading, spacing: 15) {
            Text("Recent Changes")
                .font(.headline)
                .padding(.horizontal)
            
            VStack(spacing: 0) {
                ForEach(recentChanges, id: \.id) { change in
                    HStack {
                        Text(change.day)
                            .foregroundColor(.secondary)
                        
                        Spacer()
                        
                        HStack(spacing: 5) {
                            Text(change.change)
                                .foregroundColor(change.isGain ? .red : .green)
                                .fontWeight(.medium)
                            
                            Image(systemName: change.isGain ? "arrow.up" : "arrow.down")
                                .foregroundColor(change.isGain ? .red : .green)
                                .font(.caption)
                        }
                    }
                    .padding(.vertical, 12)
                    .padding(.horizontal)
                    .background(Color.white)
                    
                    if change.id != recentChanges.last?.id {
                        Divider()
                            .padding(.horizontal)
                    }
                }
            }
            .background(Color.white)
            .cornerRadius(12)
            .shadow(color: Color.black.opacity(0.1), radius: 4, x: 0, y: 2)
            .padding(.horizontal)
        }
        .id(refreshTrigger) // 使用ID强制刷新
        .onAppear {
            loadRecentChanges()
        }
        .onReceive(NotificationCenter.default.publisher(for: Notification.Name("WeightDataUpdated"))) { _ in
            print("RecentWeightChangesSection: 收到体重数据更新通知，刷新数据...")
            loadRecentChanges()
            refreshTrigger = UUID() // 更新触发器强制刷新UI
        }
        // 添加新的通知监听，以便在添加新的早晚体重记录时刷新
        .onReceive(NotificationCenter.default.publisher(for: Notification.Name("MorningWeightUpdated"))) { _ in
            print("RecentWeightChangesSection: 收到早晨体重更新通知，刷新数据...")
            loadRecentChanges()
            refreshTrigger = UUID() // 更新触发器强制刷新UI
        }
        .onReceive(NotificationCenter.default.publisher(for: Notification.Name("EveningWeightUpdated"))) { _ in
            print("RecentWeightChangesSection: 收到晚间体重更新通知，刷新数据...")
            loadRecentChanges()
            refreshTrigger = UUID() // 更新触发器强制刷新UI
        }
    }
    
    private func loadRecentChanges() {
        // 使用WeightRecordService获取最近4天的数据
        let calendar = Calendar.current
        let today = Date()
        
        // 获取最近4天的日期
        var dates: [Date] = []
        for i in 0..<4 {
            if let date = calendar.date(byAdding: .day, value: -i, to: today) {
                dates.append(date)
            }
        }
        
        let dateFormatter = DateFormatter()
        dateFormatter.dateFormat = "yyyyMMdd"
        
        // 存储每天的体重数据
        var dailyWeights: [(date: Date, morning: Double, evening: Double)] = []
        let dispatchGroup = DispatchGroup()
        
        print("RecentWeightChangesSection: 开始获取最近4天的体重记录...")
        
        // 为每一天获取体重记录
        for date in dates {
            let dateStr = dateFormatter.string(from: date)
            dispatchGroup.enter()
            
            WeightRecordService.shared.getUserWeightRecordsForDay(date: dateStr, userData: userData) { result in
                defer { dispatchGroup.leave() }
                
                var morningWeight: Double = 0.0
                var eveningWeight: Double = 0.0
                
                switch result {
                case .success(let records):
                    // 查找早晨和晚上的记录
                    for record in records {
                        if record.timeType == "AM" && record.weight > 0 {
                            morningWeight = record.weight // 使用最新的早晨记录
                        } else if record.timeType == "PM" && record.weight > 0 {
                            eveningWeight = record.weight // 使用最新的晚上记录
                        }
                    }
                    print("RecentWeightChangesSection: \(dateStr) - 早晨:\(morningWeight)kg, 晚上:\(eveningWeight)kg")
                    
                case .failure(let error):
                    print("RecentWeightChangesSection: 获取\(dateStr)体重记录失败: \(error.localizedDescription)")
                    // 保持默认值0.0
                }
                
                // 线程安全地添加数据
                DispatchQueue.main.async {
                    dailyWeights.append((date: date, morning: morningWeight, evening: eveningWeight))
                }
            }
        }
        
        // 所有请求完成后处理数据
        dispatchGroup.notify(queue: .main) {
            // 按日期排序（最新的在前）
            dailyWeights.sort { $0.date > $1.date }
            
            // 计算变化并生成显示数据
            var changes: [(id: UUID, day: String, change: String, isGain: Bool)] = []
            
            let dayFormatter = DateFormatter()
            dayFormatter.dateFormat = "MMM d"
            
            for (_, dayData) in dailyWeights.enumerated() {
                let dayName: String
                if calendar.isDateInToday(dayData.date) {
                    dayName = "Today"
                } else if calendar.isDateInYesterday(dayData.date) {
                    dayName = "Yesterday"
                } else {
                    dayName = dayFormatter.string(from: dayData.date)
                }
                
                // 只计算当天的早晚体重变化，不进行跨天比较
                if dayData.morning > 0 && dayData.evening > 0 {
                    // 计算当天晚上体重 - 早上体重的变化（与UserData.swift中的逻辑保持一致）
                    let weightChange = dayData.evening - dayData.morning
                    let displayWeight = self.userData.getWeightValue(abs(weightChange))
                    let changeStr = String(format: "%.1f %@", displayWeight, self.userData.weightUnit)
                    
                    changes.append((
                        id: UUID(),
                        day: dayName,
                        change: changeStr,
                        isGain: weightChange > 0 // 正数表示增重（晚上比早上重）
                    ))
                    
                    print("RecentWeightChangesSection: \(dayName) 当天变化(晚-早): \(changeStr), 增重: \(weightChange > 0)")
                } else {
                    // 如果当天早晚体重不完整，显示"No data"
                    changes.append((
                        id: UUID(),
                        day: dayName,
                        change: "No data",
                        isGain: false
                    ))
                    print("RecentWeightChangesSection: \(dayName) - 早晚体重数据不完整")
                }
            }
            
            self.recentChanges = changes
            self.refreshTrigger = UUID()
            print("RecentWeightChangesSection: 已加载\(changes.count)条最近体重变化记录")
        }
    }
    

}



// DailyWeightRecordFullScreenView - 全屏版每日体重记录详情视图
struct DailyWeightRecordFullScreenView: View {
    @Environment(\.presentationMode) private var presentationMode
    @EnvironmentObject var userData: UserData
    @State private var selectedDate: Date = Date()
    @State private var showDatePicker = false
    @State private var showWeightInputPopup = false
    @State private var selectedWeightTime: WeightView.WeightTime = .morning
    @State private var isLoading = false
    @State private var dailyWeightRecords: [LocalWeightRecord] = []
    
    private func convertToDisplayUnit(kgValue: Double) -> Double {
        if userData.weightUnit == "lbs" {
            return kgValue * 2.20462
        }
        return kgValue
    }
    
    // 格式化所选日期为"Thursday, Oct 12"格式
    private var formattedDate: String {
        let formatter = DateFormatter()
        formatter.dateFormat = "EEEE, MMM d"
        return formatter.string(from: selectedDate)
    }
    
    // 获取选定日期的早晨体重
    private var morningWeightString: String {
        if let record = dailyWeightRecords.first(where: { $0.isMorning }) {
            let value = convertToDisplayUnit(kgValue: record.weight)
            return String(format: "%.1f", value)
        } else {
            return "0.0"
        }
    }
    
    // 获取选定日期的晚上体重
    private var eveningWeightString: String {
        if let record = dailyWeightRecords.first(where: { !$0.isMorning }) {
            let value = convertToDisplayUnit(kgValue: record.weight)
            return String(format: "%.1f", value)
        } else {
            return "0.0"
        }
        }
        
    // 计算体重变化
    private var weightChange: (value: Double, isGain: Bool) {
        if let morningRecord = dailyWeightRecords.first(where: { $0.isMorning }),
           let eveningRecord = dailyWeightRecords.first(where: { !$0.isMorning }) {
            // 计算公斤差值
            let diffInKg = eveningRecord.weight - morningRecord.weight
            // 将差值转换为显示单位
            let displayDiff = convertToDisplayUnit(kgValue: diffInKg)
            return (abs(displayDiff), displayDiff > 0)
        } else {
            return (0.0, false)
        }
    }
    
    // 计算平均体重
    private var averageWeight: Double {
        if dailyWeightRecords.isEmpty {
            return 0.0
        }
        
        let totalWeightInKg = dailyWeightRecords.reduce(0.0) { $0 + $1.weight }
        let averageWeightInKg = totalWeightInKg / Double(dailyWeightRecords.count)
        return convertToDisplayUnit(kgValue: averageWeightInKg)
    }
    
    var body: some View {
        ScrollView {
            VStack(spacing: 20) {
                // 日期选择器
                HStack {
                    Button(action: {
                        selectPreviousDay()
                    }) {
                        Image(systemName: "chevron.left")
                            .foregroundColor(.secondary)
                    }
                    
                    Spacer()
                    
                    Text(formattedDate)
                        .font(.headline)
                    
                    Spacer()
                    
                    Button(action: {
                        selectNextDay()
                    }) {
                        Image(systemName: "chevron.right")
                            .foregroundColor(.secondary)
                    }
                }
                .padding()
                .background(Color(red: 0.96, green: 0.98, blue: 0.96))
                
                // 30天体重趋势图 - 使用新的DailyWeightDetailChart替代WeightTrendChart
                if isLoading {
                    HStack {
                        Spacer()
                        ProgressView()
                            .progressViewStyle(CircularProgressViewStyle())
                        Spacer()
                    }
                    .padding()
                    .frame(height: 250)
                    .background(Color.white)
                    .cornerRadius(12)
                    .shadow(color: Color.black.opacity(0.1), radius: 4, x: 0, y: 2)
                    .padding(.horizontal)
                } else {
                    DailyWeightDetailChart(userData: userData, selectedDate: selectedDate)
                        .frame(height: 250)
                        .padding(.vertical, 10)
                        .padding(.leading, 5) // 向左偏移留出更多空间给右侧
                        .padding(.trailing, 15) // 右侧留出更多空间显示30标签
                        .background(Color.white)
                        .cornerRadius(12)
                        .shadow(color: Color.black.opacity(0.1), radius: 4, x: 0, y: 2)
                        .padding(.horizontal)
                }

                // 使用统一的体重编辑组件
                UnifiedWeightEditSection(
                    morningWeightString: morningWeightString,
                    eveningWeightString: eveningWeightString,
                    weightUnit: userData.weightUnit,
                    onMorningEdit: {
                        selectedWeightTime = .morning
                        showWeightInputPopup = true
                    },
                    onEveningEdit: {
                        selectedWeightTime = .evening
                        showWeightInputPopup = true
                    },
                    style: .detailed
                )
                
                // 日常统计
        VStack(alignment: .leading, spacing: 15) {
                    Text("Daily Statistics")
                .font(.headline)
                .padding(.horizontal)
            
            VStack(spacing: 0) {
                        // 体重变化
                    HStack {
                            Text("Weight Change")
                            .foregroundColor(.secondary)
                        
                        Spacer()
                        
                            HStack {
                                if weightChange.value > 0 {
                                    Text("\(weightChange.isGain ? "+" : "-")\(String(format: "%.1f", weightChange.value)) \(userData.weightUnit)")
                                        .foregroundColor(weightChange.isGain ? .red : .green)
                                .fontWeight(.medium)
                                } else {
                                    Text("0.0 \(userData.weightUnit)")
                                        .foregroundColor(.secondary)
                                        .fontWeight(.medium)
                        }
                    }
                        }
                        .padding()
                        .background(Color(UIColor.systemBackground))
                        
                        Divider()
                            .padding(.horizontal)
                        
                        // 平均体重
                        HStack {
                            Text("Average Weight")
                                .foregroundColor(.secondary)
                            
                            Spacer()
                            
                            Text("\(String(format: "%.2f", averageWeight)) \(userData.weightUnit)")
                                .fontWeight(.medium)
                        }
                        .padding()
                        .background(Color(UIColor.systemBackground))
            }
                    .background(Color(UIColor.systemBackground))
            .cornerRadius(12)
            .shadow(color: Color.black.opacity(0.1), radius: 4, x: 0, y: 2)
            .padding(.horizontal)
        }
            }
            .padding(.vertical)
        }
        .navigationTitle("Daily Weight Record")
        .navigationBarTitleDisplayMode(.inline)
        .navigationBarItems(trailing: Button(action: {
            showDatePicker = true
        }) {
            Image(systemName: "calendar")
                .foregroundColor(.green)
        })
        .onAppear {
            // 统计
            XDTrackTool.shared.appear("体重详情页面")

            loadDailyWeightData()
        }
        .onChange(of: selectedDate) { oldValue, newValue in
            loadDailyWeightData()
            // 添加这一行确保图表刷新
            self.isLoading = false
        }
        .overlay(
            Group {
                if showDatePicker {
                    DatePickerView(
                        isShowing: $showDatePicker,
                        selectedDate: $selectedDate,
                        userCreationDate: userData.accountCreationDate
                    )
                }
                
                if showWeightInputPopup {
                    WeightInputPopup(
                        isShowing: $showWeightInputPopup,
                        weightTime: selectedWeightTime,
                        initialWeight: selectedWeightTime == .morning ? morningWeightString : eveningWeightString,
                        initialUnit: userData.weightUnit,
                        onSave: { weight, unit in
                            // 保存体重数据
                            saveWeightData(weight: weight, unit: unit, isMorning: selectedWeightTime == .morning)
        }
                    )
                }
            }
        )
    }
    
    // 选择前一天
    private func selectPreviousDay() {
        if let previousDay = Calendar.current.date(byAdding: .day, value: -1, to: selectedDate) {
            if previousDay >= userData.accountCreationDate {
                selectedDate = previousDay
            }
        }
    }
    
    // 选择后一天
    private func selectNextDay() {
        if let nextDay = Calendar.current.date(byAdding: .day, value: 1, to: selectedDate) {
            if nextDay <= Date() {
                selectedDate = nextDay
            }
        }
    }
    
    // 加载选定日期的体重数据
    private func loadDailyWeightData() {
        isLoading = true
        print("加载日期\(selectedDate)的体重数据...")
        
        // 使用选定日期格式化为YYYYMMDD格式
        let dateFormatter = DateFormatter()
        dateFormatter.dateFormat = "yyyyMMdd"
        let dateStr = dateFormatter.string(from: selectedDate)
        
        // 使用新方法同时获取AM和PM的记录
        WeightRecordService.shared.getUserWeightRecordsForDay(date: dateStr, userData: userData) { result in
            DispatchQueue.main.async {
                self.isLoading = false
                
                switch result {
                case .success(let apiRecords):
                    print("成功从API获取\(apiRecords.count)条体重记录")
                    
                    // 将API返回的记录转换为应用内部使用的LocalWeightRecord结构
                    var weightRecords: [LocalWeightRecord] = []
                    
                    for record in apiRecords {
                        if let date = record.getDate() {
                            // 根据timeType确定是早晨还是晚上的记录
                            let isMorning = record.timeType == "AM"
                            
                            // 检查体重值是否合理
                            if record.weight <= 0 {
                                print("警告: 记录体重值异常 (\(record.weight)kg)，已跳过")
                                continue
                            }
                            
                            // 创建内部使用的LocalWeightRecord
                            let weightRecord = LocalWeightRecord(date: date, weight: record.weight, isMorning: isMorning)
                            weightRecords.append(weightRecord)
                        } else {
                            print("警告: 记录缺少有效日期或无法解析")
                        }
                    }
                    
                    // 过滤出所选日期的记录
                    let calendar = Calendar.current
                    let filteredRecords = weightRecords.filter { record in
                        return calendar.isDate(record.date, inSameDayAs: self.selectedDate)
                    }
                    
                    // 按日期时间排序，确保最新的记录在前面
                    self.dailyWeightRecords = filteredRecords.sorted(by: { $0.date > $1.date })
                    
                    print("选定日期\(self.selectedDate)共找到\(self.dailyWeightRecords.count)条记录")
                    
                    // 更新当天的morningWeight和eveningWeight（如果选择的是今天）
                    if calendar.isDateInToday(self.selectedDate) {
                        // 获取最新的早晨记录
                        if let morningRecord = self.dailyWeightRecords.first(where: { $0.isMorning }) {
                            self.userData.morningWeight = morningRecord.weight
                            print("更新今日早晨体重: \(morningRecord.weight)kg")
                        } else {
                            // 如果今天没有早晨记录，设置为0表示未打卡
                            self.userData.morningWeight = 0.0
                            print("今日无早晨体重记录，设置为0")
                        }
                        
                        // 获取最新的晚上记录
                        if let eveningRecord = self.dailyWeightRecords.first(where: { !$0.isMorning }) {
                            self.userData.eveningWeight = eveningRecord.weight
                            print("更新今日晚上体重: \(eveningRecord.weight)kg")
                        } else {
                            // 如果今天没有晚上记录，设置为0表示未打卡
                            self.userData.eveningWeight = 0.0
                            print("今日无晚上体重记录，设置为0")
                        }
                        
                        // 保存到userData
                        self.userData.saveSettings()
                    }
                    
                case .failure(let error):
                    print("获取体重记录失败: \(error.localizedDescription)")
                }
            }
        }
    }
    
    // 保存体重数据
    private func saveWeightData(weight: Double, unit: String, isMorning: Bool) {
        isLoading = true
        print("开始保存体重数据: 体重=\(weight) \(unit), 日期=\(selectedDate), 是早晨=\(isMorning)")
        
        // 转换到kg（如果后端总是使用kg存储）
        var weightInKg = weight
        if unit == "lbs" {
            weightInKg = userData.convertToKg(weight)
            print("转换体重从lbs到kg: \(weight)lbs -> \(weightInKg)kg")
        }
        
        // 将isMorning转换为timeType
        let timeType = isMorning ? "AM" : "PM"
        
        // 使用所选日期格式化为YYYYMMDD格式
        let dateFormatter = DateFormatter()
        dateFormatter.dateFormat = "yyyyMMdd"
        let dateStr = dateFormatter.string(from: selectedDate)
        
        // 调用WeightRecordService创建体重记录
        WeightRecordService.shared.createWeightRecord(weight: weightInKg, timeType: timeType, dateStr: dateStr, userData: userData) { result in
            DispatchQueue.main.async {
                self.isLoading = false
                
                switch result {
                case .success(_):
                    print("成功保存体重记录到API")
                    
                    // 重新加载体重记录
                    self.loadDailyWeightData()
                    
                    // 更新当天的morningWeight或eveningWeight（如果选择的是今天）
                    let calendar = Calendar.current
                    if calendar.isDateInToday(self.selectedDate) {
                        if isMorning {
                            self.userData.morningWeight = weightInKg
                            print("更新今日早晨体重: \(weightInKg)kg")
                            // 发送早晨体重更新通知
                            NotificationCenter.default.post(name: Notification.Name("MorningWeightUpdated"), object: nil)
                        } else {
                            self.userData.eveningWeight = weightInKg
                            print("更新今日晚上体重: \(weightInKg)kg")
                            // 发送晚上体重更新通知
                            NotificationCenter.default.post(name: Notification.Name("EveningWeightUpdated"), object: nil)
                        }
                        
                        // 重新加载主页面数据
                        self.userData.saveSettings()
                        
                        // 发送通知，通知体重数据已更新，触发所有相关视图刷新
                        NotificationCenter.default.post(name: Notification.Name("WeightDataUpdated"), object: nil)
                        print("已发送WeightDataUpdated通知")
                    } else {
                        print("记录的日期不是今天，仅更新图表数据")
                        // 仍然发送通知以更新其他视图
                        NotificationCenter.default.post(name: Notification.Name("WeightDataUpdated"), object: nil)
            }
                    
                case .failure(let error):
                    print("创建体重记录失败: \(error.localizedDescription)")
                    // 可以在这里添加错误处理逻辑，例如显示错误提示
        }
    }
}
    }
        }



// 添加全屏版本的运动详情视图
struct ExerciseFullScreenView: View {
    @Environment(\.presentationMode) private var presentationMode
    @EnvironmentObject var userData: UserData
    @State private var selectedDate: Date = Date()
    @State private var exerciseRecords: [ExerciseRecord] = []
    @State private var totalCalories: Int = 0
    @State private var calorieGoal: Int = 1200
    @State private var isLoading = false
    @State private var showAddExercise = false
    @State private var displayedMonth: String = ""
    @State private var displayedDates: [Date] = []
    @State private var refreshTrigger = Date() // 添加刷新触发器
    
    private let calendar = Calendar.current
    private var dateFormatter: DateFormatter = {
        let formatter = DateFormatter()
        formatter.dateFormat = "d"  // 只显示日期的数字部分（1-31）
        return formatter
    }()
    private var monthFormatter: DateFormatter = {
        let formatter = DateFormatter()
        formatter.dateFormat = "MMMM yyyy"  // 显示完整月份和年份
        return formatter
    }()
    
    var body: some View {
        VStack(spacing: 0) {
            // 月份显示
            Text(displayedMonth)
                .font(.subheadline)
                .foregroundColor(.secondary)
                .padding(.top, 10)
            
            // 日期滚动选择条
            ScrollView(.horizontal, showsIndicators: false) {
                ScrollViewReader { scrollProxy in
                    HStack(spacing: 8) {
                        ForEach(displayedDates, id: \.self) { date in
                            let isSelected = calendar.isDate(date, inSameDayAs: selectedDate)
                            let isToday = calendar.isDateInToday(date)
                            let dayNumber = calendar.component(.day, from: date)
                            
                            VStack(spacing: 4) {
                                Text("\(dayNumber)")
                                    .font(.system(.headline, design: .rounded))
                                    .foregroundColor(isSelected ? .white : (isToday ? .green : .primary))
                                
                                Text(dayOfWeek(date))
                                    .font(.caption2)
                                    .foregroundColor(isSelected ? .white : .secondary)
                            }
                            .frame(width: 40, height: 60)
                            .background(
                                ZStack {
                                    if isSelected {
                                        RoundedRectangle(cornerRadius: 20)
                                            .fill(Color.green)
                                            .frame(width: 38, height: 58)
                                    }
                                }
                            )
                            .id(date) // 添加id用于滚动定位
                            .onTapGesture {
                                if !calendar.isDate(date, inSameDayAs: selectedDate) {
                                    selectedDate = date
                                    loadExerciseData()
                                }
                            }
                        }
                    }
                    .padding(.horizontal)
                    .padding(.bottom, 5)
                    .onAppear {
                        // 在视图出现时自动滚动到今天
                        if let todayIndex = displayedDates.firstIndex(where: { calendar.isDateInToday($0) }) {
                            DispatchQueue.main.asyncAfter(deadline: .now() + 0.1) {
                                withAnimation {
                                    scrollProxy.scrollTo(displayedDates[todayIndex], anchor: .center)
                                }
                            }
                        }
                    }
                }
            }
            
            // 今日数据
            VStack(spacing: 5) {
                Text(calendar.isDateInToday(selectedDate) ? "Today, \(formattedDate)" : formattedDate)
                    .font(.subheadline)
                
                HStack(alignment: .firstTextBaseline) {
                    Text("\(totalCalories)")
                        .font(.system(size: 32, weight: .bold))
                        .foregroundColor(.green)
                    
                    Text("kcal")
                        .font(.headline)
                        .foregroundColor(.green)
                }
                
                HStack {
                    // 总时间
                    HStack(spacing: 4) {
                        Image(systemName: "clock")
                            .font(.caption)
                        
                        Text("Total Time: \(totalDuration) mins")
                            .font(.caption)
                    }
                    .foregroundColor(.secondary)
                }
                
                // 进度环
                ZStack {
                    Circle()
                        .stroke(Color.green.opacity(0.2), lineWidth: 6)
                        .frame(width: 90, height: 90)
                    
                    Circle()
                        .trim(from: 0, to: calculateProgress(current: totalCalories, goal: calorieGoal))
                        .stroke(Color.green, style: StrokeStyle(lineWidth: 6, lineCap: .round))
                        .frame(width: 90, height: 90)
                        .rotationEffect(.degrees(-90))
                    
                    Text(formatProgressPercentage(current: totalCalories, goal: calorieGoal))
                        .font(.system(.headline, design: .rounded))
                        .fontWeight(.bold)
        }
                .padding(.top, 5)
            }
            .padding()
            .background(Color.white)
            .cornerRadius(16)
            .shadow(color: Color.black.opacity(0.1), radius: 8, x: 0, y: 4)
            .padding(.horizontal)
            
            // 运动记录标题
            HStack {
                Text("Exercise Records")
                    .font(.headline)
                
                Spacer()
                
                Button(action: {
                    showAddExercise = true
                }) {
                    Image(systemName: "plus.circle.fill")
                        .foregroundColor(.green)
                        .font(.title2)
                }
            }
            .padding()
            
            if isLoading {
                Spacer()
                ProgressView()
                    .progressViewStyle(CircularProgressViewStyle())
                Spacer()
            } else if exerciseRecords.isEmpty {
                Spacer()
                VStack(spacing: 15) {
                    Image(systemName: "figure.walk")
                        .font(.system(size: 50))
                        .foregroundColor(.secondary)
                    
                    Text("No Exercise Records")
                        .font(.headline)
                        .foregroundColor(.secondary)
                    
                    Text("Start adding your exercise activities")
                        .font(.subheadline)
                        .foregroundColor(.secondary)
                    
                    Button(action: {
                        showAddExercise = true
                    }) {
                        Text("Add Exercise")
                            .font(.headline)
                            .foregroundColor(.white)
                            .padding(.horizontal, 30)
                            .padding(.vertical, 10)
                            .background(Color.green)
                            .cornerRadius(25)
                    }
                    .padding(.top, 10)
                }
                .padding()
                Spacer()
            } else {
                ScrollView {
                    VStack(spacing: 0) {
                        ForEach(exerciseRecords) { record in
                            HStack(spacing: 15) {
                                // 运动图标
                                ZStack {
                                    Circle()
                                        .fill(Color.green.opacity(0.2))
                                        .frame(width: 50, height: 50)
                                    
                                    Image(systemName: iconForExerciseType(record.type))
                                        .font(.system(size: 24))
                                        .foregroundColor(.green)
                                }
                                
                                VStack(alignment: .leading, spacing: 4) {
                                    Text(record.type)
                                        .font(.headline)
                                    
                                    Text("\(record.duration) mins")
                                        .font(.subheadline)
                                        .foregroundColor(.secondary)
                                }
                                
                                Spacer()
                                
                                VStack(alignment: .trailing, spacing: 4) {
                                    Text("\(record.calories) kcal")
                                        .font(.headline)
                                        .foregroundColor(.green)
                                    
                                    // 使用API返回的时间或默认时间
                                    Text(record.timeString)
                                        .font(.caption)
                                        .foregroundColor(.secondary)
                                }
                            }
                            .padding()
                            .background(Color(UIColor.systemBackground))
                            
                            Divider()
                                .padding(.leading, 0)
                        }
                    }
                }
            }
        }
        .navigationTitle("Exercise")
        .navigationBarTitleDisplayMode(.inline)
        .onAppear {
            // 统计
            XDTrackTool.shared.appear("运动详情页面")

            initializeDates()

            // 在加载数据前先对userData中的运动记录进行去重
            userData.deduplicateExerciseRecords()

            loadExerciseData()

            // 添加通知监听
            NotificationCenter.default.addObserver(forName: NSNotification.Name("RefreshExerciseData"), object: nil, queue: .main) { _ in
                self.refreshTrigger = Date()
            }
        }
        .onDisappear {
            // 移除通知监听
            NotificationCenter.default.removeObserver(self, name: NSNotification.Name("RefreshExerciseData"), object: nil)
        }
        .onChange(of: selectedDate) { oldValue, newValue in
            // 更新月份标题
            displayedMonth = monthFormatter.string(from: newValue)
            loadExerciseData()
        }
        .onChange(of: refreshTrigger) { _, _ in
            loadExerciseData()
        }
        .overlay(
            Group {
                if showAddExercise {
                    AddExerciseView(isShowing: $showAddExercise, selectedDate: selectedDate, onSave: { record in
                        // 通知其他页面刷新 - 确保在主线程上发送通知
                        DispatchQueue.main.async {
                            NotificationCenter.default.post(name: NSNotification.Name("RefreshExerciseData"), object: nil)
                            NotificationCenter.default.post(name: Notification.Name("ExerciseDataUpdated"), object: nil)
                        }
                    })
                    .environmentObject(userData)
                }
            }
        )
    }
    
    // 初始化日期数据
    private func initializeDates() {
        // 获取当前日期和一个月前的日期
        let today = Date()
        
        // 默认选中今天
        selectedDate = today
        
        // 获取过去15天和未来15天的日期（总共31天，今天在中间）
        guard let pastDays = calendar.date(byAdding: .day, value: -15, to: today) else { return }
        guard let futureDays = calendar.date(byAdding: .day, value: 15, to: today) else { return }
        
        // 设置显示的月份为当前选中日期的月份
        monthFormatter.dateFormat = "MMMM yyyy" // 确保正确设置格式
        displayedMonth = monthFormatter.string(from: selectedDate)
        
        // 生成日期数组
        var currentDate = pastDays
        var dates: [Date] = []
        
        while currentDate <= futureDays {
            // 只添加不超过今天的日期
            if currentDate <= today {
                dates.append(currentDate)
            }
            guard let nextDate = calendar.date(byAdding: .day, value: 1, to: currentDate) else { break }
            currentDate = nextDate
        }
        
        displayedDates = dates
        
        // 确保日期格式化器正确设置
        dateFormatter.dateFormat = "d"
    }
    
    // 格式化显示的日期
    private var formattedDate: String {
        let formatter = DateFormatter()
        formatter.dateFormat = "MMMM d, yyyy"
        return formatter.string(from: selectedDate)
    }
    
    // 获取星期几
    private func dayOfWeek(_ date: Date) -> String {
        let formatter = DateFormatter()
        formatter.dateFormat = "E"
        return formatter.string(from: date)
    }
    
    // 总运动时长
    private var totalDuration: Int {
        return exerciseRecords.reduce(0) { $0 + $1.duration }
    }
    
    // 计算进度值（0.0-1.0）
    private func calculateProgress(current: Int, goal: Int) -> CGFloat {
        return CGFloat(min(Double(current) / Double(max(1, goal)), 1.0))
    }
    
    // 格式化进度百分比为文本
    private func formatProgressPercentage(current: Int, goal: Int) -> String {
        let percentage = Int(calculateProgress(current: current, goal: goal) * 100)
        return "\(percentage)%"
    }
    
    // 加载运动数据
    private func loadExerciseData() {
        isLoading = true
        
        // 获取目标卡路里
        ExerciseAPIService.shared.fetchDailyCalorieTarget(userData: userData) { goal in
            self.calorieGoal = goal
            
            // 直接从userData获取选中日期的运动记录
            let calendar = Calendar.current
            let startOfDay = calendar.startOfDay(for: self.selectedDate)
            let endOfDay = calendar.date(byAdding: .day, value: 1, to: startOfDay)!
            
            // 过滤选中日期的运动记录，确保没有重复的ID
            var dayRecords = self.userData.exerciseRecords.filter { record in
                return record.date >= startOfDay && record.date < endOfDay
            }
            
            // 去重处理 - 确保没有相同ID的记录
            let uniqueIDs = Set(dayRecords.map { $0.id })
            if uniqueIDs.count < dayRecords.count {
                // 如果有重复ID，进行去重
                var seenIDs = Set<UUID>()
                dayRecords = dayRecords.filter { record in
                    // 保留第一次出现的记录
                    if seenIDs.contains(record.id) {
                        return false
                    } else {
                        seenIDs.insert(record.id)
                        return true
                    }
                }
                
                // 更新userData中的记录（可选，如果希望全局去重）
                let allRecordIDs = Set(self.userData.exerciseRecords.map { $0.id })
                if allRecordIDs.count < self.userData.exerciseRecords.count {
                    var seenAllIDs = Set<UUID>()
                    self.userData.exerciseRecords = self.userData.exerciseRecords.filter { record in
                        if seenAllIDs.contains(record.id) {
                            return false
                        } else {
                            seenAllIDs.insert(record.id)
                            return true
                        }
                    }
                    // 保存更新后的记录
                    self.userData.saveSettings()
                }
            }
            
            // 确保按创建时间降序排序，最新添加的运动会显示在前面
            self.exerciseRecords = dayRecords.sorted(by: { $0.createdAt > $1.createdAt })
            self.totalCalories = dayRecords.reduce(0) { $0 + $1.calories }
            self.isLoading = false
        }
    }
    
    // 根据运动类型返回对应的图标名称
    private func iconForExerciseType(_ type: String) -> String {
        switch type {
        case "Running":
            return "figure.run"
        case "Walking":
            return "figure.walk"
        case "Cycling":
            return "bicycle"
        case "Swimming":
            return "figure.pool.swim"
        case "Weight Training":
            return "dumbbell"
        case "Gym":
            return "dumbbell"
        case "Yoga":
            return "figure.yoga"
        case "Jump Rope":
            return "figure.jumprope"
        case "Basketball":
            return "basketball"
        case "Climbing":
            return "mountain.2"
        default:
            return "figure.walk"
        }
    }
}

// 追加在文件末尾

// 日期详情页的专用体重趋势图表组件
struct DailyWeightDetailChart: View {
    @ObservedObject var userData: UserData
    let selectedDate: Date
    @State private var refreshTrigger = UUID() // 添加刷新触发器
    
    // 表示一个体重数据点
    struct WeightDataPoint: Identifiable {
        var id = UUID()
        var day: Int
        var weight: Double
        var isMorning: Bool
        var date: Date
        var isSelected: Bool // 标记是否是所选日期
    }
    
    // 处理数据并准备图表数据点
    private var chartData: [WeightDataPoint] {
        // 使用refreshTrigger强制刷新计算，但不产生实际影响
        _ = refreshTrigger
        
        // 直接从userData.weightHistory获取数据
        let allRecords = userData.weightHistory
        
        print("DailyWeightDetailChart: userData.weightHistory中共有\(allRecords.count)条记录")
        
        // 如果没有记录，返回空数组
        if allRecords.isEmpty {
            print("DailyWeightDetailChart: 没有找到任何体重历史记录")
            return []
        }
        
        // 设置日期范围（以所选日期为中心的30天）
        let calendar = Calendar.current
        let selectedDay = calendar.startOfDay(for: selectedDate)
        let daysBeforeCount = 15
        let daysAfterCount = 14
        
        guard let startDate = calendar.date(byAdding: .day, value: -daysBeforeCount, to: selectedDay),
              let endDate = calendar.date(byAdding: .day, value: daysAfterCount+1, to: selectedDay) else {
            print("DailyWeightDetailChart: 无法计算日期范围")
            return []
        }
        
        print("DailyWeightDetailChart: 日期范围从 \(startDate) 到 \(endDate)")
        
        // 过滤日期范围内的记录
        let filteredRecords = allRecords.filter { record in
            let recordDate = calendar.startOfDay(for: record.date)
            return recordDate >= startDate && recordDate < endDate
        }
        
        print("DailyWeightDetailChart: 日期范围内共有\(filteredRecords.count)条记录")
        
        // 按早晨/晚上和日期分组
        var morningRecordsByDay: [Date: WeightRecord] = [:]
        var eveningRecordsByDay: [Date: WeightRecord] = [:]
        
        for record in filteredRecords {
            let recordDate = calendar.startOfDay(for: record.date)
            
            // 确保日期在有效范围内
            guard recordDate >= startDate && recordDate < endDate else {
                continue
            }
            
            if record.isMorning {
                // 保留同一天中最新的记录
                if let existingRecord = morningRecordsByDay[recordDate],
                   existingRecord.date > record.date {
                    // 已有更新的记录，跳过
                    continue
                }
                morningRecordsByDay[recordDate] = record
            } else {
                // 保留同一天中最新的记录
                if let existingRecord = eveningRecordsByDay[recordDate],
                   existingRecord.date > record.date {
                    // 已有更新的记录，跳过
                    continue
                }
                eveningRecordsByDay[recordDate] = record
            }
        }
        
        let morningRecords = Array(morningRecordsByDay.values)
        let eveningRecords = Array(eveningRecordsByDay.values)
        
        print("DailyWeightDetailChart: 按日期去重后 - 早晨记录数量=\(morningRecords.count), 晚上记录数量=\(eveningRecords.count)")
        
        // 创建数据点
        var dataPoints: [WeightDataPoint] = []
        
        // 生成日期范围内的所有日期
        var allDates: [Date] = []
        var currentDate = startDate
        
        while currentDate < endDate {
            allDates.append(currentDate)
            
            guard let nextDay = calendar.date(byAdding: .day, value: 1, to: currentDate) else { break }
            currentDate = nextDay
        }
        
        print("DailyWeightDetailChart: 生成了\(allDates.count)个日期点作为X轴")
        
        // 创建日期到天数的映射
        var dateToDay: [Date: Int] = [:]
        for (index, date) in allDates.enumerated() {
            dateToDay[date] = index + 1
        }
        
        // 添加早晨记录数据点
        for record in morningRecords {
            let recordDate = calendar.startOfDay(for: record.date)
            let isSelectedDate = calendar.isDate(recordDate, inSameDayAs: selectedDay)
            
            // 严格匹配日期到对应的天数
            if let day = dateToDay[recordDate] {
                let weight = userData.getWeightValue(record.weight)
                print("DailyWeightDetailChart: 添加早晨数据点 day=\(day), weight=\(weight), date=\(record.date), 是所选日期=\(isSelectedDate)")
                
                dataPoints.append(
                    WeightDataPoint(
                        day: day,
                        weight: weight,
                        isMorning: true,
                        date: record.date,
                        isSelected: isSelectedDate
                    )
                )
            } else {
                print("DailyWeightDetailChart: 早晨记录日期\(recordDate)超出范围，不添加")
            }
        }
        
        // 添加晚上记录数据点
        for record in eveningRecords {
            let recordDate = calendar.startOfDay(for: record.date)
            let isSelectedDate = calendar.isDate(recordDate, inSameDayAs: selectedDay)
            
            // 严格匹配日期到对应的天数
            if let day = dateToDay[recordDate] {
                let weight = userData.getWeightValue(record.weight)
                print("DailyWeightDetailChart: 添加晚上数据点 day=\(day), weight=\(weight), date=\(record.date), 是所选日期=\(isSelectedDate)")
                
                dataPoints.append(
                    WeightDataPoint(
                        day: day,
                        weight: weight,
                        isMorning: false,
                        date: record.date,
                        isSelected: isSelectedDate
                    )
                )
            } else {
                print("DailyWeightDetailChart: 晚上记录日期\(recordDate)超出范围，不添加")
            }
        }
        
        // 确保数据点按天排序
        let sortedPoints = dataPoints.sorted { $0.day < $1.day }
        print("DailyWeightDetailChart: 最终生成\(sortedPoints.count)个数据点用于绘图")
        
        return sortedPoints
    }
    
    // 数据存在的天数范围
    private var dayRange: ClosedRange<Int> {
        if chartData.isEmpty {
            return 1...30
        }
        
        guard let minDay = chartData.map({ $0.day }).min(),
              let maxDay = chartData.map({ $0.day }).max() else {
            return 1...30
        }
        
        return minDay...maxDay
    }
    
    // 计算所选日期对应的天数索引
    private var selectedDayIndex: Int {
        let calendar = Calendar.current
        _ = calendar.startOfDay(for: selectedDate)
        
        // 默认为居中位置
        let centerDay = 16
        
        // 计算所选日期对应的索引
        for point in chartData where point.isSelected {
            return point.day
        }
        
        return centerDay // 默认返回居中位置
    }
    
    // 获取Y轴的最小值和最大值
    private var yAxisRange: (min: Double, max: Double) {
        if chartData.isEmpty {
            // 默认范围围绕当前体重
            let baseWeight = userData.getWeightValue(userData.morningWeight)
            return (baseWeight - 3, baseWeight + 3)
        }
        
        let weights = chartData.map { $0.weight }
        
        // 如果只有一个点，创建一个合理的范围
        if weights.count == 1, let singleWeight = weights.first {
            return (singleWeight - 3, singleWeight + 3)
        }
        
        let minWeight = weights.min() ?? userData.getWeightValue(userData.morningWeight) - 3
        let maxWeight = weights.max() ?? userData.getWeightValue(userData.morningWeight) + 3
        
        // 计算范围并添加一些边距
        let range = maxWeight - minWeight
        let padding = max(1.0, range * 0.1) // 至少1单位或范围的10%
        
        return (minWeight - padding, maxWeight + padding)
    }
    
    var body: some View {
        VStack(alignment: .leading, spacing: 10) {
            if chartData.isEmpty {
                // 当没有数据时显示提示信息
                VStack(spacing: 15) {
                    Image(systemName: "chart.line.downtrend.xyaxis")
                        .font(.system(size: 40))
                        .foregroundColor(.secondary)
                        .padding(.top, 20)
                    
                    Text("No weight data to display")
                        .font(.headline)
                        .foregroundColor(.secondary)
                    
                    Text("No weight records in the selected time period")
                        .font(.subheadline)
                        .foregroundColor(.secondary)
                        .multilineTextAlignment(.center)
                        .padding(.horizontal)
                        .padding(.bottom, 20)
                }
                .frame(maxWidth: .infinity, minHeight: 200)
            } else {
                Chart {
                    // 绘制早晨体重折线 - 使用foreground实现连接有数据的点
                    let morningData = chartData.filter { $0.isMorning }.sorted { $0.day < $1.day }
                    if !morningData.isEmpty {
                        // 按照连续的数据段分组绘制
                        let morningGroups = groupConsecutiveData(morningData)
                        ForEach(Array(morningGroups.enumerated()), id: \.0) { idx, group in
                            ForEach(group) { dataPoint in
                                LineMark(
                                    x: .value("Day", dataPoint.day),
                                    y: .value("Weight", dataPoint.weight)
                                )
                                .foregroundStyle(by: .value("Time", "Morning"))
                                .lineStyle(StrokeStyle(lineWidth: 2))
                                .interpolationMethod(.catmullRom)
                            }
                        }
                        
                        // 绘制数据点
                        ForEach(morningData) { dataPoint in
                            PointMark(
                                x: .value("Day", dataPoint.day),
                                y: .value("Weight", dataPoint.weight)
                            )
                            .foregroundStyle(by: .value("Time", "Morning"))
                            .symbolSize(dataPoint.isSelected ? 30 : 20) // 选中的日期点更大
                            .symbol(dataPoint.isSelected ? .circle : .circle) // 可以使用不同形状
                            .annotation(position: .top) {
                                // 显示所有数据点的注释，不再只限于特定日期
                                    Text(String(format: "%.1f", dataPoint.weight))
                                        .font(.caption2)
                                        .foregroundColor(.green)
                            }
                        }
                    }
                    
                    // 绘制晚上体重折线 - 分组绘制避免连接不连续的点
                    let eveningData = chartData.filter { !$0.isMorning }.sorted { $0.day < $1.day }
                    if !eveningData.isEmpty {
                        // 按照连续的数据段分组绘制
                        let eveningGroups = groupConsecutiveData(eveningData)
                        ForEach(Array(eveningGroups.enumerated()), id: \.0) { idx, group in
                            ForEach(group) { dataPoint in
                                LineMark(
                                    x: .value("Day", dataPoint.day),
                                    y: .value("Weight", dataPoint.weight)
                                )
                                .foregroundStyle(by: .value("Time", "Evening"))
                                .lineStyle(StrokeStyle(lineWidth: 2))
                                .interpolationMethod(.catmullRom)
                            }
                        }
                        
                        // 绘制数据点
                        ForEach(eveningData) { dataPoint in
                            PointMark(
                                x: .value("Day", dataPoint.day),
                                y: .value("Weight", dataPoint.weight)
                            )
                            .foregroundStyle(by: .value("Time", "Evening"))
                            .symbolSize(dataPoint.isSelected ? 30 : 20) // 选中的日期点更大
                            .symbol(dataPoint.isSelected ? .circle : .circle) // 可以使用不同形状
                            .annotation(position: .top) {
                                // 显示所有数据点的注释，不再只限于特定日期
                                    Text(String(format: "%.1f", dataPoint.weight))
                                        .font(.caption2)
                                        .foregroundColor(Color(red: 0.98, green: 0.85, blue: 0.35))
                            }
                        }
                    }
                    
                    // 添加垂直参考线标记所选日期
                    RuleMark(
                        x: .value("Selected Day", selectedDayIndex)
                    )
                    .foregroundStyle(.green)
                    .lineStyle(StrokeStyle(lineWidth: 2, dash: [5, 5]))
                }
                .chartForegroundStyleScale([
    "Morning": Color.green,
    "Evening": Color(red: 0.98, green: 0.85, blue: 0.35)
])
.chartXScale(domain: 1...31) // 扩展X轴范围到31，确保30标签能显示
.chartYScale(domain: yAxisRange.min...yAxisRange.max)
.chartXAxis {
    AxisMarks(values: [1, 5, 10, 15, 20, 25, 30]) { value in
        if let day = value.as(Int.self) {
            AxisValueLabel {
                // 确保显示所有标签，特别是30
                Text("\(day)")
                    .font(.caption)
                    .foregroundColor(.secondary)
                    .fixedSize() // 确保文本不被截断
            }
            .foregroundStyle(.secondary)
            AxisGridLine()
            AxisTick()
        }
    }
}
.chartYAxis {
    AxisMarks(position: .leading) { value in
        AxisValueLabel()
        AxisGridLine()
        AxisTick()
    }
}
.frame(minWidth: 0, maxWidth: .infinity, minHeight: 200) // 确保图表有足够宽度显示所有标签
.padding(.horizontal, 4) // 减少内边距但保留一些空间
.padding(.trailing, 16) // 右侧留出足够空间显示30标签

// 图例
HStack(spacing: 20) {
    Spacer()
    
    Text("Weight in \(userData.weightUnit)")
        .font(.caption)
        .foregroundColor(.secondary)
}
            }
        }
        .onReceive(NotificationCenter.default.publisher(for: Notification.Name("WeightDataUpdated"))) { _ in
            print("DailyWeightDetailChart: 收到体重数据更新通知，刷新图表...")
            self.refreshTrigger = UUID() // 刷新触发器更新将强制视图重新计算
        }
    }
    
    // 获取围绕指定日期的体重历史记录 - 确保前后15天
    private func getWeightHistoryAroundDate(_ date: Date, days: Int) -> [WeightRecord] {
        print("获取日期\(date)周围\(days)天的体重历史记录")
        let calendar = Calendar.current
        let selectedDay = calendar.startOfDay(for: date)
        
        // 如果userData.weightHistory为空，直接返回
        if userData.weightHistory.isEmpty {
            print("警告: userData.weightHistory为空")
            return []
        }
        
        print("开始处理，userData.weightHistory中共有\(userData.weightHistory.count)条记录")
        
        // 如果记录数量很少(少于5条)，直接返回所有记录
        if userData.weightHistory.count < 5 {
            print("记录数量少于5条，返回全部\(userData.weightHistory.count)条记录")
            return userData.weightHistory
        }
        
        // 确定日期范围 - 前后各15天
        let daysBeforeCount = days / 2  // 前15天
        let daysAfterCount = days - daysBeforeCount - 1  // 后14天（包括当天）
        
        guard let startDate = calendar.date(byAdding: .day, value: -daysBeforeCount, to: selectedDay),
              let endDate = calendar.date(byAdding: .day, value: daysAfterCount + 1, to: selectedDay) else {
            print("无法计算日期范围，返回全部记录")
            return userData.weightHistory
        }
        
        // 如果结束日期超过今天，则使用今天作为结束日期
        let today = calendar.startOfDay(for: Date())
        let effectiveEndDate = endDate > today ? calendar.date(byAdding: .day, value: 1, to: today)! : endDate
        
        print("查询日期范围: \(startDate) 到 \(effectiveEndDate)")
        
        // 从userData获取体重记录，并筛选日期范围内的记录
        let filteredRecords = userData.weightHistory.filter { record in
            let recordDate = calendar.startOfDay(for: record.date)
            return recordDate >= startDate && recordDate < effectiveEndDate
        }
        
        print("在日期范围内找到\(filteredRecords.count)条记录")
        
        // 如果过滤后没有找到记录，返回最近10条或全部记录
        if filteredRecords.isEmpty && !userData.weightHistory.isEmpty {
            print("过滤后没有记录，改为返回最近记录")
            let sortedRecords = userData.weightHistory.sorted(by: { $0.date > $1.date })
            let recentRecords = Array(sortedRecords.prefix(10))
            print("返回最近的\(recentRecords.count)条记录")
            return recentRecords
        }
        
        return filteredRecords
    }
    
    // 将数据点分组为连续的序列，以避免在缺少数据的地方连接折线
    private func groupConsecutiveData(_ data: [WeightDataPoint]) -> [[WeightDataPoint]] {
        guard !data.isEmpty else { return [] }
        
        var result: [[WeightDataPoint]] = []
        var currentGroup: [WeightDataPoint] = [data[0]]
        
        for i in 1..<data.count {
            // 如果当前点和前一个点是连续的（天数差为1），则添加到当前组
            if data[i].day - data[i-1].day == 1 {
                currentGroup.append(data[i])
            } else {
                // 否则，开始一个新组
                result.append(currentGroup)
                currentGroup = [data[i]]
            }
        }
        
        // 添加最后一组
        if !currentGroup.isEmpty {
            result.append(currentGroup)
        }
        
        return result
    }
}

// 向后兼容的MorningEveningWeightSection
struct MorningEveningWeightSection: View {
    let morningWeightString: String
    let eveningWeightString: String
    let weightUnit: String
    let onMorningEdit: () -> Void
    let onEveningEdit: () -> Void
    
    var body: some View {
        UnifiedWeightEditSection(
            morningWeightString: morningWeightString,
            eveningWeightString: eveningWeightString,
            weightUnit: weightUnit,
            onMorningEdit: onMorningEdit,
            onEveningEdit: onEveningEdit,
            style: .compact
        )
    }
}

// 访客模式Weight页面
struct GuestWeightView: View {
    @EnvironmentObject var userData: UserData
    @State private var animationScale1: CGFloat = 1.0
    @State private var animationScale2: CGFloat = 1.0
    @State private var animationScale3: CGFloat = 1.0
    @State private var animationScale4: CGFloat = 1.0
    
    var body: some View {
        GeometryReader { geometry in
            ZStack {
                // 线性渐变背景
                LinearGradient(
                    gradient: Gradient(colors: [
                        Color(red: 0.91, green: 0.96, blue: 0.91),
                        Color(red: 1, green: 1, blue: 1)
                    ]),
                    startPoint: .top,
                    endPoint: .bottom
                )
                .ignoresSafeArea()
                
                VStack(spacing: 0) {
                    // 上部分内容 - 向上移动
                    VStack(spacing: 40) {
                        Spacer()
                            .frame(height: geometry.size.height * 0.1) // 顶部间距
                        
                        // 加载动画效果 (类似引导页面的分析动画)
                        ZStack {
                            // 外圈 - 最慢的动画
                            Circle()
                                .stroke(Color.green.opacity(0.2), lineWidth: 8)
                                .frame(width: 200, height: 200)
                                .scaleEffect(animationScale1)
                                .animation(Animation.easeInOut(duration: 2.0).repeatForever(autoreverses: true), value: animationScale1)
                            
                            // 中外圈
                            Circle()
                                .stroke(Color.green.opacity(0.3), lineWidth: 6)
                                .frame(width: 160, height: 160)
                                .scaleEffect(animationScale2)
                                .animation(Animation.easeInOut(duration: 1.5).repeatForever(autoreverses: true), value: animationScale2)
                            
                            // 中内圈
                            Circle()
                                .fill(Color.green.opacity(0.4))
                                .frame(width: 120, height: 120)
                                .scaleEffect(animationScale3)
                                .animation(Animation.easeInOut(duration: 1.2).repeatForever(autoreverses: true), value: animationScale3)
                            
                            // 最内圈 - 最快的动画
                            Circle()
                                .fill(Color.green)
                                .frame(width: 80, height: 80)
                                .scaleEffect(animationScale4)
                                .animation(Animation.easeInOut(duration: 1.0).repeatForever(autoreverses: true), value: animationScale4)
                        }
                        
                        // 文本内容
                        VStack(spacing: 16) {
                            Text("Weight Management")
                                .font(.custom("Inter", size: 24))
                                .fontWeight(.medium) // 字重500
                                .foregroundColor(Color(red: 0.11, green: 0.37, blue: 0.13))
                            
                            Text("Login to view personal data and\nunlock premium features")
                                .font(.custom("Inter", size: 16))
                                .fontWeight(.regular) // 字重400
                                .foregroundColor(Color(red: 0.29, green: 0.33, blue: 0.39))
                                .multilineTextAlignment(.center)
                                .lineSpacing(8) // 行高24px - 字号16px = 8px行间距
                        }
                        
                        Spacer()
                    }
                    
                    // Login按钮 - 与tab栏保持1/3距离
                    VStack {
                        Spacer()
                            .frame(height: geometry.size.height * 0.1) // 与tab栏的1/3距离
                        
                        Button(action: {
                            userData.exitGuestMode()
                        }) {
                            Text("Login")
                                .font(.system(size: 18, weight: .semibold))
                                .foregroundColor(.white)
                                .frame(maxWidth: .infinity)
                                .padding(.vertical, 16)
                                .background(Color(red: 0.18, green: 0.49, blue: 0.2))
                                .cornerRadius(12)
                        }
                        .padding(.horizontal, 24)
                        .padding(.bottom, geometry.safeAreaInsets.bottom + 20)
                    }
                }
            }
        }
        .onAppear {
            // 启动动画
            animationScale1 = 0.8
            animationScale2 = 1.2
            animationScale3 = 0.9
            animationScale4 = 1.1
        }
    }
}
