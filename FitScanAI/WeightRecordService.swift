import Foundation
import SwiftUI

struct WeightRecordAPI: Codable {
    let createdTime: String?
    let dateStr: String?
    let weight: Double
    let timeType: String
    let id: Int?
    
    // 时间转换为Date对象
    func getDate() -> Date? {
        // 优先使用dateStr字段解析日期 (格式为YYYYMMDD)
        if let dateString = dateStr {
            let formatter = DateFormatter()
            formatter.dateFormat = "yyyyMMdd"
            if let date = formatter.date(from: dateString) {
                return date
            } else {
                print("⚠️ 无法解析dateStr格式: \(dateString)")
            }
        }
        
        // 尝试使用createdTime字段
        if let timeString = createdTime {
            let formatter = ISO8601DateFormatter()
            return formatter.date(from: timeString)
        }
        
        // 当既没有dateStr也没有createdTime时，记录警告并返回当前日期
        print("⚠️ 记录既没有dateStr也没有createdTime字段，使用当前日期")
        return Date()
    }
}

struct WeightRecordResponse: Codable {
    let code: Int
    let message: String?
    let data: [WeightRecordAPI]?
    let timestamp: Int64?
}

struct WeightRecordCreateRequest: Codable {
    let weight: Double
    let timeType: String
    let dateStr: String
}

struct WeightRecordCreateResponse: Codable {
    let code: Int
    let message: String?
    let data: Bool?
    let timestamp: Int64?
}

class WeightRecordService {
    static let shared = WeightRecordService()
    
    private let baseURL = "https://fsai.pickgoodspro.com"
    private init() {}
    
    // 创建/更新体重记录
    func createWeightRecord(weight: Double, timeType: String, dateStr: String, userData: UserData, completion: @escaping (Result<Bool, Error>) -> Void) {
        // 构建URL - 使用新的编辑接口
        let urlString = "\(baseURL)/ns/app/weight-records/edit"
        guard let url = URL(string: urlString) else {
            let error = NSError(domain: "WeightRecordError", code: 0, userInfo: [NSLocalizedDescriptionKey: "无效的URL"])
            completion(.failure(error))
            return
        }
        
        // 创建请求
        var request = URLRequest(url: url)
        request.httpMethod = "POST"
        request.addValue("application/json", forHTTPHeaderField: "Content-Type")
        
        // 添加授权头
        guard !userData.accessToken.isEmpty else {
            let error = NSError(domain: "WeightRecordError", code: 401, userInfo: [NSLocalizedDescriptionKey: "未登录或访问令牌无效"])
            completion(.failure(error))
            return
        }
        request.addValue("Bearer \(userData.accessToken)", forHTTPHeaderField: "Authorization")
        
        // 创建请求体
        let recordRequest = WeightRecordCreateRequest(weight: weight, timeType: timeType, dateStr: dateStr)
        
        do {
            let jsonData = try JSONEncoder().encode(recordRequest)
            request.httpBody = jsonData
            
            // 日志信息
            print("🌐 创建/更新体重记录 [POST] \(urlString)")
            print("🔑 请求头: \(request.allHTTPHeaderFields ?? [:])")
            if let bodyString = String(data: jsonData, encoding: .utf8) {
                print("📦 请求体: \(bodyString)")
            }
            
            // 发送请求
            let task = URLSession.shared.dataTask(with: request) { data, response, error in
                if let error = error {
                    print("❌ 创建/更新体重记录错误: \(error.localizedDescription)")
                    DispatchQueue.main.async {
                        completion(.failure(error))
                    }
                    return
                }
                
                guard let data = data else {
                    let error = NSError(domain: "WeightRecordError", code: 0, userInfo: [NSLocalizedDescriptionKey: "服务器返回空数据"])
                    print("❌ 创建/更新体重记录错误: 服务器返回空数据")
                    DispatchQueue.main.async {
                        completion(.failure(error))
                    }
                    return
                }
                
                // 打印响应数据
                if let responseString = String(data: data, encoding: .utf8) {
                    print("📄 创建/更新体重记录响应: \(responseString)")
                }
                
                do {
                    let response = try JSONDecoder().decode(WeightRecordCreateResponse.self, from: data)
                    
                    if response.code == 0, let isSuccess = response.data, isSuccess {
                        print("✅ 体重记录创建/更新成功")
                        DispatchQueue.main.async {
                            completion(.success(true))
                        }
                    } else {
                        let errorMessage = response.message ?? "创建/更新体重记录失败"
                        let error = NSError(domain: "WeightRecordError", code: response.code, userInfo: [NSLocalizedDescriptionKey: errorMessage])
                        print("❌ 创建/更新体重记录API错误: \(errorMessage)")
                        DispatchQueue.main.async {
                            completion(.failure(error))
                        }
                    }
                } catch {
                    print("❌ 解析创建/更新体重记录响应失败: \(error.localizedDescription)")
                    DispatchQueue.main.async {
                        completion(.failure(error))
                    }
                }
            }
            task.resume()
        } catch {
            print("❌ 编码请求体失败: \(error.localizedDescription)")
            completion(.failure(error))
        }
    }
    
    // 获取用户体重记录
    func getUserWeightRecords(date: String, timeType: String, userData: UserData, completion: @escaping (Result<[WeightRecordAPI], Error>) -> Void) {
        // 构建URL
        var urlComponents = URLComponents(string: "\(baseURL)/ns/app/weight-records/user")
        
        // 添加必需的查询参数
        let queryItems = [
            URLQueryItem(name: "date", value: date),
            URLQueryItem(name: "timeType", value: timeType)
        ]
        urlComponents?.queryItems = queryItems
        
        guard let url = urlComponents?.url else {
            let error = NSError(domain: "WeightRecordError", code: 0, userInfo: [NSLocalizedDescriptionKey: "无效的URL"])
            completion(.failure(error))
            return
        }
        
        // 创建请求
        var request = URLRequest(url: url)
        request.httpMethod = "GET"
        
        // 添加授权头
        guard !userData.accessToken.isEmpty else {
            let error = NSError(domain: "WeightRecordError", code: 401, userInfo: [NSLocalizedDescriptionKey: "未登录或访问令牌无效"])
            completion(.failure(error))
            return
        }
        request.addValue("Bearer \(userData.accessToken)", forHTTPHeaderField: "Authorization")
        
        // 日志信息
        print("🌐 获取用户体重记录 [GET] \(url)")
        print("🔑 请求头: \(request.allHTTPHeaderFields ?? [:])")
        print("📝 查询参数 - date: \(date), timeType: \(timeType)")
        
        // 发送请求
        let task = URLSession.shared.dataTask(with: request) { data, response, error in
            if let error = error {
                print("❌ 获取体重记录错误: \(error.localizedDescription)")
                DispatchQueue.main.async {
                    completion(.failure(error))
                }
                return
            }
            
            guard let data = data else {
                let error = NSError(domain: "WeightRecordError", code: 0, userInfo: [NSLocalizedDescriptionKey: "服务器返回空数据"])
                print("❌ 获取体重记录错误: 服务器返回空数据")
                DispatchQueue.main.async {
                    completion(.failure(error))
                }
                return
            }
            
            // 打印响应数据
            if let responseString = String(data: data, encoding: .utf8) {
                print("📄 获取体重记录响应: \(responseString)")
            }
            
            do {
                let response = try JSONDecoder().decode(WeightRecordResponse.self, from: data)
                
                if response.code == 0, let records = response.data {
                    print("✅ 成功获取 \(records.count) 条体重记录")
                    
                    // 打印记录中的日期字段，便于调试
                    for (index, record) in records.enumerated() {
                        print("📅 记录[\(index)]: id=\(record.id ?? 0), dateStr=\(record.dateStr ?? "nil"), 解析日期=\(record.getDate()?.description ?? "解析失败")")
                    }
                    
                    DispatchQueue.main.async {
                        completion(.success(records))
                    }
                } else {
                    let errorMessage = response.message ?? "获取体重记录失败"
                    let error = NSError(domain: "WeightRecordError", code: response.code, userInfo: [NSLocalizedDescriptionKey: errorMessage])
                    print("❌ 获取体重记录API错误: \(errorMessage)")
                    DispatchQueue.main.async {
                        completion(.failure(error))
                    }
                }
            } catch {
                print("❌ 解析获取体重记录响应失败: \(error.localizedDescription)")
                DispatchQueue.main.async {
                    completion(.failure(error))
                }
            }
        }
        task.resume()
    }
    
    // 获取指定日期的体重记录
    func getUserWeightRecordsForDay(date: String, userData: UserData, completion: @escaping (Result<[WeightRecordAPI], Error>) -> Void) {
        print("🔍 开始获取日期\(date)的所有体重记录（AM和PM）...")
        
        // 首先获取早晨的记录
        getUserWeightRecords(date: date, timeType: "AM", userData: userData) { resultAM in
            switch resultAM {
            case .success(let amRecords):
                print("✅ 成功获取\(date)的早晨(AM)体重记录: \(amRecords.count)条")
                
                // 然后获取晚上的记录
                self.getUserWeightRecords(date: date, timeType: "PM", userData: userData) { resultPM in
                    switch resultPM {
                    case .success(let pmRecords):
                        print("✅ 成功获取\(date)的晚上(PM)体重记录: \(pmRecords.count)条")
                        
                        // 合并两组记录
                        let allRecords = amRecords + pmRecords
                        print("✅ 日期\(date)共获取\(allRecords.count)条体重记录")
                
                DispatchQueue.main.async {
                            completion(.success(allRecords))
                        }
                        
                    case .failure(let error):
                        print("❌ 获取\(date)的晚上体重记录失败: \(error.localizedDescription)")
                        // 即使PM记录获取失败，仍然返回AM记录
                        DispatchQueue.main.async {
                            completion(.success(amRecords))
                        }
                    }
                }
                
            case .failure(let error):
                print("❌ 获取\(date)的早晨体重记录失败: \(error.localizedDescription)")
                DispatchQueue.main.async {
                    completion(.failure(error))
                }
            }
        }
    }
    
    // 获取指定日期范围内的体重记录
    func getUserWeightRecordsForDateRange(startDate: Date, endDate: Date, userData: UserData, completion: @escaping (Result<[WeightRecordAPI], Error>) -> Void) {
        // 用于存储所有记录
        var allRecords: [WeightRecordAPI] = []
        let dateFormatter = DateFormatter()
        dateFormatter.dateFormat = "yyyyMMdd"
        
        // 创建一个任务组
        let dispatchGroup = DispatchGroup()
        
        // 获取日期范围内的每一天
        var currentDate = startDate
        let calendar = Calendar.current
        
        print("🔍 获取从\(dateFormatter.string(from: startDate))到\(dateFormatter.string(from: endDate))的体重记录")
        
        while currentDate <= endDate {
            // 格式化当前日期
            let dateStr = dateFormatter.string(from: currentDate)
            
            // 进入任务组
            dispatchGroup.enter()
            
            // 获取当天记录
            getUserWeightRecordsForDay(date: dateStr, userData: userData) { result in
                switch result {
                case .success(let dayRecords):
                    // 添加到总记录中
                    allRecords.append(contentsOf: dayRecords)
                    print("📅 获取\(dateStr)体重记录: \(dayRecords.count)条")
                case .failure(let error):
                    print("❌ 获取\(dateStr)体重记录失败: \(error.localizedDescription)")
                }
                
                // 离开任务组
                dispatchGroup.leave()
            }
            
            // 前进到下一天
            guard let nextDay = calendar.date(byAdding: .day, value: 1, to: currentDate) else { break }
            currentDate = nextDay
        }
        
        // 当所有任务完成时
        dispatchGroup.notify(queue: .main) {
            print("✅ 日期范围内共获取\(allRecords.count)条体重记录")
            completion(.success(allRecords))
        }
    }
    
    // 辅助方法: 获取按日期分组的体重记录
    func getWeightRecordsByDay(records: [WeightRecordAPI]) -> [String: [WeightRecordAPI]] {
        var recordsByDay: [String: [WeightRecordAPI]] = [:]
        
        let dateFormatter = DateFormatter()
        dateFormatter.dateFormat = "yyyy-MM-dd"
        
        for record in records {
            if let date = record.getDate() {
                let dateString = dateFormatter.string(from: date)
                if recordsByDay[dateString] == nil {
                    recordsByDay[dateString] = []
                }
                recordsByDay[dateString]?.append(record)
            }
        }
        
        return recordsByDay
    }
    
    // 辅助方法: 获取过去N天的体重记录
    func getWeightRecordsForPastDays(records: [WeightRecordAPI], days: Int) -> [WeightRecordAPI] {
        let currentDate = Date()
        let calendar = Calendar.current
        let pastDate = calendar.date(byAdding: .day, value: -days, to: currentDate)!
        
        return records.filter { record in
            if let date = record.getDate() {
                return date >= pastDate && date <= currentDate
            }
            return false
        }
    }
    
    // 辅助方法: 根据日期和时间类型获取特定体重记录
    func getWeightRecord(for date: Date, timeType: String, from records: [WeightRecordAPI]) -> WeightRecordAPI? {
        let calendar = Calendar.current
        let dateComponents = calendar.dateComponents([.year, .month, .day], from: date)
        
        return records.first { record in
            guard let recordDate = record.getDate() else { return false }
            let recordComponents = calendar.dateComponents([.year, .month, .day], from: recordDate)
            return dateComponents.year == recordComponents.year &&
                   dateComponents.month == recordComponents.month &&
                   dateComponents.day == recordComponents.day &&
                   record.timeType == timeType
        }
    }
    
    // 辅助方法: 转换KG为LBS
    func kgToLbs(_ kg: Double) -> Double {
        return kg * 2.205
    }
    
    // 辅助方法: 转换LBS为KG
    func lbsToKg(_ lbs: Double) -> Double {
        return lbs / 2.205
    }
    
    // 辅助方法: 根据用户设置的单位转换体重值用于显示
    func weightForDisplay(kg: Double, userData: UserData) -> Double {
        return userData.weightUnit == "lbs" ? kgToLbs(kg) : kg
    }
} 