import SwiftUI

/// 管理连续打卡天数的卡片视图
struct StreakCardView: View {
    let streakDays: Int
    let hasGoal: Bool
    let daysSinceLastCheckIn: Int
    let consecutiveNonCheckInDays: Int // 新增：连续未打卡天数
    
    // 提供默认参数的初始化器，兼容现有调用
    init(streakDays: Int, hasGoal: Bool = true, daysSinceLastCheckIn: Int = 0, consecutiveNonCheckInDays: Int = 0) {
        self.streakDays = streakDays
        self.hasGoal = hasGoal
        self.daysSinceLastCheckIn = daysSinceLastCheckIn
        self.consecutiveNonCheckInDays = consecutiveNonCheckInDays
    }
    
    var body: some View {
        // 只有在有目标且匹配特定条件时才显示卡片
        Group {
            if !hasGoal {
                // 未设置目标显示开始旅程卡片
                startJourneyCard()
                    .background(Color.green.opacity(0.1))
                    .cornerRadius(12)
                    .padding(.horizontal)
            } else if hasGoal && streakDays == 7 {
                // 图一：连续打卡7天（需要有目标）
                keepGoingCard()
                    .background(Color.green.opacity(0.1))
                    .cornerRadius(12)
                    .padding(.horizontal)
            } else if hasGoal && streakDays == 15 {
                // 图二：连续打卡15天（需要有目标）
                fantasticProgressCard()
                    .background(Color.green.opacity(0.1))
                    .cornerRadius(12)
                    .padding(.horizontal)
            } else if hasGoal && streakDays == 17 {
                // 图三：连续打卡17天（需要有目标）
                nearlyThereCard()
                    .background(Color.green.opacity(0.1))
                    .cornerRadius(12)
                    .padding(.horizontal)
            } else if hasGoal && streakDays == 30 {
                // 图四：连续打卡30天（需要有目标）
                fireStreakCard()
                    .background(Color.green.opacity(0.1))
                    .cornerRadius(12)
                    .padding(.horizontal)
            } else if hasGoal && streakDays == 50 {
                // 图五：连续打卡50天（需要有目标）
                outstandingStreakCard()
                    .background(Color.green.opacity(0.1))
                    .cornerRadius(12)
                    .padding(.horizontal)
            } else if hasGoal && streakDays == 51 {
                // 新增：连续打卡51天（需要有目标） - Amazing!
                amazingStreakCard()
                    .background(Color(red: 1, green: 0.97, blue: 0.88))
                    .cornerRadius(12)
                    .padding(.horizontal)
            } else if shouldShowBackOnTrackCard() {
                // 连续N天没打卡（N<5）显示Back on Track
                backOnTrackCard()
                    .background(Color.green.opacity(0.1))
                    .cornerRadius(12)
                    .padding(.horizontal)
            } else if shouldShowDontGiveUpCard() {
                // 连续N天没打卡（N≥5）显示Don't Give Up
                dontGiveUpCard()
                    .background(Color.red.opacity(0.1))
                    .cornerRadius(12)
                    .padding(.horizontal)
            } else if shouldShowWelcomeBackCard() {
                // 图六：前天未打卡，但昨天打卡完成，今天显示
                welcomeBackCard()
                    .background(Color.green.opacity(0.1))
                    .cornerRadius(12)
                    .padding(.horizontal)
            } else if shouldShowBackOnTrackStreakCard() {
                // 图七：如周一未打卡，但周二周三周四成功连续打卡，周五显示
                backOnTrackStreakCard()
                    .background(Color.green.opacity(0.1))
                    .cornerRadius(12)
                    .padding(.horizontal)
            } else {
                // 其他情况不显示任何卡片，也不应用任何背景色
                EmptyView()
            }
        }
    }
    
    // 新增：判断是否显示"Back on Track"卡片（基于连续未打卡天数）
    // 条件：有设立目标 + 连续N天没打卡（N<5且N>0）
    private func shouldShowBackOnTrackCard() -> Bool {
        return hasGoal && consecutiveNonCheckInDays > 0 && consecutiveNonCheckInDays < 5
    }
    
    // 新增：判断是否显示"Don't Give Up"卡片
    // 条件：有设立目标 + 连续N天没打卡（N≥5）
    private func shouldShowDontGiveUpCard() -> Bool {
        return hasGoal && consecutiveNonCheckInDays >= 5
    }
    
    // 原有：判断是否显示"Welcome Back"卡片
    // 图六：前天未打卡，但昨天打卡完成，今天显示
    private func shouldShowWelcomeBackCard() -> Bool {
        // 需要有目标 + 当前连续打卡天数为1天，且前一天有未打卡记录
        return hasGoal && streakDays == 1 && daysSinceLastCheckIn >= 1
    }
    
    // 原有：判断是否显示"Back on Track"卡片（基于连续打卡）  
    // 图七：如周一未打卡，但周二周三周四成功连续打卡，周五显示
    private func shouldShowBackOnTrackStreakCard() -> Bool {
        // 需要有目标 + 当前连续打卡天数为3天，且之前有未打卡记录
        return hasGoal && streakDays == 3 && daysSinceLastCheckIn >= 1
    }
    
    // 未设置目标卡片
    private func startJourneyCard() -> some View {
        VStack(alignment: .leading, spacing: 0) {
            HStack {
                Text("Start Your Journey!")
                    .font(.headline)
                
                Spacer()
                
                Image(systemName: "star")
                    .foregroundColor(.green)
            }
            
            Text("Set a goal to begin tracking")
                .font(.caption)
                .foregroundColor(.secondary)
        }
        .padding()
    }
    
    // 新增：Back on Track卡片（基于连续未打卡天数，N<5）
    private func backOnTrackCard() -> some View {
        VStack(alignment: .leading, spacing: 0) {
            HStack {
                Text("Back on Track!")
                    .font(.headline)
                
                Spacer()
                
                Image(systemName: "star")
                    .foregroundColor(.green)
            }
            
            Text("\(consecutiveNonCheckInDays) days since your last break")
                .font(.caption)
                .foregroundColor(.secondary)
        }
        .padding()
    }
    
    // 图一：7天打卡卡片
    private func keepGoingCard() -> some View {
        VStack(alignment: .leading, spacing: 0) {
            HStack {
                Text("Keep Going!")
                    .font(.headline)
                
                Spacer()
                
                Image(systemName: "star")
                    .foregroundColor(.green)
            }
            
            Text("Current streak: 7days")
                .font(.caption)
                .foregroundColor(.secondary)
        }
        .padding()
    }
    
    // 图二：15天打卡卡片
    private func fantasticProgressCard() -> some View {
        VStack(alignment: .leading, spacing: 0) {
            HStack {
                Text("Fantastic Progress!")
                    .font(.headline)
                
                Spacer()
                
                Image(systemName: "star.fill")
                    .foregroundColor(.green)
            }
            
            Text("Current streak: 15 days")
                .font(.caption)
                .foregroundColor(.secondary)
        }
        .padding()
    }
    
    // 图三：17天打卡卡片
    private func nearlyThereCard() -> some View {
        VStack(alignment: .leading, spacing: 0) {
            HStack {
                Text("Nearly There!")
                    .font(.headline)
                
                Spacer()
                
                Image(systemName: "star")
                    .foregroundColor(.green)
            }
            
            Text("Just 3 days to reach 20 days streak")
                .font(.caption)
                .foregroundColor(.secondary)
        }
        .padding()
    }
    
    // 图四：30天打卡卡片
    private func fireStreakCard() -> some View {
        VStack(alignment: .leading, spacing: 0) {
            HStack {
                Text("You're On Fire!")
                    .font(.headline)
                
                Spacer()
                
                Image(systemName: "trophy")
                    .foregroundColor(.green)
            }
            
            Text("Current streak: 30 days")
                .font(.caption)
                .foregroundColor(.secondary)
        }
        .padding()
    }
    
    // 图五：50天打卡卡片
    private func outstandingStreakCard() -> some View {
        VStack(alignment: .leading, spacing: 0) {
            HStack {
                Text("Outstanding!")
                    .font(.headline)
                
                Spacer()
                
                Image(systemName: "crown")
                    .foregroundColor(.green)
            }
            
            Text("You've reached 50 days total")
                .font(.caption)
                .foregroundColor(.secondary)
        }
        .padding()
    }
    
    // Don't Give Up卡片（连续N天没打卡，N≥5）
    private func dontGiveUpCard() -> some View {
        VStack(alignment: .leading, spacing: 0) {
            HStack {
                Text("Don't Give Up!")
                    .font(.headline)
                
                Spacer()
                
                Image(systemName: "heart.fill")
                    .foregroundColor(Color.red.opacity(0.3))
            }
            
            Text("Streak reset - Start fresh today")
                .font(.caption)
                .foregroundColor(.secondary)
        }
        .padding()
    }
    
    // 图六：前天未打卡，但昨天打卡完成，今天显示
    private func welcomeBackCard() -> some View {
        VStack(alignment: .leading, spacing: 0) {
            HStack {
                Text("Welcome Back!")
                    .font(.headline)
                
                Spacer()
                
                Image(systemName: "star")
                    .foregroundColor(.green)
            }
            
            Text("You've taken a break, but you're back on track now")
                .font(.caption)
                .foregroundColor(.secondary)
        }
        .padding()
    }
    
    // 图七：如周一未打卡，但周二周三周四成功连续打卡，周五显示
    private func backOnTrackStreakCard() -> some View {
        VStack(alignment: .leading, spacing: 0) {
            HStack {
                Text("Back on Track!")
                    .font(.headline)
                
                Spacer()
                
                Image(systemName: "star")
                    .foregroundColor(.green)
            }
            
            Text("You've been consistent, keep it up!")
                .font(.caption)
                .foregroundColor(.secondary)
        }
        .padding()
    }
    
    // 新增：连续打卡51天 - Amazing!
    private func amazingStreakCard() -> some View {
        VStack(alignment: .leading, spacing: 0) {
            HStack {
                Text("Amazing!")
                    .font(.headline)
                
                Spacer()
                
                Image(systemName: "trophy")
                    .foregroundColor(.orange)
            }
            
            Text("Keep going! Every day counts!")
                .font(.caption)
                .foregroundColor(.secondary)
        }
        .padding()
    }
}

// 预览
struct StreakCardView_Previews: PreviewProvider {
    static var previews: some View {
        VStack(spacing: 20) {
            StreakCardView(streakDays: 0, hasGoal: false, daysSinceLastCheckIn: 0)
                .previewDisplayName("无目标")
            
            StreakCardView(streakDays: 0, hasGoal: true, daysSinceLastCheckIn: 3, consecutiveNonCheckInDays: 3)
                .previewDisplayName("新增：Back on Track - 3天")
            
            StreakCardView(streakDays: 0, hasGoal: true, daysSinceLastCheckIn: 5, consecutiveNonCheckInDays: 5)
                .previewDisplayName("新增：Don't Give Up - 5天")
            
            StreakCardView(streakDays: 1, hasGoal: true, daysSinceLastCheckIn: 1)
                .previewDisplayName("图六：Welcome Back")
            
            StreakCardView(streakDays: 3, hasGoal: true, daysSinceLastCheckIn: 1)
                .previewDisplayName("图七：Back on Track Streak")
            
            StreakCardView(streakDays: 7)
                .previewDisplayName("图一：连续7天")
            
            StreakCardView(streakDays: 15)
                .previewDisplayName("图二：连续15天")
            
            StreakCardView(streakDays: 17)
                .previewDisplayName("图三：连续17天")
            
            StreakCardView(streakDays: 30)
                .previewDisplayName("图四：连续30天")
            
            StreakCardView(streakDays: 50)
                .previewDisplayName("图五：连续50天")
            
            StreakCardView(streakDays: 51)
                .previewDisplayName("新增：连续打卡51天 - Amazing!")
        }
        .padding()
    }
} 