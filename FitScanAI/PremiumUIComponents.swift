import SwiftUI
import Charts

// MARK: - Plan页面的Premium组件

// 周营养组件 - 未订阅状态
struct WeeklyNutritionPremiumView: View {
    @EnvironmentObject var userData: UserData
    @State private var showPremiumView = false
    
    var body: some View {
        VStack(spacing: 16) {
            HStack {
                Text("This Week's Nutrition")
                    .font(.headline)
                    .fontWeight(.semibold)
                Spacer()
            }
            
            // 营养数据容器，添加灰色背景和锁定遮罩
            HStack(spacing: 15) {
                // Protein
                VStack(spacing: 8) {
                    ZStack {
                        Circle()
                            .stroke(Color.gray.opacity(0.2), lineWidth: 4)
                            .frame(width: 70, height: 70)
                        
                        Text("--")
                            .font(.caption)
                            .fontWeight(.medium)
                            .foregroundColor(.gray)
                    }
                    
                    Text("Protein")
                        .font(.caption)
                        .foregroundColor(.secondary)
                    
                    Text("--/--g")
                        .font(.caption)
                        .foregroundColor(.secondary)
                }
                .padding(12)
                .background(Color(red: 0.98, green: 0.98, blue: 0.98))
                .cornerRadius(12)
                .frame(maxWidth: .infinity)
                
                // Fat
                VStack(spacing: 8) {
                    ZStack {
                        Circle()
                            .stroke(Color.gray.opacity(0.2), lineWidth: 4)
                            .frame(width: 70, height: 70)
                        
                        Text("--")
                            .font(.caption)
                            .fontWeight(.medium)
                            .foregroundColor(.gray)
                    }
                    
                    Text("Fat")
                        .font(.caption)
                        .foregroundColor(.secondary)
                    
                    Text("--/--g")
                        .font(.caption)
                        .foregroundColor(.secondary)
                }
                .padding(12)
                .background(Color(red: 0.98, green: 0.98, blue: 0.98))
                .cornerRadius(12)
                .frame(maxWidth: .infinity)
                
                // Calories
                VStack(spacing: 8) {
                    ZStack {
                        Circle()
                            .stroke(Color.gray.opacity(0.2), lineWidth: 4)
                            .frame(width: 70, height: 70)
                        
                        Text("--")
                            .font(.caption)
                            .fontWeight(.medium)
                            .foregroundColor(.gray)
                    }
                    
                    Text("Calories")
                        .font(.caption)
                        .foregroundColor(.secondary)
                    
                    Text("--/--kcal")
                        .font(.caption)
                        .foregroundColor(.secondary)
                }
                .padding(12)
                .background(Color(red: 0.98, green: 0.98, blue: 0.98))
                .cornerRadius(12)
                .frame(maxWidth: .infinity)
            }
            .overlay(
                // 锁定遮罩
                RoundedRectangle(cornerRadius: 12)
                    .fill(Color.white.opacity(0.8))
                    .overlay(
                        VStack(spacing: 8) {
                            Image(systemName: "lock.fill")
                                .font(.title2)
                                .foregroundColor(.gray)
                            
                            Text("Premium Feature")
                                .font(.subheadline)
                                .fontWeight(.medium)
                                .foregroundColor(.gray)
                        }
                    )
            )
            
           
            
            // 添加订阅按钮
            Button(action: {
                showPremiumView = true
            }) {
                Text("upgrade to pro")
                    .font(.subheadline)
                    .fontWeight(.medium)
                    .foregroundColor(.white)
                    .frame(width: 350, height: 40)
                    .background(Color(red: 0.3, green: 0.69, blue: 0.31))
                    .cornerRadius(9999)
            }
        }
        .padding()
        .background(Color.white)
        .cornerRadius(12)
        .fullScreenCover(isPresented: $showPremiumView, onDismiss: {
            // 当订阅视图关闭时，主动刷新状态，确保UI正确更新
            Task {
                await userData.checkAndUpdateSubscriptionStatus()
            }
        }) {
            NavigationView {
                FullScreenPremiumView()
                    .environmentObject(userData)
                    .navigationBarTitle("Premium Features", displayMode: .inline)
                    .navigationBarTitleDisplayMode(.inline)
                    .toolbar {
                        ToolbarItem(placement: .navigationBarLeading) {
                            Button("Close") {
                                showPremiumView = false
                            }
                        }
                    }
            }
        }
    }
}

// 推荐活动组件 - 未订阅状态
struct RecommendedActivitiesPremiumView: View {
    @EnvironmentObject var userData: UserData
    @State private var showPremiumView = false
    
    var body: some View {
        VStack(spacing: 12) {
            HStack {
                Text("Recommended Activities")
                    .font(.headline)
                    .fontWeight(.semibold)
                Spacer()
            }
            
            // 灰色背景框架包裹运动卡片和提示文本
            VStack(spacing: 24) {
                HStack(spacing: 30) {
                    // Walking
                    VStack(spacing: 12) {
                        ZStack {
                            Circle()
                                .fill(Color.gray.opacity(0.1))
                                .frame(width: 65, height: 65)
                            
                            VStack(spacing: 1) {
                                Image(systemName: "figure.walk")
                                    .font(.title3)
                                    .foregroundColor(.gray)
                                    .frame(width: 20, height: 16)
                                
                                Image(systemName: "lock.fill")
                                    .font(.caption2)
                                    .foregroundColor(.gray)
                                    .frame(width: 12, height: 12)
                            }
                        }
                        
                        Text("Walking")
                            .font(.caption)
                            .foregroundColor(.secondary)
                    }
                    .frame(maxWidth: .infinity)
                    
                    // Swimming
                    VStack(spacing: 12) {
                        ZStack {
                            Circle()
                                .fill(Color.gray.opacity(0.1))
                                .frame(width: 65, height: 65)
                            
                            VStack(spacing: 1) {
                                Image(systemName: "figure.pool.swim")
                                    .font(.title3)
                                    .foregroundColor(.gray)
                                    .frame(width: 20, height: 16)
                                
                                Image(systemName: "lock.fill")
                                    .font(.caption2)
                                    .foregroundColor(.gray)
                                    .frame(width: 12, height: 12)
                            }
                        }
                        
                        Text("Swimming")
                            .font(.caption)
                            .foregroundColor(.secondary)
                    }
                    .frame(maxWidth: .infinity)
                    
                    // Cycling
                    VStack(spacing: 12) {
                        ZStack {
                            Circle()
                                .fill(Color.gray.opacity(0.1))
                                .frame(width: 65, height: 65)
                            
                            VStack(spacing: 1) {
                                Image(systemName: "bicycle")
                                    .font(.title3)
                                    .foregroundColor(.gray)
                                    .frame(width: 20, height: 16)
                                
                                Image(systemName: "lock.fill")
                                    .font(.caption2)
                                    .foregroundColor(.gray)
                                    .frame(width: 12, height: 12)
                            }
                        }
                        
                        Text("Cycling")
                            .font(.caption)
                            .foregroundColor(.secondary)
                    }
                    .frame(maxWidth: .infinity)
                }
                
                
            }
            .padding(20)
            .background(Color(red: 0.98, green: 0.98, blue: 0.98))
            .cornerRadius(8)
            
            Button(action: {
                showPremiumView = true
            }) {
                Text("upgrade to pro")
                    .font(.subheadline)
                    .fontWeight(.medium)
                    .foregroundColor(.white)
                    .frame(width: 350, height: 40)
                    .background(Color(red: 0.3, green: 0.69, blue: 0.31))
                    .cornerRadius(9999)
            }
        }
        .padding()
        .background(Color.white)
        .cornerRadius(12)
        .fullScreenCover(isPresented: $showPremiumView, onDismiss: {
            // 当订阅视图关闭时，主动刷新状态，确保UI正确更新
            Task {
                await userData.checkAndUpdateSubscriptionStatus()
            }
        }) {
            NavigationView {
                FullScreenPremiumView()
                    .environmentObject(userData)
                    .navigationBarTitle("Premium Features", displayMode: .inline)
                    .navigationBarTitleDisplayMode(.inline)
                    .toolbar {
                        ToolbarItem(placement: .navigationBarLeading) {
                            Button("Close") {
                                showPremiumView = false
                            }
                        }
                    }
            }
        }
    }
}

// Premium Tips组件 - 未订阅状态
struct PremiumTipsView: View {
    @EnvironmentObject var userData: UserData
    @State private var showPremiumView = false
    
    var body: some View {
        VStack(spacing: 16) {
            // 不显示title文本，直接开始组件内容
            
            // 将所有内容放在灰色背景框架内
            VStack(spacing: 12) {
                VStack(spacing: 8) {
                    // 锁图标和Premium Tips在一行
                    HStack {
                        Image(systemName: "lock.fill")
                            .foregroundColor(.gray)
                            .font(.subheadline)
                        
                        Text("Premium Tips")
                            .font(.subheadline)
                            .fontWeight(.medium)
                        
                        Spacer()
                    }
                }
                
                // 提示文本在组件内下一行靠左对齐
                HStack {
                    Text("Upgrade to Premium to access personalized tips")
                        .font(.caption)
                        .foregroundColor(.secondary)
                        .multilineTextAlignment(.leading)
                    Spacer()
                }
            }
            .padding()
            .frame(height: 112)
            .background(Color(red: 0.98, green: 0.98, blue: 0.98))
            .cornerRadius(12)
            
            // 添加订阅按钮
            Button(action: {
                showPremiumView = true
            }) {
                Text("upgrade to pro")
                    .font(.subheadline)
                    .fontWeight(.medium)
                    .foregroundColor(.white)
                    .frame(width: 350, height: 40)
                    .background(Color(red: 0.3, green: 0.69, blue: 0.31))
                    .cornerRadius(9999)
            }
        }
        .padding()
        .background(Color.white)
        .cornerRadius(12)
        .fullScreenCover(isPresented: $showPremiumView, onDismiss: {
            // 当订阅视图关闭时，主动刷新状态，确保UI正确更新
            Task {
                await userData.checkAndUpdateSubscriptionStatus()
            }
        }) {
            NavigationView {
                FullScreenPremiumView()
                    .environmentObject(userData)
                    .navigationBarTitle("Premium Features", displayMode: .inline)
                    .navigationBarTitleDisplayMode(.inline)
                    .toolbar {
                        ToolbarItem(placement: .navigationBarLeading) {
                            Button("Close") {
                                showPremiumView = false
                            }
                        }
                    }
            }
        }
    }
}

// 周运动卡路里组件 - 未订阅状态
struct WeeklyExerciseCaloriesPremiumView: View {
    @EnvironmentObject var userData: UserData
    @State private var showPremiumView = false
    
    var body: some View {
        VStack(spacing: 12) {
            HStack {
                Text("This Week's Exercise Calories")
                    .font(.headline)
                    .fontWeight(.semibold)
                
                Spacer()
                
                
            }
            
            // 运动卡路里详情视图 - 锁定状态（缩小版本）
            VStack(spacing: 10) {
                // 卡路里统计和进度环（紧凑布局）
                HStack(alignment: .center) {
                    VStack(alignment: .leading, spacing: 4) {
                        Text("-- / ---")
                            .font(.system(size: 20, weight: .bold))
                            .foregroundColor(.gray)
                        
                        Text("calories burned")
                            .font(.caption2)
                            .foregroundColor(.secondary)
                    }
                    
                    Spacer()
                    
                    // 进度环锁定状态（缩小）
                    ZStack {
                        Circle()
                            .stroke(Color.gray.opacity(0.2), lineWidth: 3)
                            .frame(width: 45, height: 45)
                        
                        Image(systemName: "lock.fill")
                            .foregroundColor(.gray)
                            .font(.caption)
                    }
                }
                
                // 分隔线
                Divider()
                
                // 运动记录列表（紧凑版本）
                VStack(spacing: 6) {
                    exerciseRowView(type: "Exercise Type", duration: "-- min", calories: "-- kcal", time: "--:--")
                    exerciseRowView(type: "Exercise Type", duration: "-- min", calories: "-- kcal", time: "--:--")
                }
                
                // 添加运动按钮（锁定状态，缩小）
                Button(action: {}) {
                    HStack {
                        Spacer()
                        Image(systemName: "plus")
                            .font(.caption)
                        Text("Add Exercise")
                            .font(.caption)
                        Spacer()
                    }
                    .padding(.vertical, 8)
                    .background(Color(red: 0.98, green: 0.98, blue: 0.98))
                    .foregroundColor(.gray)
                    .cornerRadius(15)
                }
                .disabled(true)
            }
            .padding(12)
            .background(Color(red: 0.98, green: 0.98, blue: 0.98))
            .cornerRadius(12)
            .overlay(
                // 锁定遮罩（缩小）
                RoundedRectangle(cornerRadius: 12)
                    .fill(Color.white.opacity(0.8))
                    .overlay(
                        VStack(spacing: 8) {
                            Image(systemName: "lock.fill")
                                .font(.title2)
                                .foregroundColor(.gray)
                            
                            Text("Premium Feature")
                                .font(.subheadline)
                                .fontWeight(.medium)
                                .foregroundColor(.gray)
                        }
                    )
            )
            
            // 添加订阅按钮
            Button(action: {
                showPremiumView = true
            }) {
                Text("upgrade to pro")
                    .font(.subheadline)
                    .fontWeight(.medium)
                    .foregroundColor(.white)
                    .frame(width: 350, height: 40)
                    .background(Color(red: 0.3, green: 0.69, blue: 0.31))
                    .cornerRadius(9999)
            }
        }
        .padding()
        .background(Color.white)
        .cornerRadius(12)
        .fullScreenCover(isPresented: $showPremiumView, onDismiss: {
            // 当订阅视图关闭时，主动刷新状态，确保UI正确更新
            Task {
                await userData.checkAndUpdateSubscriptionStatus()
            }
        }) {
            NavigationView {
                FullScreenPremiumView()
                    .environmentObject(userData)
                    .navigationBarTitle("Premium Features", displayMode: .inline)
                    .navigationBarTitleDisplayMode(.inline)
                    .toolbar {
                        ToolbarItem(placement: .navigationBarLeading) {
                            Button("Close") {
                                showPremiumView = false
                            }
                        }
                    }
            }
        }
    }
    
    // 虚拟运动记录行 - 锁定状态版本（紧凑）
    private func exerciseRowView(type: String, duration: String, calories: String, time: String) -> some View {
        HStack {
            // 运动图标（缩小）
            ZStack {
                Circle()
                    .fill(Color(red: 0.98, green: 0.98, blue: 0.98))
                    .frame(width: 32, height: 32)
                
                Text("?")
                    .foregroundColor(.gray)
                    .font(.caption)
            }
            
            VStack(alignment: .leading, spacing: 2) {
                Text(type)
                    .font(.caption)
                    .foregroundColor(.gray)
                
                HStack {
                    Image(systemName: "clock")
                        .font(.caption2)
                    
                    Text(duration)
                        .font(.caption2)
                }
                .foregroundColor(.gray)
            }
            
            Spacer()
            
            VStack(alignment: .trailing, spacing: 2) {
                Text(calories)
                    .font(.caption)
                    .foregroundColor(.gray)
                
                Text(time)
                    .font(.caption2)
                    .foregroundColor(.gray)
            }
        }
        .padding(.vertical, 4)
    }
}

// Today's Recommendations组件 - 未订阅状态
struct TodayAIRecommendationsPremiumView: View {
    @EnvironmentObject var userData: UserData
    @State private var showPremiumView = false
    
    var body: some View {
        VStack(spacing: 16) {
            HStack {
                Text("Today's Recommendations")
                    .font(.headline)
                    .fontWeight(.semibold)
                
                Spacer()
                
                Image(systemName: "star")
                    .foregroundColor(.green)
            }
            
            VStack(spacing: 12) {
                ZStack {
                    RoundedRectangle(cornerRadius: 12)
                        .fill(Color.white)
                        .shadow(color: .gray.opacity(0.1), radius: 2, x: 0, y: 1)
                    
                    VStack(spacing: 8) {
                        Image(systemName: "lock.fill")
                            .font(.title)
                            .foregroundColor(.green)
                        
                        Text("Premium Feature")
                            .font(.subheadline)
                            .fontWeight(.medium)
                            .foregroundColor(.green)
                    }
                    .padding()
                }
                .frame(height: 80)
            }
            
            Button(action: {
                showPremiumView = true
            }) {
                Text("Upgrade to Premium")
                    .fontWeight(.semibold)
                    .frame(maxWidth: .infinity)
                    .padding()
                    .background(Color.green)
                    .foregroundColor(.white)
                    .cornerRadius(10)
            }
        }
        .padding()
        .background(Color.white)
        .cornerRadius(12)
        .fullScreenCover(isPresented: $showPremiumView, onDismiss: {
            // 当订阅视图关闭时，主动刷新状态，确保UI正确更新
            Task {
                await userData.checkAndUpdateSubscriptionStatus()
            }
        }) {
            NavigationView {
                FullScreenPremiumView()
                    .environmentObject(userData)
                    .navigationBarTitle("Premium Features", displayMode: .inline)
                    .navigationBarTitleDisplayMode(.inline)
                    .toolbar {
                        ToolbarItem(placement: .navigationBarLeading) {
                            Button("Close") {
                                showPremiumView = false
                            }
                        }
                    }
            }
        }
    }
}

// MARK: - Weight页面和Nutrition页面的Premium组件

// Weight页面 - 今日运动Premium限制视图
struct TodayExercisePremiumView: View {
    @EnvironmentObject var userData: UserData
    @State private var showPremiumView = false
    
    var body: some View {
        ZStack {
            // 背景虚拟数据 - 缩小版本
            VStack(spacing: 8) {
                // 卡路里和进度环（虚拟数据）- 缩小
                HStack(alignment: .center) {
                    VStack(alignment: .leading, spacing: 3) {
                        Text("? ? ? / ? ? ?")
                            .font(.subheadline)
                            .fontWeight(.semibold)
                            .foregroundColor(.gray.opacity(0.3))
                        
                        Text("calories burned")
                            .font(.caption2)
                            .foregroundColor(.secondary.opacity(0.3))
                    }
                    
                    Spacer()
                    
                    // 进度环（虚拟数据）- 缩小
                    ZStack {
                        Circle()
                            .stroke(lineWidth: 4)
                            .opacity(0.1)
                            .foregroundColor(Color.gray)
                        
                        Text("???")
                            .font(.caption)
                            .fontWeight(.bold)
                            .foregroundColor(.gray.opacity(0.3))
                    }
                    .frame(width: 40, height: 40)
                }
                .padding(.horizontal, 12)
                .padding(.top, 8)
                
                Divider()
                    .padding(.horizontal, 12)
                    .opacity(0.3)
                
                // 虚拟运动记录 - 缩小
                VStack(spacing: 4) {
                    exerciseRowView(type: "? ? ?", duration: "? ? min", calories: "? ? ? kcal", time: "?:??")
                    exerciseRowView(type: "? ? ?", duration: "? ? min", calories: "? ? ? kcal", time: "?:??")
                }
                .padding(.horizontal, 12)
                .opacity(0.3)
                
                // 添加运动按钮（虚拟）- 缩小
                Button(action: {}) {
                    HStack {
                        Spacer()
                        Image(systemName: "plus")
                            .font(.caption)
                        Text("Add Exercise")
                            .font(.caption)
                        Spacer()
                    }
                    .padding(.vertical, 8)
                    .background(Color.gray.opacity(0.2))
                    .foregroundColor(.gray.opacity(0.5))
                    .cornerRadius(15)
                }
                .padding(.horizontal, 12)
                .padding(.bottom, 8)
                .disabled(true)
            }
            .background(Color.white)
            .cornerRadius(12)
            .padding(.horizontal, getHorizontalPadding())
            
            // Premium功能蒙层 - 居中显示并缩小
            VStack(alignment: .leading, spacing: 12) {
                // 图标和Premium Feature文本 - 改为HStack靠左对齐
                HStack(spacing: 8) {
                    Image(systemName: "exclamationmark.triangle.fill")
                        .foregroundColor(.orange)
                        .font(.title3)
                    
                    Text("Premium Feature")
                        .font(.headline)
                        .fontWeight(.semibold)
                    
                    Spacer()
                }
                
                VStack(alignment: .leading, spacing: 10) {
                    // Subscribe文本 - 靠左对齐
                    Text("Subscribe to unlock personalized health goals")
                        .font(.subheadline)
                        .foregroundColor(.secondary)
                        .multilineTextAlignment(.leading)
                        .lineLimit(2)
                    
                    // Get Premium按钮 - 靠左对齐，固定尺寸
                    Button(action: {
                        showPremiumView = true
                    }) {
                        Text("Get Premium")
                            .font(.subheadline)
                            .fontWeight(.semibold)
                            .foregroundColor(.white)
                            .frame(width: 109, height: 36)
                            .background(Color.green)
                            .cornerRadius(8)
                    }
                }
                
                Spacer()
            }
            .padding(16)
            .background(
                RoundedRectangle(cornerRadius: 12)
                    .fill(Color.yellow.opacity(0.1))
                    .overlay(
                        RoundedRectangle(cornerRadius: 12)
                            .stroke(Color.orange.opacity(0.3), lineWidth: 1)
                    )
            )
            .padding(.horizontal, getHorizontalPadding())
        }
        .fullScreenCover(isPresented: $showPremiumView, onDismiss: {
            // 当订阅视图关闭时，主动刷新状态，确保UI正确更新
            Task {
                await userData.checkAndUpdateSubscriptionStatus()
            }
        }) {
            NavigationView {
                FullScreenPremiumView()
                    .environmentObject(userData)
                    .navigationBarTitle("Premium Features", displayMode: .inline)
                    .navigationBarTitleDisplayMode(.inline)
                    .toolbar {
                        ToolbarItem(placement: .navigationBarLeading) {
                            Button("Close") {
                                showPremiumView = false
                            }
                        }
                    }
            }
        }
    }
    
    // 屏幕自适应水平边距
    private func getHorizontalPadding() -> CGFloat {
        let screenWidth = UIScreen.main.bounds.width
        if screenWidth < 375 { // iPhone SE等小屏设备
            return 12
        } else if screenWidth < 414 { // iPhone 标准尺寸
            return 16
        } else { // iPhone Plus/Max等大屏设备
            return 20
        }
    }
    
    // 虚拟运动记录行 - 缩小版本
    private func exerciseRowView(type: String, duration: String, calories: String, time: String) -> some View {
        HStack {
            // 运动图标 - 缩小
            ZStack {
                Circle()
                    .fill(Color(red: 0.98, green: 0.98, blue: 0.98))
                    .frame(width: 30, height: 30)
                
                Text("?")
                    .foregroundColor(.gray.opacity(0.3))
                    .font(.caption)
            }
            
            VStack(alignment: .leading, spacing: 2) {
                Text(type)
                    .font(.caption)
                    .foregroundColor(.gray.opacity(0.3))
                
                HStack {
                    Image(systemName: "clock")
                        .font(.caption2)
                    
                    Text(duration)
                        .font(.caption2)
                }
                .foregroundColor(.gray.opacity(0.3))
            }
            
            Spacer()
            
            VStack(alignment: .trailing, spacing: 2) {
                Text(calories)
                    .font(.caption)
                    .foregroundColor(.gray.opacity(0.3))
                
                Text(time)
                    .font(.caption2)
                    .foregroundColor(.gray.opacity(0.3))
            }
        }
        .padding(.vertical, 3)
    }
}

// Nutrition页面 - 日营养计算Premium限制视图
struct DailyNutritionPremiumView: View {
    @EnvironmentObject var userData: UserData
    @State private var showPremiumView = false
    
    var body: some View {
        ZStack {
            // 背景虚拟数据
            HStack(spacing: 0) {
                // 卡路里（虚拟数据）
                VStack(alignment: .leading, spacing: 8) {
                    HStack {
                        Image(systemName: "flame.fill")
                            .foregroundColor(.red.opacity(0.15))
                        Text("Calories")
                            .font(.headline)
                            .foregroundColor(.gray.opacity(0.15))
                    }
                    
                    HStack(alignment: .firstTextBaseline, spacing: 2) {
                        Text("? ? ?")
                            .font(.title3)
                            .fontWeight(.bold)
                            .foregroundColor(.gray.opacity(0.15))
                        Text("/ ? ? ?")
                            .font(.system(size: 14))
                            .fontWeight(.bold)
                            .foregroundColor(.gray.opacity(0.15))
                        Text("kcal")
                            .font(.system(size: 14))
                            .fontWeight(.bold)
                            .foregroundColor(.gray.opacity(0.15))
                    }
                    
                    ProgressView(value: 0.0)
                        .progressViewStyle(LinearProgressViewStyle(tint: .gray.opacity(0.15)))
                        .padding(.top, 8)
                }
                .frame(maxWidth: .infinity, alignment: .leading)
                .padding()
                
                // 蛋白质（虚拟数据）
                VStack(alignment: .leading, spacing: 8) {
                    HStack {
                        Image(systemName: "bolt.fill")
                            .foregroundColor(.blue.opacity(0.15))
                        Text("Protein")
                            .font(.headline)
                            .foregroundColor(.gray.opacity(0.15))
                    }
                    
                    HStack(alignment: .firstTextBaseline, spacing: 2) {
                        Text("? ?")
                            .font(.title3)
                            .fontWeight(.bold)
                            .foregroundColor(.gray.opacity(0.15))
                        Text("/ ? ?")
                            .font(.system(size: 14))
                            .fontWeight(.bold)
                            .foregroundColor(.gray.opacity(0.15))
                        Text("g")
                            .font(.system(size: 14))
                            .fontWeight(.bold)
                            .foregroundColor(.gray.opacity(0.15))
                    }
                    
                    ProgressView(value: 0.0)
                        .progressViewStyle(LinearProgressViewStyle(tint: .gray.opacity(0.15)))
                        .padding(.top, 8)
                }
                .frame(maxWidth: .infinity, alignment: .leading)
                .padding()
            }
            .blur(radius: 2.0) // 稍微增加模糊效果
            
            // Premium功能蒙层
            VStack(alignment: .leading, spacing: 15) {
                HStack {
                    Image(systemName: "exclamationmark.triangle.fill")
                        .foregroundColor(.orange)
                        .font(.title2)
                    
                    Text("Premium Feature")
                        .font(.headline)
                        .fontWeight(.semibold)
                    
                    Spacer()
                }
                
                VStack(alignment: .leading, spacing: 12) {
                    Text("Subscribe to unlock personalized health goals")
                        .font(.subheadline)
                        .foregroundColor(.secondary)
                        .multilineTextAlignment(.leading)
                    
                    Button(action: {
                        showPremiumView = true
                    }) {
                        Text("Get Premium")
                            .font(.subheadline)
                            .fontWeight(.semibold)
                            .foregroundColor(.white)
                            .frame(width: 109, height: 36)
                            .background(Color.green)
                            .cornerRadius(8)
                    }
                }
                
                Spacer()
            }
            .padding(20)
            .background(
                RoundedRectangle(cornerRadius: 12)
                    .fill(Color.yellow.opacity(0.1))
                    .overlay(
                        RoundedRectangle(cornerRadius: 12)
                            .stroke(Color.orange.opacity(0.3), lineWidth: 1)
                    )
            )
        }
        .fullScreenCover(isPresented: $showPremiumView, onDismiss: {
            // 当订阅视图关闭时，主动刷新状态，确保UI正确更新
            Task {
                await userData.checkAndUpdateSubscriptionStatus()
            }
        }) {
            NavigationView {
                FullScreenPremiumView()
                    .environmentObject(userData)
                    .navigationBarTitle("Premium Features", displayMode: .inline)
                    .navigationBarTitleDisplayMode(.inline)
                    .toolbar {
                        ToolbarItem(placement: .navigationBarLeading) {
                            Button("Close") {
                                showPremiumView = false
                            }
                        }
                    }
            }
        }
    }
}

// MARK: - Weight页面的Premium组件

// 日建议Premium组件 - 未订阅状态 with 虚拟数据预览
struct WeightAIRecommendationsPremiumView: View {
    @EnvironmentObject var userData: UserData
    @State private var showPremiumView = false
    
    // 虚拟的日建议数据
    private let demoRecommendations = [
        "Increase your protein intake to 85g daily to support muscle recovery",
        "Add 20 minutes of light cardio 3x per week to boost metabolism",
        "Aim for 7-8 hours of sleep to optimize weight management hormones"
    ]
    
    var body: some View {
        VStack(spacing: 16) {
            HStack {
                Text("Today's Recommendations")
                    .font(.headline)
                    .fontWeight(.semibold)
                
                Spacer()
                
                Image(systemName: "star")
                    .foregroundColor(.green)
            }
            
            // 显示虚拟数据但带有蒙层效果
            ZStack {
                VStack(alignment: .leading, spacing: 12) {
                    ForEach(Array(demoRecommendations.enumerated()), id: \.offset) { index, recommendation in
                        HStack(alignment: .top, spacing: 10) {
                            Image(systemName: "checkmark.circle.fill")
                                .foregroundColor(.green)
                                .font(.system(size: 16))
                            
                            Text(recommendation)
                                .font(.subheadline)
                                .fixedSize(horizontal: false, vertical: true)
                        }
                        .opacity(0.6) // 提高透明度，让用户能够介于看清和看不清之间
                    }
                }
                .padding()
                .background(Color.white)
                .cornerRadius(12)
                .shadow(color: .gray.opacity(0.1), radius: 2, x: 0, y: 1)
                .blur(radius: 1.6) // 稍微增加模糊效果
                
                // 蒙层覆盖
                RoundedRectangle(cornerRadius: 12)
                    .fill(Color.white.opacity(0.85))
                    .overlay(
                        VStack(spacing: 16) {
                            Image(systemName: "lock.fill")
                                .font(.title)
                                .foregroundColor(.green)
                            
                            Text("Premium Feature")
                                .font(.headline)
                                .fontWeight(.semibold)
                                .foregroundColor(.green)
                            
                            Text("Get personalized recommendations")
                                .font(.subheadline)
                                .foregroundColor(.secondary)
                                .multilineTextAlignment(.center)
                            
                            // 将按钮移动到覆盖层内部
                            Button(action: {
                                showPremiumView = true
                            }) {
                                Text("Get Premium")
                                    .font(.subheadline)
                                    .fontWeight(.semibold)
                                    .frame(width: 172.44, height: 40)
                                    .background(Color.green)
                                    .foregroundColor(.white)
                                    .cornerRadius(9999)
                            }
                        }
                        .padding()
                    )
            }
        }
        .padding(.horizontal, getHorizontalPadding())
        .fullScreenCover(isPresented: $showPremiumView, onDismiss: {
            // 当订阅视图关闭时，主动刷新状态，确保UI正确更新
            Task {
                await userData.checkAndUpdateSubscriptionStatus()
            }
        }) {
            NavigationView {
                FullScreenPremiumView()
                    .environmentObject(userData)
                    .navigationBarTitle("Premium Features", displayMode: .inline)
                    .navigationBarTitleDisplayMode(.inline)
                    .toolbar {
                        ToolbarItem(placement: .navigationBarLeading) {
                            Button("Close") {
                                showPremiumView = false
                            }
                        }
                    }
            }
        }
    }
    
    // 屏幕自适应水平边距
    private func getHorizontalPadding() -> CGFloat {
        let screenWidth = UIScreen.main.bounds.width
        if screenWidth < 375 { // iPhone SE等小屏设备
            return 12
        } else if screenWidth < 414 { // iPhone 标准尺寸
            return 16
        } else { // iPhone Plus/Max等大屏设备
            return 20
        }
    }
}

// 30天折线图Premium组件 - 未订阅状态 with 虚拟数据预览
struct Weight30DayChartPremiumView: View {
    @EnvironmentObject var userData: UserData
    @State private var showPremiumView = false
    
    // 虚拟体重数据结构
    struct DemoWeightPoint: Identifiable {
        let id = UUID()
        let day: Int
        let weight: Double
        let isMorning: Bool
    }
    
    // 生成虚拟的30天体重数据
    private var demoWeightData: [DemoWeightPoint] {
        var data: [DemoWeightPoint] = []
        let baseWeight = 70.0 // 基础体重
        
        // 只生成部分天的数据点，创建简单的上下起伏效果
        let selectedDays = [1, 3, 6, 9, 12, 15, 18, 21, 24, 27, 30] // 11个数据点
        
        for (index, day) in selectedDays.enumerated() {
            // 创建简单的上下交替模式
            let isUp = index % 2 == 0  // 偶数索引向上，奇数索引向下
            let amplitude = 0.08  // 很小的起伏幅度（±0.08kg）
            
            // 简单的上下变化，不用复杂的正弦波
            let variation = isUp ? amplitude : -amplitude
            
            // 整体轻微的减重趋势
            let overallTrend = -0.008 * Double(day)  // 更轻微的趋势
            
            // 早晨体重：基础体重 + 简单起伏 + 轻微趋势
            let morningWeight = baseWeight + variation + overallTrend
            data.append(DemoWeightPoint(day: day, weight: morningWeight, isMorning: true))
            
            // 晚上体重：比早晨重0.25kg左右，保持稳定差值
            let eveningWeight = morningWeight + 0.25
            data.append(DemoWeightPoint(day: day, weight: eveningWeight, isMorning: false))
        }
        
        return data
    }
    
    private var yAxisRange: (min: Double, max: Double) {
        let weights = demoWeightData.map { $0.weight }
        let minWeight = weights.min() ?? 68.0
        let maxWeight = weights.max() ?? 72.0
        let padding = (maxWeight - minWeight) * 0.1
        return (minWeight - padding, maxWeight + padding)
    }
    
    var body: some View {
        VStack(spacing: 16) {
            HStack {
                Text("30-Day Weight Trend")
                    .font(.headline)
                    .fontWeight(.semibold)
                
                Spacer()
            }
            .padding(.horizontal)
            
            // 显示虚拟折线图但带有蒙层效果
            ZStack {
                // 背景虚拟图表
                VStack(spacing: 10) {
                    Chart {
                        // 早晨体重折线（绿色）
                        let morningData = demoWeightData.filter { $0.isMorning }.sorted { $0.day < $1.day }
                        ForEach(morningData) { point in
                            LineMark(
                                x: .value("Day", point.day),
                                y: .value("Weight", point.weight)
                            )
                            .foregroundStyle(by: .value("Time", "Morning"))
                            .lineStyle(StrokeStyle(lineWidth: 2.5))
                            .interpolationMethod(.linear) // 直线连接，突出上下起伏
                        }
                        
                        // 早晨体重数据点
                        ForEach(morningData) { point in
                            PointMark(
                                x: .value("Day", point.day),
                                y: .value("Weight", point.weight)
                            )
                            .foregroundStyle(by: .value("Time", "Morning"))
                            .symbolSize(25)
                        }
                        
                        // 晚上体重折线（黄色）
                        let eveningData = demoWeightData.filter { !$0.isMorning }.sorted { $0.day < $1.day }
                        ForEach(eveningData) { point in
                            LineMark(
                                x: .value("Day", point.day),
                                y: .value("Weight", point.weight)
                            )
                            .foregroundStyle(by: .value("Time", "Evening"))
                            .lineStyle(StrokeStyle(lineWidth: 2.5))
                            .interpolationMethod(.linear) // 直线连接，突出上下起伏
                        }
                        
                        // 晚上体重数据点
                        ForEach(eveningData) { point in
                            PointMark(
                                x: .value("Day", point.day),
                                y: .value("Weight", point.weight)
                            )
                            .foregroundStyle(by: .value("Time", "Evening"))
                            .symbolSize(25)
                        }
                    }
                    .chartForegroundStyleScale([
                        "Morning": Color.green.opacity(0.7),
                        "Evening": Color(red: 0.98, green: 0.85, blue: 0.35).opacity(0.7)
                    ])
                    .chartXScale(domain: 1...30)
                    .chartYScale(domain: yAxisRange.min...yAxisRange.max)
                    .chartXAxis {
                        AxisMarks(values: [1, 5, 10, 15, 20, 25, 30]) { value in
                            if let day = value.as(Int.self) {
                                AxisValueLabel {
                                    Text("\(day)")
                                        .font(.caption)
                                        .foregroundColor(.secondary)
                                }
                                AxisGridLine()
                                AxisTick()
                            }
                        }
                    }
                    .chartYAxis {
                        AxisMarks(position: .leading) { value in
                            AxisValueLabel()
                            AxisGridLine()
                            AxisTick()
                        }
                    }
                    .frame(height: 220)
                    

                }
                .padding()
                .blur(radius: 2.5) // 稍微增加模糊效果
                
                // 蒙层覆盖 - 回退到原来的设计
                RoundedRectangle(cornerRadius: 12)
                    .fill(Color.white.opacity(0.85))
                    .overlay(
                        VStack(spacing: 16) {
                            Image(systemName: "lock.fill")
                                .font(.largeTitle)
                                .foregroundColor(.green)
                            
                            Text("Unlock Premium")
                                .font(.headline)
                                .fontWeight(.semibold)
                                .foregroundColor(.green)
                            
                            Text("View your complete 30-day weight trend")
                                .font(.subheadline)
                                .foregroundColor(.secondary)
                                .multilineTextAlignment(.center)
                            
                            // 将按钮移动到覆盖层内部
                            Button(action: {
                                showPremiumView = true
                            }) {
                                Text("Get Premium")
                                    .font(.subheadline)
                                    .fontWeight(.semibold)
                                    .frame(width: 172.44, height: 40)
                                    .background(Color.green)
                                    .foregroundColor(.white)
                                    .cornerRadius(9999)
                            }
                        }
                        .padding()
                    )
            }
            .padding(.horizontal)
        }
        .fullScreenCover(isPresented: $showPremiumView, onDismiss: {
            // 当订阅视图关闭时，主动刷新状态，确保UI正确更新
            Task {
                await userData.checkAndUpdateSubscriptionStatus()
            }
        }) {
            NavigationView {
                FullScreenPremiumView()
                    .environmentObject(userData)
                    .navigationBarTitle("Premium Features", displayMode: .inline)
                    .navigationBarTitleDisplayMode(.inline)
                    .toolbar {
                        ToolbarItem(placement: .navigationBarLeading) {
                            Button("Close") {
                                showPremiumView = false
                            }
                        }
                    }
            }
        }
    }
}

// MARK: - 通用Premium组件

// Premium功能锁定覆盖层
struct PremiumOverlayView: View {
    let title: String
    let description: String
    let onUpgrade: () -> Void
    
    var body: some View {
        ZStack {
            Color.black.opacity(0.3)
            
            VStack(spacing: 16) {
                Image(systemName: "lock.fill")
                    .font(.largeTitle)
                    .foregroundColor(.green)
                
                Text(title)
                    .font(.headline)
                    .fontWeight(.semibold)
                    .multilineTextAlignment(.center)
                
                Text(description)
                    .font(.subheadline)
                    .foregroundColor(.secondary)
                    .multilineTextAlignment(.center)
                
                Button(action: onUpgrade) {
                    Text("Upgrade to Premium")
                        .fontWeight(.semibold)
                        .frame(maxWidth: .infinity)
                        .padding()
                        .background(Color.green)
                        .foregroundColor(.white)
                        .cornerRadius(10)
                }
                .padding(.horizontal)
            }
            .padding()
            .background(Color.white)
            .cornerRadius(16)
            .shadow(radius: 10)
            .padding()
        }
    }
}

// Premium状态检查修饰符
struct PremiumGate: ViewModifier {
    @EnvironmentObject var userData: UserData
    let premiumContent: AnyView
    let freeContent: AnyView
    
    func body(content: Content) -> some View {
        Group {
            if userData.isPremium {
                premiumContent
            } else {
                freeContent
            }
        }
    }
}

extension View {
    func premiumGate<PremiumContent: View, FreeContent: View>(
        @ViewBuilder premiumContent: () -> PremiumContent,
        @ViewBuilder freeContent: () -> FreeContent
    ) -> some View {
        self.modifier(PremiumGate(
            premiumContent: AnyView(premiumContent()),
            freeContent: AnyView(freeContent())
        ))
    }
}

// MARK: - 使用示例和辅助方法

struct PremiumUIComponentsPreview: View {
    @StateObject private var userData = UserData()
    
    var body: some View {
        ScrollView {
            VStack(spacing: 20) {
                WeeklyNutritionPremiumView()
                    .environmentObject(userData)
                
                RecommendedActivitiesPremiumView()
                    .environmentObject(userData)
                
                PremiumTipsView()
                    .environmentObject(userData)
                
                TodayAIRecommendationsPremiumView()
                    .environmentObject(userData)
                
                WeightAIRecommendationsPremiumView()
                    .environmentObject(userData)
                
                Weight30DayChartPremiumView()
                    .environmentObject(userData)
                
                TodayExercisePremiumView()
                    .environmentObject(userData)
                
                DailyNutritionPremiumView()
                    .environmentObject(userData)
            }
            .padding()
        }
    }
}

#Preview {
    PremiumUIComponentsPreview()
} 