import Foundation
import Combine

/*
 * AI建议服务 - API使用说明
 * 
 * API端点：/ns/app/plan-advices/{adviceType}/latest
 * 
 * 请求参数类型：
 * - WEEK：获取周建议数据，包含营养数值字段（calorieRecommend, proteinRecommend, fatRecommend, exerciseCalorieBurnRecommend）
 * - DAY：获取日建议数据，只包含建议文本（content数组）
 * 
 * 各页面使用方式：
 * 
 * 1. Weight页面：
 *    - 日建议文本：DAY请求 → getDailyAdviceTexts()
 *    - 日燃脂分母：WEEK请求 → getSafeDailyExerciseBurnGoal() (周数据/7)
 * 
 * 2. Plan页面：
 *    - 周营养分母：WEEK请求 → getSafeWeeklyNutritionGoals()
 *    - 周燃脂分母：WEEK请求 → getSafeWeeklyExerciseBurnGoal()
 *    - 周建议文本：WEEK请求 → getPlanAdviceTexts()
 * 
 * 3. Nutrition页面(ContentView)：
 *    - 日营养分母：WEEK请求 → getSafeDailyNutritionGoals() (周数据/7)
 */

// MARK: - 数据模型
struct PlanAdvicesResponse: Codable {
    let adviceType: String
    let content: [String]
    let calorieRecommend: String?        // DAY类型不包含，设为可选
    let proteinRecommend: String?        // DAY类型不包含，设为可选
    let fatRecommend: String?           // DAY类型不包含，设为可选
    let exerciseCalorieBurnRecommend: String?  // DAY类型不包含，设为可选
}

struct PlanAdvicesAPIResponse: Codable {
    let code: Int
    let message: String
    let data: PlanAdvicesResponse?
    let timestamp: Int64
}

// 执行建议接口响应模型
struct ExecuteAdviceResponse: Codable {
    let code: Int
    let message: String
    let data: Bool?
    let timestamp: Int64
}

// MARK: - AI建议服务
@MainActor
class PlanAdviceService: ObservableObject {
    static let shared = PlanAdviceService()
    
    private let baseURL = "https://fsai.pickgoodspro.com"
    private let session = URLSession.shared
    
    // 发布的属性用于UI更新
    @Published var weeklyAdvice: PlanAdvicesResponse?
    @Published var dailyAdvice: PlanAdvicesResponse?
    @Published var isLoading = false
    @Published var errorMessage: String?
    
    // 定时器相关
    private var dailyRefreshTimer: Timer?
    private var weeklyRefreshTimer: Timer?
    private let userDefaults = UserDefaults.standard
    
    // 缓存键
    private let lastDailyRefreshKey = "LastDailyAdviceRefresh"
    private let lastWeeklyRefreshKey = "LastWeeklyAdviceRefresh"
    
    private init() {
        setupAutomaticRefresh()
    }
    
    deinit {
        stopAutomaticRefresh()
    }
    
    // MARK: - 定时刷新机制
    
    /// 设置自动刷新机制
    private func setupAutomaticRefresh() {
        print("🕒 [PlanAdvice] 设置自动刷新机制")
        
        // 检查是否需要立即刷新
        checkAndRefreshIfNeeded()
        
        // 设置定时器
        setupDailyRefreshTimer()
        setupWeeklyRefreshTimer()
    }
    
    /// 检查并根据需要刷新建议
    private func checkAndRefreshIfNeeded() {
        let calendar = Calendar.current
        let now = Date()
        
        // 检查日建议是否需要刷新
        let lastDailyRefresh = userDefaults.object(forKey: lastDailyRefreshKey) as? Date ?? Date.distantPast
        if !calendar.isDate(lastDailyRefresh, inSameDayAs: now) {
            print("🕒 [PlanAdvice] 需要刷新日建议（上次刷新: \(lastDailyRefresh)）")
            refreshDailyAdvice()
        }
        
        // 检查周建议是否需要刷新
        let lastWeeklyRefresh = userDefaults.object(forKey: lastWeeklyRefreshKey) as? Date ?? Date.distantPast
        let lastRefreshWeekOfYear = calendar.component(.weekOfYear, from: lastWeeklyRefresh)
        let currentWeekOfYear = calendar.component(.weekOfYear, from: now)
        let lastRefreshYear = calendar.component(.year, from: lastWeeklyRefresh)
        let currentYear = calendar.component(.year, from: now)
        
        if lastRefreshYear != currentYear || lastRefreshWeekOfYear != currentWeekOfYear {
            print("🕒 [PlanAdvice] 需要刷新周建议（上次刷新: \(lastWeeklyRefresh)）")
            refreshWeeklyAdvice()
        }
    }
    
    /// 刷新日建议
    private func refreshDailyAdvice() {
        print("🔄 [PlanAdvice] 自动刷新日建议")
        fetchLatestAdvice(adviceType: .day) { [weak self] result in
            switch result {
            case .success(_):
                self?.userDefaults.set(Date(), forKey: self?.lastDailyRefreshKey ?? "")
                print("✅ [PlanAdvice] 日建议自动刷新成功")
            case .failure(let error):
                print("❌ [PlanAdvice] 日建议自动刷新失败: \(error.localizedDescription)")
            }
        }
    }
    
    /// 刷新周建议
    private func refreshWeeklyAdvice() {
        print("🔄 [PlanAdvice] 自动刷新周建议")
        fetchLatestAdvice(adviceType: .week) { [weak self] result in
            switch result {
            case .success(_):
                self?.userDefaults.set(Date(), forKey: self?.lastWeeklyRefreshKey ?? "")
                print("✅ [PlanAdvice] 周建议自动刷新成功")
            case .failure(let error):
                print("❌ [PlanAdvice] 周建议自动刷新失败: \(error.localizedDescription)")
            }
        }
    }
    
    /// 手动触发刷新（供外部调用）
    func manualRefresh() {
        print("🔄 [PlanAdvice] 手动触发刷新")
        refreshDailyAdvice()
        refreshWeeklyAdvice()
    }
    
    // 🆕 优化的刷新方法，带完成回调
    func refreshAdvicesWithCompletion(completion: @escaping (Bool) -> Void) {
        print("🔄 [PlanAdvice] 手动触发刷新（带回调）")
        
        let group = DispatchGroup()
        var weeklySuccess = false
        var dailySuccess = false
        
        // 刷新周建议
        group.enter()
        fetchLatestAdvice(adviceType: .week) { result in
            defer { group.leave() }
            switch result {
            case .success(_):
                weeklySuccess = true
                print("✅ [PlanAdvice] 周建议手动刷新成功")
                // 🆕 立即发送周建议数据就绪通知
                DispatchQueue.main.async {
                    NotificationCenter.default.post(name: Notification.Name("WeeklyAdviceDataReady"), object: nil)
                }
            case .failure(let error):
                print("❌ [PlanAdvice] 周建议手动刷新失败: \(error.localizedDescription)")
            }
        }
        
        // 刷新日建议
        group.enter()
        fetchLatestAdvice(adviceType: .day) { result in
            defer { group.leave() }
            switch result {
            case .success(_):
                dailySuccess = true
                print("✅ [PlanAdvice] 日建议手动刷新成功")
            case .failure(let error):
                print("❌ [PlanAdvice] 日建议手动刷新失败: \(error.localizedDescription)")
            }
        }
        
        group.notify(queue: .main) {
            let overallSuccess = weeklySuccess && dailySuccess
            print("🏁 [PlanAdvice] 刷新完成 - 整体成功: \(overallSuccess)")
            
            // 🆕 发送通知告知数据已更新
            if overallSuccess && self.isWeeklyAdviceDataComplete() {
                NotificationCenter.default.post(name: Notification.Name("PlanAdviceDataUpdated"), object: nil)
                print("📢 [PlanAdvice] 已发送数据更新通知")
            }
            
            completion(overallSuccess)
        }
    }
    
    // MARK: - 优化加载方法
    
    /// 快速加载所有建议（优化版本）
    func loadAllAdvicesOptimized(completion: @escaping () -> Void = {}) {
        print("🚀 [PlanAdvice] 开始优化加载所有建议")
        
        // 如果有缓存数据且不需要刷新，直接返回
        if hasValidCachedData() {
            print("✅ [PlanAdvice] 使用缓存数据，无需重新加载")
            DispatchQueue.main.async {
                completion()
            }
            return
        }
        
        // 并发加载日建议和周建议
        let group = DispatchGroup()
        var weeklyLoaded = false
        var dailyLoaded = false
        
        // 优先加载周建议（Plan页面更需要）
        group.enter()
        fetchLatestAdvice(adviceType: .week) { result in
            defer { group.leave() }
            switch result {
            case .success(_):
                weeklyLoaded = true
                print("✅ [PlanAdvice] 周建议加载成功")
                // 🆕 立即发送周建议数据就绪通知
                DispatchQueue.main.async {
                    NotificationCenter.default.post(name: Notification.Name("WeeklyAdviceDataReady"), object: nil)
                }
            case .failure(let error):
                print("❌ [PlanAdvice] 周建议加载失败: \(error.localizedDescription)")
            }
        }
        
        // 同时加载日建议
        group.enter()
        fetchLatestAdvice(adviceType: .day) { result in
            defer { group.leave() }
            switch result {
            case .success(_):
                dailyLoaded = true
                print("✅ [PlanAdvice] 日建议加载成功")
            case .failure(let error):
                print("❌ [PlanAdvice] 日建议加载失败: \(error.localizedDescription)")
            }
        }
        
        group.notify(queue: .main) {
            print("🏁 [PlanAdvice] 建议数据批量加载完成 - 周建议:\(weeklyLoaded ? "✅" : "❌"), 日建议:\(dailyLoaded ? "✅" : "❌")")
            completion()
        }
    }
    
    /// 检查是否有有效的缓存数据
    private func hasValidCachedData() -> Bool {
        let calendar = Calendar.current
        let now = Date()
        
        // 检查是否有周建议缓存
        let hasWeeklyCache = weeklyAdvice != nil
        
        // 检查是否有日建议缓存
        let hasDailyCache = dailyAdvice != nil
        
        if !hasWeeklyCache || !hasDailyCache {
            return false
        }
        
        // 检查缓存是否过期
        let lastDailyRefresh = userDefaults.object(forKey: lastDailyRefreshKey) as? Date ?? Date.distantPast
        let lastWeeklyRefresh = userDefaults.object(forKey: lastWeeklyRefreshKey) as? Date ?? Date.distantPast
        
        let isDailyFresh = calendar.isDate(lastDailyRefresh, inSameDayAs: now)
        let isWeeklyFresh = {
            let lastRefreshWeekOfYear = calendar.component(.weekOfYear, from: lastWeeklyRefresh)
            let currentWeekOfYear = calendar.component(.weekOfYear, from: now)
            let lastRefreshYear = calendar.component(.year, from: lastWeeklyRefresh)
            let currentYear = calendar.component(.year, from: now)
            return lastRefreshYear == currentYear && lastRefreshWeekOfYear == currentWeekOfYear
        }()
        
        return isDailyFresh && isWeeklyFresh
    }
    
    // MARK: - 获取最新建议
    func fetchLatestAdvice(adviceType: AdviceType, completion: @escaping (Result<PlanAdvicesResponse, Error>) -> Void) {
        isLoading = true
        errorMessage = nil
        
        let urlString = "\(baseURL)/ns/app/plan-advices/\(adviceType.rawValue)/latest"
        print("🔍 [PlanAdvice] 请求URL: \(urlString)")
        print("🔍 [PlanAdvice] 建议类型: \(adviceType.rawValue)")
        
        guard let url = URL(string: urlString) else {
            let error = NSError(domain: "Invalid URL", code: -1, userInfo: nil)
            print("❌ [PlanAdvice] 无效的URL: \(urlString)")
            completion(.failure(error))
            isLoading = false
            return
        }
        
        var request = URLRequest(url: url)
        request.httpMethod = "GET"
        request.setValue("application/x-www-form-urlencoded", forHTTPHeaderField: "Content-Type")
        
        // 添加Authorization header
        if let token = getAccessToken() {
            request.setValue("Bearer \(token)", forHTTPHeaderField: "Authorization")
            print("🔍 [PlanAdvice] 使用Token: Bearer \(String(token.prefix(20)))...")
        } else {
            print("⚠️ [PlanAdvice] 未找到访问Token")
        }
        
        print("🔍 [PlanAdvice] 开始请求 \(adviceType.rawValue) 类型的建议...")
        
        session.dataTask(with: request) { [weak self] data, response, error in
            Task { @MainActor in
                guard let self = self else { return }

                self.isLoading = false
            
                if let httpResponse = response as? HTTPURLResponse {
                    print("🔍 [PlanAdvice] HTTP状态码: \(httpResponse.statusCode)")
                }
                
                if let error = error {
                    print("❌ [PlanAdvice] 网络请求失败 (\(adviceType.rawValue)): \(error.localizedDescription)")
                    self.errorMessage = error.localizedDescription
                    completion(.failure(error))
                    return
                }
                
                guard let data = data else {
                    let error = NSError(domain: "No data received", code: -1, userInfo: nil)
                    print("❌ [PlanAdvice] 未收到数据 (\(adviceType.rawValue))")
                    self.errorMessage = "未收到数据"
                    completion(.failure(error))
                    return
                }
                
                do {
                    let apiResponse = try JSONDecoder().decode(PlanAdvicesAPIResponse.self, from: data)
                    
                    if apiResponse.code == 0, let adviceData = apiResponse.data {
                        print("✅ [PlanAdvice] API响应成功，建议类型: \(adviceData.adviceType)")
                        
                        switch adviceType {
                        case .week:
                            self.weeklyAdvice = adviceData
                            print("✅ [PlanAdvice] 已更新周建议数据")
                            NotificationCenter.default.post(name: Notification.Name("PlanAdviceDataUpdated"), object: nil)
                            
                        case .day:
                            self.dailyAdvice = adviceData
                            print("✅ [PlanAdvice] 已更新日建议数据")
                        }
                        completion(.success(adviceData))
                    } else {
                        // 🔧 修复：检查4004错误，如果是建议数据不存在，检查会员状态后再执行建议生成
                        if apiResponse.code == 4004 {
                            print("🔄 [PlanAdvice] 检测到4004错误（建议数据不存在），检查会员状态...")
                            
                            // 🆕 添加会员状态检查 - 只有活跃会员才能执行建议生成
                            if !self.isUserActivePremium() {
                                print("❌ [PlanAdvice] 用户非活跃会员，无法执行建议生成（4004错误）")
                                let error = NSError(domain: "Subscription Required", code: 4003, userInfo: [NSLocalizedDescriptionKey: "需要会员权限才能生成建议"])
                                self.errorMessage = "需要会员权限才能生成建议"
                                completion(.failure(error))
                                return
                            }
                            
                            print("✅ [PlanAdvice] 用户是活跃会员，开始执行建议生成流程")
                            
                            // 针对不同类型的建议，执行相应的生成流程
                            switch adviceType {
                            case .week:
                                self.executeAdviceWeek { executeResult in
                                    switch executeResult {
                                    case .success(let executeSuccess):
                                        if executeSuccess {
                                            print("✅ [PlanAdvice] 周建议生成成功，重新获取数据")
                                            // 等待2秒后重新获取，确保数据已生成
                                            DispatchQueue.main.asyncAfter(deadline: .now() + 2.0) {
                                                self.fetchLatestAdvice(adviceType: .week, completion: completion)
                                            }
                                        } else {
                                            let error = NSError(domain: "API Error", code: apiResponse.code, userInfo: [NSLocalizedDescriptionKey: apiResponse.message])
                                            print("❌ [PlanAdvice] 周建议生成失败")
                                            self.errorMessage = apiResponse.message
                                            completion(.failure(error))
                                        }
                                    case .failure(let executeError):
                                        print("❌ [PlanAdvice] 周建议生成执行失败: \(executeError.localizedDescription)")
                                        completion(.failure(executeError))
                                    }
                                }
                            case .day:
                                self.executeAdviceDay { executeResult in
                                    switch executeResult {
                                    case .success(let executeSuccess):
                                        if executeSuccess {
                                            print("✅ [PlanAdvice] 日建议生成成功，重新获取数据")
                                            // 等待2秒后重新获取，确保数据已生成
                                            DispatchQueue.main.asyncAfter(deadline: .now() + 2.0) {
                                                self.fetchLatestAdvice(adviceType: .day, completion: completion)
                                            }
                                        } else {
                                            let error = NSError(domain: "API Error", code: apiResponse.code, userInfo: [NSLocalizedDescriptionKey: apiResponse.message])
                                            print("❌ [PlanAdvice] 日建议生成失败")
                                            self.errorMessage = apiResponse.message
                                            completion(.failure(error))
                                        }
                                    case .failure(let executeError):
                                        print("❌ [PlanAdvice] 日建议生成执行失败: \(executeError.localizedDescription)")
                                        completion(.failure(executeError))
                                    }
                                }
                            }
                        } else {
                            let error = NSError(domain: "API Error", code: apiResponse.code, userInfo: [NSLocalizedDescriptionKey: apiResponse.message])
                            print("❌ [PlanAdvice] API错误 (\(adviceType.rawValue)): code=\(apiResponse.code), message=\(apiResponse.message)")
                            self.errorMessage = apiResponse.message
                            completion(.failure(error))
                        }
                    }
                } catch {
                    print("❌ [PlanAdvice] 数据解析失败 (\(adviceType.rawValue)): \(error.localizedDescription)")
                    self.errorMessage = "数据解析失败"
                    completion(.failure(error))
                }
            }
        }.resume()
    }
    
    // MARK: - 便利方法
    
    // 获取周建议
    func fetchWeeklyAdvice(completion: @escaping (Result<PlanAdvicesResponse, Error>) -> Void) {
        fetchLatestAdvice(adviceType: .week, completion: completion)
    }
    
    // 获取日建议
    func fetchDailyAdvice(completion: @escaping (Result<PlanAdvicesResponse, Error>) -> Void) {
        fetchLatestAdvice(adviceType: .day, completion: completion)
    }
    
    // MARK: - 执行建议接口
    
    /// 检查用户是否为活跃会员（增强版本，支持多数据源检查）
    private func isUserActivePremium() -> Bool {
        // 方法1：检查UserDefaults中的标志
        let isPremiumFlag = UserDefaults.standard.bool(forKey: "isPremium")
        let subscriptionStatus = UserDefaults.standard.string(forKey: "subscriptionStatus")
        let isActiveSubscription = subscriptionStatus == "ACTIVE"
        
        // 方法2：检查访问令牌相关的订阅信息
        let accessToken = getAccessToken()
        let hasValidToken = accessToken != nil && !accessToken!.isEmpty
        
        let finalResult = isPremiumFlag || isActiveSubscription
        
        print("🔍 [PlanAdvice] 增强会员状态检查:")
        print("  - UserDefaults isPremium: \(isPremiumFlag)")
        print("  - UserDefaults subscriptionStatus: \(subscriptionStatus ?? "无")")
        print("  - 访问令牌有效: \(hasValidToken)")
        print("  - 最终结果: \(finalResult)")
        
        return finalResult
    }
    
    /// 执行日建议
    func executeAdviceDay(completion: @escaping (Result<Bool, Error>) -> Void) {
        executeAdvice(adviceType: .day, completion: completion)
    }
    
    /// 执行周建议
    func executeAdviceWeek(completion: @escaping (Result<Bool, Error>) -> Void) {
        executeAdvice(adviceType: .week, completion: completion)
    }
    
    /// 通用执行建议方法
    private func executeAdvice(adviceType: AdviceType, completion: @escaping (Result<Bool, Error>) -> Void) {
        isLoading = true
        errorMessage = nil
        
        // 🆕 检查会员状态 - 只有活跃会员才能执行建议生成
        if !isUserActivePremium() {
            print("❌ [PlanAdvice] 用户非活跃会员，无法执行\(adviceType == .day ? "日" : "周")建议生成")
            let error = NSError(domain: "Subscription Required", code: 4003, userInfo: [NSLocalizedDescriptionKey: "需要会员权限才能生成建议"])
            self.errorMessage = "需要会员权限才能生成建议"
            completion(.failure(error))
            isLoading = false
            return
        }
        
        let endpoint = adviceType == .day ? "exec-plan-advice-day" : "exec-plan-advice-week"
        let urlString = "\(baseURL)/ns/app/plan-advices/\(endpoint)"
        
        print("🔄 [PlanAdvice] 用户是活跃会员，执行\(adviceType == .day ? "日" : "周")建议")
        print("🔍 [PlanAdvice] 请求URL: \(urlString)")
        
        guard let url = URL(string: urlString) else {
            let error = NSError(domain: "Invalid URL", code: -1, userInfo: nil)
            print("❌ [PlanAdvice] 无效的URL: \(urlString)")
            completion(.failure(error))
            isLoading = false
            return
        }
        
        var request = URLRequest(url: url)
        request.httpMethod = "GET"
        request.setValue("application/x-www-form-urlencoded", forHTTPHeaderField: "Content-Type")
        
        // 添加Authorization header
        if let token = getAccessToken() {
            request.setValue("Bearer \(token)", forHTTPHeaderField: "Authorization")
            print("🔍 [PlanAdvice] 执行建议请求已添加Authorization头，token前缀: \(String(token.prefix(10)))...")
        } else {
            print("⚠️ [PlanAdvice] 未找到访问Token")
            let error = NSError(domain: "No access token", code: -1, userInfo: [NSLocalizedDescriptionKey: "用户未登录"])
            completion(.failure(error))
            isLoading = false
            return
        }
        
        session.dataTask(with: request) { [weak self] data, response, error in
            Task { @MainActor in
                guard let self = self else { return }
                
                self.isLoading = false
                
                if let httpResponse = response as? HTTPURLResponse {
                    print("🔍 [PlanAdvice] 执行\(adviceType == .day ? "日" : "周")建议HTTP状态码: \(httpResponse.statusCode)")
                }
                
                if let error = error {
                    print("❌ [PlanAdvice] 执行\(adviceType == .day ? "日" : "周")建议网络请求失败: \(error.localizedDescription)")
                    self.errorMessage = error.localizedDescription
                    completion(.failure(error))
                    return
                }
                
                guard let data = data else {
                    let error = NSError(domain: "No data received", code: -1, userInfo: nil)
                    print("❌ [PlanAdvice] 执行\(adviceType == .day ? "日" : "周")建议未收到数据")
                    self.errorMessage = "未收到数据"
                    completion(.failure(error))
                    return
                }
                
                // 打印响应数据用于调试
                if let responseString = String(data: data, encoding: .utf8) {
                    print("🔍 [PlanAdvice] 执行\(adviceType == .day ? "日" : "周")建议响应数据: \(responseString)")
                }
                
                do {
                    let apiResponse = try JSONDecoder().decode(ExecuteAdviceResponse.self, from: data)
                    
                    if apiResponse.code == 0 {
                        let success = apiResponse.data ?? false
                        print("✅ [PlanAdvice] 执行\(adviceType == .day ? "日" : "周")建议成功: \(success)")
                        completion(.success(success))
                    } else {
                        let error = NSError(domain: "API Error", code: apiResponse.code, userInfo: [NSLocalizedDescriptionKey: apiResponse.message])
                        print("❌ [PlanAdvice] 执行\(adviceType == .day ? "日" : "周")建议失败: code=\(apiResponse.code), message=\(apiResponse.message)")
                        self.errorMessage = apiResponse.message
                        completion(.failure(error))
                    }
                } catch {
                    print("❌ [PlanAdvice] 执行\(adviceType == .day ? "日" : "周")建议数据解析失败: \(error.localizedDescription)")
                    self.errorMessage = "数据解析失败"
                    completion(.failure(error))
                }
            }
        }.resume()
    }
    
    // MARK: - 订阅成功后的建议流程
    
    /// 用户订阅成功后的完整建议流程 - 带用户数据版本
    /// 1. 执行日建议 -> 2. 执行周建议 -> 3. 获取日建议数据 -> 4. 获取周建议数据
    func executeAndFetchAdvicesAfterSubscription(with userData: UserData? = nil, completion: @escaping (Bool) -> Void) {
        print("🚀 [PlanAdvice] 开始订阅成功后的建议生成和获取流程")
        
        // 验证用户是否为活跃会员 - 如果传入了userData，优先使用
        let isPremium: Bool
        if let userData = userData {
            isPremium = userData.isPremium
            print("🔍 [PlanAdvice] 使用传入的UserData检查会员状态: isPremium=\(isPremium)")
        } else {
            isPremium = isUserActivePremium()
        }
        
        if !isPremium {
            print("❌ [PlanAdvice] 用户非活跃会员，无法执行订阅后建议流程")
            completion(false)
            return
        }
        
        // 第一步：执行日建议
        executeAdviceDay { [weak self] result in
            switch result {
            case .success(let daySuccess):
                print("✅ [PlanAdvice] 步骤1完成 - 执行日建议: \(daySuccess)")
                
                // 第二步：执行周建议
                self?.executeAdviceWeek { [weak self] result in
                    switch result {
                    case .success(let weekSuccess):
                        print("✅ [PlanAdvice] 步骤2完成 - 执行周建议: \(weekSuccess)")
                        
                        // 第三步和第四步：并发获取建议数据
                        let group = DispatchGroup()
                        var fetchDaySuccess = false
                        var fetchWeekSuccess = false
                        
                        // 获取日建议数据
                        group.enter()
                        self?.fetchDailyAdvice { result in
                            defer { group.leave() }
                            switch result {
                            case .success(_):
                                fetchDaySuccess = true
                                print("✅ [PlanAdvice] 步骤3完成 - 获取日建议数据成功")
                            case .failure(let error):
                                print("❌ [PlanAdvice] 步骤3失败 - 获取日建议数据失败: \(error.localizedDescription)")
                            }
                        }
                        
                        // 获取周建议数据
                        group.enter()
                        self?.fetchWeeklyAdvice { result in
                            defer { group.leave() }
                            switch result {
                            case .success(_):
                                fetchWeekSuccess = true
                                print("✅ [PlanAdvice] 步骤4完成 - 获取周建议数据成功")
                                
                                // 🆕 立即在主线程上发送专门的营养分母更新通知
                                DispatchQueue.main.async {
                                    print("📢 [PlanAdvice] 周建议数据获取成功，立即发送营养分母更新通知")
                                    NotificationCenter.default.post(name: Notification.Name("WeeklyAdviceDataReady"), object: nil)
                                }
                                
                            case .failure(let error):
                                print("❌ [PlanAdvice] 步骤4失败 - 获取周建议数据失败: \(error.localizedDescription)")
                            }
                        }
                        
                        // 等待所有获取操作完成
                        group.notify(queue: .main) {
                            let overallSuccess = daySuccess && weekSuccess && fetchDaySuccess && fetchWeekSuccess
                            print("🏁 [PlanAdvice] 订阅成功后建议流程完成 - 整体成功: \(overallSuccess)")
                            print("   - 执行日建议: \(daySuccess ? "✅" : "❌")")
                            print("   - 执行周建议: \(weekSuccess ? "✅" : "❌")")
                            print("   - 获取日建议: \(fetchDaySuccess ? "✅" : "❌")")
                            print("   - 获取周建议: \(fetchWeekSuccess ? "✅" : "❌")")
                            
                            // 发送数据更新通知
                            if overallSuccess {
                                NotificationCenter.default.post(name: Notification.Name("PlanAdviceDataUpdated"), object: nil)
                                print("📢 [PlanAdvice] 已发送建议数据更新通知")
                            }
                            
                            completion(overallSuccess)
                        }
                        
                    case .failure(let error):
                        print("❌ [PlanAdvice] 步骤2失败 - 执行周建议失败: \(error.localizedDescription)")
                        completion(false)
                    }
                }
                
            case .failure(let error):
                print("❌ [PlanAdvice] 步骤1失败 - 执行日建议失败: \(error.localizedDescription)")
                completion(false)
            }
        }
    }
    
    /// 用户订阅成功后的完整建议流程 - 兼容版本
    func executeAndFetchAdvicesAfterSubscription(completion: @escaping (Bool) -> Void) {
        executeAndFetchAdvicesAfterSubscription(with: nil, completion: completion)
    }
    
    // MARK: - 数据处理方法
    
    // 安全转换字符串到Double的辅助方法
    private func safeDoubleConversion(_ string: String) -> Double {
        // 添加调试信息
        print("🔄 [PlanAdvice] safeDoubleConversion - 输入字符串: '\(string)'")
        
        // 处理空字符串
        guard !string.isEmpty else { 
            print("🔄 [PlanAdvice] safeDoubleConversion - 字符串为空，返回0.0")
            return 0.0 
        }
        
        // 尝试去除可能的空白字符和特殊字符
        let trimmedString = string.trimmingCharacters(in: .whitespacesAndNewlines)
        print("🔄 [PlanAdvice] safeDoubleConversion - 处理后字符串: '\(trimmedString)'")
        
        // 尝试直接转换
        if let value = Double(trimmedString) {
            print("🔄 [PlanAdvice] safeDoubleConversion - 直接转换成功: \(value)")
            return validateAndReturnValue(value)
        }
        
        // 如果直接转换失败，尝试提取数字部分（处理包含单位的情况）
        print("🔄 [PlanAdvice] safeDoubleConversion - 直接转换失败，尝试提取数字部分")
        
        // 使用正则表达式提取数字（包括小数）
        let pattern = #"(\d+(?:\.\d+)?)"#
        if let regex = try? NSRegularExpression(pattern: pattern),
           let match = regex.firstMatch(in: trimmedString, range: NSRange(trimmedString.startIndex..., in: trimmedString)),
           let range = Range(match.range, in: trimmedString) {
            
            let numberString = String(trimmedString[range])
            print("🔄 [PlanAdvice] safeDoubleConversion - 提取到的数字字符串: '\(numberString)'")
            
            if let value = Double(numberString) {
                print("🔄 [PlanAdvice] safeDoubleConversion - 数字提取转换成功: \(value)")
                return validateAndReturnValue(value)
            }
        }
        
        print("❌ [PlanAdvice] safeDoubleConversion - 所有转换方法都失败，字符串: '\(trimmedString)'")
        return 0.0
    }
    
    // 验证并返回有效值的辅助方法
    private func validateAndReturnValue(_ value: Double) -> Double {
        // 检查是否为NaN或无穷大
        if value.isNaN || value.isInfinite {
            print("❌ [PlanAdvice] validateAndReturnValue - 值为NaN或无穷大: \(value)")
            return 0.0
        }
        
        // 检查是否为负数，营养和运动数据不应该是负数
        if value < 0 {
            print("❌ [PlanAdvice] validateAndReturnValue - 值为负数: \(value)")
            return 0.0
        }
        
        print("✅ [PlanAdvice] validateAndReturnValue - 最终返回值: \(value)")
        return value
    }
    
    // 安全转换Double到Int的辅助方法
    private func safeIntConversion(_ value: Double) -> Int {
        // 检查是否为NaN或无穷大
        if value.isNaN || value.isInfinite {
            return 0
        }
        
        // 检查是否为负数
        if value < 0 {
            return 0
        }
        
        // 确保值在合理范围内（避免过大的值）
        let clampedValue = min(value, 999999.0)
        
        return Int(clampedValue)
    }
    
    // 获取日营养分母（周数值/7）
    func getDailyNutritionDenominator() -> (calories: Double, protein: Double) {
        guard let weeklyAdvice = weeklyAdvice else { 
            print("⚠️ [PlanAdvice] 获取日营养分母失败：周建议数据为空")
            return (0, 0) 
        }
        
        // 添加详细的调试信息
        print("🔍 [PlanAdvice] 调试 - 原始周数据:")
        print("  - calorieRecommend: '\(weeklyAdvice.calorieRecommend ?? "nil")'")
        print("  - proteinRecommend: '\(weeklyAdvice.proteinRecommend ?? "nil")'")
        
        let calorieString = weeklyAdvice.calorieRecommend ?? ""
        let proteinString = weeklyAdvice.proteinRecommend ?? ""
        
        print("🔍 [PlanAdvice] 调试 - 处理后的字符串:")
        print("  - calorieString: '\(calorieString)'")
        print("  - proteinString: '\(proteinString)'")
        print("  - calorieString.isEmpty: \(calorieString.isEmpty)")
        print("  - proteinString.isEmpty: \(proteinString.isEmpty)")
        
        let weeklyCalories = safeDoubleConversion(calorieString)
        let weeklyProtein = safeDoubleConversion(proteinString)
        
        print("🔍 [PlanAdvice] 调试 - 转换后的数值:")
        print("  - weeklyCalories: \(weeklyCalories)")
        print("  - weeklyProtein: \(weeklyProtein)")
        
        let dailyCalories = weeklyCalories / 7.0
        let dailyProtein = weeklyProtein / 7.0
        
        print("📊 [PlanAdvice] 日营养分母计算: 周卡路里=\(weeklyCalories) -> 日卡路里=\(dailyCalories)")
        print("📊 [PlanAdvice] 日营养分母计算: 周蛋白质=\(weeklyProtein) -> 日蛋白质=\(dailyProtein)")
        
        return (calories: dailyCalories, protein: dailyProtein)
    }
    
    // 获取Plan页面周营养分母
    func getWeeklyNutritionDenominator() -> (calories: Double, fat: Double, protein: Double) {
        guard let weeklyAdvice = weeklyAdvice else { 
            print("⚠️ [PlanAdvice] 获取周营养分母失败：周建议数据为空")
            return (0, 0, 0) 
        }
        
        let calories = safeDoubleConversion(weeklyAdvice.calorieRecommend ?? "")
        let fat = safeDoubleConversion(weeklyAdvice.fatRecommend ?? "")
        let protein = safeDoubleConversion(weeklyAdvice.proteinRecommend ?? "")
        
        print("📊 [PlanAdvice] 周营养分母: 卡路里=\(calories), 脂肪=\(fat), 蛋白质=\(protein)")
        
        return (calories: calories, fat: fat, protein: protein)
    }
    
    // 获取周燃脂分母
    func getWeeklyExerciseBurnDenominator() -> Double {
        guard let weeklyAdvice = weeklyAdvice else { 
            print("⚠️ [PlanAdvice] 获取周燃脂分母失败：周建议数据为空")
            return 0 
        }
        
        let burnGoal = safeDoubleConversion(weeklyAdvice.exerciseCalorieBurnRecommend ?? "")
        print("📊 [PlanAdvice] 周燃脂分母: \(burnGoal)")
        
        return burnGoal
    }
    
    // 获取日燃脂分母（周燃脂/7）
    func getDailyExerciseBurnDenominator() -> Double {
        let weeklyBurn = getWeeklyExerciseBurnDenominator()
        return weeklyBurn / 7.0
    }
    
    // 获取Plan页面的建议文本（按营养、运动、睡眠顺序取前3个）
    func getPlanAdviceTexts() -> [String] {
        guard let weeklyAdvice = weeklyAdvice else { return [] }
        
        // 取前3个建议，如果不足3个就返回所有的
        let maxCount = min(3, weeklyAdvice.content.count)
        return Array(weeklyAdvice.content.prefix(maxCount))
    }
    
    // 获取Weight页面的日建议文本
    func getDailyAdviceTexts() -> [String] {
        guard let dailyAdvice = dailyAdvice else { return [] }
        return dailyAdvice.content
    }
    
    // 安全获取营养目标的Int值（用于UI显示）
    func getSafeWeeklyNutritionGoals() -> (calories: Int, fat: Int, protein: Int) {
        let nutrition = getWeeklyNutritionDenominator()
        
        let result = (
            calories: safeIntConversion(nutrition.calories),
            fat: safeIntConversion(nutrition.fat),
            protein: safeIntConversion(nutrition.protein)
        )
        
        print("🔢 [PlanAdvice] 安全周营养目标: 卡路里=\(result.calories), 脂肪=\(result.fat), 蛋白质=\(result.protein)")
        
        return result
    }
    
    // 安全获取日营养目标的Int值（用于UI显示）
    func getSafeDailyNutritionGoals() -> (calories: Int, protein: Int) {
        // 实时检查周建议数据状态
        print("🔍 [PlanAdvice] getSafeDailyNutritionGoals - 实时数据状态检查:")
        print("  - weeklyAdvice是否存在: \(weeklyAdvice != nil)")
        
        if let advice = weeklyAdvice {
            print("  - 卡路里推荐: '\(advice.calorieRecommend ?? "nil")' (长度: \(advice.calorieRecommend?.count ?? 0))")
            print("  - 蛋白质推荐: '\(advice.proteinRecommend ?? "nil")' (长度: \(advice.proteinRecommend?.count ?? 0))")
            print("  - 脂肪推荐: '\(advice.fatRecommend ?? "nil")' (长度: \(advice.fatRecommend?.count ?? 0))")
            print("  - 运动燃脂推荐: '\(advice.exerciseCalorieBurnRecommend ?? "nil")' (长度: \(advice.exerciseCalorieBurnRecommend?.count ?? 0))")
        } else {
            print("  - weeklyAdvice为nil")
        }
        
        let nutrition = getDailyNutritionDenominator()
        
        let result = (
            calories: safeIntConversion(nutrition.calories),
            protein: safeIntConversion(nutrition.protein)
        )
        
        print("🔢 [PlanAdvice] 安全日营养目标: 卡路里=\(result.calories), 蛋白质=\(result.protein)")
        
        return result
    }
    
    // 安全获取周燃脂目标的Int值（用于UI显示）
    func getSafeWeeklyExerciseBurnGoal() -> Int {
        let weeklyBurn = getWeeklyExerciseBurnDenominator()
        return safeIntConversion(weeklyBurn)
    }
    
    // 安全获取日燃脂目标的Int值（用于UI显示）
    func getSafeDailyExerciseBurnGoal() -> Int {
        let dailyBurn = getDailyExerciseBurnDenominator()
        return safeIntConversion(dailyBurn)
    }
    
    // MARK: - 辅助方法
    
    private func getAccessToken() -> String? {
        // 从UserDefaults获取accessToken
        return UserDefaults.standard.string(forKey: "accessToken")
    }
    
    // 检查周建议数据是否完整
    func isWeeklyAdviceDataComplete() -> Bool {
        guard let weeklyAdvice = weeklyAdvice else {
            print("❌ [PlanAdvice] 周建议数据为空")
            return false
        }
        
        let hasCalorie = weeklyAdvice.calorieRecommend != nil && !weeklyAdvice.calorieRecommend!.isEmpty
        let hasProtein = weeklyAdvice.proteinRecommend != nil && !weeklyAdvice.proteinRecommend!.isEmpty
        let hasFat = weeklyAdvice.fatRecommend != nil && !weeklyAdvice.fatRecommend!.isEmpty
        let hasExercise = weeklyAdvice.exerciseCalorieBurnRecommend != nil && !weeklyAdvice.exerciseCalorieBurnRecommend!.isEmpty
        
        print("🔍 [PlanAdvice] 周建议数据完整性检查:")
        print("  - 卡路里数据: \(hasCalorie ? "✅" : "❌") (\(weeklyAdvice.calorieRecommend ?? "nil"))")
        print("  - 蛋白质数据: \(hasProtein ? "✅" : "❌") (\(weeklyAdvice.proteinRecommend ?? "nil"))")
        print("  - 脂肪数据: \(hasFat ? "✅" : "❌") (\(weeklyAdvice.fatRecommend ?? "nil"))")
        print("  - 运动燃脂数据: \(hasExercise ? "✅" : "❌") (\(weeklyAdvice.exerciseCalorieBurnRecommend ?? "nil"))")
        
        // 添加详细的字符串分析
        if let calorieStr = weeklyAdvice.calorieRecommend {
            print("🔬 [PlanAdvice] 详细分析 - 卡路里字符串:")
            print("    - 原始内容: '\(calorieStr)'")
            print("    - 字符串长度: \(calorieStr.count)")
            print("    - 字符代码: \(calorieStr.map { String($0.asciiValue ?? 0) }.joined(separator: ","))")
            print("    - 去空格后: '\(calorieStr.trimmingCharacters(in: .whitespacesAndNewlines))'")
            if let doubleValue = Double(calorieStr.trimmingCharacters(in: .whitespacesAndNewlines)) {
                print("    - 直接转换Double: \(doubleValue)")
            } else {
                print("    - 直接转换Double: 失败")
            }
        }
        
        if let proteinStr = weeklyAdvice.proteinRecommend {
            print("🔬 [PlanAdvice] 详细分析 - 蛋白质字符串:")
            print("    - 原始内容: '\(proteinStr)'")
            print("    - 字符串长度: \(proteinStr.count)")
            print("    - 字符代码: \(proteinStr.map { String($0.asciiValue ?? 0) }.joined(separator: ","))")
            print("    - 去空格后: '\(proteinStr.trimmingCharacters(in: .whitespacesAndNewlines))'")
            if let doubleValue = Double(proteinStr.trimmingCharacters(in: .whitespacesAndNewlines)) {
                print("    - 直接转换Double: \(doubleValue)")
            } else {
                print("    - 直接转换Double: 失败")
            }
        }
        
        return hasCalorie && hasProtein && hasFat && hasExercise
    }
    
    // 检查日建议数据是否完整
    func isDailyAdviceDataComplete() -> Bool {
        guard let dailyAdvice = dailyAdvice else {
            print("❌ [PlanAdvice] 日建议数据为空")
            return false
        }
        
        let hasContent = !dailyAdvice.content.isEmpty
        
        print("🔍 [PlanAdvice] 日建议数据完整性检查:")
        print("  - 建议内容: \(hasContent ? "✅" : "❌") (\(dailyAdvice.content.count)条)")
        
        return hasContent
    }
    
    // MARK: - 定时刷新机制
    
    /// 停止自动刷新
    nonisolated private func stopAutomaticRefresh() {
        DispatchQueue.main.async { [weak self] in
            print("🕒 [PlanAdvice] 停止自动刷新机制")
            self?.dailyRefreshTimer?.invalidate()
            self?.weeklyRefreshTimer?.invalidate()
            self?.dailyRefreshTimer = nil
            self?.weeklyRefreshTimer = nil
        }
    }
    
    /// 设置每日刷新定时器（每天0点0分）
    private func setupDailyRefreshTimer() {
        // 计算到下一个0点的时间间隔
        let calendar = Calendar.current
        let now = Date()
        
        var nextMidnight = calendar.startOfDay(for: now)
        nextMidnight = calendar.date(byAdding: .day, value: 1, to: nextMidnight) ?? nextMidnight
        
        let timeInterval = nextMidnight.timeIntervalSince(now)
        
        print("🕒 [PlanAdvice] 设置日建议刷新定时器，下次刷新时间: \(nextMidnight), 间隔: \(timeInterval)秒")
        
        // 先设置一个到下一个0点的定时器
        dailyRefreshTimer = Timer.scheduledTimer(withTimeInterval: timeInterval, repeats: false) { [weak self] _ in
            Task { @MainActor in
                self?.refreshDailyAdvice()
                // 刷新后设置每24小时重复的定时器
                self?.setupRepeatingDailyTimer()
            }
        }
    }
    
    /// 设置重复的每日定时器（每24小时）
    private func setupRepeatingDailyTimer() {
        print("🕒 [PlanAdvice] 设置重复的日建议刷新定时器（每24小时）")
        dailyRefreshTimer = Timer.scheduledTimer(withTimeInterval: 24 * 60 * 60, repeats: true) { [weak self] _ in
            Task { @MainActor in
                self?.refreshDailyAdvice()
            }
        }
    }
    
    /// 设置每周刷新定时器（每周一0点0分）
    private func setupWeeklyRefreshTimer() {
        let calendar = Calendar.current
        let now = Date()
        
        // 找到下一个周一的0点
        let currentWeekday = calendar.component(.weekday, from: now)
        let daysUntilMonday = (2 - currentWeekday + 7) % 7 // 2表示周一（周日=1，周一=2）
        let daysToAdd = daysUntilMonday == 0 ? 7 : daysUntilMonday // 如果今天是周一，则下一次是7天后
        
        var nextMonday = calendar.startOfDay(for: now)
        nextMonday = calendar.date(byAdding: .day, value: daysToAdd, to: nextMonday) ?? nextMonday
        
        let timeInterval = nextMonday.timeIntervalSince(now)
        
        print("🕒 [PlanAdvice] 设置周建议刷新定时器，下次刷新时间: \(nextMonday), 间隔: \(timeInterval)秒")
        
        // 先设置一个到下一个周一0点的定时器
        weeklyRefreshTimer = Timer.scheduledTimer(withTimeInterval: timeInterval, repeats: false) { [weak self] _ in
            Task { @MainActor in
                self?.refreshWeeklyAdvice()
                // 刷新后设置每7天重复的定时器
                self?.setupRepeatingWeeklyTimer()
            }
        }
    }
    
    /// 设置重复的每周定时器（每7天）
    private func setupRepeatingWeeklyTimer() {
        print("🕒 [PlanAdvice] 设置重复的周建议刷新定时器（每7天）")
        weeklyRefreshTimer = Timer.scheduledTimer(withTimeInterval: 7 * 24 * 60 * 60, repeats: true) { [weak self] _ in
            Task { @MainActor in
                self?.refreshWeeklyAdvice()
            }
        }
    }
}

// MARK: - 建议类型枚举
enum AdviceType: String, CaseIterable {
    case week = "WEEK"
    case day = "DAY"
    
    // 添加调试信息
    var debugDescription: String {
        return "AdviceType.\(self) = \"\(self.rawValue)\""
    }
}

// MARK: - 扩展方法，用于初始化时加载数据
extension PlanAdviceService {
    
    // 初始化加载所有建议数据
    func loadAllAdvices() {
        print("🚀 [PlanAdvice] 开始加载所有建议数据...")
        
        // 验证枚举值
        print("🔍 [PlanAdvice] 枚举验证: \(AdviceType.week.debugDescription)")
        print("🔍 [PlanAdvice] 枚举验证: \(AdviceType.day.debugDescription)")
        
        // 验证URL构建
        let weekURL = "\(self.baseURL)/ns/app/plan-advices/\(AdviceType.week.rawValue)/latest"
        let dayURL = "\(self.baseURL)/ns/app/plan-advices/\(AdviceType.day.rawValue)/latest"
        print("🔍 [PlanAdvice] 周建议URL: \(weekURL)")
        print("🔍 [PlanAdvice] 日建议URL: \(dayURL)")
        
        // 加载周建议
        print("🔄 [PlanAdvice] 加载周建议...")
        fetchWeeklyAdvice { result in
            switch result {
            case .success(let advice):
                print("✅ [PlanAdvice] 周建议加载成功")
                print("✅ [PlanAdvice] 周建议内容: \(advice.content)")
                print("✅ [PlanAdvice] 周建议营养数据: 卡路里=\(advice.calorieRecommend ?? "未提供"), 蛋白质=\(advice.proteinRecommend ?? "未提供"), 脂肪=\(advice.fatRecommend ?? "未提供")")
                
                // 🔧 修复：周建议加载成功后立即发送通知，触发UI的除7计算
                print("📢 [PlanAdvice] 周建议已在loadAllAdvices中加载成功，发送通知触发除7计算")
                DispatchQueue.main.async {
                    NotificationCenter.default.post(name: Notification.Name("PlanAdviceDataUpdated"), object: nil)
                    // 🆕 同时发送专门的周建议数据就绪通知
                    NotificationCenter.default.post(name: Notification.Name("WeeklyAdviceDataReady"), object: nil)
                }
                
            case .failure(let error):
                print("❌ [PlanAdvice] 周建议加载失败: \(error.localizedDescription)")
            }
        }
        
        // 加载日建议 - 明确使用DAY参数
        print("🔄 [PlanAdvice] 加载日建议 (使用DAY参数)...")
        fetchDailyAdvice { result in
            switch result {
            case .success(let advice):
                print("✅ [PlanAdvice] 日建议加载成功")
                print("✅ [PlanAdvice] 日建议类型: \(advice.adviceType)")
                print("✅ [PlanAdvice] 日建议内容数量: \(advice.content.count)")
                print("✅ [PlanAdvice] 日建议内容: \(advice.content)")
                print("✅ [PlanAdvice] 日建议营养数据: 卡路里=\(advice.calorieRecommend ?? "未提供"), 蛋白质=\(advice.proteinRecommend ?? "未提供"), 脂肪=\(advice.fatRecommend ?? "未提供"), 运动燃脂=\(advice.exerciseCalorieBurnRecommend ?? "未提供")")
            case .failure(let error):
                print("❌ [PlanAdvice] 日建议加载失败: \(error.localizedDescription)")
                if let nsError = error as NSError? {
                    print("❌ [PlanAdvice] 日建议错误详情: domain=\(nsError.domain), code=\(nsError.code), userInfo=\(nsError.userInfo)")
                }
            }
        }
    }
    
    // 刷新建议数据
    func refreshAdvices() {
        loadAllAdvices()
    }
} 