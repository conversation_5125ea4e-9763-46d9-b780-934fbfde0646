import SwiftUI
import AVFoundation
import Photos

class CameraManager: NSObject, ObservableObject {
    @Published var session = AVCaptureSession()
    @Published var output = AVCapturePhotoOutput()
    @Published var preview: AVCaptureVideoPreviewLayer?
    @Published var isCameraAuthorized = false
    @Published var isCameraUnavailable = false
    @Published var flashMode: AVCaptureDevice.FlashMode = .off
    @Published var capturedImage: UIImage?
    @Published var isSessionRunning = false // 添加会话运行状态追踪
    @Published var showPhotoLibrary = false // 控制相册选择器显示
    
    var completion: ((UIImage?) -> Void)?
    
    override init() {
        super.init()
        // 初始化后立即检查权限
        checkPermissions()
    }
    
    func checkPermissions() {
        print("Checking camera permission status...")
        switch AVCaptureDevice.authorizationStatus(for: .video) {
        case .authorized:
            print("Camera permission authorized")
            self.isCameraAuthorized = true
            self.setupCamera() // 权限已授权，立即设置相机
        case .notDetermined:
            print("Camera permission not determined, requesting permission...")
            AVCaptureDevice.requestAccess(for: .video) { [weak self] granted in
                DispatchQueue.main.async {
                    print("Camera permission request result: \(granted)")
                    self?.isCameraAuthorized = granted
                    if granted {
                        self?.setupCamera()
                    } else {
                        self?.isCameraUnavailable = true
                    }
                }
            }
        case .denied, .restricted:
            print("Camera permission denied or restricted")
            self.isCameraAuthorized = false
            self.isCameraUnavailable = true
        @unknown default:
            print("Camera permission status unknown")
            self.isCameraAuthorized = false
            self.isCameraUnavailable = true
        }
    }
    
    func setupCamera() {
        // 确保在后台线程设置会话
        DispatchQueue.global(qos: .userInitiated).async { [weak self] in
            guard let self = self else { return }
            
            self.session.beginConfiguration()
            
            // 设置高质量捕获
            self.session.sessionPreset = .high
            
            // 设置输入
            guard let device = AVCaptureDevice.default(.builtInWideAngleCamera, for: .video, position: .back) else {
                DispatchQueue.main.async {
                    self.isCameraUnavailable = true
                }
                self.session.commitConfiguration()
                return
            }
            
            // 尝试获取最佳配置
            try? device.lockForConfiguration()
            if device.isLowLightBoostSupported {
                device.automaticallyEnablesLowLightBoostWhenAvailable = true
            }
            device.unlockForConfiguration()
            
            // 设置设备输入
            do {
                let input = try AVCaptureDeviceInput(device: device)
                
                if self.session.canAddInput(input) {
                    self.session.addInput(input)
                }
                
                // 设置输出
                if self.session.canAddOutput(self.output) {
                    self.session.addOutput(self.output)
                }
                
                self.session.commitConfiguration()
                
                // 创建预览层
                DispatchQueue.main.async {
                    let preview = AVCaptureVideoPreviewLayer(session: self.session)
                    preview.videoGravity = .resizeAspectFill
                    
                    // 设置预览方向
                    if let connection = preview.connection {
                        if #available(iOS 17.0, *) {
                            // iOS 17+：不显式设置已弃用的 API，保持系统默认（通常为竖屏）
                        } else {
                            if connection.isVideoOrientationSupported {
                                connection.videoOrientation = .portrait
                            }
                        }
                    }
                    
                    self.preview = preview
                    
                    // 相机设置完成后立即启动会话
                    self.startSession()
                }
            } catch {
                print("Camera setup error: \(error.localizedDescription)")
                DispatchQueue.main.async {
                    self.isCameraUnavailable = true
                }
            }
        }
    }
    
    func startSession() {
        // 避免在应用被挂起时启动会话
        guard !isSessionRunning else { return }
        
        // 先在主线程设置状态标志，避免重复启动
        DispatchQueue.main.async {
            print("Preparing to start camera session...")
        }
        
        // 先确保会话没有在运行
        if session.isRunning {
            print("Session already running, skipping start")
            DispatchQueue.main.async {
                self.isSessionRunning = true
            }
            return
        }
        
        // 使用后台队列启动会话
        DispatchQueue.global(qos: .userInitiated).async { [weak self] in
            guard let self = self else { return }
            
            // 确保停止当前可能正在运行的会话
            if self.session.isRunning {
                self.session.stopRunning()
            }
            
            // 延迟一点启动会话，让UI有时间准备
            DispatchQueue.main.asyncAfter(deadline: .now() + 0.3) {
                // 重新回到后台线程启动会话
                DispatchQueue.global(qos: .userInitiated).async {
                    // 尝试启动会话
                    self.session.startRunning()
                    
                    // 在主线程更新状态
                    DispatchQueue.main.async {
                        self.isSessionRunning = self.session.isRunning
                        print("Camera session started: \(self.isSessionRunning)")
                    }
                }
            }
        }
    }
    
    func stopSession() {
        // 避免在会话未运行时尝试停止它
        guard isSessionRunning else { return }
        
        // 使用后台队列停止会话
        DispatchQueue.global(qos: .userInitiated).async { [weak self] in
            guard let self = self else { return }
            self.session.stopRunning()
            
            DispatchQueue.main.async {
                self.isSessionRunning = self.session.isRunning
                print("Camera session stopped: \(!self.isSessionRunning)")
            }
        }
    }
    
    func capturePhoto(completion: @escaping (UIImage?) -> Void) {
        self.completion = completion
        
        let photoSettings = AVCapturePhotoSettings()
        
        // 闪光灯设置
        if self.output.supportedFlashModes.contains(self.flashMode) {
            photoSettings.flashMode = self.flashMode
        }
        
        self.output.capturePhoto(with: photoSettings, delegate: self)
    }
    
    func toggleFlash() {
        switch flashMode {
        case .off:
            flashMode = .on
        case .on:
            flashMode = .auto
        case .auto:
            flashMode = .off
        @unknown default:
            flashMode = .off
        }
    }
    
    func getFlashIcon() -> String {
        switch flashMode {
        case .off:
            return "bolt.slash.fill"
        case .on:
            return "bolt.fill"
        case .auto:
            return "bolt.badge.a.fill"
        @unknown default:
            return "bolt.slash.fill"
        }
    }
    
    // MARK: - 释放资源
    func cleanUp() {
        // 确保停止会话
        if isSessionRunning {
            stopSession()
        }
        
        // 清除捕获的图像，释放内存
        capturedImage = nil
        completion = nil
    }
    
    // 检查相册权限
    private func checkPhotoLibraryPermission() {
        print("📸 CameraManager: Requesting photo library permission with user-friendly explanation")
        
        // 使用增强的权限管理器
        PhotoLibraryPermissionManager.shared.requestPhotoLibraryAccess { status in
            DispatchQueue.main.async {
                switch status {
                case .authorized, .limited:
                    print("✅ CameraManager: Photo library access granted, showing picker")
                    self.showPhotoLibrary = true
                default:
                    print("❌ CameraManager: Photo library access denied")
                    // 权限被拒绝，不显示相册选择器
                    break
                }
            }
        }
    }
    
    // 获取当前顶层视图控制器
    private func topViewController() -> UIViewController? {
        // 使用更安全的方式获取顶层视图控制器，减少辅助功能相关警告
        guard let windowScene = UIApplication.shared.connectedScenes
            .compactMap({ $0 as? UIWindowScene })
            .first(where: { $0.activationState == .foregroundActive }),
              let window = windowScene.windows.first(where: { $0.isKeyWindow }),
              let rootViewController = window.rootViewController else {
            print("❌ CameraManager: Unable to find root view controller")
            return nil
        }
        
        var topController = rootViewController
        while let presentedViewController = topController.presentedViewController {
            topController = presentedViewController
        }
        
        return topController
    }
}

// MARK: - AVCapturePhotoCaptureDelegate
extension CameraManager: AVCapturePhotoCaptureDelegate {
    func photoOutput(_ output: AVCapturePhotoOutput, didFinishProcessingPhoto photo: AVCapturePhoto, error: Error?) {
        if let error = error {
            print("Photo capture error: \(error.localizedDescription)")
            self.completion?(nil)
            return
        }

        guard let imageData = photo.fileDataRepresentation(),
              let image = UIImage(data: imageData) else {
            print("Could not create UIImage from captured photo")
            self.completion?(nil)
            return
        }

        DispatchQueue.main.async { [weak self] in
            self?.capturedImage = image
            self?.completion?(image)

            // 上报拍照事件
            XDTrackTool.shared.trackPhotoCapture(source: "camera")
        }
    }
}

// MARK: - 相机预览视图
struct CameraPreviewView: UIViewRepresentable {
    @ObservedObject var cameraManager: CameraManager
    
    func makeUIView(context: Context) -> UIView {
        let view = UIView(frame: UIScreen.main.bounds)
        view.backgroundColor = .black
        
        updatePreviewLayer(on: view)
        
        return view
    }
    
    func updateUIView(_ uiView: UIView, context: Context) {
        updatePreviewLayer(on: uiView)
    }
    
    private func updatePreviewLayer(on view: UIView) {
        // 移除所有现有预览层
        view.layer.sublayers?.filter { $0 is AVCaptureVideoPreviewLayer }.forEach { $0.removeFromSuperlayer() }
        
        // 添加新的预览层
        if let previewLayer = cameraManager.preview {
            previewLayer.frame = view.bounds
            previewLayer.videoGravity = .resizeAspectFill
            
            // 在主线程添加预览层
            DispatchQueue.main.async {
                view.layer.addSublayer(previewLayer)
                print("Camera preview layer updated and added to view")
            }
        } else {
            print("Camera preview layer not created, please check camera setup")
        }
    }
}

// MARK: - 真实相机视图
struct RealCameraView: View {
    @Binding var isPresented: Bool
    @Binding var showAnalyzing: Bool
    @Binding var selectedImage: UIImage?
    @StateObject private var cameraManager = CameraManager()
    @State private var showPermissionAlert = false
    @State private var showPhotoLibraryPermissionAlert = false
    @State private var showPhotoLibrary = false
    @State private var alertTitle = "Camera Permission"
    @State private var alertMessage = "Please allow FitScanAI to access the camera in Settings to use this feature."
    
    var body: some View {
        ZStack {
            // 黑色背景确保全屏
            Color.black.ignoresSafeArea()
            
            if cameraManager.isCameraAuthorized && !cameraManager.isCameraUnavailable {
                // 相机预览 - 确保全屏并在Z轴最底层
                CameraPreviewView(cameraManager: cameraManager)
                    .ignoresSafeArea()
                    .zIndex(0) // 确保在底层
                
                // 网格线 - 放在相机预览之上
                ZStack {
                    // 使用叠加方式放置网格线和边框
                    GridLines()
                        .frame(width: UIScreen.main.bounds.width - 40, height: UIScreen.main.bounds.width - 40)
                        .clipShape(RoundedRectangle(cornerRadius: 15))
                        .overlay(
                            RoundedRectangle(cornerRadius: 15)
                                .stroke(Color.green, lineWidth: 2)
                        )
                }
                .zIndex(1) // 确保在相机预览之上
                
                // UI元素 - 放在最上层
                VStack {
                    // 顶部标题
                    HStack {
                        Button(action: {
                            isPresented = false
                        }) {
                            Image(systemName: "xmark")
                                .font(.title2)
                                .foregroundColor(.white)
                                .padding()
                        }
                        
                        Spacer()
                        
                        Text("Position food in the frame")
                            .font(.headline)
                            .foregroundColor(.green)
                        
                        Spacer()
                        
                        // 这里删除了闪光灯按钮，移到底部
                        // 保留一个空的视图以保持布局平衡
                        Color.clear
                            .frame(width: 40, height: 40)
                            .padding()
                    }
                    
                    Spacer()
                    
                    // 底部提示和控制
                    VStack {
                        Text("For best results, ensure good lighting")
                            .font(.subheadline)
                            .foregroundColor(.secondary)
                            .padding(.bottom, 20)
                        
                        HStack(spacing: 50) {
                            // 相册按钮
                            Button(action: {
                                checkPhotoLibraryPermission()
                            }) {
                                Image(systemName: "photo")
                                    .font(.system(size: 24))
                                    .foregroundColor(.white)
                                    .frame(width: 60, height: 60)
                            }
                            
                            // 拍照按钮
                            Button(action: {
                                cameraManager.capturePhoto { image in
                                    if let capturedImage = image {
                                        selectedImage = capturedImage
                                        isPresented = false
                                        showAnalyzing = true
                                    }
                                }
                            }) {
                                Circle()
                                    .fill(Color.green)
                                    .frame(width: 70, height: 70)
                                    .overlay(
                                        Circle()
                                            .stroke(Color.white, lineWidth: 4)
                                            .frame(width: 60, height: 60)
                                    )
                            }
                            
                            // 闪光灯按钮 (原来是保存按钮的位置)
                            Button(action: {
                                cameraManager.toggleFlash()
                            }) {
                                Image(systemName: cameraManager.getFlashIcon())
                                    .font(.system(size: 24))
                                    .foregroundColor(.white)
                                    .frame(width: 60, height: 60)
                            }
                        }
                        .padding(.bottom, 40)
                    }
                }
                .padding(.horizontal)
                .zIndex(2) // 确保在最上层
            } else {
                // 相机不可用或未授权
                VStack(spacing: 20) {
                    Image(systemName: "camera.fill")
                        .font(.system(size: 70))
                        .foregroundColor(.gray)
                    
                    Text("Camera Unavailable")
                        .font(.title2)
                        .fontWeight(.bold)
                    
                    if cameraManager.isCameraUnavailable {
                        Text("The camera is not available on this device.")
                            .multilineTextAlignment(.center)
                            .foregroundColor(.secondary)
                            .padding(.horizontal)
                    } else {
                        Text("Please allow camera access in Settings to use this feature.")
                            .multilineTextAlignment(.center)
                            .foregroundColor(.secondary)
                            .padding(.horizontal)
                        
                        Button("Open Settings") {
                            if let url = URL(string: UIApplication.openSettingsURLString) {
                                UIApplication.shared.open(url)
                            }
                        }
                        .padding()
                        .background(Color.green)
                        .foregroundColor(.white)
                        .cornerRadius(10)
                    }
                    
                    Button("Cancel") {
                        isPresented = false
                    }
                    .padding(.top)
                }
                .padding()
            }
        }
        .onAppear {
            // 统计
            XDTrackTool.shared.appear("相机页面")

            // 视图出现时，检查相机权限并确保相机会话启动
            if cameraManager.isCameraAuthorized && !cameraManager.isCameraUnavailable {
                // 确保会话已启动
                DispatchQueue.main.asyncAfter(deadline: .now() + 0.5) {
                    print("View appeared, preparing to start camera session")
                    if !cameraManager.isSessionRunning {
                        cameraManager.startSession()
                    }
                }
            } else if !cameraManager.isCameraAuthorized && !cameraManager.isCameraUnavailable {
                // 检查相机权限
                AVCaptureDevice.requestAccess(for: .video) { granted in
                    DispatchQueue.main.async {
                        if !granted {
                            alertTitle = "Camera Permission"
                            alertMessage = "Please allow FitScanAI to access the camera in Settings to use this feature."
                            showPermissionAlert = true
                        } else {
                            // 授权成功，设置相机
                            cameraManager.isCameraAuthorized = true
                            cameraManager.setupCamera()
                            // 增加延迟确保相机初始化完成
                            DispatchQueue.main.asyncAfter(deadline: .now() + 1.0) {
                                if !cameraManager.isSessionRunning {
                                    print("Permission obtained, starting camera session")
                                    cameraManager.startSession()
                                }
                            }
                        }
                    }
                }
            }
        }
        .onDisappear {
            // 确保在视图消失时清理资源
            cameraManager.cleanUp()
        }
        .sheet(isPresented: $showPhotoLibrary) {
            if #available(iOS 14, *) {
                StandardPhotoPicker(selectedImage: $selectedImage, isPresented: $showPhotoLibrary) {
                    isPresented = false
                    showAnalyzing = true
                }
            } else {
                UnsupportedPhotoPicker(isPresented: $showPhotoLibrary) {
                    // 如果设备不支持，回调后什么都不做
                }
            }
        }
        .alert(isPresented: $showPermissionAlert) {
            Alert(
                title: Text(alertTitle),
                message: Text(alertMessage),
                primaryButton: .default(Text("Go to Settings")) {
                    if let url = URL(string: UIApplication.openSettingsURLString) {
                        UIApplication.shared.open(url)
                    }
                },
                secondaryButton: .cancel(Text("Cancel")) {
                    isPresented = false
                }
            )
        }
    }
    
    // 检查相册权限
    private func checkPhotoLibraryPermission() {
        print("📸 CameraManager: Requesting photo library permission with user-friendly explanation")
        
        // 使用增强的权限管理器
        PhotoLibraryPermissionManager.shared.requestPhotoLibraryAccess { status in
            DispatchQueue.main.async {
                switch status {
                case .authorized, .limited:
                    print("✅ CameraManager: Photo library access granted, showing picker")
                    self.showPhotoLibrary = true
                default:
                    print("❌ CameraManager: Photo library access denied")
                    // 权限被拒绝，不显示相册选择器
                    break
                }
            }
        }
    }
}

// 注意：GridLines和UnsupportedPhotoPicker已在ContentView.swift中定义，避免重复定义
