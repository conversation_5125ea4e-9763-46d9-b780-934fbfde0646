import Foundation

/// 打卡历史记录服务
class CheckInHistoryService: ObservableObject {
    static let shared = CheckInHistoryService()
    
    private let baseURL = "https://fsai.pickgoodspro.com"
    private let session = URLSession.shared
    internal var lastQueryStartDate: Date? = nil // 存储最后一次查询的开始日期
    
    private init() {}
    
    /// 获取指定时间范围内的打卡日志记录
    /// - Parameters:
    ///   - startDate: 开始日期
    ///   - endDate: 结束日期
    ///   - userData: 用户数据
    ///   - completion: 完成回调
    func getCheckInLogs(startDate: Date, endDate: Date, userData: UserData, completion: @escaping (Result<[UserPlansLogsResponse], Error>) -> Void) {
        guard !userData.accessToken.isEmpty else {
            completion(.failure(CheckInHistoryError.invalidToken))
            return
        }
        
        let dateFormatter = DateFormatter()
        dateFormatter.dateFormat = "yyyyMMdd"
        
        let startDateStr = dateFormatter.string(from: startDate)
        let endDateStr = dateFormatter.string(from: endDate)
        
        guard let url = URL(string: "\(baseURL)/ns/app/user-plans-logs/list") else {
            completion(.failure(CheckInHistoryError.invalidURL))
            return
        }
        
        var components = URLComponents(url: url, resolvingAgainstBaseURL: false)
        components?.queryItems = [
            URLQueryItem(name: "begDateStr", value: startDateStr),
            URLQueryItem(name: "endDateStr", value: endDateStr)
        ]
        
        guard let finalURL = components?.url else {
            completion(.failure(CheckInHistoryError.invalidURL))
            return
        }
        
        var request = URLRequest(url: finalURL)
        request.httpMethod = "GET"
        request.setValue("Bearer \(userData.accessToken)", forHTTPHeaderField: "Authorization")
        request.setValue("application/json", forHTTPHeaderField: "Accept")
        
        print("📅 [CheckInHistory] 请求打卡日志: \(startDateStr) 到 \(endDateStr)")
        
        session.dataTask(with: request) { data, response, error in
            if let error = error {
                print("❌ [CheckInHistory] 网络请求失败: \(error.localizedDescription)")
                completion(.failure(error))
                return
            }
            
            guard let httpResponse = response as? HTTPURLResponse else {
                completion(.failure(CheckInHistoryError.invalidResponse))
                return
            }
            
            guard let data = data else {
                completion(.failure(CheckInHistoryError.noData))
                return
            }
            
            // 记录响应状态码
            print("📅 [CheckInHistory] 响应状态码: \(httpResponse.statusCode)")
            
            if httpResponse.statusCode == 200 {
                do {
                    let response = try JSONDecoder().decode(ResponseBaseListUserPlansLogsResponse.self, from: data)
                    if response.code == 0 {
                        let logs = response.data ?? []
                        print("✅ [CheckInHistory] 成功获取打卡日志: \(logs.count)条记录")
                        completion(.success(logs))
                    } else {
                        print("❌ [CheckInHistory] 服务器返回错误: \(response.message ?? "未知错误")")
                        completion(.failure(CheckInHistoryError.serverError(response.message ?? "未知错误")))
                    }
                } catch {
                    print("❌ [CheckInHistory] 解析JSON失败: \(error.localizedDescription)")
                    if let jsonString = String(data: data, encoding: .utf8) {
                        print("📄 [CheckInHistory] 原始响应: \(jsonString)")
                    }
                    completion(.failure(error))
                }
            } else {
                print("❌ [CheckInHistory] HTTP错误: \(httpResponse.statusCode)")
                completion(.failure(CheckInHistoryError.httpError(httpResponse.statusCode)))
            }
        }.resume()
    }
    
    /// 获取最近51天的打卡记录（不包含今天）
    /// - Parameters:
    ///   - userData: 用户数据
    ///   - completion: 完成回调
    func getRecentCheckInLogs(userData: UserData, completion: @escaping (Result<[UserPlansLogsResponse], Error>) -> Void) {
        let calendar = Calendar.current
        let today = Date()
        
        // 计算52天前的日期，这样能获取到除了今天的51天
        guard let startDate = calendar.date(byAdding: .day, value: -52, to: today),
              let yesterday = calendar.date(byAdding: .day, value: -1, to: today) else {
            completion(.failure(CheckInHistoryError.invalidDate))
            return
        }
        
        // 获取从52天前到昨天的记录
        getCheckInLogs(startDate: startDate, endDate: yesterday, userData: userData, completion: completion)
    }
    
    /// 根据计划创建时间获取打卡记录（不包含今天）
    /// - Parameters:
    ///   - userData: 用户数据
    ///   - completion: 完成回调
    func getCheckInLogsBasedOnPlanCreation(userData: UserData, completion: @escaping (Result<[UserPlansLogsResponse], Error>) -> Void) {
        let calendar = Calendar.current
        let today = Date()
        
        guard let yesterday = calendar.date(byAdding: .day, value: -1, to: today) else {
            completion(.failure(CheckInHistoryError.invalidDate))
            return
        }
        
        let startDate: Date
        
        // 如果有计划创建时间，计算从今天到创建时间的天数
        if let planCreatedTime = userData.planCreatedTime {
            let daysSinceCreation = calendar.dateComponents([.day], from: calendar.startOfDay(for: planCreatedTime), to: calendar.startOfDay(for: today)).day ?? 0
            
            print("📅 [CheckInHistory] 计划创建于: \(planCreatedTime), 距今 \(daysSinceCreation) 天")
            
            if daysSinceCreation == 0 {
                // 如果今天创建计划，没有历史数据可查询，返回空数组
                print("📅 [CheckInHistory] 今天创建计划，没有历史数据可查询")
                // 设置查询开始日期为今天，这样calculateConsecutiveNonCheckInDays就不会计算任何天数
                self.lastQueryStartDate = today
                completion(.success([]))
                return
            } else if daysSinceCreation <= 51 {
                // 如果创建时间距今不超过51天，查询从创建时间到昨天的记录
                startDate = calendar.startOfDay(for: planCreatedTime)
                print("📅 [CheckInHistory] 使用计划创建时间作为查询起始时间: \(startDate)")
            } else {
                // 如果创建时间距今超过51天，查询最近51天（从52天前到昨天）
                guard let fiftyTwoDaysAgo = calendar.date(byAdding: .day, value: -52, to: today) else {
                    completion(.failure(CheckInHistoryError.invalidDate))
                    return
                }
                startDate = fiftyTwoDaysAgo
                print("📅 [CheckInHistory] 计划创建时间超过51天，使用最近51天查询: \(startDate)")
            }
        } else {
            // 如果没有计划创建时间，使用默认的51天查询
            guard let fiftyTwoDaysAgo = calendar.date(byAdding: .day, value: -52, to: today) else {
                completion(.failure(CheckInHistoryError.invalidDate))
                return
            }
            startDate = fiftyTwoDaysAgo
            print("📅 [CheckInHistory] 没有计划创建时间，使用默认51天查询: \(startDate)")
        }
        
        // 存储查询开始日期，供后续计算使用
        self.lastQueryStartDate = startDate
        
        // 获取从计算出的开始日期到昨天的记录
        getCheckInLogs(startDate: startDate, endDate: yesterday, userData: userData, completion: completion)
    }
    
    /// 计算连续打卡天数
    /// - Parameter logs: 打卡日志列表
    /// - Returns: 连续打卡天数
    func calculateStreakDays(from logs: [UserPlansLogsResponse]) -> Int {
        let calendar = Calendar.current
        let today = calendar.startOfDay(for: Date())
        
        // 从昨天开始计算，不包含今天
        guard let yesterday = calendar.date(byAdding: .day, value: -1, to: today) else {
            return 0
        }
        
        // 创建日期到打卡记录的映射
        var dateToLogsMap: [String: UserPlansLogsResponse] = [:]
        
        for log in logs {
            // 检查是否完全打卡（5个类型都有）
            if isCompleteCheckIn(log) {
                dateToLogsMap[log.dateStr] = log
            }
        }
        
        var streakDays = 0
        var checkDate = yesterday // 从昨天开始
        
        // 从昨天开始向前检查连续打卡，最多检查51天
        for _ in 0..<51 {
            let dateFormatter = DateFormatter()
            dateFormatter.dateFormat = "yyyyMMdd"
            let dateStr = dateFormatter.string(from: checkDate)
            
            if dateToLogsMap[dateStr] != nil {
                streakDays += 1
                // 检查前一天
                guard let previousDate = calendar.date(byAdding: .day, value: -1, to: checkDate) else { break }
                checkDate = previousDate
            } else {
                // 遇到未打卡的日期，中断连续计算
                break
            }
        }
        
        print("📊 [CheckInHistory] 计算连续打卡天数（不算今天）: \(streakDays)天")
        return streakDays
    }
    
    /// 计算上次打卡距今天数
    /// - Parameter logs: 打卡日志列表
    /// - Returns: 距今天数
    func calculateDaysSinceLastCheckIn(from logs: [UserPlansLogsResponse]) -> Int {
        let calendar = Calendar.current
        let today = calendar.startOfDay(for: Date())
        
        // 找到最近的完整打卡日期（不包含今天）
        var latestCheckInDate: Date?
        
        for log in logs {
            if isCompleteCheckIn(log) {
                let dateFormatter = DateFormatter()
                dateFormatter.dateFormat = "yyyyMMdd"
                if let logDate = dateFormatter.date(from: log.dateStr) {
                    // 只考虑今天之前的日期
                    if logDate < today && (latestCheckInDate == nil || logDate > latestCheckInDate!) {
                        latestCheckInDate = logDate
                    }
                }
            }
        }
        
        // 如果找到最近打卡日期，计算距今天数
        if let lastDate = latestCheckInDate {
            let daysDiff = calendar.dateComponents([.day], from: lastDate, to: today).day ?? 0
            let result = max(1, daysDiff) // 至少1天，因为不包含今天
            print("📊 [CheckInHistory] 距离上次打卡: \(result)天 (最后打卡日期: \(lastDate))")
            return result
        }
        
        // 如果没有找到任何打卡记录，返回很大的数值
        print("📊 [CheckInHistory] 没有找到任何历史打卡记录")
        return 999
    }
    
    /// 计算连续未打卡天数（从今天开始往前计算）
    /// - Parameters:
    ///   - logs: 打卡日志列表
    ///   - queryStartDate: 查询开始日期（可选，用于限制检查范围）
    /// - Returns: 连续未打卡天数
    func calculateConsecutiveNonCheckInDays(from logs: [UserPlansLogsResponse], queryStartDate: Date? = nil) -> Int {
        let calendar = Calendar.current
        let today = calendar.startOfDay(for: Date())
        
        // 特殊情况：如果queryStartDate是今天或之后，说明没有历史数据可以检查
        if let queryStart = queryStartDate, queryStart >= today {
            print("📊 [CheckInHistory] 查询开始日期是今天或之后，没有历史数据可检查，返回0")
            return 0
        }
        
        // 创建日期到打卡记录的映射
        var dateToLogsMap: [String: UserPlansLogsResponse] = [:]
        
        for log in logs {
            // 检查是否完全打卡（5个类型都有）
            if isCompleteCheckIn(log) {
                dateToLogsMap[log.dateStr] = log
            }
        }
        
        let dateFormatter = DateFormatter()
        dateFormatter.dateFormat = "yyyyMMdd"
        
        var consecutiveNonCheckInDays = 0
        
        // 从昨天开始向前检查连续未打卡天数（不包含今天）
        guard let yesterday = calendar.date(byAdding: .day, value: -1, to: today) else {
            return 0
        }
        
        var checkDate = yesterday
        
        // 确定检查的下界（最早检查到哪一天）
        let earliestCheckDate = queryStartDate ?? calendar.date(byAdding: .day, value: -51, to: today) ?? yesterday
        
        // 从昨天开始向前检查，直到查询开始日期或遇到打卡记录
        while checkDate >= calendar.startOfDay(for: earliestCheckDate) {
            let dateStr = dateFormatter.string(from: checkDate)
            
            if dateToLogsMap[dateStr] == nil {
                // 这一天没有打卡，增加计数
                consecutiveNonCheckInDays += 1
                // 检查前一天
                guard let previousDate = calendar.date(byAdding: .day, value: -1, to: checkDate) else { break }
                checkDate = previousDate
            } else {
                // 遇到打卡的日期，停止计算
                break
            }
        }
        
        print("📊 [CheckInHistory] 连续未打卡天数: \(consecutiveNonCheckInDays)天 (仅计算查询范围内的数据)")
        return consecutiveNonCheckInDays
    }
    
    /// 检查打卡记录是否完整（包含所有5个类型）
    /// - Parameter log: 打卡日志
    /// - Returns: 是否完整
    private func isCompleteCheckIn(_ log: UserPlansLogsResponse) -> Bool {
        let requiredTypes: Set<String> = ["MORNING_WEIGHT", "EVENING_WEIGHT", "LOG_BREAKFAST", "LOG_LUNCH", "LOG_DINNER"]
        let logTypes = Set(log.logsTypeList)
        return requiredTypes.isSubset(of: logTypes)
    }
}

// MARK: - 数据模型

/// 打卡日志列表响应
struct ResponseBaseListUserPlansLogsResponse: Codable {
    let code: Int
    let message: String?
    let data: [UserPlansLogsResponse]?
    let timestamp: Int64
}

/// 单条打卡日志响应
struct UserPlansLogsResponse: Codable {
    let createdTime: String?
    let dateStr: String
    let logsTypeList: [String]
}

// MARK: - 错误类型

enum CheckInHistoryError: Error, LocalizedError {
    case invalidToken
    case invalidURL
    case invalidResponse
    case noData
    case invalidDate
    case serverError(String)
    case httpError(Int)
    
    var errorDescription: String? {
        switch self {
        case .invalidToken:
            return "无效的访问令牌"
        case .invalidURL:
            return "无效的URL"
        case .invalidResponse:
            return "无效的响应"
        case .noData:
            return "没有数据"
        case .invalidDate:
            return "无效的日期"
        case .serverError(let message):
            return "服务器错误: \(message)"
        case .httpError(let code):
            return "HTTP错误: \(code)"
        }
    }
} 