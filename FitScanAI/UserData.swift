import SwiftUI
import UserNotifications

// 用户数据模型
class UserData: ObservableObject {
    // 添加用户ID
    @Published var id: String = UUID().uuidString // 用于用户标识
    
    // 基本用户信息
    @Published var name: String = ""  // 从后端获取的真实用户名
    @Published var nickname: String = ""  // 从后端获取的用户昵称
    @Published var email: String = ""
    @Published var profileImage: UIImage? = nil
    @Published var selectedTab: Int = 0
    
    // 访客模式状态
    @Published var isGuestMode: Bool = false
    
    // 🆕 用户显示名称（只使用真实的nickname，为空或虚假时返回空字符串，访客模式返回"Guest"）
    var displayName: String {
        if isGuestMode {
            return "Guest"
        } else if !nickname.isEmpty {
            return nickname
        } else {
            return ""  // 只显示nickname，不使用name作为备用
        }
    }
    @Published var accountCreationDate: Date = Calendar.current.date(byAdding: .month, value: -3, to: Date()) ?? Date() // 模拟账户创建日期为3个月前
    
    // 跟踪用户是否设置了目标体重（旧属性，保留用于兼容）
    @Published var hasSetWeightGoal: Bool = false
    
    // 表示用户是否有计划（从API获取）
    @Published var hasPlan: Bool = false
    
    // 订阅状态 - 现在从API获取
    @Published var isPremium: Bool = false
    @Published var subscriptionStartDate: Date?
    @Published var subscriptionEndDate: Date?
    @Published var subscriptionType: String = "" // "monthly", "annual"
    @Published var subscriptionStatus: String = "" // API返回的状态
    @Published var isFirstTimeSubscriber: Bool = true // 是否首次订阅
    
    // 认证Token
    @Published var accessToken: String = ""
    @Published var refreshToken: String = ""
    @Published var tokenExpiresAt: Date?
    
    // 导航状态
    @Published var showChangePasswordView: Bool = false
    
    // 单位设置
    @Published var weightUnit: String = "kg" // kg 或 lbs
    @Published var heightUnit: String = "cm" // cm 或 ft
    
    // 通知设置
    @Published var dailyReminderEnabled: Bool = true
    @Published var vibrationEnabled: Bool = true
    @Published var soundEnabled: Bool = true
    @Published var reminderTime: String = "07:30 AM / 10:30 PM"
    
    // 体重管理数据
    @Published var morningWeight: Double = 0 // 以kg为单位
    @Published var eveningWeight: Double = 0 // 以kg为单位
    @Published var goalWeight: Double = 0 // 以kg为单位
    @Published var initialWeight: Double = 0 // 开始记录时的体重，用于计算整体进度
    @Published var startWeight: Double = 0.0 // 用户创建计划时的体重，用于显示在plan页面
    @Published var currentManagementWeight: Double = 0.0 // 用户在goal weight management中设置的当前体重
    @Published var goalDate: Date = Calendar.current.date(byAdding: .day, value: 90, to: Date()) ?? Date() // 目标达成日期
    @Published var goalStartDate: Date? = nil // 设置目标的日期，用于计算当前周数
    @Published var goalTimelineWeeks: Int = 0
    @Published var planCreatedTime: Date? = nil // 计划创建时间，从API的createdTime字段获取
    @Published var gender: String = "" // "Male", "Female", "Other"
    @Published var age: Int = 0
    @Published var height: Double = 0 // 以cm为单位
    @Published var weightHistory: [WeightRecord] = []
    
    // 运动相关数据
    @Published var exerciseRecords: [ExerciseRecord] = []
    @Published var dailyCalorieBurnGoal: Int = 1200  // 每日燃烧卡路里目标
    
    // 食物和营养数据
    @Published var foodEntries: [FoodEntry] = []
    @Published var dailyCalorieGoal: Int = 2000
    @Published var dailyProteinGoal: Int = 80
    @Published var dailyFatGoal: Int = 67 // 约为2000卡路里的30%除以9
    
    @Published var aiRecommendations: [String] = [
        "Maintain a balanced diet with lean proteins",
        "Include more fiber-rich vegetables",
        "30 minutes moderate cardio exercise",
        "Stay hydrated with 8-10 glasses of water"
    ]
    
    // 每日打卡记录
    @Published var checkInRecords: [Date: Bool] = [:]
    
    // 当前食物分析结果 - 用于在视图之间传递数据
    var currentFoodAnalysis: FoodAnalysisResult? = nil
    
    // Token自动刷新计时器
    private var tokenRefreshTimer: Timer?
    
    // 添加重试计数器，避免频繁重试
    private var refreshRetryCount: Int = 0
    private let maxRefreshRetries: Int = 3
    
    // 启动Token自动刷新
    func startTokenAutoRefresh() {
        // 停止现有的计时器
        stopTokenAutoRefresh()
        
        print("🔄 启动Token自动刷新管理")
        
        // 检查是否有refreshToken
        guard !refreshToken.isEmpty else {
            print("⚠️ 没有refreshToken，无法启动自动刷新")
            return
        }
        
        // 计算距离Token过期的时间
        guard let expiresAt = tokenExpiresAt else {
            print("⚠️ 没有Token过期时间，无法启动自动刷新")
            return
        }
        
        let now = Date()
        let timeToExpiry = expiresAt.timeIntervalSince(now)
        
        // 如果Token已经过期，立即刷新
        if timeToExpiry <= 0 {
            print("🔄 Token已过期，立即刷新")
            refreshAccessTokenIfNeeded()
            return
        }
        
        // 在Token过期前1小时刷新（3600秒 = 1小时）
        let refreshInterval: TimeInterval = max(timeToExpiry - 3600, 60) // 至少等待60秒
        
        print("⏰ 将在 \(refreshInterval/3600) 小时后自动刷新Token")
        
        // 创建计时器
        tokenRefreshTimer = Timer.scheduledTimer(withTimeInterval: refreshInterval, repeats: false) { [weak self] _ in
            self?.refreshAccessTokenIfNeeded()
        }
    }
    
    // 停止Token自动刷新
    func stopTokenAutoRefresh() {
        tokenRefreshTimer?.invalidate()
        tokenRefreshTimer = nil
        print("⏹️ 已停止Token自动刷新")
    }
    
    // 检查并刷新AccessToken
    func refreshAccessTokenIfNeeded() {
        print("🔄 检查是否需要刷新AccessToken")
        
        // 检查是否有refreshToken
        guard !refreshToken.isEmpty else {
            print("⚠️ 没有refreshToken，检查是否可以使用苹果登录信息重新登录")
            // 如果是苹果登录用户，尝试从Keychain恢复登录
            if UserDefaults.standard.bool(forKey: "isAppleLogin") {
                attemptAppleRelogin()
            } else {
                // 没有refreshToken，用户需要重新登录
                DispatchQueue.main.async {
                    self.logout()
                }
            }
            return
        }
        
        // 检查Token是否即将过期（距离过期少于1小时）
        let shouldRefresh: Bool
        if let expiresAt = tokenExpiresAt {
            let timeUntilExpiry = expiresAt.timeIntervalSinceNow
            shouldRefresh = timeUntilExpiry < 3600 // 1小时 = 3600秒
            print("📅 Token将在 \(timeUntilExpiry/3600) 小时后过期，是否需要刷新：\(shouldRefresh)")
        } else {
            shouldRefresh = true // 没有过期时间，强制刷新
            print("⚠️ 没有Token过期时间，强制刷新")
        }
        
        if shouldRefresh {
            print("🔄 开始刷新AccessToken")
            NetworkService.shared.refreshAccessToken(refreshToken: self.refreshToken) { [weak self] result in
                DispatchQueue.main.async {
                    guard let self = self else { return }
                    
                    switch result {
                    case .success(let refreshResponse):
                        print("✅ AccessToken刷新成功")
                        
                        // 重置重试计数器
                        self.refreshRetryCount = 0
                        
                        // 更新Token信息
                        if let newAccessToken = refreshResponse.accessToken {
                            self.accessToken = newAccessToken
                            UserDefaults.standard.set(newAccessToken, forKey: "accessToken")
                            print("💾 已更新AccessToken")
                            
                            // 如果是苹果登录用户，也保存到Keychain
                            if UserDefaults.standard.bool(forKey: "isAppleLogin") {
                                do {
                                    try KeychainHelper.shared.saveString(newAccessToken, forKey: "appleLoginAccessToken")
                                    print("💾 已将新AccessToken保存到Keychain")
                                } catch {
                                    print("⚠️ 保存AccessToken到Keychain失败: \(error)")
                                }
                            }
                        }
                        
                        if let newRefreshToken = refreshResponse.refreshToken {
                            self.refreshToken = newRefreshToken
                            UserDefaults.standard.set(newRefreshToken, forKey: "refreshToken")
                            print("💾 已更新RefreshToken")
                            
                            // 如果是苹果登录用户，也保存到Keychain
                            if UserDefaults.standard.bool(forKey: "isAppleLogin") {
                                do {
                                    try KeychainHelper.shared.saveString(newRefreshToken, forKey: "appleLoginRefreshToken")
                                    print("💾 已将新RefreshToken保存到Keychain")
                                } catch {
                                    print("⚠️ 保存RefreshToken到Keychain失败: \(error)")
                                }
                            }
                        }
                        
                        // 更新过期时间
                        if let expiresIn = refreshResponse.expiresIn {
                            let newExpiresAt = Date().addingTimeInterval(TimeInterval(expiresIn))
                            self.tokenExpiresAt = newExpiresAt
                            UserDefaults.standard.set(newExpiresAt, forKey: "tokenExpiresAt")
                            print("📅 Token新的过期时间：\(newExpiresAt)")
                        }
                        
                        // 重新启动自动刷新计时器
                        self.startTokenAutoRefresh()
                        
                        // 发送Token刷新成功通知
                        NotificationCenter.default.post(name: Notification.Name("AccessTokenRefreshed"), object: nil)
                        
                    case .failure(let error):
                        print("❌ AccessToken刷新失败: \(error.localizedDescription)")
                        
                        // 如果刷新失败，可能是refreshToken也过期了
                        let nsError = error as NSError
                        if nsError.code == 401 || nsError.code == 403 {
                            print("🚪 RefreshToken可能已过期，需要重新登录")
                            
                            // 如果是苹果登录用户，检查重试次数
                            if UserDefaults.standard.bool(forKey: "isAppleLogin") {
                                self.refreshRetryCount += 1
                                print("🍎 检测到苹果登录用户，当前重试次数: \(self.refreshRetryCount)/\(self.maxRefreshRetries)")
                                
                                if self.refreshRetryCount >= self.maxRefreshRetries {
                                    print("🍎 已达到最大重试次数，执行登出操作")
                                    self.refreshRetryCount = 0 // 重置计数器
                                    self.attemptAppleRelogin()
                                } else {
                                    print("🍎 将在30秒后重试token刷新")
                                    DispatchQueue.main.asyncAfter(deadline: .now() + 30) {
                                        self.refreshAccessTokenIfNeeded()
                                    }
                                }
                            } else {
                                // 普通用户直接退出登录
                                self.logout()
                            }
                        } else {
                            // 网络错误等，稍后重试
                            print("🔄 网络错误，将在5分钟后重试")
                            DispatchQueue.main.asyncAfter(deadline: .now() + 300) { // 5分钟后重试
                                self.refreshAccessTokenIfNeeded()
                            }
                        }
                    }
                }
            }
        } else {
            print("✅ Token仍然有效，无需刷新")
            // 重新设置下次刷新计时器
            startTokenAutoRefresh()
        }
    }
    
    // 尝试使用Keychain中的苹果登录信息重新登录
    private func attemptAppleRelogin() {
        print("🍎 检测到苹果登录用户的refreshToken过期")
        print("⚠️ 苹果的identityToken和authorizationCode具有时效性且不能重复使用")
        print("🚪 需要用户重新进行苹果登录授权")
        
        // 清除过期的token但保留苹果授权信息标记
        // 这样用户在登录页面会看到苹果登录选项
        DispatchQueue.main.async {
            // 清除token但保留isAppleLogin标记，让用户知道之前是通过苹果登录的
            self.accessToken = ""
            self.refreshToken = ""
            self.tokenExpiresAt = nil
            
            // 清除UserDefaults中的token
            UserDefaults.standard.removeObject(forKey: "accessToken")
            UserDefaults.standard.removeObject(forKey: "refreshToken")
            UserDefaults.standard.removeObject(forKey: "tokenExpiresAt")
            UserDefaults.standard.set(false, forKey: "isLoggedIn")
            
            // 清除Keychain中的过期token
            do {
                try KeychainHelper.shared.deleteString(forKey: "appleLoginAccessToken")
                try KeychainHelper.shared.deleteString(forKey: "appleLoginRefreshToken")
                print("✅ 已清除Keychain中的过期token")
            } catch {
                print("⚠️ 清除Keychain中的token失败: \(error)")
            }
            
            // 保留苹果授权信息，但这些信息可能也已过期，主要用于标识用户之前使用过苹果登录
            // 实际重新登录时会获取新的授权信息
            
            // 发送登出通知，让用户回到登录页面重新进行苹果登录
            NotificationCenter.default.post(name: Notification.Name("UserLoggedOut"), object: nil)
            print("📣 已发送UserLoggedOut通知，用户需要重新进行苹果登录")
        }
    }
    
    // 设置默认的食物分析数据
    func setDefaultFoodAnalysis() -> FoodAnalysisResult {
        return FoodAnalysisResult(
            foodName: "Unknown food",
            nutritionScore: 70,
            calories: 300,
            protein: 20,
            carbs: 10,
            fat: 5,
            water: 60,
            fiber: 6,
            sugar: 5,
            caloriePercentage: 30,
            recommendations: [
                "Recommendations unavailable. Please check your connection and retry.",
                "Make sure the food fills the frame and is well-lit.",
                "Try shooting near a window or under brighter lights."
            ],
            conversationId: ""
        )
    }
    
    // 添加食物条目到日记
    func addFoodEntry(name: String, calories: Int, protein: Int, carbs: Int, fat: Int, date: Date, image: UIImage?) {
        var imageData: Data? = nil
        
        // 如果有图片，将其转换为Data并限制大小
        if let image = image {
            // 压缩图片以节省空间，限制最大尺寸为300x300像素
            let maxSize = CGSize(width: 300, height: 300)
            let resizedImage = resizeImage(image: image, targetSize: maxSize)
            
            // 使用较低的压缩质量来减少数据大小
            if let compressedImageData = resizedImage.jpegData(compressionQuality: 0.3) {
                // 如果压缩后的图片仍然超过100KB，则不保存图片数据
                if compressedImageData.count <= 100 * 1024 {
                imageData = compressedImageData
                    print("食物图片已压缩，大小: \(compressedImageData.count) bytes")
                } else {
                    print("食物图片过大(\(compressedImageData.count) bytes)，跳过保存")
                }
            }
        }
        
        let entry = FoodEntry(
            id: UUID(),
            name: name,
            calories: calories,
            protein: protein,
            carbs: carbs,
            fat: fat,
            time: date,
            image: imageData
        )
        
        foodEntries.append(entry)
        
        // 限制食物记录数量，只保留最近50条
        if foodEntries.count > 50 {
            foodEntries = Array(foodEntries.suffix(50))
            print("食物记录数量超限，已保留最近50条记录")
        }
        
        // 检查数据大小，如果过大则进一步减少
        if let encodedData = try? JSONEncoder().encode(foodEntries),
           encodedData.count > 500 * 1024 { // 如果超过500KB
            foodEntries = Array(foodEntries.suffix(20))
            print("食物记录数据过大，已减少到最近20条记录")
        }
        
        saveSettings() // 保存到用户设置
        
        // 发送通知，告知添加了新的食物记录
        NotificationCenter.default.post(name: Notification.Name("FoodEntryAdded"), object: nil)
        
        print("添加了新的食物记录: \(name), 卡路里: \(calories), 时间: \(date)")
    }
    
    // 辅助方法：调整图片大小
    private func resizeImage(image: UIImage, targetSize: CGSize) -> UIImage {
        let size = image.size
        
        let widthRatio  = targetSize.width  / size.width
        let heightRatio = targetSize.height / size.height
        
        // 选择较小的比例，确保图片完全适应目标尺寸
        let ratio = min(widthRatio, heightRatio)
        
        let newSize = CGSize(width: size.width * ratio, height: size.height * ratio)
        
        let rect = CGRect(origin: .zero, size: newSize)
        
        UIGraphicsBeginImageContextWithOptions(newSize, false, 1.0)
        image.draw(in: rect)
        let newImage = UIGraphicsGetImageFromCurrentImageContext()
        UIGraphicsEndImageContext()
        
        return newImage ?? image
    }
    
    // 获取今日食物条目
    func getTodayFoodEntries() -> [FoodEntry]? {
        let calendar = Calendar.current
        let today = calendar.startOfDay(for: Date())
        
        let todayEntries = foodEntries.filter { entry in
            calendar.isDate(entry.time, inSameDayAs: today)
        }.sorted { $0.time > $1.time }
        
        return todayEntries.isEmpty ? nil : todayEntries
    }
    
    // 获取今日总卡路里
    func getTodayTotalCalories() -> Int {
        guard let todayEntries = getTodayFoodEntries() else { return 0 }
        return todayEntries.reduce(0) { $0 + $1.calories }
    }
    
    // 获取今日总蛋白质
    func getTodayTotalProtein() -> Int {
        guard let todayEntries = getTodayFoodEntries() else { return 0 }
        return todayEntries.reduce(0) { $0 + $1.protein }
    }
    
    // 获取今日总碳水
    func getTodayTotalCarbs() -> Int {
        guard let todayEntries = getTodayFoodEntries() else { return 0 }
        return todayEntries.reduce(0) { $0 + $1.carbs }
    }
    
    // 获取今日总脂肪
    func getTodayTotalFat() -> Int {
        guard let todayEntries = getTodayFoodEntries() else { return 0 }
        return todayEntries.reduce(0) { $0 + $1.fat }
    }
    
    // 单位转换方法
    func formatWeight(_ weightInKg: Double) -> String {
        if weightUnit == "kg" {
            return String(format: "%.1f kg", weightInKg)
        } else {
            let weightInLbs = weightInKg * 2.205
            return String(format: "%.1f lbs", weightInLbs)
        }
    }
    
    func formatHeight(_ heightInCm: Double) -> String {
        if heightUnit == "cm" {
            return String(format: "%.1f cm", heightInCm)
        } else {
            let totalInches = heightInCm / 2.54
            let feet = Int(totalInches / 12)
            let inches = Int(totalInches.truncatingRemainder(dividingBy: 12))
            return "\(feet)'\(inches)\""
        }
    }
    
    // 获取体重历史记录，过滤最近几天的记录
    func getFilteredWeightHistory(days: Int = 30) -> [WeightRecord] {
        print("getFilteredWeightHistory: 请求过去\(days)天的历史记录")
        print("getFilteredWeightHistory: 当前有\(weightHistory.count)条历史记录")
        
        // 如果没有记录，直接返回空数组
        if weightHistory.isEmpty {
            print("getFilteredWeightHistory: 没有历史记录可过滤")
            return []
        }
        
        // 如果只有少量记录(少于10条)，直接返回全部记录而不过滤
        if weightHistory.count < 10 {
            print("getFilteredWeightHistory: 记录数量少于10条，返回全部\(weightHistory.count)条记录")
            return weightHistory
        }
        
        let calendar = Calendar.current
        let today = Date()
        
        guard let startDate = calendar.date(byAdding: .day, value: -days, to: today) else {
            print("getFilteredWeightHistory: 无法计算起始日期，返回全部记录")
            return weightHistory
        }
        
        let filteredRecords = weightHistory.filter { $0.date >= startDate }
        print("getFilteredWeightHistory: 过滤后返回\(filteredRecords.count)条记录")
        
        return filteredRecords
    }
    
    // 获取最近体重变化信息，用于展示
    func getRecentWeightChanges() -> [(day: String, change: String, isGain: Bool)] {
        var results: [(day: String, change: String, isGain: Bool)] = []
        
        // 获取排序后的历史记录
        let calendar = Calendar.current
        let today = calendar.startOfDay(for: Date())
        let history = weightHistory.filter { $0.date >= calendar.date(byAdding: .day, value: -4, to: today)! }
        
        // 按日期分组历史记录
        var groupedByDate: [Date: [WeightRecord]] = [:]
        
        for record in history {
            // 获取日期部分（忽略时间）
            let components = calendar.dateComponents([.year, .month, .day], from: record.date)
            if let dateKey = calendar.date(from: components) {
                if groupedByDate[dateKey] == nil {
                    groupedByDate[dateKey] = []
                }
                groupedByDate[dateKey]?.append(record)
            }
        }
        
        // 先检查今天的早晚体重情况
        let todayMorningWeight = morningWeight
        let todayEveningWeight = eveningWeight
        
        // 如果今天早晚体重都有值，计算当天的体重变化
        if todayMorningWeight > 0 && todayEveningWeight > 0 {
            let changeValue = todayEveningWeight - todayMorningWeight
            let isGain = changeValue > 0
            let absChange = abs(changeValue)
            
            results.append((
                day: "Today",
                change: (isGain ? "+" : "-") + String(format: "%.1f", getWeightValue(absChange)) + " \(weightUnit)",
                isGain: isGain
            ))
        } else if todayMorningWeight > 0 || todayEveningWeight > 0 {
            // 如果只有早晨或晚上其中一个有体重记录
            results.append((
                day: "Today",
                change: "0.0 \(weightUnit)",
                isGain: false
            ))
        }
        
        // 计算之前几天的变化
        let dateKeys = groupedByDate.keys.sorted(by: >)
        var processedDays = 0
        
        for dateKey in dateKeys {
            if processedDays >= 3 || (processedDays >= 2 && !results.isEmpty) {
                // 如果已经有Today的记录，则最多再处理2天，否则处理3天
                break
            }
            
            // 跳过今天的记录，因为已经单独处理了
            if calendar.isDateInToday(dateKey) {
                continue
            }
            
            if let records = groupedByDate[dateKey] {
                // 找出这一天最新的晨测和晚测记录
                let morningRecords = records.filter { $0.isMorning }.sorted { $0.date > $1.date }
                let eveningRecords = records.filter { !$0.isMorning }.sorted { $0.date > $1.date }
                
                // 仅当同一天同时有早晨和晚上记录时才计算变化
                if let morningWeight = morningRecords.first?.weight,
                   let eveningWeight = eveningRecords.first?.weight {
                    // 计算早晚体重差异
                    let changeValue = eveningWeight - morningWeight
                    let isGain = changeValue > 0
                    let absChange = abs(changeValue)
                    
                    // 计算与今天的天数差距
                    let daysDiff = calendar.dateComponents([.day], from: dateKey, to: today).day ?? 0
                    let dayString: String
                    switch daysDiff {
                    case 1: dayString = "Yesterday"
                    case 2: dayString = "2 days ago"
                    case 3: dayString = "3 days ago"
                    default: dayString = "\(daysDiff) days ago"
                    }
                    
                    // 添加到结果
                    results.append((
                        day: dayString,
                        change: (isGain ? "+" : "-") + String(format: "%.1f", getWeightValue(absChange)) + " \(weightUnit)",
                        isGain: isGain
                    ))
                    
                    processedDays += 1
                } else if (morningRecords.isEmpty && !eveningRecords.isEmpty) || (!morningRecords.isEmpty && eveningRecords.isEmpty) {
                    // 如果只有早上或只有晚上的记录，显示为无变化
                    let daysDiff = calendar.dateComponents([.day], from: dateKey, to: today).day ?? 0
                    let dayString: String
                    switch daysDiff {
                    case 1: dayString = "Yesterday"
                    case 2: dayString = "2 days ago"
                    case 3: dayString = "3 days ago"
                    default: dayString = "\(daysDiff) days ago"
                    }
                    
                    results.append((
                        day: dayString,
                        change: "0.0 \(weightUnit)",
                        isGain: false
                    ))
                    
                    processedDays += 1
                }
            }
        }
        
        // 如果记录不足4条，用零值补足
        while results.count < 4 {
            let index = results.count
            let dayString: String
            switch index {
                case 0: dayString = "Today"
                case 1: dayString = "Yesterday"
                case 2: dayString = "2 days ago"
                case 3: dayString = "3 days ago"
                default: dayString = "\(index) days ago"
            }
            
            results.append((
                day: dayString,
                change: "0.0 \(weightUnit)",
                isGain: false
            ))
        }
        
        return results
    }
    
    // 获取针对体重的AI建议
    func getWeightRecommendations() -> [String] {
        // 根据体重情况生成不同的建议
        let weightChange = calculateWeightChange(for: 7) // 一周的变化
        
        if abs(weightChange) < 0.3 {
            // 体重基本稳定
            return [
                "Your weight is stable, continue your current diet habits",
                "Try increasing weekly exercise frequency to improve metabolism",
                "Maintain adequate sleep, 7-8 hours each night",
                "Control dinner portions, avoid eating before bedtime"
            ]
        } else if weightChange > 0 {
            // 体重增加
            return [
                "Weight increased this week, reduce daily calorie intake by 300-500 calories",
                "Increase protein intake to help maintain satiety",
                "Perform aerobic exercise 3-4 times per week, 30-45 minutes each session",
                "Reduce refined carbohydrates and processed food intake"
            ]
        } else {
            // 体重减少
            return [
                "Weight decreased this week, continue if this matches your goal",
                "Ensure adequate protein intake to protect muscle tissue",
                "Consider increasing healthy fat intake, such as nuts and avocados",
                "Consider strength training to increase muscle mass and metabolism"
            ]
        }
    }
    
    func getWeightValue(_ weightInKg: Double) -> Double {
        if weightUnit == "kg" {
            return weightInKg
        } else {
            return weightInKg * 2.205
        }
    }
    
    // 从lbs转换为kg
    func convertToKg(_ weightInLbs: Double) -> Double {
        return weightInLbs / 2.205
    }
    
    // 从kg转换为lbs
    func convertToLbs(_ weightInKg: Double) -> Double {
        return weightInKg * 2.205
    }
    
    // 计算已经减少/增加的体重（负值表示减重，正值表示增重）
    func weightLostOrGained() -> Double {
        // 计算并返回初始体重与当前体重的差值
        // 负值表示减重，正值表示增重
        return startWeight - currentManagementWeight
    }
    
    // 计算还需要减少/增加的体重
    func weightToGoal() -> Double {
        let weightValue = getWeightValue(currentManagementWeight - goalWeight)
        return weightValue
    }
    
    // 体重变化计算
    func calculateWeightChange(for days: Int = 1) -> Double {
        guard weightHistory.count >= days + 1 else { return 0 }
        
        let sortedHistory = weightHistory.sorted(by: { $0.date > $1.date })
        let recentWeight = sortedHistory[0].weight
        let previousWeight = sortedHistory[days].weight
        
        return recentWeight - previousWeight
    }
    
    // 添加新的体重记录
    func addWeightRecord(weight: Double, isMorning: Bool) {
        let newRecord = WeightRecord(date: Date(), weight: weight, isMorning: isMorning)
        weightHistory.append(newRecord)
        
        if isMorning {
            morningWeight = weight
        } else {
            eveningWeight = weight
        }
        
        // 保存到UserDefaults
        saveSettings()
        
        // 生成新的AI建议
        generateAIRecommendations()
    }
    
    // 生成AI建议（模拟）
    func generateAIRecommendations() {
        // 在实际应用中，这里会调用AI API
        // 这里仅提供模拟数据
        let weightChange = calculateWeightChange()
        
        if abs(weightChange) < 0.2 {
            aiRecommendations = [
                "Maintain your current diet and exercise routine",
                "Focus on consistent meal timing",
                "Aim for 7-8 hours of quality sleep",
                "Consider adding light strength training"
            ]
        } else if weightChange > 0 {
            aiRecommendations = [
                "Reduce calorie intake by 200-300 calories per day",
                "Increase protein intake to preserve muscle mass",
                "Add 45 minutes of cardio 3-4 times per week",
                "Limit sodium and processed foods to reduce water retention"
            ]
        } else {
            aiRecommendations = [
                "Increase healthy calorie intake by 300-500 per day",
                "Focus on nutrient-dense foods",
                "Include more healthy fats in your diet",
                "Incorporate resistance training to build muscle"
            ]
        }
    }
    
    // 更新订阅状态 - 基于API响应
    func updateSubscriptionStatus(from subscriptionInfo: SubscriptionUserResponse?) {
        if let subscription = subscriptionInfo {
            // 🔧 修复：移除重复的isPremium设置，这个值已经在checkAndUpdateSubscriptionStatus中设置
            // isPremium = subscription.status == "ACTIVE"  // 已移除，避免重复设置
            subscriptionStatus = subscription.status
            
            // 解析日期 - 支持多种格式
            let dateFormatters = [
                ISO8601DateFormatter(),
                {
                    let formatter = DateFormatter()
                    formatter.dateFormat = "yyyy-MM-dd'T'HH:mm:ss.SSSZ"
                    formatter.locale = Locale(identifier: "en_US_POSIX")
                    return formatter
                }(),
                {
                    let formatter = DateFormatter()
                    formatter.dateFormat = "yyyy-MM-dd'T'HH:mm:ssZ"
                    formatter.locale = Locale(identifier: "en_US_POSIX")
                    return formatter
                }(),
                {
                    let formatter = DateFormatter()
                    formatter.dateFormat = "yyyy-MM-dd'T'HH:mm:ss"
                    formatter.locale = Locale(identifier: "en_US_POSIX")
                    return formatter
                }(),
                {
                    let formatter = DateFormatter()
                    formatter.dateFormat = "yyyy-MM-dd HH:mm:ss"
                    formatter.locale = Locale(identifier: "en_US_POSIX")
                    return formatter
                }()
            ]
            
            // 输出后端返回的原始日期字符串用于调试
            print("📅 UserData: 后端返回的原始日期字符串:")
            print("  - 开始日期: '\(subscription.startDate ?? "无")'")
            print("  - 结束日期: '\(subscription.endDate ?? "无")'")
            
            // 尝试解析开始日期
            subscriptionStartDate = nil
            if let startDateString = subscription.startDate {
                for (index, formatter) in dateFormatters.enumerated() {
                    if let startDate = (formatter as? ISO8601DateFormatter)?.date(from: startDateString) ?? 
                                      (formatter as? DateFormatter)?.date(from: startDateString) {
                        subscriptionStartDate = startDate
                        print("✅ UserData: 开始日期解析成功，使用格式\(index): \(startDate)")
                        break
                    }
                }
                
                if subscriptionStartDate == nil {
                    print("❌ UserData: 开始日期解析失败，原始字符串: '\(startDateString)'")
                }
            } else {
                print("ℹ️ UserData: 开始日期为空（可能是INIT状态）")
            }
            
            // 尝试解析结束日期
            subscriptionEndDate = nil
            if let endDateString = subscription.endDate {
                for (index, formatter) in dateFormatters.enumerated() {
                    if let endDate = (formatter as? ISO8601DateFormatter)?.date(from: endDateString) ?? 
                                    (formatter as? DateFormatter)?.date(from: endDateString) {
                        subscriptionEndDate = endDate
                        print("✅ UserData: 结束日期解析成功，使用格式\(index): \(endDate)")
                        break
                    }
                }
                
                if subscriptionEndDate == nil {
                    print("❌ UserData: 结束日期解析失败，原始字符串: '\(endDateString)'")
                }
            } else {
                print("ℹ️ UserData: 结束日期为空（可能是INIT状态）")
            }
            
            // 根据产品ID或channelRequestId判断订阅类型
            let productInfo = subscription.channelRequestId.lowercased()
            if productInfo.contains("monthly") {
                subscriptionType = "monthly"
            } else if productInfo.contains("annual") || productInfo.contains("year") {
                subscriptionType = "annual"
            }
            
            print("✅ UserData: 订阅详细信息已更新 - status: \(subscriptionStatus), type: \(subscriptionType)")
            print("✅ UserData: 订阅时间 - 开始: \(subscriptionStartDate?.description ?? "N/A"), 结束: \(subscriptionEndDate?.description ?? "N/A")")
        } else {
            // 🔧 修复：这里也不设置isPremium，保持调用方已设置的值
            // isPremium = false  // 已移除，避免重复设置
            subscriptionStatus = ""
            subscriptionStartDate = nil
            subscriptionEndDate = nil
            subscriptionType = ""
            print("✅ UserData: 订阅详细信息已清空 - 用户无有效订阅")
        }
        
        saveSettings()
    }
    
    // 检查并更新订阅状态
    func checkAndUpdateSubscriptionStatus() async {
        print("📱 UserData: 开始检查并更新订阅状态...")
        let result = await SubscriptionService.shared.checkUserSubscriptionStatus()
        
        await MainActor.run {
            // 🔧 修复：统一订阅状态设置逻辑，避免重复设置
            print("📱 UserData: API返回订阅信息 - status: \(result.status), isSubscribed: \(result.isSubscribed)")
            if let subscription = result.subscription {
                print("📱 UserData: 订阅详情 - status: \(subscription.status), startDate: \(subscription.startDate ?? "无"), endDate: \(subscription.endDate ?? "无")")
            }
            
            // 优先使用API返回的订阅状态直接判断
            if let subscription = result.subscription {
                self.isPremium = subscription.status == "ACTIVE"
                print("📱 UserData: 基于API状态设置 isPremium = \(self.isPremium) (status: \(subscription.status))")
            } else {
                self.isPremium = false
                print("📱 UserData: 无订阅数据，设置 isPremium = false")
            }
            
            // 移除本地存储依赖，完全基于API返回的订阅记录判断
            // 如果无订阅记录，则为首次订阅
            self.isFirstTimeSubscriber = (result.status == .firstTime)
            self.updateSubscriptionStatus(from: result.subscription)
            
            print("📱 UserData: 订阅状态最终更新完毕 - isPremium: \(self.isPremium), isFirstTimeSubscriber: \(self.isFirstTimeSubscriber)")
            
            // 🆕 发送订阅状态更新通知，确保所有UI组件能及时响应
            NotificationCenter.default.post(name: Notification.Name("SubscriptionStatusUpdated"), object: nil, userInfo: ["isPremium": self.isPremium])
        }
        
        // 检查试用期资格
        await checkTrialEligibility()
    }
    
    // 添加试用期资格状态
    @Published var hasTrialEligibility: Bool = true // 默认假设有资格
    
    // 获取订阅按钮文本 - 根据试用期动态显示（同步版本，用于已检查过资格的情况）
    func getSubscriptionButtonText() -> String {
        if isPremium {
            return "Manage Subscription"
        }

        // 根据已检查的试用期资格状态返回文本
        if hasTrialEligibility {
            return subscriptionButtonText
        } else {
            return "Subscribe Now"
        }
    }

    // 存储订阅按钮文本
    @Published var subscriptionButtonText: String = "Subscribe Now"

    // 异步获取订阅按钮文本 - 检查真实试用期资格
    @MainActor
    func updateSubscriptionButtonText() async {
        if isPremium {
            subscriptionButtonText = "Manage Subscription"
            return
        }

        // 检查是否有免费试用期
        if #available(iOS 15.0, *) {
            let storeKitManager = StoreKitManager.shared

            // 检查年度产品是否有免费试用期
            if let annualProduct = storeKitManager.products.first(where: { $0.id.contains("yearly") || $0.id.contains("annual") }) {
                let trialInfo = await storeKitManager.getTrialInfo(for: annualProduct)
                if trialInfo.hasFreeTrial {
                    hasTrialEligibility = true
                    subscriptionButtonText = "Start \(trialInfo.trialText)"
                    return
                }
            }

            // 检查月度产品是否有免费试用期
            if let monthlyProduct = storeKitManager.products.first(where: { $0.id.contains("monthly") }) {
                let trialInfo = await storeKitManager.getTrialInfo(for: monthlyProduct)
                if trialInfo.hasFreeTrial {
                    hasTrialEligibility = true
                    subscriptionButtonText = "Start \(trialInfo.trialText)"
                    return
                }
            }
        }

        // 没有试用期时的默认文本
        hasTrialEligibility = false
        subscriptionButtonText = "Subscribe Now"
    }
    
    // 检查试用期资格 - 异步方法，使用新的真实资格检查
    @MainActor
    func checkTrialEligibility() async {
        // 直接调用更新订阅按钮文本的方法，它会检查真实的试用期资格
        await updateSubscriptionButtonText()
        print("🔍 UserData: 试用期资格检查完成 - hasTrialEligibility: \(hasTrialEligibility), buttonText: \(subscriptionButtonText)")
    }
    
    // 获取订阅剩余天数 - 基于API数据
    func getRemainingSubscriptionDays() -> Int {
        guard isPremium, let endDate = subscriptionEndDate else { return 0 }
        
        let calendar = Calendar.current
        let today = calendar.startOfDay(for: Date())
        let end = calendar.startOfDay(for: endDate)
        
        let components = calendar.dateComponents([.day], from: today, to: end)
        return max(0, components.day ?? 0)
    }
    
    // 获取订阅进度 - 基于API数据
    func getSubscriptionProgress() -> Double {
        guard isPremium, 
              let startDate = subscriptionStartDate,
              let endDate = subscriptionEndDate else { return 0.0 }
        
        let calendar = Calendar.current
        let start = calendar.startOfDay(for: startDate)
        let end = calendar.startOfDay(for: endDate)
        let today = calendar.startOfDay(for: Date())
        
        let totalDays = calendar.dateComponents([.day], from: start, to: end).day ?? 1
        let remainingDays = calendar.dateComponents([.day], from: today, to: end).day ?? 0
        
        return min(1.0, Double(totalDays - remainingDays) / Double(totalDays))
    }
    
    // 格式化订阅结束日期 - 基于API数据
    func formatSubscriptionEndDate() -> String {
        guard let endDate = subscriptionEndDate else { return "N/A" }
        
        let formatter = DateFormatter()
        formatter.dateFormat = "MMM d, yyyy"
        return formatter.string(from: endDate)
    }
    
    // 获取总订阅天数 - 基于API数据
    func getTotalSubscriptionDays() -> Int {
        guard let startDate = subscriptionStartDate,
              let endDate = subscriptionEndDate else { return 0 }
        
        let calendar = Calendar.current
        let start = calendar.startOfDay(for: startDate)
        let end = calendar.startOfDay(for: endDate)
        
        let components = calendar.dateComponents([.day], from: start, to: end)
        return max(0, components.day ?? 0)
    }
    
    // 检查用户计划状态
    func checkPlanStatus() {
        print("📊 正在检查用户计划状态...")
        
        // 确保用户已登录
        guard !accessToken.isEmpty else {
            print("⚠️ 用户未登录，无法检查计划状态")
            hasPlan = false
            saveSettings()
            return
        }
        
        // 调用API检查用户是否有计划
        UserPlanService.shared.checkUserHasPlan(userData: self) { hasPlan in
            DispatchQueue.main.async {
                self.hasPlan = hasPlan
                self.saveSettings()
                
                // 发送通知，告知用户计划状态已更新
                NotificationCenter.default.post(name: Notification.Name("UserPlanStatusUpdated"), object: nil, userInfo: ["hasPlan": hasPlan])
                
                print("✅ 用户计划状态已更新: hasPlan=\(hasPlan)")
            }
        }
    }
    
    // 保存用户设置到UserDefaults
    func saveSettings() {
        let defaults = UserDefaults.standard
        
        // 获取当前用户ID作为数据存储的键前缀
        // 如果用户未登录，使用默认ID
        let userPrefix = !id.isEmpty ? "user_\(id)_" : "default_user_"
        
        // 保存基本设置
        defaults.set(weightUnit, forKey: userPrefix + "weightUnit")
        defaults.set(heightUnit, forKey: userPrefix + "heightUnit")
        defaults.set(dailyReminderEnabled, forKey: userPrefix + "dailyReminderEnabled")
        defaults.set(vibrationEnabled, forKey: userPrefix + "vibrationEnabled")
        defaults.set(soundEnabled, forKey: userPrefix + "soundEnabled")
        // reminderTime 现在是固定值，不需要保存到UserDefaults
        // 🔧 修复：游客模式下不保存selectedTab，或者总是保存为0
        if isGuestMode {
            defaults.set(0, forKey: userPrefix + "selectedTab")
            print("🚪 UserData: 游客模式，保存selectedTab为0")
        } else {
            defaults.set(selectedTab, forKey: userPrefix + "selectedTab")
        }
        
        // 保存id（这个应该是全局的，不需要前缀）
        defaults.set(id, forKey: "userId")
        
        // 保存体重数据
        defaults.set(morningWeight, forKey: userPrefix + "morningWeight")
        defaults.set(eveningWeight, forKey: userPrefix + "eveningWeight")
        // goalWeight 不再本地存储，只从API获取
        // defaults.set(goalWeight, forKey: userPrefix + "goalWeight")
        defaults.set(initialWeight, forKey: userPrefix + "initialWeight")
        defaults.set(startWeight, forKey: userPrefix + "startWeight")
        // currentManagementWeight 不再本地存储，只从API获取
        // defaults.set(currentManagementWeight, forKey: userPrefix + "currentManagementWeight")
        defaults.set(goalTimelineWeeks, forKey: userPrefix + "goalTimelineWeeks")
        defaults.set(hasSetWeightGoal, forKey: userPrefix + "hasSetWeightGoal")
        defaults.set(hasPlan, forKey: userPrefix + "hasPlan")
        
        if let goalStartDate = goalStartDate {
            defaults.set(goalStartDate, forKey: userPrefix + "goalStartDate")
        } else {
            defaults.removeObject(forKey: userPrefix + "goalStartDate")
        }
        
        if let planCreatedTime = planCreatedTime {
            defaults.set(planCreatedTime, forKey: userPrefix + "planCreatedTime")
        } else {
            defaults.removeObject(forKey: userPrefix + "planCreatedTime")
        }
        
        defaults.set(gender, forKey: userPrefix + "gender")
        defaults.set(age, forKey: userPrefix + "age")
        defaults.set(height, forKey: userPrefix + "height")
        
        // 限制食物记录数量，只保存最近50条记录，避免数据过大
        let limitedFoodEntries = Array(foodEntries.suffix(50))
        if !UserDefaultsManager.shared.safeSet(limitedFoodEntries, forKey: userPrefix + "foodEntries") {
            // 如果保存失败，进一步减少数据
            let furtherLimitedEntries = Array(limitedFoodEntries.suffix(20))
            _ = UserDefaultsManager.shared.safeSet(furtherLimitedEntries, forKey: userPrefix + "foodEntries")
            print("食物记录数据过大，已减少到\(furtherLimitedEntries.count)条记录")
        }
        
        // 限制体重历史记录数量，只保存最近100条记录
        let limitedWeightHistory = Array(weightHistory.suffix(100))
        if !UserDefaultsManager.shared.safeSet(limitedWeightHistory, forKey: userPrefix + "weightHistory") {
            // 如果保存失败，进一步减少数据
            let furtherLimitedHistory = Array(limitedWeightHistory.suffix(50))
            _ = UserDefaultsManager.shared.safeSet(furtherLimitedHistory, forKey: userPrefix + "weightHistory")
            print("体重历史数据过大，已减少到\(furtherLimitedHistory.count)条记录")
        }
        
        // 限制运动记录数量，只保存最近50条记录
        let limitedExerciseRecords = Array(exerciseRecords.suffix(50))
        if !UserDefaultsManager.shared.safeSet(limitedExerciseRecords, forKey: userPrefix + "exerciseRecords") {
            // 如果保存失败，进一步减少数据
            let furtherLimitedExercise = Array(limitedExerciseRecords.suffix(25))
            _ = UserDefaultsManager.shared.safeSet(furtherLimitedExercise, forKey: userPrefix + "exerciseRecords")
            print("运动记录数据过大，已减少到\(furtherLimitedExercise.count)条记录")
        }
        
        // 限制打卡记录，只保存最近90天的记录
        let ninetyDaysAgo = Calendar.current.date(byAdding: .day, value: -90, to: Date()) ?? Date()
        let recentCheckInRecords = checkInRecords.filter { $0.key >= ninetyDaysAgo }
        
        // 将Date:Bool字典转换为可序列化的数据
        var recordsDict: [String: Bool] = [:]
        for (date, isCompleted) in recentCheckInRecords {
            let timestamp = date.timeIntervalSince1970
            recordsDict["\(timestamp)"] = isCompleted
        }
        
        if !UserDefaultsManager.shared.safeSet(recordsDict, forKey: userPrefix + "checkInRecords") {
            // 如果保存失败，进一步减少数据（只保存最近30天）
            let thirtyDaysAgo = Calendar.current.date(byAdding: .day, value: -30, to: Date()) ?? Date()
            let furtherLimitedRecords = checkInRecords.filter { $0.key >= thirtyDaysAgo }
            
            var limitedDict: [String: Bool] = [:]
            for (date, isCompleted) in furtherLimitedRecords {
                let timestamp = date.timeIntervalSince1970
                limitedDict["\(timestamp)"] = isCompleted
            }
            
            _ = UserDefaultsManager.shared.safeSet(limitedDict, forKey: userPrefix + "checkInRecords")
            print("打卡记录数据过大，已减少到最近30天")
        }
        
        // 编码并保存每日卡路里和蛋白质目标
        defaults.set(dailyCalorieGoal, forKey: userPrefix + "dailyCalorieGoal")
        defaults.set(dailyProteinGoal, forKey: userPrefix + "dailyProteinGoal")
        defaults.set(dailyCalorieBurnGoal, forKey: userPrefix + "dailyCalorieBurnGoal")
        
        // 编码并保存每日脂肪目标
        defaults.set(dailyFatGoal, forKey: userPrefix + "dailyFatGoal")
        
        // 记录日志
        print("已为用户\(userPrefix)保存设置（已限制数据大小）")
        
        // 保存基本用户信息到UserDefaults
        UserDefaults.standard.set(self.nickname, forKey: "userNickname")
        UserDefaults.standard.set(self.email, forKey: "userEmail")
        
        // 🆕 保存用户头像到本地缓存
        saveProfileImageToCache()
    }
    
    // 从UserDefaults加载用户设置
    func loadSettings() {
        let defaults = UserDefaults.standard
        
        // 先加载用户ID
        if let userId = defaults.string(forKey: "userId"), !userId.isEmpty {
            id = userId
            print("已加载用户ID: \(userId)")
        }
        
        // 获取当前用户的键前缀
        let userPrefix = !id.isEmpty ? "user_\(id)_" : "default_user_"
        print("正在使用前缀\(userPrefix)加载用户数据")
        
        // 🔧 修复：移除强制重置订阅状态的逻辑，让订阅状态保持当前值
        // 订阅状态的重置应该只在真正需要时（如登出、切换账号）进行，
        // 而不是在每次loadSettings时都重置，这会导致UI显示错误的初始状态
        // 
        // 注释掉的代码：
        // isPremium = false
        // isFirstTimeSubscriber = true
        // subscriptionStartDate = nil
        // subscriptionEndDate = nil
        // subscriptionType = ""
        // subscriptionStatus = ""
        
        print("🔄 UserData: loadSettings - 保持当前订阅状态 isPremium=\(isPremium)")
        
        // 加载基本设置
        weightUnit = defaults.string(forKey: userPrefix + "weightUnit") ?? "kg"
        heightUnit = defaults.string(forKey: userPrefix + "heightUnit") ?? "cm"
        dailyReminderEnabled = defaults.bool(forKey: userPrefix + "dailyReminderEnabled")
        vibrationEnabled = defaults.bool(forKey: userPrefix + "vibrationEnabled")
        soundEnabled = defaults.bool(forKey: userPrefix + "soundEnabled")
        reminderTime = "07:30 AM / 10:30 PM" // 固定通知时间，不从UserDefaults加载
        selectedTab = defaults.integer(forKey: userPrefix + "selectedTab")
        
        // 加载体重数据 - 不再设置默认值，让用户自行填写
        morningWeight = defaults.double(forKey: userPrefix + "morningWeight")
        eveningWeight = defaults.double(forKey: userPrefix + "eveningWeight")
        // goalWeight 不再从本地存储读取，只从API获取，切换账号时重置为0
        goalWeight = 0
        initialWeight = defaults.double(forKey: userPrefix + "initialWeight")
        
        // 加载startWeight，如果不存在则使用initialWeight
        startWeight = defaults.double(forKey: userPrefix + "startWeight")
        if startWeight == 0 && initialWeight > 0 { startWeight = initialWeight } // 仅当initialWeight存在时使用
        
        // currentManagementWeight 不再从本地存储读取，只从API获取，切换账号时重置为0
        currentManagementWeight = 0
        // if currentManagementWeight == 0 && startWeight > 0 { currentManagementWeight = startWeight }
        
        goalTimelineWeeks = defaults.integer(forKey: userPrefix + "goalTimelineWeeks")
        
        // 从UserDefaults直接加载hasSetWeightGoal和hasPlan状态
        hasSetWeightGoal = defaults.bool(forKey: userPrefix + "hasSetWeightGoal")
        hasPlan = defaults.bool(forKey: userPrefix + "hasPlan")
        
        // 兼容处理：如果hasSetWeightGoal为true但hasPlan未设置，同步hasPlan状态
        if hasSetWeightGoal && (defaults.object(forKey: userPrefix + "hasPlan").map({ $0 as? Bool }).flatMap({ $0 }) == nil) {
            hasPlan = hasSetWeightGoal
            defaults.set(hasPlan, forKey: userPrefix + "hasPlan")
        }
        
        // 加载每日卡路里和蛋白质目标
        dailyCalorieBurnGoal = defaults.integer(forKey: userPrefix + "dailyCalorieBurnGoal")
        if dailyCalorieBurnGoal == 0 {
            // 设置默认值，避免除以零错误
            dailyCalorieBurnGoal = 1200
            defaults.set(dailyCalorieBurnGoal, forKey: userPrefix + "dailyCalorieBurnGoal")
        }
        
        dailyCalorieGoal = defaults.integer(forKey: userPrefix + "dailyCalorieGoal")
        if dailyCalorieGoal == 0 {
            dailyCalorieGoal = 2000
            defaults.set(dailyCalorieGoal, forKey: userPrefix + "dailyCalorieGoal")
        }
        
        dailyProteinGoal = defaults.integer(forKey: userPrefix + "dailyProteinGoal")
        if dailyProteinGoal == 0 {
            dailyProteinGoal = 80
            defaults.set(dailyProteinGoal, forKey: userPrefix + "dailyProteinGoal")
        }
        
        // 加载每日脂肪目标
        dailyFatGoal = defaults.integer(forKey: userPrefix + "dailyFatGoal")
        if dailyFatGoal == 0 {
            dailyFatGoal = 67 // 约为2000卡路里的30%除以9
            defaults.set(dailyFatGoal, forKey: userPrefix + "dailyFatGoal")
        }
        
        // 加载目标开始日期 - 使用安全的类型转换
        if let savedDate = defaults.object(forKey: userPrefix + "goalStartDate") as? Date {
            goalStartDate = savedDate
        } else {
            goalStartDate = nil
        }
        
        if let savedPlanCreatedTime = defaults.object(forKey: userPrefix + "planCreatedTime") as? Date {
            planCreatedTime = savedPlanCreatedTime
        } else {
            planCreatedTime = nil
        }
        
        gender = defaults.string(forKey: userPrefix + "gender") ?? ""
        age = defaults.integer(forKey: userPrefix + "age")
        height = defaults.double(forKey: userPrefix + "height")
        
        // 加载食物记录
        if let decodedFoodEntries = UserDefaultsManager.shared.safeGet([FoodEntry].self, forKey: userPrefix + "foodEntries") {
            foodEntries = decodedFoodEntries
            print("已加载\(foodEntries.count)条食物记录")
        } else {
            // 创建一些示例食物记录
            createSampleFoodEntries()
        }
        
        // 加载体重历史记录
        if let decodedWeightHistory = UserDefaultsManager.shared.safeGet([WeightRecord].self, forKey: userPrefix + "weightHistory") {
            weightHistory = decodedWeightHistory
            print("已加载\(weightHistory.count)条体重历史记录")
        }
        
        // 加载运动记录
        if let decodedExerciseRecords = UserDefaultsManager.shared.safeGet([ExerciseRecord].self, forKey: userPrefix + "exerciseRecords") {
            exerciseRecords = decodedExerciseRecords
            print("已加载\(exerciseRecords.count)条运动记录")
        }
        
        // 加载打卡记录
        loadCheckInRecords()

        // 监听StoreKit产品加载完成通知
        NotificationCenter.default.addObserver(
            forName: Notification.Name("StoreKitProductsLoaded"),
            object: nil,
            queue: .main
        ) { _ in
            Task {
                await self.updateSubscriptionButtonText()
            }
        }

        // 如果用户已登录，检查计划状态和订阅状态
        if !accessToken.isEmpty {
            checkPlanStatus()
            // 在加载设置后，如果用户已登录，则检查并更新订阅状态
            print("🔄 UserData: 用户已登录，将异步检查订阅状态")
            Task {
                await checkAndUpdateSubscriptionStatus()
            }
        } else {
            // 如果用户未登录，确保订阅状态被重置为false
            print("🔄 UserData: 用户未登录，重置订阅状态为false")
            isPremium = false
            isFirstTimeSubscriber = true
            subscriptionStartDate = nil
            subscriptionEndDate = nil
            subscriptionType = ""
            subscriptionStatus = ""
        }
        
        // 加载访客模式状态
        isGuestMode = UserDefaults.standard.bool(forKey: "isGuestMode")
        print("🚪 UserData: 访客模式状态: \(isGuestMode)")
        
        // 🔧 修复：游客模式下总是从nutrition页面开始
        if isGuestMode {
            selectedTab = 0
            print("🚪 UserData: 游客模式，重置selectedTab为0（nutrition页面）")
        }
    }
    
    // 保存打卡记录到UserDefaults
    private func saveCheckInRecords() {
        let defaults = UserDefaults.standard
        
        // 获取当前用户的键前缀
        let userPrefix = !id.isEmpty ? "user_\(id)_" : "default_user_"
        
        // 将Date:Bool字典转换为可序列化的数据
        var recordsDict: [String: Bool] = [:]
        
        // 使用日期的时间戳作为键
        for (date, isCompleted) in checkInRecords {
            let timestamp = date.timeIntervalSince1970
            recordsDict["\(timestamp)"] = isCompleted
        }
        
        // 将字典转换为JSON数据
        if let encodedData = try? JSONEncoder().encode(recordsDict) {
            defaults.set(encodedData, forKey: userPrefix + "checkInRecords")
            print("已保存\(recordsDict.count)条打卡记录")
        }
    }
    
    // 从UserDefaults加载打卡记录
    private func loadCheckInRecords() {
        let defaults = UserDefaults.standard
        
        // 获取当前用户的键前缀
        let userPrefix = !id.isEmpty ? "user_\(id)_" : "default_user_"
        
        if let savedData = defaults.data(forKey: userPrefix + "checkInRecords"),
           let decodedDict = try? JSONDecoder().decode([String: Bool].self, from: savedData) {
            checkInRecords = [:]
            
            // 将时间戳转换回Date对象
            for (timestampStr, isCompleted) in decodedDict {
                if let timestamp = Double(timestampStr),
                   let date = Calendar.current.startOfDay(for: Date(timeIntervalSince1970: timestamp)) as Date? {
                    checkInRecords[date] = isCompleted
                }
            }
            
            print("已加载\(checkInRecords.count)条打卡记录")
        }
    }
    
    // 重置每日早晚体重为0，但不影响打卡记录
    func resetDailyWeight() {
        // 保存当前的早晚体重到历史记录
        if morningWeight > 0 {
            let morningRecord = WeightRecord(date: Date(), weight: morningWeight, isMorning: true)
            weightHistory.append(morningRecord)
        }
        
        if eveningWeight > 0 {
            let eveningRecord = WeightRecord(date: Date(), weight: eveningWeight, isMorning: false)
            weightHistory.append(eveningRecord)
        }
        
        // 重置早晚体重为0
        morningWeight = 0
        eveningWeight = 0
        
        // 保存设置但不影响打卡记录
        saveSettings()
    }
    
    // 检查指定日期是否已打卡
    func isDateCheckedIn(_ date: Date) -> Bool {
        let calendar = Calendar.current
        let startOfDay = calendar.startOfDay(for: date)
        return checkInRecords[startOfDay] == true
    }
    
    // 更新指定日期的打卡状态
    func updateCheckInStatus(for date: Date, isCompleted: Bool) {
        let calendar = Calendar.current
        let startOfDay = calendar.startOfDay(for: date)
        checkInRecords[startOfDay] = isCompleted
        saveSettings()
    }
    
    // 根据任务完成情况更新今日打卡状态
    func updateTodayCheckInStatus(tasksCompleted: Int, totalTasks: Int) {
        // 如果完成了至少一半的任务，则算作今日已打卡
        let isCompleted = totalTasks > 0 && (Double(tasksCompleted) / Double(totalTasks) >= 0.5)
        updateCheckInStatus(for: Date(), isCompleted: isCompleted)
    }
    
    // 创建默认的体重历史记录
    private func createDefaultWeightHistory() {
        // 不再创建默认体重历史记录
        // 用户需要自己添加体重记录
    }
    
    // 创建示例食物记录
    private func createSampleFoodEntries() {
        // 不再创建默认食物记录
        // 用户需要自己添加食物记录
    }
    
    // 设置通知
    func scheduleNotifications() {
        guard dailyReminderEnabled else {
            // 如果通知被禁用，则取消所有通知
            UNUserNotificationCenter.current().removeAllPendingNotificationRequests()
            return
        }
        
        // 请求通知权限
        UNUserNotificationCenter.current().requestAuthorization(options: [.alert, .sound, .badge]) { granted, error in
            if granted {
                // 权限被授予，可以继续设置通知
                self.setupNotificationTriggers()
            } else {
                // 处理权限拒绝的情况
                print("通知权限被拒绝")
            }
        }
    }
    
    private func setupNotificationTriggers() {
        // 解析时间字符串
        let times = reminderTime.split(separator: "/")
        if times.count >= 2 {
            let morningTimeStr = String(times[0]).trimmingCharacters(in: .whitespaces)
            let eveningTimeStr = String(times[1]).trimmingCharacters(in: .whitespaces)
            
            // 设置早晨和晚上的通知
            scheduleReminderAt(timeString: morningTimeStr, identifier: "morningReminder")
            scheduleReminderAt(timeString: eveningTimeStr, identifier: "eveningReminder")
        }
    }
    
    private func scheduleReminderAt(timeString: String, identifier: String) {
        let formatter = DateFormatter()
        formatter.dateFormat = "hh:mm a"
        
        guard let date = formatter.date(from: timeString) else {
            print("无法解析时间: \(timeString)")
            return
        }
        
        let calendar = Calendar.current
        let components = calendar.dateComponents([.hour, .minute], from: date)
        
        // 创建通知内容
        let content = UNMutableNotificationContent()
        content.title = "体重记录提醒"
        content.body = "别忘了今天记录你的体重！"
        content.sound = soundEnabled ? .default : nil
        
        // 设置通知触发器
        var trigger: UNNotificationTrigger
        
        if let hour = components.hour, let minute = components.minute {
            var dateComponents = DateComponents()
            dateComponents.hour = hour
            dateComponents.minute = minute
            trigger = UNCalendarNotificationTrigger(dateMatching: dateComponents, repeats: true)
            
            // 创建通知请求
            let request = UNNotificationRequest(identifier: identifier, content: content, trigger: trigger)
            
            // 添加通知请求
            UNUserNotificationCenter.current().add(request) { error in
                if let error = error {
                    print("设置通知失败: \(error)")
                }
            }
        }
    }
    
    // 设置头像更新通知监听
    private func setupAvatarUpdateNotifications() {
        NotificationCenter.default.addObserver(
            forName: Notification.Name("UserAvatarUpdated"),
            object: nil,
            queue: .main
        ) { [weak self] notification in
            if let image = notification.userInfo?["avatarImage"] as? UIImage {
                self?.profileImage = image
                print("✅ 从通知中更新了用户头像")
                
                // 🆕 保存头像到本地缓存
                self?.saveProfileImageToCache()
            }
        }
    }
    
    // 🆕 保存用户头像到本地缓存
    private func saveProfileImageToCache() {
        guard let image = profileImage, !id.isEmpty else { return }
        
        DispatchQueue.global(qos: .background).async {
            let documentsDirectory = FileManager.default.urls(for: .documentDirectory, in: .userDomainMask).first!
            let profileImageDirectory = documentsDirectory.appendingPathComponent("ProfileImages")
            
            // 创建目录（如果不存在）
            if !FileManager.default.fileExists(atPath: profileImageDirectory.path) {
                try? FileManager.default.createDirectory(at: profileImageDirectory, withIntermediateDirectories: true, attributes: nil)
            }
            
            // 使用用户ID作为文件名
            let imageURL = profileImageDirectory.appendingPathComponent("\(self.id).jpg")
            
            // 压缩并保存图片
            if let imageData = image.jpegData(compressionQuality: 0.8) {
                try? imageData.write(to: imageURL)
                print("✅ 用户头像已保存到本地缓存: \(imageURL.path)")
            }
        }
    }
    
    // 🆕 从本地缓存加载用户头像
    private func loadProfileImageFromCache() {
        guard !id.isEmpty else { return }
        
        DispatchQueue.global(qos: .background).async {
            let documentsDirectory = FileManager.default.urls(for: .documentDirectory, in: .userDomainMask).first!
            let imageURL = documentsDirectory.appendingPathComponent("ProfileImages/\(self.id).jpg")
            
            if FileManager.default.fileExists(atPath: imageURL.path),
               let imageData = try? Data(contentsOf: imageURL),
               let image = UIImage(data: imageData) {
                
                DispatchQueue.main.async {
                    self.profileImage = image
                    print("✅ 从本地缓存加载用户头像成功")
                }
            } else {
                print("⚠️ 本地缓存中未找到用户头像")
            }
        }
    }
    
    // 🆕 清除用户头像缓存
    private func clearProfileImageCache() {
        guard !id.isEmpty else { return }
        
        DispatchQueue.global(qos: .background).async {
            let documentsDirectory = FileManager.default.urls(for: .documentDirectory, in: .userDomainMask).first!
            let imageURL = documentsDirectory.appendingPathComponent("ProfileImages/\(self.id).jpg")
            
            if FileManager.default.fileExists(atPath: imageURL.path) {
                try? FileManager.default.removeItem(at: imageURL)
                print("✅ 已清除用户头像本地缓存: \(self.id)")
            }
        }
    }
    
    // 🆕 清除所有用户头像缓存（用于完全清理）
    private func clearAllProfileImageCache() {
        DispatchQueue.global(qos: .background).async {
            let documentsDirectory = FileManager.default.urls(for: .documentDirectory, in: .userDomainMask).first!
            let profileImageDirectory = documentsDirectory.appendingPathComponent("ProfileImages")
            
            if FileManager.default.fileExists(atPath: profileImageDirectory.path) {
                do {
                    let fileURLs = try FileManager.default.contentsOfDirectory(at: profileImageDirectory, includingPropertiesForKeys: nil)
                    for fileURL in fileURLs {
                        try FileManager.default.removeItem(at: fileURL)
                    }
                    print("✅ 已清除所有用户头像本地缓存")
                } catch {
                    print("❌ 清除所有用户头像缓存失败: \(error.localizedDescription)")
                }
            }
        }
    }
    
    // 析构函数中移除通知监听
    deinit {
        NotificationCenter.default.removeObserver(self)
    }
    
    // 对运动记录进行去重，确保没有重复ID
    func deduplicateExerciseRecords() {
        let originalCount = exerciseRecords.count
        
        // 使用Set来跟踪已经看到的ID
        var seenIDs = Set<UUID>()
        exerciseRecords = exerciseRecords.filter { record in
            // 如果ID已经在集合中，则过滤掉这条记录
            if seenIDs.contains(record.id) {
                return false
            } else {
                // 否则，添加ID到集合并保留该记录
                seenIDs.insert(record.id)
                return true
            }
        }
        
        // 如果有记录被移除，保存更新后的数据
        if originalCount > exerciseRecords.count {
            print("已从运动记录中移除 \(originalCount - exerciseRecords.count) 条重复记录")
            saveSettings()
        }
    }
    
    // 保存用户登录状态和令牌
    func saveLoginState(userId: String?, userNickname: String?, email: String?, accessToken: String?, refreshToken: String?, expiresIn: Int?) {
        // 🔧 重要修复：先强制重置订阅状态，防止保留上一个账号的会员状态
        isPremium = false
        isFirstTimeSubscriber = true
        subscriptionStartDate = nil
        subscriptionEndDate = nil
        subscriptionType = ""
        subscriptionStatus = ""
        
        // 🔧 清除全局会员状态标志，确保新账号不继承旧账号状态
        UserDefaults.standard.removeObject(forKey: "isPremium")
        UserDefaults.standard.removeObject(forKey: "subscriptionStatus")
        print("🔄 UserData: saveLoginState时已重置订阅状态并清除全局会员标志")
        
        // 🆕 清除当前显示的头像，防止显示上一个账号的头像
        self.profileImage = nil
        
        if let userId = userId {
            self.id = userId
            print("保存用户ID: \(userId)")
        }
        
        if let userNickname = userNickname, !userNickname.isEmpty {
            // 过滤虚假的nickname
            let filteredNickname = filterNickname(userNickname)
            self.nickname = filteredNickname
            print("保存用户昵称: \(userNickname) -> 过滤后: \(filteredNickname)")
        }
        
        if let email = email, !email.isEmpty {
            self.email = email
            print("保存用户邮箱: \(email)")
        }
        
        if let accessToken = accessToken {
            self.accessToken = accessToken
            UserDefaults.standard.set(accessToken, forKey: "accessToken")
            print("保存accessToken成功")
        }
        
        if let refreshToken = refreshToken {
            self.refreshToken = refreshToken
            UserDefaults.standard.set(refreshToken, forKey: "refreshToken")
            print("保存refreshToken成功")
        }
        
        if let expiresIn = expiresIn {
            // 计算过期时间
            let expiresAt = Date().addingTimeInterval(TimeInterval(expiresIn))
            self.tokenExpiresAt = expiresAt
            UserDefaults.standard.set(expiresAt, forKey: "tokenExpiresAt")
            print("Token过期时间设置为: \(expiresAt)")
        }
        
        // 保存基本用户信息到UserDefaults
        UserDefaults.standard.set(self.nickname, forKey: "userNickname")
        UserDefaults.standard.set(self.email, forKey: "userEmail")
        
        // 🆕 加载本地缓存的用户头像（如果存在）
        loadProfileImageFromCache()
        
        saveSettings()
        
        // 启动Token自动刷新
        startTokenAutoRefresh()
        
        // 重置重试计数器
        refreshRetryCount = 0
        
        // 登录状态保存后，立即检查并更新该用户的真实订阅状态
        Task {
            await self.checkAndUpdateSubscriptionStatus()
        }
    }
    
    // 加载登录状态和令牌
    func loadLoginState() {
        // 首先从UserDefaults加载token
        if let accessToken = UserDefaults.standard.string(forKey: "accessToken") {
            self.accessToken = accessToken
            print("已加载accessToken")
        }
        
        if let refreshToken = UserDefaults.standard.string(forKey: "refreshToken") {
            self.refreshToken = refreshToken
            print("已加载refreshToken")
        }
        
        // 安全加载token过期时间
        if let expiresAtObj = UserDefaults.standard.object(forKey: "tokenExpiresAt"),
           let expiresAt = expiresAtObj as? Date {
            self.tokenExpiresAt = expiresAt
            print("已加载token过期时间: \(expiresAt)")
        } else {
            self.tokenExpiresAt = nil
            print("⚠️ 无法加载token过期时间或数据格式不正确")
        }
        
        // 如果是苹果登录用户但没有token，尝试从Keychain恢复
        if UserDefaults.standard.bool(forKey: "isAppleLogin") && (accessToken.isEmpty || refreshToken.isEmpty) {
            print("🍎 检测到苹果登录用户但缺少token，尝试从Keychain恢复")
            loadTokensFromKeychain()
        }
        
        // 如果是谷歌登录用户但没有token，尝试从Keychain恢复
        if UserDefaults.standard.bool(forKey: "isGoogleLogin") && (accessToken.isEmpty || refreshToken.isEmpty) {
            print("🌐 检测到谷歌登录用户但缺少token，尝试从Keychain恢复")
            loadGoogleTokensFromKeychain()
        }
        
        // 加载用户昵称和邮箱
        if let userNickname = UserDefaults.standard.string(forKey: "userNickname"), !userNickname.isEmpty {
            self.nickname = userNickname
            print("已加载用户昵称: \(userNickname)")
        }
        
        if let userEmail = UserDefaults.standard.string(forKey: "userEmail"), !userEmail.isEmpty {
            self.email = userEmail
            print("已加载用户邮箱: \(userEmail)")
        }
        
        // 🆕 加载本地缓存的用户头像
        if !id.isEmpty {
            loadProfileImageFromCache()
        }
        
        // 如果有有效的Token，启动自动刷新
        if !accessToken.isEmpty && !refreshToken.isEmpty {
            startTokenAutoRefresh()
        }
    }
    
    // 从Keychain加载苹果登录的token
    private func loadTokensFromKeychain() {
        do {
            if let keychainAccessToken = try KeychainHelper.shared.loadString(forKey: "appleLoginAccessToken"),
               !keychainAccessToken.isEmpty {
                self.accessToken = keychainAccessToken
                UserDefaults.standard.set(keychainAccessToken, forKey: "accessToken")
                print("✅ 从Keychain恢复accessToken")
            }
            
            if let keychainRefreshToken = try KeychainHelper.shared.loadString(forKey: "appleLoginRefreshToken"),
               !keychainRefreshToken.isEmpty {
                self.refreshToken = keychainRefreshToken
                UserDefaults.standard.set(keychainRefreshToken, forKey: "refreshToken")
                print("✅ 从Keychain恢复refreshToken")
            }
        } catch {
            print("⚠️ 从Keychain加载token失败: \(error)")
        }
    }
    
    // 从Keychain加载谷歌登录的token
    private func loadGoogleTokensFromKeychain() {
        do {
            if let keychainAccessToken = try KeychainHelper.shared.loadString(forKey: "googleLoginAccessToken"),
               !keychainAccessToken.isEmpty {
                self.accessToken = keychainAccessToken
                UserDefaults.standard.set(keychainAccessToken, forKey: "accessToken")
                print("✅ 从Keychain恢复谷歌登录accessToken")
            }
            
            if let keychainRefreshToken = try KeychainHelper.shared.loadString(forKey: "googleLoginRefreshToken"),
               !keychainRefreshToken.isEmpty {
                self.refreshToken = keychainRefreshToken
                UserDefaults.standard.set(keychainRefreshToken, forKey: "refreshToken")
                print("✅ 从Keychain恢复谷歌登录refreshToken")
            }
        } catch {
            print("⚠️ 从Keychain加载谷歌登录token失败: \(error)")
        }
    }
    
    // 检查令牌是否已过期
    func isTokenExpired() -> Bool {
        guard let expiresAt = tokenExpiresAt else {
            return true // 如果没有过期时间，则认为已过期
        }
        
        // 如果当前时间已经超过过期时间，则令牌已过期
        return Date() >= expiresAt
    }
    
    // 注销登录
    func logout(isAccountDeletion: Bool = false) {
        // 先调用退出登录API
        UserService.shared.logoutWithAPI { [weak self] result in
            guard let self = self else { return }
            
            switch result {
            case .success(_):
                print("成功调用退出登录API，服务器会话已清除")
            case .failure(let error):
                print("调用退出登录API失败，将继续清除本地登录状态: \(error.localizedDescription)")
            }
            
            // 无论API调用成功与否，都清除本地数据
            DispatchQueue.main.async {
        // 清除令牌和用户信息
                self.accessToken = ""
                self.refreshToken = ""
                self.tokenExpiresAt = nil
                
                // 停止Token自动刷新
                self.stopTokenAutoRefresh()
                
                // 清除UserDefaults中的token
                UserDefaults.standard.removeObject(forKey: "accessToken")
                UserDefaults.standard.removeObject(forKey: "refreshToken")
                UserDefaults.standard.removeObject(forKey: "tokenExpiresAt")
                
                // 如果是苹果登录用户，清除Keychain中的token但保留授权信息
                if UserDefaults.standard.bool(forKey: "isAppleLogin") {
                    do {
                        try KeychainHelper.shared.deleteString(forKey: "appleLoginAccessToken")
                        try KeychainHelper.shared.deleteString(forKey: "appleLoginRefreshToken")
                        print("✅ 已清除Keychain中的苹果登录token")
                        // 注意：不删除appleAuthUserId、appleAuthIdentityToken、appleAuthAuthorizationCode
                        // 这些信息保留用于将来的自动重新登录
                    } catch {
                        print("⚠️ 清除Keychain中的苹果登录token失败: \(error)")
                    }
                }
                
                // 如果是谷歌登录用户，清除Keychain中的token但保留授权信息
                if UserDefaults.standard.bool(forKey: "isGoogleLogin") {
                    do {
                        try KeychainHelper.shared.deleteString(forKey: "googleLoginAccessToken")
                        try KeychainHelper.shared.deleteString(forKey: "googleLoginRefreshToken")
                        print("✅ 已清除Keychain中的谷歌登录token")
                        // 注意：不删除googleAuthUserId、googleAuthIdToken、googleAuthEmail
                        // 这些信息保留用于将来的自动重新登录
                    } catch {
                        print("⚠️ 清除Keychain中的谷歌登录token失败: \(error)")
                    }
                }
                
                // 清除用户特定信息，但保留App设置如单位等，除非也希望重置它们
                // 🆕 在清除用户信息前先清除头像缓存
                if isAccountDeletion {
                    // 账号删除时清除所有头像缓存
                    self.clearAllProfileImageCache()
                } else {
                    // 普通退出登录时只清除当前用户的头像缓存
                self.clearProfileImageCache()
                }
                
                self.name = ""
                self.nickname = ""
                self.email = ""
                self.profileImage = nil
                self.id = "" // 清空用户ID，以便下次加载时使用 "default_user_" 前缀或重新生成
                
                // 清除访客模式状态
                self.isGuestMode = false
                
                // 重置目标体重，确保切换账号后不显示上个账号的目标体重
                self.goalWeight = 0
                
                // 重置当前管理体重，确保切换账号后不显示上个账号的体重
                self.currentManagementWeight = 0
                
                // 重置计划状态
                self.hasPlan = false
                
                // 重置订阅状态，确保切换账号后不显示上个账号的订阅信息
                self.isPremium = false
                self.isFirstTimeSubscriber = true
                self.subscriptionStartDate = nil
                self.subscriptionEndDate = nil
                self.subscriptionType = ""
                self.subscriptionStatus = ""
        
        // 清除UserDefaults中的登录信息
                UserDefaults.standard.removeObject(forKey: "accessToken")
                UserDefaults.standard.removeObject(forKey: "refreshToken")
                UserDefaults.standard.removeObject(forKey: "tokenExpiresAt")
                UserDefaults.standard.removeObject(forKey: "userEmail")
                UserDefaults.standard.removeObject(forKey: "userNickname")
                UserDefaults.standard.removeObject(forKey: "userId") // 清除保存的userId
                UserDefaults.standard.removeObject(forKey: "userName") // 清除保存的userName
                UserDefaults.standard.removeObject(forKey: "isGuestMode") // 清除访客模式状态
                
                // 🔧 重要修复：清除全局会员状态标志，防止新账号继承旧账号的会员状态
                let defaults = UserDefaults.standard
                defaults.removeObject(forKey: "isPremium")
                defaults.removeObject(forKey: "subscriptionStatus")
                print("🔄 UserData: 已清除全局会员状态标志 - isPremium, subscriptionStatus")
                
                // 清除订阅状态相关的UserDefaults (如果之前有保存的话，确保彻底清除)
                let userPrefix = "user_\(self.id)_" // 使用当前的id构造，尽管它可能已被清空
                defaults.removeObject(forKey: userPrefix + "isPremium")
                defaults.removeObject(forKey: userPrefix + "subscriptionStartDate")
                defaults.removeObject(forKey: userPrefix + "subscriptionEndDate")
                defaults.removeObject(forKey: userPrefix + "subscriptionType")
                defaults.removeObject(forKey: userPrefix + "subscriptionStatus")
                defaults.removeObject(forKey: userPrefix + "isFirstTimeSubscriber")
                
                // 🔧 重要修复：清除全局的原始交易ID，防止新账号使用旧账号的交易信息
                defaults.removeObject(forKey: "lastOriginalTransactionId")
                print("🔄 UserData: 已清除全局原始交易ID，防止账号间交易信息污染")
                
                // 🔧 补充修复：清除用户特定的原始交易ID
                if !self.id.isEmpty {
                    let userSpecificKey = "user_\(self.id)_lastOriginalTransactionId"
                    defaults.removeObject(forKey: userSpecificKey)
                    print("🔄 UserData: 已清除用户\(self.id)的原始交易ID")
                }
                
                // 🔧 重要修复：清除保存的交易信息，防止账号间的交易信息污染
                defaults.removeObject(forKey: "savedTransactionInfo")
                print("🔄 UserData: 已清除保存的交易信息，防止账号间交易信息污染")
                
                // 🔒 防洗账号：清除当前Apple ID标记，但保留绑定关系和试用期记录
                defaults.removeObject(forKey: "currentAppleID")
                print("🔄 UserData: 已清除当前Apple ID标记")
                
                // 注意：不清除以下账号数据，因为它们需要跨账号保持：
                // - trialUsedAppleIDs (已使用试用期的Apple ID列表)
                // - appleIDUserBindings (Apple ID与用户的绑定关系)
                // - deviceUniqueIdentifier (设备唯一标识符)
                // - deviceFingerprint (设备指纹)
                // - device_*_trialUsed (设备试用期使用标记)
                // - fingerprint_*_trialUsed (指纹试用期使用标记)
                
                print("🔒 UserData: 防洗账号核心数据已保留，防止重复获取试用期")
                
                // 根据是否为账号删除选择重置方式
                if isAccountDeletion {
                    // 账号删除时使用强制重置，确保完全清理会话状态
                    NetworkService.shared.forceResetSessionState()
                    print("🧹 账号删除：使用强制重置会话状态")
                } else {
                    // 普通登出使用常规重置
                    NetworkService.shared.resetSessionState()
                    print("🔄 普通登出：使用常规重置会话状态")
                }
                
                // 发送登出通知
                NotificationCenter.default.post(name: Notification.Name("UserLoggedOut"), object: nil)
                print("📣 已发送UserLoggedOut通知，并清除了本地登录状态")

                // 登出后不需要检查订阅状态，避免使用残留token造成状态混乱
                // Task {
                //     await self.checkAndUpdateSubscriptionStatus()
                // }
            }
        }
    }
    
    // 检测是否为虚假的nickname（如"用户名_编号"格式）
    private func isFakeNickname(_ nickname: String?) -> Bool {
        guard let nickname = nickname, !nickname.isEmpty else { return false }
        
        // 检测包含下划线后跟数字的模式，如"用户名_123456"
        let pattern = "^.+_\\d+$"
        let regex = try? NSRegularExpression(pattern: pattern, options: [])
        let range = NSRange(location: 0, length: nickname.utf16.count)
        
        if let _ = regex?.firstMatch(in: nickname, options: [], range: range) {
            print("🔍 检测到虚假昵称格式: \(nickname)")
            return true
        }
        
        // 检测是否包含"用户"字样的自动生成格式
        if nickname.contains("用户") {
            print("🔍 检测到包含'用户'的虚假昵称: \(nickname)")
            return true
        }
        
        return false
    }
    
    // 处理并过滤nickname
    func filterNickname(_ nickname: String?) -> String {
        guard let nickname = nickname else { return "" }
        
        // 如果是虚假格式，返回空字符串
        if isFakeNickname(nickname) {
            print("🚫 过滤虚假昵称，设置为空: \(nickname)")
            return ""
        }
        
        return nickname
    }

    // 设置访客模式
    func setGuestMode() {
        print("🚪 设置访客模式")
        
        // 清除任何现有的登录状态
        accessToken = ""
        refreshToken = ""
        tokenExpiresAt = nil
        
        // 设置访客模式标志
        isGuestMode = true
        
        // 设置访客用户信息
        id = "guest_\(UUID().uuidString)"
        name = ""
        nickname = ""
        email = ""
        profileImage = nil
        
        // 确保选中的标签页是Nutrition(首页)
        selectedTab = 0
        
        // 重置订阅状态
        isPremium = false
        isFirstTimeSubscriber = true
        subscriptionStartDate = nil
        subscriptionEndDate = nil
        subscriptionType = ""
        subscriptionStatus = ""
        
        // 重置计划状态
        hasPlan = false
        hasSetWeightGoal = false
        goalWeight = 0
        currentManagementWeight = 0
        
        // 保存访客模式状态
        UserDefaults.standard.set(true, forKey: "isGuestMode")
        UserDefaults.standard.set(true, forKey: "isLoggedIn")  // 访客模式也算作已登录状态
        
        // 清除任何已保存的登录信息
        UserDefaults.standard.removeObject(forKey: "accessToken")
        UserDefaults.standard.removeObject(forKey: "refreshToken")
        UserDefaults.standard.removeObject(forKey: "tokenExpiresAt")
        UserDefaults.standard.removeObject(forKey: "userEmail")
        UserDefaults.standard.removeObject(forKey: "userNickname")
        UserDefaults.standard.removeObject(forKey: "userId")
        UserDefaults.standard.removeObject(forKey: "userName")
        
        print("✅ 访客模式设置完成")
    }
    
    // 检查是否为访客模式
    var isGuest: Bool {
        return isGuestMode && accessToken.isEmpty
    }
    
    // 退出访客模式并要求登录
    func exitGuestMode() {
        isGuestMode = false
        UserDefaults.standard.set(false, forKey: "isGuestMode")
        
        // 发送需要登录的通知
        NotificationCenter.default.post(name: Notification.Name("GuestNeedsLogin"), object: nil)
    }

    // 设置登录状态并保存用户数据
    func setLogin(_ loginData: DirectLoginData) {
        print("⭐️ 开始设置登录数据")
        
        // 🚪 清除访客模式状态
        isGuestMode = false
        UserDefaults.standard.set(false, forKey: "isGuestMode")
        print("🚪 已清除访客模式状态")
        
        // 🔧 重要修复：先强制重置订阅状态，防止保留上一个账号的会员状态
        isPremium = false
        isFirstTimeSubscriber = true
        subscriptionStartDate = nil
        subscriptionEndDate = nil
        subscriptionType = ""
        subscriptionStatus = ""
        
        // 🔧 清除全局会员状态标志，确保新账号不继承旧账号状态
        UserDefaults.standard.removeObject(forKey: "isPremium")
        UserDefaults.standard.removeObject(forKey: "subscriptionStatus")
        print("🔄 UserData: 登录时已重置订阅状态并清除全局会员标志")
        
        // 🆕 清除当前显示的头像，防止显示上一个账号的头像
        self.profileImage = nil
        
        self.accessToken = loginData.accessToken ?? ""
        self.refreshToken = loginData.refreshToken ?? ""
        
        // 设置Token过期时间
        if let expiresIn = loginData.expiresIn {
            let expiresAt = Date().addingTimeInterval(TimeInterval(expiresIn))
            self.tokenExpiresAt = expiresAt
            UserDefaults.standard.set(expiresAt, forKey: "tokenExpiresAt")
            print("📅 Token过期时间设置为: \(expiresAt)")
        } else {
            // 如果没有提供过期时间，默认设置为24小时
            let expiresAt = Date().addingTimeInterval(86400) // 24小时
            self.tokenExpiresAt = expiresAt
            UserDefaults.standard.set(expiresAt, forKey: "tokenExpiresAt")
            print("📅 使用默认Token过期时间（24小时）: \(expiresAt)")
        }
        
        // 设置用户信息
        if let userInfo = loginData.userInfo {
            self.id = userInfo.id ?? UUID().uuidString // 如果没有id，生成一个新的
            self.name = userInfo.name ?? ""
            // 过滤虚假的nickname
            self.nickname = filterNickname(userInfo.nickname)
            self.email = userInfo.email ?? ""
            print("📝 从用户信息中获取数据: ID=\(self.id), 昵称=\(self.nickname)")
        } else {
            // 如果没有userInfo，尝试从其他字段获取
            if let userId = loginData.userId {
                self.id = userId
                print("📝 从userId获取ID: \(userId)")
            } else {
                self.id = UUID().uuidString // 确保有个ID
            }
            
            if let userName = loginData.userName {
                self.name = userName
                // 过滤虚假的nickname，如果userName是虚假格式则不作为nickname
                self.nickname = filterNickname(userName)
                print("📝 从userName获取名称: \(userName)，过滤后昵称: \(self.nickname)")
            }
            
            print("⚠️ 登录数据中没有完整的用户信息")
        }
        
        // 保存登录状态到UserDefaults
        UserDefaults.standard.set(true, forKey: "isLoggedIn") // 全局登录标志
        UserDefaults.standard.set(self.accessToken, forKey: "accessToken")
        UserDefaults.standard.set(self.refreshToken, forKey: "refreshToken")
        UserDefaults.standard.set(self.id, forKey: "userId") // 保存当前用户的ID
        UserDefaults.standard.set(self.name, forKey: "userName")
        UserDefaults.standard.set(self.nickname, forKey: "userNickname")
        UserDefaults.standard.set(self.email, forKey: "userEmail")
        
        print("💾 已保存登录状态到UserDefaults")
        
        // 加载该用户的特定设置（非订阅部分）
        // 注意：loadSettings内部现在不会加载订阅信息了
        self.loadSettings()
        
        // 🆕 加载本地缓存的用户头像（如果存在）
        loadProfileImageFromCache()
        
        // 启动Token自动刷新
        startTokenAutoRefresh()
        
        // 登录成功后，立即检查并更新该用户的真实订阅状态
        Task {
            await self.checkAndUpdateSubscriptionStatus()
        }
        
        // 确保在主线程发送通知
        DispatchQueue.main.async {
            // 通知登录成功
            NotificationCenter.default.post(name: Notification.Name("UserLoggedIn"), object: nil)
            print("📣 已发送UserLoggedIn通知")
        }
    }
    
    // 登录成功后的操作
    func doSthAfterLoginSuccess() {
        print("🔄 开始执行登录成功后的操作")
        
        // 获取用户是否为新用户
        let service = UserExtendService.shared
        service.isNewUser { [weak self] result in
            guard self != nil else { return }
            
            // 确保在主线程更新UI
            DispatchQueue.main.async {
                switch result {
                case .success(let isNewUser):
                    if isNewUser {
                        print("👤 检测到新用户，发送NavigateToOnboarding通知")
                        // 如果是新用户，跳转到新用户引导流程
                        NotificationCenter.default.post(name: Notification.Name("NavigateToOnboarding"), object: nil)
                        // 设置标记
                        UserDefaults.standard.set(true, forKey: "isNewUser")
                    } else {
                        print("👤 检测到老用户，确保跳转到Nutrition首页")
                        // 如果不是新用户，直接跳转到Nutrition首页（索引0）
                        self?.selectedTab = 0
                        self?.objectWillChange.send()
                        NotificationCenter.default.post(name: Notification.Name("NavigateToNutritionView"), object: nil)
                        // 清除标记（如果存在）
                        if UserDefaults.standard.bool(forKey: "isNewUser") {
                            UserDefaults.standard.removeObject(forKey: "isNewUser")
                        }
                    }
                case .failure(let error):
                    print("❌ 检查新用户状态失败: \(error.localizedDescription)")
                    print("👤 默认发送NavigateToHome通知")
                    // 默认跳转到主页
                    NotificationCenter.default.post(name: Notification.Name("NavigateToHome"), object: nil)
                }
            }
        }
    }
    
    // 验证苹果登录状态，并在需要时清理
    func validateAppleLoginState() {
        // 检查是否通过苹果登录
        let isAppleLogin = UserDefaults.standard.bool(forKey: "isAppleLogin")
        
        // 如果是苹果登录用户，检查Token状态
        if isAppleLogin {
            print("🍎 验证苹果登录状态")
            
            // 检查令牌是否存在和有效
            if !accessToken.isEmpty && !isTokenExpired() {
                print("✅ 苹果登录令牌有效，用户已登录")
                
                // 🚪 清除访客模式状态（如果存在）
                if isGuestMode {
                    isGuestMode = false
                    UserDefaults.standard.set(false, forKey: "isGuestMode")
                    print("🚪 苹果登录时已清除访客模式状态")
                }
                
                // 确保登录状态标志正确设置
                if !UserDefaults.standard.bool(forKey: "isLoggedIn") {
                    UserDefaults.standard.set(true, forKey: "isLoggedIn")
                    print("✅ 更新isLoggedIn标志为true")
                    
                    // 发送通知以更新UI
                    NotificationCenter.default.post(name: Notification.Name("UserLoggedIn"), object: nil)
                }
            } else {
                // 令牌无效或过期，清理登录状态
                print("⚠️ 苹果登录令牌无效或已过期，清理登录状态")
                accessToken = ""
                refreshToken = ""
                tokenExpiresAt = nil
                
                UserDefaults.standard.removeObject(forKey: "accessToken")
                UserDefaults.standard.removeObject(forKey: "refreshToken")
                UserDefaults.standard.removeObject(forKey: "tokenExpiresAt")
                UserDefaults.standard.set(false, forKey: "isLoggedIn")
                UserDefaults.standard.set(false, forKey: "isAppleLogin")
                
                // 通知登出
                NotificationCenter.default.post(name: Notification.Name("UserLoggedOut"), object: nil)
            }
        }
    }
    
    // 验证谷歌登录状态
    func validateGoogleLoginState() {
        // 检查是否通过谷歌登录
        let isGoogleLogin = UserDefaults.standard.bool(forKey: "isGoogleLogin")
        
        // 如果是谷歌登录用户，检查Token状态
        if isGoogleLogin {
            print("🌐 验证谷歌登录状态")
            
            // 检查令牌是否存在和有效
            if !accessToken.isEmpty && !isTokenExpired() {
                print("✅ 谷歌登录令牌有效，用户已登录")
                
                // 🚪 清除访客模式状态（如果存在）
                if isGuestMode {
                    isGuestMode = false
                    UserDefaults.standard.set(false, forKey: "isGuestMode")
                    print("🚪 谷歌登录时已清除访客模式状态")
                }
                
                // 确保登录状态标志正确设置
                if !UserDefaults.standard.bool(forKey: "isLoggedIn") {
                    UserDefaults.standard.set(true, forKey: "isLoggedIn")
                    print("✅ 更新isLoggedIn标志为true")
                    
                    // 发送通知以更新UI
                    NotificationCenter.default.post(name: Notification.Name("UserLoggedIn"), object: nil)
                }
            } else {
                // 令牌无效或过期，清理登录状态
                print("⚠️ 谷歌登录令牌无效或已过期，清理登录状态")
                accessToken = ""
                refreshToken = ""
                tokenExpiresAt = nil
                
                UserDefaults.standard.removeObject(forKey: "accessToken")
                UserDefaults.standard.removeObject(forKey: "refreshToken")
                UserDefaults.standard.removeObject(forKey: "tokenExpiresAt")
                UserDefaults.standard.set(false, forKey: "isLoggedIn")
                UserDefaults.standard.set(false, forKey: "isGoogleLogin")
                
                // 通知登出
                NotificationCenter.default.post(name: Notification.Name("UserLoggedOut"), object: nil)
            }
        }
    }
    
    // 清理UserDefaults中的大数据，防止超过CFPreferences限制
    private func cleanupUserDefaults() {
        // 使用UserDefaultsManager进行数据大小管理
        let manager = UserDefaultsManager.shared
        
        // 打印当前数据大小报告
        manager.printSizeReport()
        
        let defaults = UserDefaults.standard
        let userPrefix = !id.isEmpty ? "user_\(id)_" : "default_user_"
        
        // 检查并清理可能过大的数据
        let keysToCheck = [
            userPrefix + "foodEntries",
            userPrefix + "weightHistory", 
            userPrefix + "exerciseRecords",
            userPrefix + "checkInRecords"
        ]
        
        var totalSize = 0
        for key in keysToCheck {
            if let data = defaults.data(forKey: key) {
                totalSize += data.count
                print("UserDefaults key '\(key)' 大小: \(data.count) bytes")
            }
        }
        
        print("用户数据总大小约: \(totalSize) bytes")
        
        // 如果总大小超过2MB，清理一些数据
        if totalSize > 2 * 1024 * 1024 {
            print("⚠️ 用户数据过大，开始清理...")
            
            // 清理食物记录，只保留最近15条
            if let foodData = defaults.data(forKey: userPrefix + "foodEntries"),
               let decodedEntries = try? JSONDecoder().decode([FoodEntry].self, from: foodData) {
                let limitedEntries = Array(decodedEntries.suffix(15))
                if manager.safeSet(limitedEntries, forKey: userPrefix + "foodEntries") {
                    print("已清理食物记录，保留\(limitedEntries.count)条")
                }
            }
            
            // 清理体重历史，只保留最近30条
            if let weightData = defaults.data(forKey: userPrefix + "weightHistory"),
               let decodedRecords = try? JSONDecoder().decode([WeightRecord].self, from: weightData) {
                let limitedRecords = Array(decodedRecords.suffix(30))
                if manager.safeSet(limitedRecords, forKey: userPrefix + "weightHistory") {
                    print("已清理体重历史，保留\(limitedRecords.count)条")
                }
            }
            
            // 清理运动记录，只保留最近20条
            if let exerciseData = defaults.data(forKey: userPrefix + "exerciseRecords"),
               let decodedRecords = try? JSONDecoder().decode([ExerciseRecord].self, from: exerciseData) {
                let limitedRecords = Array(decodedRecords.suffix(20))
                if manager.safeSet(limitedRecords, forKey: userPrefix + "exerciseRecords") {
                    print("已清理运动记录，保留\(limitedRecords.count)条")
                }
            }
            
            // 清理后再次打印报告
            print("\n清理后的数据大小:")
            manager.printSizeReport()
        }
    }
    
    // 初始化，加载默认数据
    init() {
        // 从UserDefaults加载用户的邮箱和名称（这些是在登录/注册成功后保存的）
        if let userEmail = UserDefaults.standard.string(forKey: "userEmail") {
            email = userEmail
        }
        
        if let userName = UserDefaults.standard.string(forKey: "userName") {
            name = userName
        }
        
        if let userNickname = UserDefaults.standard.string(forKey: "userNickname") {
            nickname = userNickname
        }
        
        // 🆕 加载用户ID（用于头像缓存）
        if let userId = UserDefaults.standard.string(forKey: "userId") {
            id = userId
        }
        
        // 加载用户设置和登录状态
        loadSettings()
        loadLoginState()
        
        // 清理可能过大的UserDefaults数据
        cleanupUserDefaults()
        
        // 设置用户头像更新通知监听
        setupAvatarUpdateNotifications()
    }
}

// 食物条目模型
struct FoodEntry: Codable, Identifiable {
    var id: UUID
    var name: String
    var calories: Int
    var protein: Int
    var carbs: Int
    var fat: Int
    var time: Date
    var image: Data?
    
    // 自定义编码器以确保UUID正确编码
    enum CodingKeys: String, CodingKey {
        case id, name, calories, protein, carbs, fat, time, image
    }
    
    init(id: UUID, name: String, calories: Int, protein: Int, carbs: Int, fat: Int, time: Date, image: Data?) {
        self.id = id
        self.name = name
        self.calories = calories
        self.protein = protein
        self.carbs = carbs
        self.fat = fat
        self.time = time
        self.image = image
    }
}

// 体重记录结构
struct WeightRecord: Codable, Identifiable {
    var id = UUID()
    var date: Date
    var weight: Double // 以kg为单位
    var isMorning: Bool // true = 早晨, false = 晚上
    
    // 自定义编码器以确保UUID正确编码
    enum CodingKeys: String, CodingKey {
        case id, date, weight, isMorning
    }
    
    init(date: Date, weight: Double, isMorning: Bool) {
        self.date = date
        self.weight = weight
        self.isMorning = isMorning
    }
    
    init(from decoder: Decoder) throws {
        let container = try decoder.container(keyedBy: CodingKeys.self)
        id = try container.decode(UUID.self, forKey: .id)
        date = try container.decode(Date.self, forKey: .date)
        weight = try container.decode(Double.self, forKey: .weight)
        isMorning = try container.decode(Bool.self, forKey: .isMorning)
    }
    
    func encode(to encoder: Encoder) throws {
        var container = encoder.container(keyedBy: CodingKeys.self)
        try container.encode(id, forKey: .id)
        try container.encode(date, forKey: .date)
        try container.encode(weight, forKey: .weight)
        try container.encode(isMorning, forKey: .isMorning)
    }
}

// 体重记录结构
struct LocalWeightRecord: Codable, Identifiable {
    var id = UUID()
    var date: Date
    var weight: Double // 以kg为单位
    var isMorning: Bool // true = 早晨, false = 晚上
    
    // 自定义编码器以确保UUID正确编码
    enum CodingKeys: String, CodingKey {
        case id, date, weight, isMorning
    }
    
    init(date: Date, weight: Double, isMorning: Bool) {
        self.date = date
        self.weight = weight
        self.isMorning = isMorning
    }
    
    init(from decoder: Decoder) throws {
        let container = try decoder.container(keyedBy: CodingKeys.self)
        id = try container.decode(UUID.self, forKey: .id)
        date = try container.decode(Date.self, forKey: .date)
        weight = try container.decode(Double.self, forKey: .weight)
        isMorning = try container.decode(Bool.self, forKey: .isMorning)
    }
    
    func encode(to encoder: Encoder) throws {
        var container = encoder.container(keyedBy: CodingKeys.self)
        try container.encode(id, forKey: .id)
        try container.encode(date, forKey: .date)
        try container.encode(weight, forKey: .weight)
        try container.encode(isMorning, forKey: .isMorning)
    }
    
    // 获取显示的重量（根据用户设置的单位）
    func displayWeight(userData: UserData) -> Double {
        return userData.weightUnit == "kg" ? weight : userData.convertToLbs(weight)
    }
    
    // 获取显示的单位
    func displayUnit(userData: UserData) -> String {
        return userData.weightUnit
    }
}
