import SwiftUI

// GoalWeightFullScreenView - 全屏版目标体重编辑视图
struct GoalWeightFullScreenView: View {
    @Environment(\.presentationMode) private var presentationMode
    @EnvironmentObject var userData: UserData
    
    @State private var goalWeight: Double = 0
    @State private var timelineWeeks: Double = 0
    @State private var gender: String = "Male"
    @State private var currentWeight: String = ""
    @State private var age: String = ""
    @State private var height: String = ""
    @State private var showWeightGoalEditPopup: Bool = false
    @State private var isLoading: Bool = false
    @State private var showErrorAlert: Bool = false
    @State private var errorMessage: String = ""
    
    var body: some View {
        NavigationView {
            ScrollView {
                VStack(alignment: .leading, spacing: 25) {
                    // 体重目标
                    VStack(alignment: .leading, spacing: 10) {
                        Text("Weight Goal")
                            .font(.headline)
                        
                        HStack {
                            Spacer()
                            
                            Text("\(String(format: "%.1f", goalWeight)) \(userData.weightUnit)")
                                .font(.system(size: 32, weight: .bold))
                                .foregroundColor(.green)
                            
                            Spacer()
                            
                            Button(action: {
                                showWeightGoalEditPopup = true
                            }) {
                                Image(systemName: "pencil")
                                    .foregroundColor(.green)
                                    .padding()
                                    .background(Color(UIColor.secondarySystemBackground))
                                    .cornerRadius(10)
                            }
                        }
                        
                        Text("Set your target weight")
                            .font(.caption)
                            .foregroundColor(.secondary)
                    }
                    .padding(.horizontal)
                    
                    // 目标时间线
                    VStack(alignment: .leading, spacing: 10) {
                        Text("Your Goal Timeline")
                            .font(.headline)
                        
                        Slider(value: $timelineWeeks, in: 4...24, step: 1)
                            .accentColor(.green)
                        
                        HStack {
                            Spacer()
                            Text("\(Int(timelineWeeks)) weeks")
                                .font(.subheadline)
                                .foregroundColor(.secondary)
                        }
                    }
                    .padding(.horizontal)
                    
                    // 个人详情
                    VStack(alignment: .leading, spacing: 15) {
                        Text("Personal Details")
                            .font(.headline)
                        
                        // 性别选择
                        VStack(alignment: .leading, spacing: 8) {
                            Text("Gender")
                                .font(.subheadline)
                            
                            HStack(spacing: 20) {
                                // 男性
                                HStack {
                                    ZStack {
                                        Circle()
                                            .stroke(Color.gray, lineWidth: 1)
                                            .fill(Color.white)
                                            .frame(width: 20, height: 20)
                                        
                                        if gender == "Male" {
                                            Image(systemName: "checkmark")
                                                .foregroundColor(.green)
                                                .font(.system(size: 12, weight: .bold))
                                        }
                                    }
                                    
                                    Text("Male")
                                        .foregroundColor(.primary)
                                }
                                .onTapGesture {
                                    gender = "Male"
                                    print("选择性别: Male")
                                }
                                
                                // 女性
                                HStack {
                                    ZStack {
                                        Circle()
                                            .stroke(Color.gray, lineWidth: 1)
                                            .fill(Color.white)
                                            .frame(width: 20, height: 20)
                                        
                                        if gender == "Female" {
                                            Image(systemName: "checkmark")
                                                .foregroundColor(.green)
                                                .font(.system(size: 12, weight: .bold))
                                        }
                                    }
                                    
                                    Text("Female")
                                        .foregroundColor(.primary)
                                }
                                .onTapGesture {
                                    gender = "Female"
                                    print("选择性别: Female")
                                }
                                
                                // 其他
                                HStack {
                                    ZStack {
                                        Circle()
                                            .stroke(Color.gray, lineWidth: 1)
                                            .fill(Color.white)
                                            .frame(width: 20, height: 20)
                                        
                                        if gender == "Other" {
                                            Image(systemName: "checkmark")
                                                .foregroundColor(.green)
                                                .font(.system(size: 12, weight: .bold))
                                        }
                                    }
                                    
                                    Text("Other")
                                        .foregroundColor(.primary)
                                }
                                .onTapGesture {
                                    gender = "Other"
                                    print("选择性别: Other")
                                }
                            }
                        }
                        
                        // 当前体重
                        VStack(alignment: .leading, spacing: 8) {
                            Text("Current Weight")
                                .font(.subheadline)
                            
                            HStack {
                                TextField("Enter weight", text: $currentWeight)
                                    .keyboardType(.decimalPad)
                                    .padding()
                                    .background(Color(UIColor.secondarySystemBackground))
                                    .cornerRadius(8)
                                    .onChange(of: currentWeight) { oldValue, newValue in
                                        currentWeight = newValue.limitedTo999_99()
                                    }
                                
                                Text(userData.weightUnit)
                                    .foregroundColor(.secondary)
                                    .padding(.leading, 8)
                            }
                        }
                        
                        // 年龄
                        VStack(alignment: .leading, spacing: 8) {
                            Text("Age")
                                .font(.subheadline)
                            
                            TextField("Enter age", text: $age)
                                .keyboardType(.numberPad)
                                .padding()
                                .background(Color(UIColor.secondarySystemBackground))
                                .cornerRadius(8)
                                .onChange(of: age) { oldValue, newValue in
                                    // 年龄只允许整数，但也使用相同的限制逻辑
                                    let filtered = newValue.filter { "0123456789".contains($0) }
                                    let limited = String(filtered.prefix(3))
                                    if let intValue = Int(limited), intValue > 999 {
                                        age = "999"
                                    } else {
                                        age = limited
                                    }
                                }
                        }
                        
                        // 身高
                        VStack(alignment: .leading, spacing: 8) {
                            Text("Height")
                                .font(.subheadline)
                            
                            HStack {
                                TextField("Enter height", text: $height)
                                    .keyboardType(.decimalPad)
                                    .padding()
                                    .background(Color(UIColor.secondarySystemBackground))
                                    .cornerRadius(8)
                                    .onChange(of: height) { oldValue, newValue in
                                        height = newValue.limitedTo999_99()
                                    }
                                
                                Text(userData.heightUnit)
                                    .foregroundColor(.secondary)
                                    .padding(.leading, 8)
                            }
                        }
                    }
                    .padding(.horizontal)
                    
                    // 提示信息
                    HStack(spacing: 12) {
                        Image(systemName: "info.circle")
                            .foregroundColor(.green)
                        
                        Text("These details help us provide more accurate weight management recommendations")
                            .font(.caption)
                            .foregroundColor(.secondary)
                            .fixedSize(horizontal: false, vertical: true)
                            .frame(maxWidth: .infinity, alignment: .leading)
                            .lineLimit(nil)
                            .multilineTextAlignment(.leading)
                    }
                    .padding()
                    .frame(maxWidth: .infinity)
                    .background(Color.green.opacity(0.1))
                    .cornerRadius(8)
                    .padding(.horizontal)
                    
                    // 保存按钮
                    Button(action: {
                        print("======== 开始保存体重目标设置 ========")
                        print("表单值 - 目标体重: \(goalWeight)\(userData.weightUnit), 目标周数: \(Int(timelineWeeks)), 性别: \(gender), 当前体重: \(currentWeight), 年龄: \(age), 身高: \(height)")
                        saveChanges() 
                    }) {
                        Text("Save Changes")
                            .font(.headline)
                            .foregroundColor(.white)
                            .frame(maxWidth: .infinity)
                            .padding()
                            .background(Color.green)
                            .cornerRadius(10)
                    }
                    .padding(.horizontal)
                    .padding(.top, 20)
                }
                .padding(.bottom, 50)
            }
            .navigationBarTitle("Goal Weight ment", displayMode: .inline)
            .contentShape(Rectangle())
            .onTapGesture {
                hideKeyboard()
        }
        .overlay(
            Group {
                if showWeightGoalEditPopup {
                    WeightInputPopup(
                        isShowing: $showWeightGoalEditPopup,
                        weightTime: .morning,
                        initialWeight: String(format: "%.1f", goalWeight),
                        initialUnit: userData.weightUnit
                    ) { weight, unit in
                        // 更新页面中的目标体重
                        if unit == userData.weightUnit {
                            goalWeight = weight
                        } else if unit == "kg" && userData.weightUnit == "lbs" {
                            // 转换kg到lbs
                            goalWeight = weight * 2.20462
                        } else if unit == "lbs" && userData.weightUnit == "kg" {
                            // 转换lbs到kg
                            goalWeight = weight / 2.20462
                        }
                    }
                }
                
                // 移除了加载状态指示器，提升用户体验
            }
        )
        .alert(isPresented: $showErrorAlert) {
            Alert(
                title: Text("Error"),
                message: Text(errorMessage),
                dismissButton: .default(Text("OK"))
            )
        }
        .onAppear {
            // 统计
            XDTrackTool.shared.appear("目标体重设置页面")

            initializeForm()
            }
        }
    }
    
    // 初始化表单
    private func initializeForm() {
        print("======== 初始化表单 ========")
        // 首先尝试从API获取最新计划
        if !userData.accessToken.isEmpty {
            print("尝试从API获取最新计划")
            fetchLatestPlanData()
        } else {
            // 如果没有访问令牌，回退到本地数据
            print("未登录状态，从本地加载数据")
            loadFromLocalData()
        }
    }
    
    // 获取最新计划数据
    private func fetchLatestPlanData() {
        print("正在获取最新计划数据...")
        UserPlanService.shared.getLatestPlan(userData: userData) { result in
            DispatchQueue.main.async {
                switch result {
                case .success(let planResponse):
                    print("成功获取最新计划数据：")
                    print("- 目标体重: \(planResponse.targetWeight)kg")
                    print("- 目标周数: \(planResponse.targetWeeks)周")
                    if let apiGender = planResponse.gender {
                        print("- 性别: \(apiGender)")
                    }
                    if let age = planResponse.age {
                        print("- 年龄: \(age)")
                    }
                    if let height = planResponse.height {
                        print("- 身高: \(height)cm")
                    }
                    if let currentWeight = planResponse.currentWeight {
                        print("- 当前体重: \(currentWeight)kg")
                    }
                    
                    // 设置目标体重
                    let weightValue = self.userData.getWeightValue(planResponse.targetWeight)
                    self.goalWeight = weightValue
                    
                    // 设置目标周期
                    self.timelineWeeks = Double(planResponse.targetWeeks)
                    
                    // 设置性别
                    if let apiGender = planResponse.gender {
                        switch apiGender {
                        case "BOY":
                            self.gender = "Male"
                        case "GIRL":
                            self.gender = "Female"
                        case "NONE":
                            self.gender = "Other"
                        default:
                            self.gender = "Other"
                        }
                    }
                    
                    // 设置当前体重
                    if let currentWeight = planResponse.currentWeight {
                        let currentWeightValue = self.userData.getWeightValue(currentWeight)
                        self.currentWeight = String(format: "%.1f", currentWeightValue)
                    }
                    
                    // 设置年龄
                    if let age = planResponse.age {
                        self.age = "\(age)"
                    }
                    
                    // 设置身高
                    if let height = planResponse.height {
                        let heightValue = self.userData.heightUnit == "cm" ? height : height / 30.48
                        self.height = String(format: "%.1f", heightValue)
                        print("- 身高转换: 从\(height)cm转为\(heightValue)\(self.userData.heightUnit)")
                    }
                    
                case .failure(let error):
                    print("获取最新计划失败，使用本地数据: \(error.localizedDescription)")
                    self.loadFromLocalData()
                }
            }
        }
    }
    
    // 从本地数据加载
    private func loadFromLocalData() {
        print("从本地数据加载表单值")
        // 加载目标体重 - 仅当用户有计划时
        if userData.hasPlan {
            let weightValue = userData.getWeightValue(userData.goalWeight)
            goalWeight = weightValue
            print("- 本地目标体重: \(weightValue) \(userData.weightUnit)")
        } else {
            // 用户从引导页面跳过，不设置默认值
            goalWeight = 0
            print("- 未设置目标体重")
        }
        
        // 加载目标周期 - 仅当用户有计划时
        if userData.hasPlan {
            timelineWeeks = Double(userData.goalTimelineWeeks)
            print("- 本地目标周数: \(userData.goalTimelineWeeks)")
        } else {
            // 用户从引导页面跳过，设置默认值为12周
            timelineWeeks = 12.0
            print("- 设置默认目标周数: 12")
        }
        
        // 加载性别 - 仅当用户有计划时
        if userData.hasPlan {
            gender = userData.gender
            print("- 本地性别: \(userData.gender)")
        } else {
            // 用户从引导页面跳过，不设置默认值
            gender = ""
            print("- 未设置性别")
        }
        
        // 加载当前体重 - 仅当用户有计划时
        if userData.hasPlan {
            let currentWeightValue = userData.getWeightValue(userData.initialWeight)
            currentWeight = String(format: "%.1f", currentWeightValue)
            print("- 本地当前体重: \(currentWeightValue) \(userData.weightUnit)")
        } else {
            // 用户从引导页面跳过，不设置默认值
            currentWeight = ""
            print("- 未设置当前体重")
        }
        
        // 加载年龄 - 仅当用户有计划时
        if userData.hasPlan {
            age = "\(userData.age)"
            print("- 本地年龄: \(userData.age)")
        } else {
            // 用户从引导页面跳过，不设置默认值
            age = ""
            print("- 未设置年龄")
        }
        
        // 加载身高 - 仅当用户有计划时
        if userData.hasPlan {
            let heightValue = userData.heightUnit == "cm" ? userData.height : userData.height / 30.48
            height = String(format: "%.1f", heightValue)
            print("- 本地身高: \(heightValue) \(userData.heightUnit)")
        } else {
            // 用户从引导页面跳过，不设置默认值
            height = ""
            print("- 未设置身高")
        }
    }
    
    // 保存更改
    private func saveChanges() {
        print("======== 开始调用API保存更改 ========")
        
        // 数据验证
        let validationResult = validateInputData()
        if !validationResult.isValid {
            errorMessage = validationResult.errorMessage
            showErrorAlert = true
            return
        }
        
        // 确认输入是有效的
        guard let currentWeightValue = Double(currentWeight),
              let ageValue = Int(age),
              let heightValue = Double(height) else {
            print("❌ 输入验证失败: currentWeight=\(currentWeight), age=\(age), height=\(height)")
            errorMessage = "Please enter valid values for all fields"
            showErrorAlert = true
            return
        }
        
        print("输入验证通过: currentWeight=\(currentWeightValue), age=\(ageValue), height=\(heightValue)")
        
        // 记录单位信息
        print("当前单位设置 - 体重单位: \(userData.weightUnit), 身高单位: \(userData.heightUnit)")
        if userData.heightUnit == "ft" {
            print("⚠️ 注意: 身高单位为英尺(ft)，将在API请求中自动转换为厘米(cm)")
        }
        
        print("准备调用API: targetWeight=\(goalWeight), targetWeeks=\(Int(timelineWeeks)), age=\(ageValue), height=\(heightValue), currentWeight=\(currentWeightValue), gender=\(gender)")
        
        // 使用通用的创建或更新方法，它会根据情况自动选择正确的API端点
        UserPlanService.shared.createOrUpdateUserPlan(
                userData: userData, 
                targetWeight: goalWeight, 
                targetWeeks: Int(timelineWeeks), 
                age: ageValue, 
                height: heightValue, 
                currentWeight: currentWeightValue, 
                gender: gender
            ) { result in
                DispatchQueue.main.async {
                    switch result {
                    case .success(_):
                        print("✅ 用户计划创建/更新成功")
                        
                        // 发送通知，通知体重数据已更新，用于刷新PlanView中的进度
                        NotificationCenter.default.post(name: Notification.Name("WeightDataUpdated"), object: nil)
                        
                        // 额外发送通知，通知PlanView刷新
                        NotificationCenter.default.post(name: Notification.Name("RefreshPlanView"), object: nil)
                        
                        // 发送通知，告知目标体重已更新
                        NotificationCenter.default.post(name: Notification.Name("WeightGoalUpdated"), object: nil)
                        
                        // 退出页面
                        self.presentationMode.wrappedValue.dismiss()
                        
                    case .failure(let error):
                        print("❌ 计划创建/更新失败: \(error.localizedDescription)")
                        // 不显示错误信息，直接使用本地备份方式
                        // 在API调用失败的情况下，使用本地备份方式更新数据
                        print("尝试本地备份保存")
                        self.saveChangesLocally()
                    }
                }
        }
    }
    
    // 数据验证函数
    private func validateInputData() -> (isValid: Bool, errorMessage: String) {
        // 检查所有字段是否都已填写
        if currentWeight.trimmingCharacters(in: .whitespacesAndNewlines).isEmpty {
            return (false, "Please enter your current weight")
        }
        
        if age.trimmingCharacters(in: .whitespacesAndNewlines).isEmpty {
            return (false, "Please enter your age")
        }
        
        if height.trimmingCharacters(in: .whitespacesAndNewlines).isEmpty {
            return (false, "Please enter your height")
        }
        
        if gender.trimmingCharacters(in: .whitespacesAndNewlines).isEmpty {
            return (false, "Please select your gender")
        }
        
        // 验证数据类型和范围
        guard let currentWeightValue = Double(currentWeight) else {
            return (false, "Please enter a valid weight")
        }
        
        guard let ageValue = Int(age) else {
            return (false, "Please enter a valid age")
        }
        
        guard let heightValue = Double(height) else {
            return (false, "Please enter a valid height")
        }
        
        // 验证体重范围（考虑单位）
        let minWeight = userData.weightUnit == "kg" ? 20.0 : 44.0  // 20kg or 44lbs
        let maxWeight = userData.weightUnit == "kg" ? 300.0 : 660.0  // 300kg or 660lbs
        
        if currentWeightValue < minWeight || currentWeightValue > maxWeight {
            let unit = userData.weightUnit
            return (false, "Weight must be between \(Int(minWeight)) and \(Int(maxWeight)) \(unit)")
        }
        
        // 验证目标体重范围
        if goalWeight < minWeight || goalWeight > maxWeight {
            let unit = userData.weightUnit
            return (false, "Goal weight must be between \(Int(minWeight)) and \(Int(maxWeight)) \(unit)")
        }
        
        // 验证年龄范围
        if ageValue < 1 || ageValue > 120 {
            return (false, "Age must be between 1 and 120 years")
        }
        
        // 验证身高范围（考虑单位）
        let minHeight = userData.heightUnit == "cm" ? 10.0 : 0.33  // 10cm or 0.33ft
        let maxHeight = userData.heightUnit == "cm" ? 250.0 : 8.2  // 250cm or 8.2ft
        
        if heightValue < minHeight || heightValue > maxHeight {
            let unit = userData.heightUnit
            let minStr = userData.heightUnit == "cm" ? "10" : "0.33"
            let maxStr = userData.heightUnit == "cm" ? "250" : "8.2"
            return (false, "Height must be between \(minStr) and \(maxStr) \(unit)")
        }
        
        // 验证时间线范围
        if timelineWeeks < 4 || timelineWeeks > 24 {  // 4-24周
            return (false, "Timeline must be between 4 and 24 weeks")
        }
        
        return (true, "")
    }
    
    // 在本地保存更改的辅助方法
    private func saveChangesLocally() {
        print("======== 开始本地保存更改 ========")
        // 保存目标体重
        var goalWeightKg = goalWeight
        if userData.weightUnit == "lbs" {
            goalWeightKg = userData.convertToKg(goalWeight)
            print("单位转换 - 目标体重: \(goalWeight)lbs → \(goalWeightKg)kg")
        } else {
            print("目标体重保持不变: \(goalWeightKg)kg")
        }
        userData.goalWeight = goalWeightKg
        
        // 保存其他数据
        userData.goalTimelineWeeks = Int(timelineWeeks)
        userData.gender = gender
        print("保存基本数据 - 目标周数: \(Int(timelineWeeks)), 性别: \(gender)")
        
        // 更新current weight (仅更新initialWeight，不影响morningWeight)
        if let currentWeightValue = Double(currentWeight) {
            var weightInKg = currentWeightValue
            if userData.weightUnit == "lbs" {
                weightInKg = userData.convertToKg(currentWeightValue)
                print("单位转换 - 当前体重: \(currentWeightValue)lbs → \(weightInKg)kg")
            } else {
                print("当前体重保持不变: \(weightInKg)kg")
            }
            
            // 设置initialWeight
            userData.initialWeight = weightInKg
            
            // 更新currentManagementWeight
            userData.currentManagementWeight = weightInKg
            print("保存体重数据 - initialWeight和currentManagementWeight: \(weightInKg)kg")
            
            // 如果用户是从skip后点击"Set Goal"进入的(hasSetWeightGoal为false)
            // 则同时更新startWeight，保持与currentManagementWeight同步
            if !userData.hasPlan {
                userData.startWeight = weightInKg
                print("首次设置，更新startWeight: \(weightInKg)kg")
            }
        }
        
        if let ageValue = Int(age) {
            userData.age = ageValue
            print("保存年龄: \(ageValue)")
        }
        
        if let heightValue = Double(height) {
            var heightCm = heightValue
            if userData.heightUnit == "ft" {
                heightCm = heightValue * 30.48
                print("单位转换 - 身高: \(heightValue)ft → \(heightCm)cm")
            } else {
                print("身高保持不变: \(heightCm)cm")
            }
            userData.height = heightCm
        }
        
        // 设置hasPlan为true，表示用户已设置目标体重
        userData.hasPlan = true
        
        // 设置目标开始日期为当前时间
        userData.goalStartDate = Date()
        
        // 保存到UserDefaults
        userData.saveSettings()
        print("✅ 已保存所有数据到本地")
        
        // 发送通知，通知体重数据已更新，用于刷新PlanView中的进度
        NotificationCenter.default.post(name: Notification.Name("WeightDataUpdated"), object: nil)
        
        // 额外发送通知，通知PlanView刷新
        NotificationCenter.default.post(name: Notification.Name("RefreshPlanView"), object: nil)
        
        // 发送通知，告知目标体重已更新
        NotificationCenter.default.post(name: Notification.Name("WeightGoalUpdated"), object: nil)
        print("已发送所有通知")
        
        // 退出页面
        presentationMode.wrappedValue.dismiss()
    }
} 