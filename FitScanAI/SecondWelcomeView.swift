import SwiftUI
import StoreKit

struct SecondWelcomeView: View {
    @EnvironmentObject private var userData: UserData
    @State private var isTrialPressed = false
    @State private var isSkipPressed = false
    @State private var isPurchasing = false
    @State private var showErrorAlert = false
    @State private var errorMessage = ""
    
    var body: some View {
        GeometryReader { geometry in
            ZStack {
                // 背景图片 - 确保完全填满屏幕
                Image("new2")
                    .resizable()
                    .aspectRatio(contentMode: .fill)
                    .frame(
                        width: max(geometry.size.width, geometry.size.height * 375 / 812),
                        height: max(geometry.size.height, geometry.size.width * 812 / 375)
                    )
                    .clipped()
                    .ignoresSafeArea()
                
                // 可滚动图片框架 - 扩大框架尺寸 (x20px, y140px, w335px, h550px)
                ScrollView {
                    Image("new3")
                        .resizable()
                        .aspectRatio(contentMode: .fit)
                        .frame(width: 335 * geometry.size.width / 375)
                }
                .frame(
                    width: 335 * geometry.size.width / 375,
                    height: 550 * geometry.size.height / 812 // 从491px增加到550px
                )
                .clipped()
                .position(
                    x: (20 + 335/2) * geometry.size.width / 375,
                    y: (140 + 550/2) * geometry.size.height / 812 // 相应调整Y位置
                )
                
                // 内容区域 - 向上移动，考虑安全区域偏移
                VStack {
                    // 顶部留白 - 减少留白让内容上移
                    Rectangle()
                        .fill(Color.clear)
                        .frame(height: geometry.safeAreaInsets.top + 20)
                    
                    Spacer()
                    
                    // 按钮和文本容器 - 向上移动
                    VStack(spacing: 0) {
                        // Start按钮
                        Button(action: {
                            print("Start 3-Day Free Trial button tapped")
                            isTrialPressed = true
                            
                            // 调用月会员创建接口
                            createMonthlySubscription()
                        }) {
                            ZStack {
                                // 线性渐变背景 157度 #66E07A -> #B6E7A5，圆角8px
                                LinearGradient(
                                    gradient: Gradient(colors: [
                                        Color(red: 0x66/255.0, green: 0xE0/255.0, blue: 0x7A/255.0),
                                        Color(red: 0xB6/255.0, green: 0xE7/255.0, blue: 0xA5/255.0)
                                    ]),
                                    startPoint: UnitPoint(x: 0.5 + 0.5 * cos(157 * .pi / 180), y: 0.5 + 0.5 * sin(157 * .pi / 180)),
                                    endPoint: UnitPoint(x: 0.5 - 0.5 * cos(157 * .pi / 180), y: 0.5 - 0.5 * sin(157 * .pi / 180))
                                )
                                .frame(
                                    width: 335 * geometry.size.width / 375,
                                    height: 50 * geometry.size.height / 812
                                )
                                .cornerRadius(8)
                                .opacity(isPurchasing ? 0.6 : 1.0)
                                
                                // 按钮文本或加载指示器
                                if isPurchasing {
                                    HStack(spacing: 8) {
                                        ProgressView()
                                            .progressViewStyle(CircularProgressViewStyle(tint: Color(red: 0x11/255.0, green: 0x5A/255.0, blue: 0x15/255.0)))
                                            .scaleEffect(0.8)
                                        Text("Processing...")
                                            .font(.custom("MyriadPro", size: 18 * min(geometry.size.width / 375, geometry.size.height / 812)))
                                            .fontWeight(.semibold)
                                            .foregroundColor(Color(red: 0x11/255.0, green: 0x5A/255.0, blue: 0x15/255.0))
                                    }
                                } else {
                                Text("Start 3-Day Free Trial - $5.99/month")
                                    .font(.custom("MyriadPro", size: 18 * min(geometry.size.width / 375, geometry.size.height / 812)))
                                    .fontWeight(.semibold)
                                    .foregroundColor(Color(red: 0x11/255.0, green: 0x5A/255.0, blue: 0x15/255.0))
                                    .multilineTextAlignment(.center)
                                    .lineSpacing(5)
                            }
                        }
                        }
                        .disabled(isPurchasing)
                        .scaleEffect(isTrialPressed ? 0.95 : 1.0)
                        .animation(.easeInOut(duration: 0.1), value: isTrialPressed)
                        .onTapGesture {
                            if !isPurchasing {
                            withAnimation(.easeInOut(duration: 0.1)) {
                                isTrialPressed = true
                            }
                            
                            DispatchQueue.main.asyncAfter(deadline: .now() + 0.1) {
                                withAnimation(.easeInOut(duration: 0.1)) {
                                    isTrialPressed = false
                                    }
                                }
                            }
                        }
                        .padding(.bottom, 16 * geometry.size.height / 812)
                        
                        // Skip和Cancel文本行
                        HStack {
                            // Skip for now 文本 - 改为可点击按钮
                            Button(action: {
                                print("Skip for now button tapped")
                                isSkipPressed = true
                                
                                // 跳转到nutrition主页
                                skipToNutrition()
                            }) {
                                Text("Skip for now (limited features)")
                                    .font(.custom("MyriadPro", size: 15 * min(geometry.size.width / 375, geometry.size.height / 812)))
                                    .foregroundColor(Color(red: 0xEF/255.0, green: 0xF8/255.0, blue: 0xEF/255.0))
                                    .frame(maxWidth: 188 * geometry.size.width / 375, alignment: .leading)
                                    .lineSpacing(9)
                            }
                            .scaleEffect(isSkipPressed ? 0.95 : 1.0)
                            .animation(.easeInOut(duration: 0.1), value: isSkipPressed)
                            .onTapGesture {
                                withAnimation(.easeInOut(duration: 0.1)) {
                                    isSkipPressed = true
                                }
                                
                                DispatchQueue.main.asyncAfter(deadline: .now() + 0.1) {
                                    withAnimation(.easeInOut(duration: 0.1)) {
                                        isSkipPressed = false
                                    }
                                }
                            }
                            
                            Spacer()
                            
                            // Cancel anytime 文本
                            Text("Cancel anytime")
                                .font(.custom("MyriadPro", size: 14 * min(geometry.size.width / 375, geometry.size.height / 812)))
                                .foregroundColor(Color(red: 0x0B/255.0, green: 0x4C/255.0, blue: 0x0F/255.0))
                                .frame(maxWidth: 90 * geometry.size.width / 375, alignment: .trailing)
                                .lineSpacing(10)
                        }
                        .padding(.horizontal, 20 * geometry.size.width / 375)
                    }
                    
                    // 底部留白 - 增加留白让内容上移
                    Rectangle()
                        .fill(Color.clear)
                        .frame(height: max(120, geometry.safeAreaInsets.bottom + 80))
                }
            }
        }
        .navigationBarHidden(true)
        .alert("Subscription Failed", isPresented: $showErrorAlert) {
            Button("Retry") {
                createMonthlySubscription()
            }
            Button("Cancel", role: .cancel) { }
        } message: {
            Text(errorMessage)
        }
        .onAppear {
            // 统计
            XDTrackTool.shared.appear("第二欢迎页面")
        }
    }
    
    // 创建月会员订阅 - 使用真实的苹果支付流程
    private func createMonthlySubscription() {
        print("🚀 SecondWelcomeView: 开始月会员订阅购买流程...")
        
        // 设置购买状态
        isPurchasing = true
        
        Task {
            
            // 1. 获取订阅产品列表
            await SubscriptionService.shared.fetchSubscriptionProducts()
            
            guard let monthlyProduct = SubscriptionService.shared.getMonthlyProduct() else {
                print("❌ SecondWelcomeView: 未找到月度订阅产品")
                await MainActor.run {
                    isPurchasing = false
                    errorMessage = "未找到订阅产品，请稍后重试"
                    showErrorAlert = true
                }
                return
            }
            
            print("📱 SecondWelcomeView: 找到月度订阅产品: \(monthlyProduct.name) - \(monthlyProduct.productId)")
            
            // 2. 检查iOS版本支持
            if #available(iOS 15.0, *) {
                // 3. 加载StoreKit产品
                await StoreKitManager.shared.loadProducts()
                
                // 4. 查找对应的StoreKit产品
                guard let storeKitProduct = StoreKitManager.shared.products.first(where: { $0.id == monthlyProduct.productId }) else {
                    print("❌ SecondWelcomeView: 在App Store中未找到产品")
                    await MainActor.run {
                        isPurchasing = false
                        errorMessage = "在App Store中未找到该产品，请检查网络连接后重试"
                        showErrorAlert = true
                    }
                    return
                }
                
                print("🛒 SecondWelcomeView: 发起苹果支付...")
                
                // 5. 发起购买
                let success = await StoreKitManager.shared.purchase(storeKitProduct)
                
                await MainActor.run {
                    isPurchasing = false
                    
                    if success {
                        print("✅ SecondWelcomeView: 订阅购买成功！")
                        
                        // 6. 刷新用户订阅状态
                        Task {
                            await self.userData.checkAndUpdateSubscriptionStatus()
                            
                            // 只有购买成功后才跳转到主页
                            await MainActor.run {
                                self.navigateToMainApp()
                            }
                        }
                    } else {
                        print("❌ SecondWelcomeView: 订阅购买失败或被取消")
                        // 购买失败或取消时，显示错误提示
                        let subscriptionError = SubscriptionService.shared.errorMessage ?? "订阅购买失败或被取消"
                        
                        if subscriptionError.contains("No active account") || subscriptionError.contains("509") {
                            errorMessage = "请在设置 > App Store中登录Apple ID账号"
                        } else if subscriptionError.contains("cancelled") || subscriptionError.contains("取消") {
                            // 用户主动取消，不显示错误提示
                            return
                        } else if subscriptionError.contains("试用期不可用") {
                            // 防洗账号验证失败
                            errorMessage = subscriptionError
                        } else {
                            errorMessage = subscriptionError
                        }
                        
                        showErrorAlert = true
                    }
                }
            } else {
                print("❌ SecondWelcomeView: iOS版本不支持订阅功能")
                await MainActor.run {
                    isPurchasing = false
                    errorMessage = "当前iOS版本不支持订阅功能，请升级到iOS 15.0或更高版本"
                    showErrorAlert = true
                }
            }
        }
    }
    
    // 跳过到nutrition主页
    private func skipToNutrition() {
        print("用户选择跳过，跳转到nutrition主页")
        navigateToMainApp()
    }
    
    // 导航到主应用
    private func navigateToMainApp() {
        // 确保选中的标签是Nutrition页面（索引为0）
        userData.selectedTab = 0
        
        // 通知UI更新
        userData.objectWillChange.send()
        
        // 保存设置到UserDefaults
        userData.saveSettings()
        
        // 清除引导页面标志，让应用显示ContentView
        UserDefaults.standard.set(false, forKey: "showOnboarding")
        UserDefaults.standard.set(false, forKey: "showSecondWelcome")
        
        // 发送通知，导航到Nutrition页面
        NotificationCenter.default.post(name: Notification.Name("NavigateToNutritionView"), object: nil)
        
        print("SecondWelcomeView完成，导航到Nutrition主页")
    }
}

struct SecondWelcomeView_Previews: PreviewProvider {
    static var previews: some View {
        SecondWelcomeView()
            .environmentObject(UserData())
            .previewDevice("iPhone 14")
        
        SecondWelcomeView()
            .environmentObject(UserData())
            .previewDevice("iPhone SE (3rd generation)")
            .previewDisplayName("iPhone SE")
        
        SecondWelcomeView()
            .environmentObject(UserData())
            .previewDevice("iPhone 14 Pro Max")
            .previewDisplayName("iPhone 14 Pro Max")
        
        SecondWelcomeView()
            .environmentObject(UserData())
            .previewDevice("iPhone 14 Pro")
            .previewDisplayName("iPhone 14 Pro (Dynamic Island)")
    }
} 