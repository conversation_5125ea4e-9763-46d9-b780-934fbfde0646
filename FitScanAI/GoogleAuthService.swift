import SwiftUI
import GoogleSignIn

// 谷歌登录协调器类
class GoogleSignInCoordinator: NSObject {
    private var userData: UserData?
    private var shouldSendNotification: Bool
    private var completion: ((Bool) -> Void)?
    
    // 定义Keychain的键名常量
    private enum KeychainKeys {
        static let googleAuthUserId = "googleAuthUserId"
        static let googleAuthIdToken = "googleAuthIdToken"
        static let googleAuthEmail = "googleAuthEmail"
    }
    
    init(userData: UserData? = nil, shouldSendNotification: Bool = true, completion: ((Bool) -> Void)? = nil) {
        self.userData = userData
        self.shouldSendNotification = shouldSendNotification
        self.completion = completion
        super.init()
    }
    
    // 处理谷歌登录结果
    func handleSignInResult(_ result: Result<GIDGoogleUser, Error>) {
        switch result {
        case .success(let user):
            guard let idToken = user.idToken?.tokenString else {
                print("❌ 谷歌登录授权失败：无法获取idToken")
                return
            }
            
            let userId = user.userID ?? ""
            let email = user.profile?.email ?? ""
            let fullName = user.profile?.name ?? ""
            let clientId = "4072cc9e-b4a3-4ab7-bd92-724783a65349"
            
            // 使用Google的CLIENT_ID作为appId，而不是bundleId
            let googleClientId = "412088765631-pb573gf5d0ndk8gnr91r487lip2modlq.apps.googleusercontent.com"
            
            // 将授权数据传递给后端服务器
            print("🌐 谷歌登录授权成功，获取到以下信息:")
            print("👤 User ID: \(userId)")
            print("📧 Email: \(email)")
            print("📝 Full Name: \(fullName)")
            print("🆔 Google Client ID (作为appId): \(googleClientId)")
            print("🔑 ID Token: \(idToken.prefix(20))...(长度:\(idToken.count))")
            print("🔐 Client ID: \(clientId)")
            
            // 保存授权信息到Keychain
            do {
                try KeychainHelper.shared.saveString(userId, forKey: KeychainKeys.googleAuthUserId)
                try KeychainHelper.shared.saveString(idToken, forKey: KeychainKeys.googleAuthIdToken)
                try KeychainHelper.shared.saveString(email, forKey: KeychainKeys.googleAuthEmail)
                print("✅ 谷歌登录信息已成功保存到Keychain")
            } catch {
                print("❌ 保存谷歌登录信息到Keychain失败: \(error)")
            }
            
            // 首先强制重置会话并刷新CSRF令牌，确保每次都能获取新的令牌
            print("🔄 强制重置会话状态并获取新的CSRF令牌")
            NetworkService.shared.resetSessionState()
            
            // 向后端发送请求，执行谷歌登录
            print("🚀 正在向后端发送谷歌登录请求...")
            net_googleLogin(appId: googleClientId, idToken: idToken, clientId: clientId) { [weak self] result in
                // 确保在主线程处理UI更新
                DispatchQueue.main.async {
                    switch result {
                    case .success(let loginData):
                        print("✅ 谷歌登录API请求成功，获取到token: \(loginData.accessToken ?? "无")")
                        
                        // 设置标记，表示这是通过谷歌登录的用户
                        UserDefaults.standard.set(true, forKey: "isGoogleLogin")
                        print("✅ 已将isGoogleLogin设置为true")
                        
                        // 为确保访问令牌一定被保存，总是保存到UserDefaults
                        if let accessToken = loginData.accessToken {
                            UserDefaults.standard.set(accessToken, forKey: "accessToken")
                            print("✅ 已直接保存accessToken到UserDefaults: \(accessToken.prefix(10))...")
                            
                            // 同时保存到Keychain，用于token过期时的自动恢复
                            do {
                                try KeychainHelper.shared.saveString(accessToken, forKey: "googleLoginAccessToken")
                                print("✅ 已将accessToken保存到Keychain")
                            } catch {
                                print("⚠️ 保存accessToken到Keychain失败: \(error)")
                            }
                        }
                        
                        if let refreshToken = loginData.refreshToken {
                            UserDefaults.standard.set(refreshToken, forKey: "refreshToken")
                            print("✅ 已直接保存refreshToken到UserDefaults")
                            
                            // 同时保存到Keychain，用于token过期时的自动恢复
                            do {
                                try KeychainHelper.shared.saveString(refreshToken, forKey: "googleLoginRefreshToken")
                                print("✅ 已将refreshToken保存到Keychain")
                            } catch {
                                print("⚠️ 保存refreshToken到Keychain失败: \(error)")
                            }
                        }
                        
                        // 确保正确保存令牌过期时间
                        if let expiresIn = loginData.expiresIn {
                            let expiresAt = Date().addingTimeInterval(TimeInterval(expiresIn))
                            UserDefaults.standard.set(expiresAt, forKey: "tokenExpiresAt")
                            print("✅ 已设置令牌过期时间: \(expiresAt)")
                        }
                        
                        if let sharedUserData = self?.userData {
                            // 使用传入的共享UserData实例
                            print("✅ 使用共享的userData实例保存登录信息")
                            sharedUserData.setLogin(loginData)
                            
                            // 手动设置登录状态标志
                            UserDefaults.standard.set(true, forKey: "isLoggedIn")
                            print("✅ 已将isLoggedIn设置为true")
                            
                            // 根据shouldSendNotification参数决定是否发送通知
                            if self?.shouldSendNotification == true {
                            NotificationCenter.default.post(name: Notification.Name("UserLoggedIn"), object: nil)
                            print("✅ 已发送UserLoggedIn通知")
                            
                            // 延迟执行后续操作，确保状态已更新
                            DispatchQueue.main.asyncAfter(deadline: .now() + 0.5) {
                                print("✅ 执行登录成功后续操作")
                                sharedUserData.doSthAfterLoginSuccess()
                                    
                                    // 调用completion回调，表示登录成功
                                    self?.completion?(true)
                                }
                            } else {
                                print("⚠️ 根据shouldSendNotification参数，跳过发送UserLoggedIn通知")
                                // 即使不发送通知，也要执行后续操作
                                DispatchQueue.main.asyncAfter(deadline: .now() + 0.5) {
                                    print("✅ 执行登录成功后续操作（无通知模式）")
                                    sharedUserData.doSthAfterLoginSuccess()
                                    
                                    // 调用completion回调，表示登录成功
                                    self?.completion?(true)
                                }
                            }
                        } else {
                            // 创建一个新的UserData实例作为后备方案
                            print("⚠️ 警告：未找到共享的userData实例，创建新实例")
                            let userData = UserData()
                            userData.setLogin(loginData)
                            
                            // 手动设置登录状态标志
                            UserDefaults.standard.set(true, forKey: "isLoggedIn")
                            print("✅ 已将isLoggedIn设置为true")
                            
                            // 根据shouldSendNotification参数决定是否发送通知
                            if self?.shouldSendNotification == true {
                            NotificationCenter.default.post(name: Notification.Name("UserLoggedIn"), object: nil)
                            print("✅ 已发送UserLoggedIn通知")
                            
                            // 延迟执行后续操作，确保状态已更新
                            DispatchQueue.main.asyncAfter(deadline: .now() + 0.5) {
                                print("✅ 执行登录成功后续操作")
                                userData.doSthAfterLoginSuccess()
                                    
                                    // 调用completion回调，表示登录成功
                                    self?.completion?(true)
                                }
                            } else {
                                print("⚠️ 根据shouldSendNotification参数，跳过发送UserLoggedIn通知")
                                // 即使不发送通知，也要执行后续操作
                                DispatchQueue.main.asyncAfter(deadline: .now() + 0.5) {
                                    print("✅ 执行登录成功后续操作（无通知模式）")
                                    userData.doSthAfterLoginSuccess()
                                    
                                    // 调用completion回调，表示登录成功
                                    self?.completion?(true)
                                }
                            }
                        }
                    case .failure(let error):
                        print("❌ 谷歌登录API请求失败: \(error.localizedDescription)")
                        
                        // 保存错误信息到UserDefaults，便于排查
                        UserDefaults.standard.set(error.localizedDescription, forKey: "googleLoginError")
                        
                        // 清除谷歌登录标记
                        UserDefaults.standard.set(false, forKey: "isGoogleLogin")
                        
                        // 调用completion回调，表示登录失败
                        self?.completion?(false)
                    }
                }
            }
            
        case .failure(let error):
            print("❌ 谷歌登录授权失败: \(error.localizedDescription)")
            
            // 保存错误信息到UserDefaults，便于调试
            UserDefaults.standard.set(error.localizedDescription, forKey: "googleAuthError")
            
            // 清除谷歌登录标记
            UserDefaults.standard.set(false, forKey: "isGoogleLogin")
            
            // 调用completion回调，表示登录失败
            self.completion?(false)
        }
    }
}

// 谷歌登录服务类
class GoogleAuthService {
    static let shared = GoogleAuthService()
    
    // 添加一个属性来保持coordinator的引用
    private var activeCoordinator: GoogleSignInCoordinator?
    
    // 初始化谷歌登录配置
    func configureGoogleSignIn() {
        guard let path = Bundle.main.path(forResource: "GoogleService-Info", ofType: "plist"),
              let plist = NSDictionary(contentsOfFile: path),
              let clientID = plist["CLIENT_ID"] as? String else {
            print("❌ 无法找到GoogleService-Info.plist文件或CLIENT_ID")
            
            // 如果没有找到配置文件，使用手动配置
            let manualConfig = GIDConfiguration(clientID: "4072cc9e-b4a3-4ab7-bd92-724783a65349")
            GIDSignIn.sharedInstance.configuration = manualConfig
            print("✅ 使用手动配置初始化谷歌登录")
            return
        }
        
        let config = GIDConfiguration(clientID: clientID)
        GIDSignIn.sharedInstance.configuration = config
        print("✅ 谷歌登录配置成功")
    }
    
    // 开始谷歌登录流程
    func startSignInWithGoogle(userData: UserData? = nil, shouldSendNotification: Bool = true, completion: ((Bool) -> Void)? = nil) {
        print("🌐 开始谷歌登录流程")
        
        // 显示传入的userData实例状态
        if let userDataInstance = userData {
            print("🌐 已传入UserData实例，当前accessToken长度: \(userDataInstance.accessToken.count)")
            print("🌐 UserData实例ID: \(userDataInstance.id)")
        } else {
            print("⚠️ 警告: 未传入UserData实例，将尝试创建新实例")
        }
        
        // 创建并保存coordinator实例
        let coordinator = GoogleSignInCoordinator(userData: userData, shouldSendNotification: shouldSendNotification, completion: completion)
        self.activeCoordinator = coordinator
        print("🌐 创建GoogleSignInCoordinator实例并保存引用")
        
        // 获取当前的根视图控制器
        guard let presentingViewController = getCurrentViewController() else {
            print("❌ 无法获取当前视图控制器")
            return
        }
        
        // 执行谷歌登录
        GIDSignIn.sharedInstance.signIn(withPresenting: presentingViewController) { [weak coordinator] result, error in
            if let error = error {
                coordinator?.handleSignInResult(.failure(error))
            } else if let result = result {
                coordinator?.handleSignInResult(.success(result.user))
            } else {
                coordinator?.handleSignInResult(.failure(NSError(domain: "GoogleAuthError", code: -1, userInfo: [NSLocalizedDescriptionKey: "Unknown error occurred"])))
            }
        }
    }
    
    // 获取当前视图控制器
    private func getCurrentViewController() -> UIViewController? {
        guard let windowScene = UIApplication.shared.connectedScenes.first as? UIWindowScene,
              let window = windowScene.windows.first else {
            return nil
        }
        return window.rootViewController
    }
    
    // 处理URL回调（如果需要）
    func handleURL(_ url: URL) -> Bool {
        return GIDSignIn.sharedInstance.handle(url)
    }
    
    // 登出
    func signOut() {
        GIDSignIn.sharedInstance.signOut()
        
        // 清除本地保存的谷歌登录信息
        UserDefaults.standard.set(false, forKey: "isGoogleLogin")
        
        // 清除Keychain中的信息
        do {
            try KeychainHelper.shared.deleteString(forKey: "googleAuthUserId")
            try KeychainHelper.shared.deleteString(forKey: "googleAuthIdToken")
            try KeychainHelper.shared.deleteString(forKey: "googleAuthEmail")
            try KeychainHelper.shared.deleteString(forKey: "googleLoginAccessToken")
            try KeychainHelper.shared.deleteString(forKey: "googleLoginRefreshToken")
            print("✅ 已清除Keychain中的谷歌登录信息")
        } catch {
            print("⚠️ 清除Keychain中的谷歌登录信息失败: \(error)")
        }
        
        print("✅ 谷歌登录已登出")
    }
} 