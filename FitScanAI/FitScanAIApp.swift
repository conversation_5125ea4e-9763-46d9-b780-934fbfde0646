//
//  FitScanAIApp.swift
//  FitScanAI
//
//  Created by 陈亚宁 on 2025/4/9.
//

import SwiftUI
import Combine
import Photos
import GoogleSignIn
import AppsFlyerLib

// 内联 AppDelegate，避免单独添加文件到 Xcode 工程
class AppDelegate: NSObject, UIApplicationDelegate, AppsFlyerLibDelegate {
    func application(_ application: UIApplication, didFinishLaunchingWithOptions launchOptions: [UIApplication.LaunchOptionsKey : Any]? = nil) -> Bool {
        AppsFlyerLib.shared().isDebug = true
        AppsFlyerLib.shared().delegate = self
        return true
    }

    func applicationDidBecomeActive(_ application: UIApplication) {
        AppsFlyerLib.shared().start { result, error in
            if let error = error {
                print("[AppsFlyer] start error: \(error.localizedDescription)")
            } else {
                print("[AppsFlyer] start completed: \(result ?? [:])")
            }
        }
    }

    func application(_ app: UIApplication, open url: URL, options: [UIApplication.OpenURLOptionsKey : Any] = [:]) -> Bool {
        AppsFlyerLib.shared().handleOpen(url, options: options)
        return true
    }

    func application(_ application: UIApplication, continue userActivity: NSUserActivity, restorationHandler: @escaping ([UIUserActivityRestoring]?) -> Void) -> Bool {
        AppsFlyerLib.shared().continue(userActivity, restorationHandler: nil)
        return true
    }

    // MARK: - AppsFlyerLibDelegate
    func onConversionDataSuccess(_ conversionInfo: [AnyHashable : Any]) {
        print("[AppsFlyer] onConversionDataSuccess: \(conversionInfo)")
    }

    func onConversionDataFail(_ error: Error) {
        print("[AppsFlyer] onConversionDataFail: \(error.localizedDescription)")
    }
}

@main
struct FitScanAIApp: App {
    // 连接 UIKit AppDelegate 以保证 AppsFlyer 生命周期事件正确回调
    @UIApplicationDelegateAdaptor(AppDelegate.self) var appDelegate
    @StateObject private var userData = UserData()
    @Environment(\.scenePhase) private var scenePhase
    @State private var lastActiveDate: Date?
    @State private var isUserLoggedIn: Bool = false
    @State private var isCheckingUserStatus: Bool = false
    @State private var cancellables = Set<AnyCancellable>()
    
    // 添加跳转状态管理
    @State private var navigateToOnboarding: Bool = false
    @State private var navigateToHome: Bool = false
    
    // 添加启动屏幕状态
    @State private var isActive = false
    
    // 添加首次启动检查
    @State private var isFirstLaunch: Bool = false
    @State private var hasCheckedFirstLaunch: Bool = false

    init() {
        XDTrackTool.shared.config()// 统计初始化
    }
    

    
    var body: some Scene {
        WindowGroup {
            if !isActive {
                // 显示启动屏幕
                SplashScreenView(isActive: $isActive)
            } else if !hasCheckedFirstLaunch {
                // 检查首次启动状态
                Color.clear
                    .onAppear {
                        checkFirstLaunch()
                    }
            } else if isFirstLaunch {
                // 首次启动显示欢迎页面
                WelcomeView()
                    .environmentObject(userData)
                    .onReceive(NotificationCenter.default.publisher(for: Notification.Name("WelcomeSkipped"))) { _ in
                        // 点击Skip按钮：结束欢迎页，并直接以游客模式进入主界面
                        isFirstLaunch = false
                        isUserLoggedIn = true
                        print("🚪 欢迎页Skip：直接以游客模式进入主界面")
                    }
                    .onReceive(NotificationCenter.default.publisher(for: Notification.Name("WelcomeGetStarted"))) { _ in
                        // 从CreateAccountView登录成功后，直接进入主页（不再触发新用户检查）
                        isFirstLaunch = false
                        isUserLoggedIn = true
                        print("🔔 CreateAccountView登录成功，直接进入主页（跳过新用户检查）")
                    }
            } else if isUserLoggedIn {
                // 登录后不再显示引导页面，仅根据第二欢迎页标记显示
                if UserDefaults.standard.bool(forKey: "showSecondWelcome") {
                    SecondWelcomeView()
                        .environmentObject(userData)
                } else {
                    ContentView()
                        .environmentObject(userData)
                        .onReceive(NotificationCenter.default.publisher(for: Notification.Name("UserLoggedOut"))) { _ in
                            // 监听登出通知
                            isUserLoggedIn = false
                            print("用户已登出，返回到登录界面")
                        }
                        .onReceive(NotificationCenter.default.publisher(for: Notification.Name("NavigateToHome"))) { _ in
                            // 导航到主页的通知 - 苹果登录时可能会触发
                            print("收到导航到主页的通知")
                            navigateToHome = true
                        }
                        // 登录后忽略任何引导页导航通知
                        .onReceive(NotificationCenter.default.publisher(for: Notification.Name("NavigateToNutritionView"))) { _ in
                            // 导航到Nutrition页面的通知 - 苹果登录时老用户会触发
                            print("收到导航到Nutrition页面的通知，确保用户跳转到Nutrition首页")
                            // 确保用户选中Nutrition标签页（索引0）
                            userData.selectedTab = 0
                            userData.objectWillChange.send()
                        }
                }
            } else {
                LoginView()
                    .environmentObject(userData)
                    .onReceive(NotificationCenter.default.publisher(for: Notification.Name("UserLoggedIn"))) { _ in
                        // 监听登录成功的通知
                        print("🔔 收到用户登录成功通知，更新登录状态")
                        print("🔔 当前isUserLoggedIn状态: \(isUserLoggedIn)")
                        print("🔔 当前userData.accessToken: \(userData.accessToken.isEmpty ? "空" : "有值")")
                        
                        isUserLoggedIn = true
                        
                        // 延迟检查用户状态，确保登录状态已完全设置
                        DispatchQueue.main.asyncAfter(deadline: .now() + 0.5) {
                            print("🔔 开始检查用户是否为新用户")
                            checkIfUserIsNew()
                        }
                    }
                    .onReceive(NotificationCenter.default.publisher(for: Notification.Name("GuestLoggedIn"))) { _ in
                        // 监听访客登录成功的通知
                        print("🚪 收到访客登录通知，设置为已登录状态")
                        isUserLoggedIn = true
                    }
                    .onReceive(NotificationCenter.default.publisher(for: Notification.Name("NavigateToHome"))) { _ in
                        // 如果收到导航到主页的通知，确保用户登录状态为true
                        print("登录页面收到导航到主页的通知")
                        isUserLoggedIn = true
                    }
                    .onReceive(NotificationCenter.default.publisher(for: Notification.Name("ResetFirstLaunch"))) { _ in
                        // 监听重置首次启动的通知（仅用于测试）
                        resetFirstLaunchStatus()
                    }
            }
        }
        .onChange(of: scenePhase) { oldPhase, newPhase in
            switch newPhase {
            case .active:
                // 应用启动时或从后台返回时
                if oldPhase == .background {
                    // 从后台返回时，短暂显示启动屏
                    isActive = false
                    // 短暂延迟后自动激活
                    DispatchQueue.main.asyncAfter(deadline: .now() + 1.5) {
                        isActive = true
                    }
                }

                // 初始化谷歌登录配置
                GoogleAuthService.shared.configureGoogleSignIn()
                
                // 加载必要的数据和状态
                userData.loadLoginState()
                ImageUploadService.shared.cleanupFoodRecordIds()
                checkDateForReset()
                checkLoginState()
                
                // 如果用户已登录，获取用户信息和检查是否为新用户
                if isUserLoggedIn {
                    // 访客模式下跳过API调用
                    if !userData.isGuestMode && !userData.accessToken.isEmpty {
                        UserService.shared.updateUserDataWithFetchedInfo(userData: userData)
                        
                        // 延迟检查用户状态，避免与注册流程冲突
                        DispatchQueue.main.asyncAfter(deadline: .now() + 1.0) {
                            self.checkIfUserIsNew()
                        }
                        
                        // 检查用户计划状态
                        userData.checkPlanStatus()
                        
                        // 检查用户订阅状态
                        Task {
                            await userData.checkAndUpdateSubscriptionStatus()
                        }
                        
                        // 检查账号状态
                        if #available(iOS 15.0, *) {
                            Task {
                                await checkAccountStatus()
                            }
                        }
                    } else if userData.isGuestMode {
                        print("🚪 访客模式，跳过用户状态检查")
                    }
                }
            case .background:
                // 应用进入后台时记录日期
                lastActiveDate = Date()
            default:
                break
            }
        }
    }
    
    // 检查用户是否为新用户
    private func checkIfUserIsNew() {
        // 访客模式下不检查用户状态
        if userData.isGuestMode {
            print("🚪 访客模式，跳过新用户检查")
            return
        }
        
        // 防止重复检查
        if isCheckingUserStatus {
            return
        }
        
        isCheckingUserStatus = true
        
        // 直接调用API检查用户状态，不再依赖本地标志
        UserExtendService.shared.getUserExtendInfo(userData: userData) { result in
            DispatchQueue.main.async {
                self.isCheckingUserStatus = false
                
                switch result {
                case .success(let isNewUser):
                    print("API检查用户状态: 用户是\(isNewUser ? "新用户" : "老用户") (登录后不再显示引导页面)")
                case .failure(let error):
                    print("检查用户状态失败: \(error.localizedDescription)")
                }
            }
        }
    }
    
    // 检查用户登录状态
    private func checkLoginState() {
        // 首先检查是否为访客模式
        if UserDefaults.standard.bool(forKey: "isGuestMode") {
            print("🚪 检测到访客模式")
            userData.isGuestMode = true
            isUserLoggedIn = true  // 访客模式也算"已登录"，可以进入主界面
            print("🚪 访客模式已设置，进入主界面")
            return
        }
        
        // 先检查是否是苹果登录
        if UserDefaults.standard.bool(forKey: "isAppleLogin") {
            print("🍎 检测到苹果登录状态标记")
            
            // 验证苹果登录状态
            userData.validateAppleLoginState()
            
            // 重新检查登录状态
            if UserDefaults.standard.bool(forKey: "isLoggedIn") && !userData.accessToken.isEmpty {
                isUserLoggedIn = true
                print("🍎 苹果登录状态有效，用户已登录")
                return
            }
        }
        
        // 检查是否是谷歌登录
        if UserDefaults.standard.bool(forKey: "isGoogleLogin") {
            print("🌐 检测到谷歌登录状态标记")
            
            // 验证谷歌登录状态
            userData.validateGoogleLoginState()
            
            // 重新检查登录状态
            if UserDefaults.standard.bool(forKey: "isLoggedIn") && !userData.accessToken.isEmpty {
                isUserLoggedIn = true
                print("🌐 谷歌登录状态有效，用户已登录")
                return
            }
        }
        
        // 非苹果登录或苹果登录无效，继续检查常规登录状态
        if UserDefaults.standard.bool(forKey: "isLoggedIn") {
            // 如果标志为true，进一步检查令牌
            if !userData.accessToken.isEmpty && !userData.isTokenExpired() {
                isUserLoggedIn = true
                print("检测到有效访问令牌，用户已登录")
            } else {
                // 令牌无效，需要重置登录状态
                resetLoginState()
            }
        } else {
            // 未找到登录标志，检查是否有令牌（兼容旧版本）
            if !userData.accessToken.isEmpty && !userData.isTokenExpired() {
                // 有有效令牌但没有登录标志，设置登录状态
                isUserLoggedIn = true
                UserDefaults.standard.set(true, forKey: "isLoggedIn")
                print("检测到有效访问令牌但无登录标志，已设置登录状态")
            } else {
                // 没有令牌或令牌无效，保持未登录状态
                isUserLoggedIn = false
                print("用户未登录，将显示登录页面")
            }
        }
    }
    
    // 重置登录状态
    private func resetLoginState() {
        print("访问令牌无效，重置登录状态")
        isUserLoggedIn = false
        userData.accessToken = ""
        userData.refreshToken = ""
        userData.tokenExpiresAt = nil
        UserDefaults.standard.removeObject(forKey: "accessToken")
        UserDefaults.standard.removeObject(forKey: "refreshToken")
        UserDefaults.standard.removeObject(forKey: "tokenExpiresAt")
        UserDefaults.standard.set(false, forKey: "isLoggedIn")
        
        // 清理旧版本兼容数据
        if let _ = UserDefaults.standard.string(forKey: "userEmail") {
            print("清理旧版本登录数据")
            UserDefaults.standard.removeObject(forKey: "userEmail")
            UserDefaults.standard.removeObject(forKey: "userNickname")
            UserDefaults.standard.removeObject(forKey: "userName")
        }
    }
    
    // 检查日期并在需要时重置数据
    private func checkDateForReset() {
        let calendar = Calendar.current
        let today = calendar.startOfDay(for: Date())
        
        if let lastDate = lastActiveDate {
            let lastActiveDay = calendar.startOfDay(for: lastDate)
            
            // 如果上次活跃日期与当前日期不同，则重置数据
            if !calendar.isDate(lastActiveDay, inSameDayAs: today) {
                // 重置每日早晚体重数据为0，保留打卡记录
                userData.resetDailyWeight()
                print("日期已更改，已重置每日体重数据")
            }
        }
        
        // 更新最后活跃日期
        lastActiveDate = today
    }
    
    // 添加新方法检查账号状态
    @available(iOS 15.0, *)
    private func checkAccountStatus() async {
        let hasAccount = await StoreKitManager.shared.checkAccountStatus()
        if !hasAccount {
            print("⚠️ App: 建议用户登录Apple ID账号以使用订阅功能")
        }
    }
    
    // 检查是否首次启动
    private func checkFirstLaunch() {
        let hasLaunchedBefore = UserDefaults.standard.bool(forKey: "hasLaunchedBefore")
        
        if !hasLaunchedBefore {
            // 首次启动
            isFirstLaunch = true
            UserDefaults.standard.set(true, forKey: "hasLaunchedBefore")
            print("首次启动应用，显示欢迎页面")
        } else {
            // 不是首次启动
            isFirstLaunch = false
            print("非首次启动，跳过欢迎页面")
        }
        
        hasCheckedFirstLaunch = true
    }
    
    // 调试方法：重置首次启动状态（仅用于测试）
    private func resetFirstLaunchStatus() {
        UserDefaults.standard.removeObject(forKey: "hasLaunchedBefore")
        isFirstLaunch = false
        hasCheckedFirstLaunch = false
        print("🔄 已重置首次启动状态，重新启动应用将显示欢迎页面")
    }
} 
