import Foundation

class ExerciseAPIService {
    static let shared = ExerciseAPIService()
    
    // API基础URL
    private let baseURL = "https://fsai.pickgoodspro.com"
    
    // API端点
    private let createRecordEndpoint = "/ns/app/calorie-records/"
    private let getUserRecordsEndpoint = "/ns/app/calorie-records/user"
    
    private init() {}
    
    // 创建运动记录的请求模型
    struct ExerciseRecordRequest: Codable {
        let exerciseItem: String
        let intensity: String
        let hours: Int
        let minutes: Int
        let dateStr: String
        let createdTime: String // 添加创建时间字段
    }
    
    // 运动记录响应模型
    struct ExerciseRecordResponse: Codable, Identifiable {
        let id: Int?
        let dateStr: String
        let planId: Int?
        let currentWeight: Double?
        let exerciseItem: String
        let intensity: String
        let hours: Int // 修改为Int类型，因为API返回的是数字
        let minutes: Int // 修改为Int类型，因为API返回的是数字
        let calories: Double
        let createdTime: String? // 添加创建时间字段
    }
    
    // API响应基础结构
    struct APIBoolResponse: Codable {
        let code: Int
        let message: String
        let data: Bool?
        let timestamp: Int64?
    }
    
    struct APIRecordsListResponse: Codable {
        let code: Int
        let message: String
        let data: [ExerciseRecordResponse]?
        let timestamp: Int64?
    }
    
    // 创建运动记录
    func createExerciseRecord(userData: UserData, exerciseItem: String, intensity: String, hours: Int, minutes: Int, date: Date, completion: @escaping (Result<Bool, Error>) -> Void) {
        // 检查用户是否登录
        guard !userData.accessToken.isEmpty else {
            // 确保在主线程上调用回调
            DispatchQueue.main.async {
                completion(.failure(NSError(domain: "ExerciseAPIService", code: 401, userInfo: [NSLocalizedDescriptionKey: "User not logged in, unable to create exercise record"])))
            }
            return
        }
        
        // 格式化日期
        let dateStr = formatDate(date)
        
        // 获取当前时间的格式化字符串 - 使用与API匹配的格式
        let timeFormatter = DateFormatter()
        timeFormatter.dateFormat = "yyyy-MM-dd'T'HH:mm:ss"
        timeFormatter.locale = Locale(identifier: "en_US_POSIX")
        let currentTimeStr = timeFormatter.string(from: Date())
        
        // 创建请求对象
        let mappedIntensity = mapIntensity(intensity)
        print("🔧 [运动强度映射] UI强度: '\(intensity)' → API强度: '\(mappedIntensity)'")
        
        let request = ExerciseRecordRequest(
            exerciseItem: exerciseItem,
            intensity: mappedIntensity,
            hours: hours,
            minutes: minutes,
            dateStr: dateStr,
            createdTime: currentTimeStr // 使用与API匹配的时间格式
        )
        
        // 创建URL
        guard let url = URL(string: baseURL + createRecordEndpoint) else {
            // 确保在主线程上调用回调
            DispatchQueue.main.async {
                completion(.failure(NSError(domain: "ExerciseAPIService", code: 400, userInfo: [NSLocalizedDescriptionKey: "Invalid URL"])))
            }
            return
        }
        
        print("🏃‍♂️ [创建运动记录] 发送请求 - 项目: \(exerciseItem), 强度: \(intensity), 时间: \(hours)小时\(minutes)分钟, 日期: \(dateStr)")
        
        // 创建请求
        var urlRequest = URLRequest(url: url)
        urlRequest.httpMethod = "POST"
        urlRequest.addValue("application/json", forHTTPHeaderField: "Content-Type")
        urlRequest.addValue("Bearer \(userData.accessToken)", forHTTPHeaderField: "Authorization")
        
        // 编码请求体
        do {
            let jsonData = try JSONEncoder().encode(request)
            urlRequest.httpBody = jsonData
            if let jsonString = String(data: jsonData, encoding: .utf8) {
                print("📤 [创建运动记录] 请求体: \(jsonString)")
            }
        } catch {
            print("❌ [创建运动记录] 序列化请求体失败: \(error.localizedDescription)")
            // 确保在主线程上调用回调
            DispatchQueue.main.async {
                completion(.failure(error))
            }
            return
        }
        
        // 发送请求
        let task = URLSession.shared.dataTask(with: urlRequest) { data, response, error in
            // 处理网络错误
            if let error = error {
                print("❌ [创建运动记录] 网络错误: \(error.localizedDescription)")
                // 确保在主线程上调用回调
                DispatchQueue.main.async {
                    completion(.failure(error))
                }
                return
            }
            
            // 检查HTTP响应
            guard let httpResponse = response as? HTTPURLResponse else {
                print("❌ [创建运动记录] 无效的HTTP响应")
                // 确保在主线程上调用回调
                DispatchQueue.main.async {
                    completion(.failure(NSError(domain: "ExerciseAPIService", code: 0, userInfo: [NSLocalizedDescriptionKey: "Invalid HTTP response"])))
                }
                return
            }
            
            print("🔢 [创建运动记录] HTTP状态码: \(httpResponse.statusCode)")
            
            // 处理HTTP错误
            if !(200...299).contains(httpResponse.statusCode) {
                var errorMessage = "Server returned error status code: \(httpResponse.statusCode)"
                
                if let data = data, let responseString = String(data: data, encoding: .utf8) {
                    print("❌ [创建运动记录] 错误响应: \(responseString)")
                    errorMessage += "\nResponse: \(responseString)"
                }
                
                // 确保在主线程上调用回调
                DispatchQueue.main.async {
                    completion(.failure(NSError(domain: "ExerciseAPIService", code: httpResponse.statusCode, userInfo: [NSLocalizedDescriptionKey: errorMessage])))
                }
                return
            }
            
            // 检查响应数据
            guard let data = data else {
                print("❌ [创建运动记录] 服务器没有返回数据")
                // 确保在主线程上调用回调
                DispatchQueue.main.async {
                    completion(.failure(NSError(domain: "ExerciseAPIService", code: 0, userInfo: [NSLocalizedDescriptionKey: "Server returned no data"])))
                }
                return
            }
            
            // 打印响应数据
            if let responseString = String(data: data, encoding: .utf8) {
                print("📥 [创建运动记录] 响应: \(responseString)")
            }
            
            // 解析响应数据
            do {
                let apiResponse = try JSONDecoder().decode(APIBoolResponse.self, from: data)
                
                if apiResponse.code == 0, let success = apiResponse.data, success {
                    print("✅ [创建运动记录] 创建成功")
                    
                    // 创建成功后，获取最新的运动记录
                    DispatchQueue.main.async {
                        self.getUserExerciseRecords(userData: userData, date: date) { recordsResult in
                            switch recordsResult {
                            case .success(let records):
                                print("✅ [创建运动记录] 已获取最新运动记录: \(records.count)条")
                                
                                // 确保在主线程上发送通知
                                DispatchQueue.main.async {
                                    // 发送通知，让UI组件刷新
                                    // 注意：移除这个通知可能会破坏其他依赖它的功能
                                    // 因此保留通知，但在PlanView中优化处理方式
                                    NotificationCenter.default.post(name: Notification.Name("ExerciseDataUpdated"), object: nil)
                                }
                                
                            case .failure(let error):
                                print("⚠️ [创建运动记录] 获取最新记录失败: \(error.localizedDescription)")
                            }
                            
                            // 无论如何返回创建成功
                            completion(.success(true))
                        }
                    }
                } else {
                    let errorMessage = apiResponse.message.isEmpty ? "Failed to create exercise record" : apiResponse.message
                    print("❌ [创建运动记录] API错误: \(errorMessage)")
                    // 确保在主线程上调用回调
                    DispatchQueue.main.async {
                        completion(.failure(NSError(domain: "ExerciseAPIService", code: apiResponse.code, userInfo: [NSLocalizedDescriptionKey: errorMessage])))
                    }
                }
            } catch {
                print("❌ [创建运动记录] 解析响应失败: \(error)")
                // 确保在主线程上调用回调
                DispatchQueue.main.async {
                    completion(.failure(error))
                }
            }
        }
        
        task.resume()
    }
    
    // 获取用户运动记录
    func getUserExerciseRecords(userData: UserData, date: Date?, completion: @escaping (Result<[ExerciseRecordResponse], Error>) -> Void) {
        // 检查用户是否登录
        guard !userData.accessToken.isEmpty else {
            // 确保在主线程上调用回调
            DispatchQueue.main.async {
                completion(.failure(NSError(domain: "ExerciseAPIService", code: 401, userInfo: [NSLocalizedDescriptionKey: "User not logged in, unable to get exercise records"])))
            }
            return
        }
        
        // 创建基础URL
        var urlComponents = URLComponents(string: baseURL + getUserRecordsEndpoint)
        
        // 如果提供了日期参数，添加查询参数
        if let date = date {
            let dateStr = formatDate(date)
            urlComponents?.queryItems = [URLQueryItem(name: "dateStr", value: dateStr)]
            print("📅 [获取运动记录] 指定日期: \(dateStr)")
        } else {
            print("📅 [获取运动记录] 未指定日期，获取所有记录")
        }
        
        // 创建URL
        guard let url = urlComponents?.url else {
            // 确保在主线程上调用回调
            DispatchQueue.main.async {
                completion(.failure(NSError(domain: "ExerciseAPIService", code: 400, userInfo: [NSLocalizedDescriptionKey: "无效的URL"])))
            }
            return
        }
        
        // 创建请求
        var urlRequest = URLRequest(url: url)
        urlRequest.httpMethod = "GET"
        urlRequest.addValue("Bearer \(userData.accessToken)", forHTTPHeaderField: "Authorization")
        
        print("🔍 [获取运动记录] 发送请求: \(url.absoluteString)")
        
        // 发送请求
        let task = URLSession.shared.dataTask(with: urlRequest) { data, response, error in
            // 处理网络错误
            if let error = error {
                print("❌ [获取运动记录] 网络错误: \(error.localizedDescription)")
                completion(.failure(error))
                return
            }
            
            // 检查HTTP响应
            guard let httpResponse = response as? HTTPURLResponse else {
                print("❌ [获取运动记录] 无效的HTTP响应")
                completion(.failure(NSError(domain: "ExerciseAPIService", code: 0, userInfo: [NSLocalizedDescriptionKey: "无效的HTTP响应"])))
                return
            }
            
            print("🔢 [获取运动记录] HTTP状态码: \(httpResponse.statusCode)")
            
            // 处理HTTP错误
            if !(200...299).contains(httpResponse.statusCode) {
                if let data = data, let responseString = String(data: data, encoding: .utf8) {
                    print("❌ [获取运动记录] 错误响应: \(responseString)")
                }
                
                completion(.failure(NSError(domain: "ExerciseAPIService", code: httpResponse.statusCode, userInfo: [NSLocalizedDescriptionKey: "Server returned error status code: \(httpResponse.statusCode)"])))
                return
            }
            
            // 检查响应数据
            guard let data = data, !data.isEmpty else {
                print("⚠️ [获取运动记录] 服务器返回空数据")
                // 确保在主线程上调用回调
                DispatchQueue.main.async {
                    completion(.success([]))
                }
                return
            }
            
            // 打印响应数据
            if let responseString = String(data: data, encoding: .utf8) {
                print("📥 [获取运动记录] 响应: \(responseString)")
            }
            
            // 解析响应数据
            do {
                let apiResponse = try JSONDecoder().decode(APIRecordsListResponse.self, from: data)
                
                if apiResponse.code == 0, let records = apiResponse.data {
                    print("✅ [获取运动记录] 成功获取\(records.count)条记录")
                    
                    // 将获取的运动记录转换为本地数据结构
                    // 在主线程中更新本地数据和回调
                    DispatchQueue.main.async {
                        // 更新UI
                        self.updateLocalExerciseRecords(userData: userData, apiRecords: records)
                        completion(.success(records))
                    }
                } else {
                    // API返回成功但没有数据
                    if apiResponse.code == 0 {
                        print("ℹ️ [获取运动记录] API返回成功但无数据")
                        // 确保在主线程上调用回调
                        DispatchQueue.main.async {
                            completion(.success([]))
                        }
                    } else {
                        let errorMessage = apiResponse.message.isEmpty ? "Failed to get exercise records" : apiResponse.message
                        print("❌ [获取运动记录] API错误: \(errorMessage)")
                        // 确保在主线程上调用回调
                        DispatchQueue.main.async {
                            completion(.failure(NSError(domain: "ExerciseAPIService", code: apiResponse.code, userInfo: [NSLocalizedDescriptionKey: errorMessage])))
                        }
                    }
                }
            } catch {
                print("❌ [获取运动记录] 解析响应失败: \(error)")
                // 确保在主线程上调用回调
                DispatchQueue.main.async {
                    completion(.failure(error))
                }
            }
        }
        
        task.resume()
    }
    
    // 更新本地运动记录
    private func updateLocalExerciseRecords(userData: UserData, apiRecords: [ExerciseRecordResponse]) {
        print("🔄 [本地数据] 开始更新本地运动记录...")
        
        // 创建新的ExerciseRecord数组
        var newRecords: [ExerciseRecord] = []
        
        // 将API返回的记录转换为本地ExerciseRecord对象
        for apiRecord in apiRecords {
            // 解析日期
            let dateFormatter = DateFormatter()
            dateFormatter.dateFormat = "yyyyMMdd"
            
            let date: Date
            if let parsedDate = dateFormatter.date(from: apiRecord.dateStr) {
                date = parsedDate
            } else {
                // 如果日期解析失败，使用当前日期
                date = Date()
                print("⚠️ [本地数据] 日期解析失败: \(apiRecord.dateStr)，使用当前日期")
            }
            
            // 解析创建时间
            var timeString = "12:00" // 默认值保持不变
            if let createdTimeStr = apiRecord.createdTime, !createdTimeStr.isEmpty {
                // 尝试多种时间格式进行解析
                var parsedDate: Date? = nil
                
                // 1. 尝试ISO 8601格式
                let isoFormatter = ISO8601DateFormatter()
                // 使用标准配置，不添加额外选项，以匹配最基本的ISO格式
                if let date = isoFormatter.date(from: createdTimeStr) {
                    parsedDate = date
                    print("✅ [本地数据] 使用标准ISO格式成功解析时间: \(createdTimeStr)")
                } 
                // 2. 尝试带有毫秒的ISO格式
                else if createdTimeStr.contains(".") {
                    let dateFormatter = DateFormatter()
                    dateFormatter.dateFormat = "yyyy-MM-dd'T'HH:mm:ss.SSSZ"
                    dateFormatter.locale = Locale(identifier: "en_US_POSIX")
                    if let date = dateFormatter.date(from: createdTimeStr) {
                        parsedDate = date
                        print("✅ [本地数据] 使用带毫秒的ISO格式成功解析时间: \(createdTimeStr)")
                    }
                }
                // 3. 尝试不带时区的ISO格式
                else {
                    let dateFormatter = DateFormatter()
                    dateFormatter.dateFormat = "yyyy-MM-dd'T'HH:mm:ss"
                    dateFormatter.locale = Locale(identifier: "en_US_POSIX")
                    if let date = dateFormatter.date(from: createdTimeStr) {
                        parsedDate = date
                        print("✅ [本地数据] 使用不带时区的ISO格式成功解析时间: \(createdTimeStr)")
                    }
                }
                
                // 4. 尝试普通日期时间格式（如果前面都失败了）
                if parsedDate == nil {
                    let normalFormatter = DateFormatter()
                    normalFormatter.dateFormat = "yyyy-MM-dd HH:mm:ss"
                    normalFormatter.locale = Locale(identifier: "en_US_POSIX")
                    if let date = normalFormatter.date(from: createdTimeStr) {
                        parsedDate = date
                        print("✅ [本地数据] 使用普通日期时间格式成功解析时间: \(createdTimeStr)")
                    }
                }
                
                // 如果成功解析了日期，提取时间部分
                if let parsedDate = parsedDate {
                    let timeFormatter = DateFormatter()
                    timeFormatter.dateFormat = "HH:mm"
                    timeString = timeFormatter.string(from: parsedDate)
                    print("✅ [本地数据] 最终解析出的时间: \(timeString)")
                } else {
                    // 5. 最后尝试直接从字符串提取时间部分
                    if let timeComponent = createdTimeStr.split(separator: "T").last?.split(separator: ":").prefix(2),
                       timeComponent.count >= 2 {
                        timeString = "\(timeComponent[0]):\(timeComponent[1])"
                        print("✅ [本地数据] 通过字符串拆分提取时间: \(timeString)")
                    } else {
                        print("⚠️ [本地数据] 所有解析方法均失败，使用默认时间: \(createdTimeStr)")
                    }
                }
            } else {
                print("⚠️ [本地数据] 创建时间为空，使用默认时间")
            }
            
            // 计算总时长为分钟
            let duration = apiRecord.hours * 60 + apiRecord.minutes
            
            // 创建新的ExerciseRecord
            let record = ExerciseRecord(
                type: apiRecord.exerciseItem,
                duration: duration,
                intensity: mapIntensityFromAPI(apiRecord.intensity),
                calories: Int(apiRecord.calories),
                date: date,
                timeString: timeString,
                id: UUID(),
                createdAt: Date()
            )
            
            newRecords.append(record)
        }
        
        print("📊 [本地数据] 已转换\(newRecords.count)条API记录为本地记录")
        
        // 确保在主线程上更新UserData
        DispatchQueue.main.async {
            // 清空并更新UserData中的运动记录
            userData.exerciseRecords = newRecords
            userData.saveSettings()
            
            print("💾 [本地数据] 本地运动记录已更新")
        }
    }
    
    // 日期格式化工具方法
    private func formatDate(_ date: Date) -> String {
        let formatter = DateFormatter()
        formatter.dateFormat = "yyyyMMdd"
        return formatter.string(from: date)
    }
    
    // 运动强度映射 - 从UI到API
    private func mapIntensity(_ intensity: String) -> String {
        switch intensity.lowercased() {
        case "light", "low", "轻度":
            return "LOW"
        case "moderate", "medium", "中度":
            return "MEDIUM"
        case "intense", "high", "高度":
            return "HIGH"
        default:
            return "MEDIUM" // 默认中度强度
        }
    }
    
    // 运动强度映射 - 从API到UI
    private func mapIntensityFromAPI(_ intensity: String) -> String {
        switch intensity.uppercased() {
        case "LOW":
            return "Light"
        case "MEDIUM":
            return "Moderate"
        case "HIGH":
            return "Intense"
        default:
            return "Moderate" // 默认中度强度
        }
    }
    
    // 获取当前时间字符串
    private func getCurrentTimeString() -> String {
        let dateFormatter = DateFormatter()
        dateFormatter.dateFormat = "HH:mm"
        dateFormatter.locale = Locale(identifier: "en_US_POSIX")
        return dateFormatter.string(from: Date())
    }
    
    // 简单的运动卡路里计算，仅在API调用失败时使用
    func calculateCalories(type: String, duration: Int, intensity: String) -> Int {
        // 基础卡路里消耗率，每分钟卡路里
        var baseRate: Double = 5
        
        // 根据运动类型调整基础消耗率
        switch type.lowercased() {
        case "running":
            baseRate = 10
        case "walking":
            baseRate = 5
        case "cycling":
            baseRate = 8
        case "swimming":
            baseRate = 12
        case "weight training", "gym":
            baseRate = 7
        case "yoga":
            baseRate = 4
        case "jump rope":
            baseRate = 11
        case "basketball":
            baseRate = 9
        case "climbing":
            baseRate = 10
        default:
            baseRate = 6
        }
        
        // 根据强度调整
        var intensityMultiplier: Double = 1.0
        switch intensity.lowercased() {
        case "light", "low":
            intensityMultiplier = 0.8
        case "moderate", "medium":
            intensityMultiplier = 1.0
        case "intense", "high":
            intensityMultiplier = 1.3
        default:
            intensityMultiplier = 1.0
        }
        
        // 计算总卡路里
        let calories = Int(Double(duration) * baseRate * intensityMultiplier)
        return calories
    }
}

// 为了兼容现有代码，提供一个旧API的包装函数
extension ExerciseAPIService {
    // 旧的接口兼容，保留格式但使用新的API
    func saveExerciseRecord(type: String, duration: Int, intensity: String, completion: @escaping (ExerciseRecord) -> Void) {
        
        // 将时长转换为小时和分钟
        let hours = duration / 60
        let minutes = duration % 60
        
        // 获取当前时间字符串（小时:分钟）
        let currentTimeString = getCurrentTimeString()
        
        // 调用新的API - 注意这里使用环境中的userData实例，而不是shared静态属性
        if let userData = findUserDataInEnvironment() {
            createExerciseRecord(
                userData: userData,
                exerciseItem: type,
                intensity: intensity,
                hours: hours,
                minutes: minutes,
                date: Date()
            ) { result in
                switch result {
                case .success(_):
                    print("通过兼容层创建运动记录成功")
                    
                    // 创建一个本地记录用于回调
                    let record = ExerciseRecord(
                        type: type,
                        duration: duration,
                        intensity: intensity,
                        calories: self.calculateCalories(type: type, duration: duration, intensity: intensity),
                        date: Date(),
                        timeString: currentTimeString, // 使用当前真实时间
                        id: UUID(),
                        createdAt: Date()
                    )
                    
                    // 确保在主线程上调用回调
                    DispatchQueue.main.async {
                        completion(record)
                    }
                    
                case .failure(let error):
                    print("通过兼容层创建运动记录失败: \(error.localizedDescription)")
                    
                    // 创建一个本地记录用于回调
                    let record = ExerciseRecord(
                        type: type,
                        duration: duration,
                        intensity: intensity,
                        calories: self.calculateCalories(type: type, duration: duration, intensity: intensity),
                        date: Date(),
                        timeString: currentTimeString, // 使用当前真实时间
                        id: UUID(),
                        createdAt: Date()
                    )
                    
                    // 仍然调用回调以保持兼容性，确保在主线程上执行
                    DispatchQueue.main.async {
                        completion(record)
                    }
                }
            }
        } else {
            // 如果无法获取userData实例，创建本地记录并回调
            print("⚠️ 无法获取UserData实例，使用本地记录")
            let record = ExerciseRecord(
                type: type,
                duration: duration,
                intensity: intensity,
                calories: self.calculateCalories(type: type, duration: duration, intensity: intensity),
                date: Date(),
                timeString: currentTimeString, // 使用当前真实时间
                id: UUID(),
                createdAt: Date()
            )
            
            // 确保在主线程上调用回调
            DispatchQueue.main.async {
                completion(record)
            }
        }
    }
    
    // 尝试从环境中找到UserData实例
    private func findUserDataInEnvironment() -> UserData? {
        // 以下方式是可以尝试获取UserData实例的几种方法
        
        // 方法1: 尝试从UserData本身获取实例
        // 如果在UserData中添加了一个shared静态属性，可以直接使用
        // 但根据错误信息，目前UserData并没有shared属性
        
        // 方法2: 从用户默认值创建一个新实例
        // 这是一个临时解决方案，创建一个新的UserData实例
        return UserData() 
        
        // 方法3: 如果有全局或静态访问点，可以直接访问
        // 如果应用中有其他方式获取UserData实例，应该使用该方式
    }
} 