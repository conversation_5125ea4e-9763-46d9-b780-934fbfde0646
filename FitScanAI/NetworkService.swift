import Foundation
import UIKit

#if canImport(FoundationNetworking)
import FoundationNetworking
#endif

// 邮箱注册请求模型
struct EmailRegisterRequest: Codable {
    let email: String
    let password: String
    let code: String
    let clientType: String = "APP"
    let identity: String
    let loginType: String = "NS"  // 修改为固定字符串"NS"
    let nickname: String
    let namespace: String = "fsai"
    let userspace: String = "fsai"
    
    // 可选字段，根据API文档
    let activity: String? = nil
    let activityCode: String? = nil
    let departments: [Int]? = nil
    let description: String? = nil
    let id: Int? = nil
    let mobile: String? = nil
    let userGroups: [Int]? = nil
    
    enum CodingKeys: String, CodingKey {
        case email, password, code, clientType, identity, loginType, nickname, namespace, userspace
        case activity, activityCode, departments, description, id, mobile, userGroups
    }
    
    init(email: String, password: String, code: String, nickname: String) {
        self.email = email
        self.password = password
        self.code = code
        self.identity = email  // 使用邮箱作为identity
        self.nickname = nickname   // 使用fullName作为nickname
    }
}

// 发送验证码请求模型
struct SendCodeRequest: Codable {
    let identity: String
    let loginType: String
    let namespace: String
    let userspace: String
    /// 用户名
    let username: String?
    
    enum CodingKeys: String, CodingKey {
        case identity, loginType, namespace, userspace, username
    }
    
    init(identity: String, loginType: String = "NS", namespace: String = "fsai", userspace: String = "fsai", username: String? = nil) {
        self.identity = identity
        self.loginType = loginType
        self.namespace = namespace
        self.userspace = userspace
        self.username = username
    }
}

// API响应通用模型
struct APIResponse<T: Codable>: Codable {
    let status: Int?
    let message: String?
    let data: T?
}

// 认证状态
enum AuthState: String, Codable {
    case certified
    case invalid
    case pending
    case unCertificate
    case update
}

// 用户类型
enum UserType: String, Codable {
    case organization
    case personal
}

// 鉴权用户视图模型
struct UserViewModel: Codable {
    let id: Int?
    let email: String?
    let name: String?
    let avatar: String?
    let mobile: String?
    let active: Bool?
    let hasPassword: Bool?
    let state: AuthState?
    let type: UserType?
    let username: String?
    let nickname: String?
}

// 验证码响应模型
struct VerificationCodeResponse: Codable {
    let identity: String?
    let expiredAt: String?
    let validated: Bool?
    let code: String?
    let scene: String?
    let sequence: String?
    let validatedTime: String?
    
    // 将ISO 8601日期字符串转换为Date对象
    var expiredAtDate: Date? {
        guard let expiredAt = expiredAt else { return nil }
        let formatter = DateFormatter()
        formatter.dateFormat = "yyyy-MM-dd HH:mm:ss"
        return formatter.date(from: expiredAt)
    }
    
    var validatedTimeDate: Date? {
        guard let validatedTime = validatedTime else { return nil }
        let formatter = ISO8601DateFormatter()
        return formatter.date(from: validatedTime)
    }
}

// 定义LoginType枚举
enum LoginType: String, Codable {
    case lawyer
    case ns
    case subuser
    case user
    
    // 提供一个静态属性，始终返回NS
    static var defaultType: LoginType {
        return .ns
    }
}

// 验证码请求通用模型（用于发送第二步验证码和验证验证码）
struct ValidateCodeRequest: Codable {
    // 必填字段
    let identity: String
    let code: String
    let loginType: String = "NS"  // 修改为固定字符串"NS"
    let namespace: String
    let userspace: String
    
    // 可选字段
    let username: String?
    let password: String?
    let accountId: String?
    let loginCode: String?
    let messageSender: String?
    
    enum CodingKeys: String, CodingKey {
        case identity, code, loginType, namespace, userspace, username, password, accountId, loginCode, messageSender
    }
    
    init(identity: String, code: String, namespace: String = "fsai", userspace: String = "fsai") {
        self.identity = identity
        self.code = code
        self.namespace = namespace
        self.userspace = userspace
        self.username = nil
        self.password = nil
        self.accountId = nil
        self.loginCode = nil
        self.messageSender = nil
    }
}

// 验证码验证响应模型（适配新的API响应格式）
struct ValidateCodeResponse: Codable {
    let isLogin: Bool?
    let needLogin: Bool?
    let validated: Bool?
    let email: String?
    let validatedTime: String?
    let expiredTime: String?
}

// 验证码验证响应模型
struct ValidateResponse: Codable {
    let count: Int?
    let createdBy: String?
    let createdDate: String?
    let deleted: Int?
    let deleteDate: String?
    let description: String?
    let id: Int?
    let lastTime: String?
    let modifiedBy: String?
    let modifiedDate: String?
    let status: String?
    
    // 将日期字符串转换为Date对象
    var createdDateObj: Date? {
        guard let dateStr = createdDate else { return nil }
        let formatter = ISO8601DateFormatter()
        return formatter.date(from: dateStr)
    }
    
    var lastTimeObj: Date? {
        guard let dateStr = lastTime else { return nil }
        let formatter = ISO8601DateFormatter()
        return formatter.date(from: dateStr)
    }
    
    // 验证是否成功
    var isSuccess: Bool {
        return status == "success"
    }
}

// 注册响应模型
struct RegisterResponse: Codable {
    let loginType: String?
    let userId: String?
    let username: String?
    let mobile: String?
    let email: String?
}

// 直接登录响应结构体
struct DirectLoginResponse: Codable {
    let success: Bool?
    let code: Int?
    let message: String?
    let data: DirectLoginData?
    
    // 处理错误响应的附加字段
    let timestamp: String?
    let status: Int?
    let error: String?
    let path: String?
    
    var isSuccess: Bool {
        return success ?? false
    }
    
    var errorMessage: String {
        if let message = message {
            return message
        } else if let error = error {
            return error
        } else {
            return "未知错误"
        }
    }
    
    var statusCode: Int {
        return code ?? status ?? 0
    }
}

// 直接登录响应数据
struct DirectLoginData: Codable {
    let accessToken: String?
    let tokenType: String?
    let refreshToken: String?
    let expiresIn: Int?
    let scope: String?
    let userId: String?
    let userType: String?
    let userName: String?
    let userInfo: UserInfo?
    
    struct UserInfo: Codable {
        let id: String?
        let name: String?
        let nickname: String?
        let email: String?
        let phone: String?
        let avatar: String?
        // 根据实际API返回添加其他需要的字段
        
        // 添加自定义初始化方法
        init(id: String?, name: String?, nickname: String?, email: String?, phone: String?, avatar: String?) {
            self.id = id
            self.name = name
            self.nickname = nickname
            self.email = email
            self.phone = phone
            self.avatar = avatar
        }
    }
    
    // 添加自定义初始化方法，方便从JSON字典手动创建实例
    init(accessToken: String?, tokenType: String?, refreshToken: String?, expiresIn: Int?, 
         scope: String?, userId: String?, userType: String?, userName: String?, userInfo: UserInfo?) {
        self.accessToken = accessToken
        self.tokenType = tokenType
        self.refreshToken = refreshToken
        self.expiresIn = expiresIn
        self.scope = scope
        self.userId = userId
        self.userType = userType
        self.userName = userName
        self.userInfo = userInfo
    }
}

// 忘记密码发送验证码请求模型
struct ForgotPasswordSendCodeRequest: Codable {
    let loginType: String
    let identity: String
    let messageSender: String?
    let namespace: String
    let userspace: String
    
    enum CodingKeys: String, CodingKey {
        case loginType, identity, messageSender, namespace, userspace
    }
    
    init(identity: String, loginType: String = "NS", messageSender: String? = nil, namespace: String = "fsai", userspace: String = "fsai") {
        self.identity = identity
        self.loginType = loginType
        self.messageSender = messageSender
        self.namespace = namespace
        self.userspace = userspace
    }
}

// 忘记密码执行重置请求模型
struct ForgotPasswordResetRequest: Codable {
    let loginType: String
    let identity: String
    let code: String
    let username: String?
    let password: String
    let accountId: String?
    let loginCode: String?
    let messageSender: String?
    let namespace: String
    let userspace: String
    
    enum CodingKeys: String, CodingKey {
        case loginType, identity, code, username, password, accountId, loginCode, messageSender, namespace, userspace
    }
    
    init(identity: String, code: String, password: String, 
         loginType: String = "NS", 
         username: String? = nil, accountId: String? = nil, 
         loginCode: String? = nil, messageSender: String? = nil,
         namespace: String = "fsai", userspace: String = "fsai") {
        self.identity = identity
        self.code = code
        self.password = password
        self.loginType = loginType
        self.username = username
        self.accountId = accountId
        self.loginCode = loginCode
        self.messageSender = messageSender
        self.namespace = namespace
        self.userspace = userspace
    }
}



// 用户模型，用于解析密码重置和用户信息API的响应
struct User: Codable {
    let id: Int?
    let type: String?
    let userspace: String?
    let identity: String?
    let username: String?
    let mobile: String?
    let email: String?
    let name: String?
    let nickname: String?
    let avatar: String?
    let state: String?
    let special: Bool?
    let clientType: String?
    let createdDate: String?
    let active: Bool?
    
    enum CodingKeys: String, CodingKey {
        case id, type, userspace, identity, username, mobile, email, name, nickname, avatar, state, special, clientType, createdDate, active
    }
}

// Apple登录请求模型
struct AppleLoginRequest: Codable {
    let userId: String
    let identityToken: String
    let authorizationCode: String
    let bundleId: String
    let loginType: String
    let namespace: String
    let userspace: String
    let clientId: String
    
    enum CodingKeys: String, CodingKey {
        case userId, identityToken, authorizationCode, bundleId, loginType, namespace, userspace, clientId
    }
    
    init(userId: String, identityToken: String, authorizationCode: String) {
        self.userId = userId
        self.identityToken = identityToken
        self.authorizationCode = authorizationCode
        // 使用应用的实际Bundle ID
        self.bundleId = Bundle.main.bundleIdentifier ?? "com.fitscanai.app"
        self.loginType = "NS"
        self.namespace = "fsai"
        self.userspace = "fsai"
        self.clientId = "4072cc9e-b4a3-4ab7-bd92-724783a65349"
    }
}

// 刷新Token请求模型
struct RefreshTokenRequest: Codable {
    let loginType: String
    let userspace: String
    let clientId: String
    let authorityScope: String
    let teamId: String?
    let deptId: String?
    let switchUserId: String?
    let refreshToken: String
    let scopes: [String]?
    let clientType: String
    let activity: String?
    let activityCode: String?
    
    enum CodingKeys: String, CodingKey {
        case loginType, userspace, clientId, authorityScope, teamId, deptId, switchUserId, refreshToken, scopes, clientType, activity, activityCode
    }
    
    init(refreshToken: String) {
        self.refreshToken = refreshToken
        self.loginType = "NS"  // 与登录时保持一致
        self.userspace = "fsai"
        self.clientId = "4072cc9e-b4a3-4ab7-bd92-724783a65349"
        self.authorityScope = "user"
        self.teamId = nil
        self.deptId = nil
        self.switchUserId = nil
        self.scopes = nil
        self.clientType = "APP"
        self.activity = nil
        self.activityCode = nil
    }
}

// 刷新Token响应模型
struct RefreshTokenResponse: Codable {
    let accessToken: String?
    let tokenType: String?
    let refreshToken: String?
    let expiresIn: Int?
    let scope: String?
    let userId: String?
    let userType: String?
    let userName: String?
    
    enum CodingKeys: String, CodingKey {
        case accessToken, tokenType, refreshToken, expiresIn, scope, userId, userType, userName
    }
}

// 网络服务类
class NetworkService {
    static let shared = NetworkService()
    
    private let baseURL = "https://fsai-auth.pickgoodspro.com/api"
    
    // HTTP请求头信息
    private let headers: [String: String] = [
        "X-CSRF-TOKEN": "29afa5f6-ccfa-44d2-b4d6-6bdd98c7ebb6", // 默认Token，但会被刷新覆盖
        "Content-Type": "application/json",
        "Accept": "*/*",
        "Host": "fsai-auth.pickgoodspro.com",
        "Connection": "keep-alive",
        "Cookie": "JSESSIONID=649D78A6D655503161761FE2A7A6E150; tgw_l7_route=5f4f12d20b9b450e602a4f2ad371ca95"
    ]
    
    // 保存会话相关信息
    private var jsessionId: String?     // 提取的JSESSIONID值
    private var csrfToken: String?      // CSRF-Token值
    
    // 添加一个单独的锁，用于异步获取CSRF-Token
    private let csrfTokenLock = DispatchSemaphore(value: 1)
    
    private init() {
        // 初始化时获取一次CSRF-Token
        refreshCSRFToken { _ in }
    }
    
    // 重置会话状态
    public func resetSessionState() {
        resetSessionState { _ in }
    }
    
    // 重置会话状态（带回调）
    public func resetSessionState(completion: @escaping (Bool) -> Void) {
        // 使用信号量确保线程安全
        csrfTokenLock.wait()
        
        // 重置所有会话相关状态
        csrfToken = nil
        jsessionId = nil
        
        csrfTokenLock.signal()
        
        print("🔄 已重置NetworkService会话状态，下次请求将重新获取CSRF-Token")
        
        // 清除URLSession缓存，确保完全重置
        URLSession.shared.configuration.urlCache?.removeAllCachedResponses()
        
        // 主动刷新一次CSRF Token，确保立即可用
        refreshCSRFToken { success in
            if success {
                print("✅ 成功获取新的CSRF-Token，会话状态已完全重置")
                completion(true)
            } else {
                print("⚠️ 获取新的CSRF-Token失败，将在下次请求时再次尝试")
                
                // 如果获取失败，立即再次尝试获取
                self.refreshCSRFToken { retrySuccess in
                    if !retrySuccess {
                        // 如果第二次尝试也失败，设置默认值确保功能可用
                        DispatchQueue.main.async {
                            self.csrfToken = "29afa5f6-ccfa-44d2-b4d6-6bdd98c7ebb6"
                            self.jsessionId = "649D78A6D655503161761FE2A7A6E150"
                            print("🔧 已设置默认CSRF-Token和JSESSIONID，确保基本功能可用")
                            completion(true)
                        }
                    } else {
                        print("✅ 重试后成功获取新的CSRF-Token")
                        completion(true)
                    }
                }
            }
        }
    }
    
    // 强制重置会话状态（用于删除账号后的完全清理）
    public func forceResetSessionState() {
        print("🧹 开始强制重置会话状态...")
        
        // 使用信号量确保线程安全
        csrfTokenLock.wait()
        
        // 重置所有会话相关状态
        csrfToken = nil
        jsessionId = nil
        
        csrfTokenLock.signal()
        
        // 清除所有可能的缓存和Cookie
        URLSession.shared.configuration.urlCache?.removeAllCachedResponses()
        HTTPCookieStorage.shared.removeCookies(since: Date.distantPast)
        
        print("🧹 已清除所有缓存和Cookie")
        
        // 立即获取新的CSRF Token和JSESSIONID
        refreshCSRFToken { [weak self] success in
            if success {
                print("✅ 强制重置后成功获取新的CSRF-Token")
            } else {
                print("⚠️ 强制重置后获取CSRF-Token失败，设置默认值")
                
                // 确保有可用的token
                DispatchQueue.main.async {
                    self?.csrfToken = "29afa5f6-ccfa-44d2-b4d6-6bdd98c7ebb6"
                    self?.jsessionId = "649D78A6D655503161761FE2A7A6E150"
                    print("🔧 已设置默认CSRF-Token和JSESSIONID")
                }
            }
            
            // 发送会话重置完成通知
            DispatchQueue.main.async {
                NotificationCenter.default.post(name: Notification.Name("SessionStateReset"), object: nil)
                print("📣 已发送SessionStateReset通知")
            }
        }
    }
    
    // 获取CSRF-Token的方法
    private func refreshCSRFToken(completion: @escaping (Bool) -> Void) {
        let endpoint = "https://fsai-auth.pickgoodspro.com/csrf-token"
        
        guard let url = URL(string: endpoint) else {
            print("无效的CSRF-Token URL")
            completion(false)
            return
        }
        
        var request = URLRequest(url: url, timeoutInterval: Double.infinity)
        request.httpMethod = "GET"
        
        // 不使用已有的JSESSIONID，让服务器创建新的会话
        // 这确保获取全新的CSRF-Token和JSESSIONID配对
        request.addValue("tgw_l7_route=5f4f12d20b9b450e602a4f2ad371ca95", forHTTPHeaderField: "Cookie")
        
        request.setValue("*/*", forHTTPHeaderField: "Accept")
        request.setValue("fsai-auth.pickgoodspro.com", forHTTPHeaderField: "Host")
        request.setValue("keep-alive", forHTTPHeaderField: "Connection")
        
        print("🔄 请求新的CSRF-Token，不使用旧的JSESSIONID以确保获取匹配的会话")
        
        let task = URLSession.shared.dataTask(with: request) { [weak self] data, response, error in
            if let error = error {
                print("获取CSRF-Token失败: \(error.localizedDescription)")
                completion(false)
                return
            }
            
            if let httpResponse = response as? HTTPURLResponse {
                print("CSRF-Token API HTTP状态码: \(httpResponse.statusCode)")
                
                // 使用统一的方法更新会话信息
                self?.updateSessionFromResponse(httpResponse)
            }
            
            guard let data = data else {
                print("获取CSRF-Token无响应数据")
                completion(false)
                return
            }
            
            // 尝试解析响应获取CSRF-Token
            if let responseString = String(data: data, encoding: .utf8) {
                print("原始CSRF-Token响应: \(responseString)")
                
                // 方法1: 尝试解析JSON
                do {
                    if let json = try JSONSerialization.jsonObject(with: data, options: []) as? [String: Any] {
                        if let token = json["token"] as? String {
                            self?.csrfToken = token
                            print("成功从JSON提取CSRF-Token: \(token)")
                            completion(true)
                            return
                        }
                    }
                } catch {
                    print("JSON解析错误: \(error.localizedDescription)")
                }
                
                // 方法2: 尝试使用正则表达式
                do {
                    let pattern = "\"token\":\"([^\"]+)\""
                    let regex = try NSRegularExpression(pattern: pattern)
                    if let match = regex.firstMatch(in: responseString, range: NSRange(responseString.startIndex..., in: responseString)),
                       match.numberOfRanges > 1,
                       let tokenRange = Range(match.range(at: 1), in: responseString) {
                        
                        let token = String(responseString[tokenRange])
                        self?.csrfToken = token
                        print("成功从正则表达式提取CSRF-Token: \(token)")
                        completion(true)
                        return
                    }
                } catch {
                    print("正则表达式错误: \(error.localizedDescription)")
                }
                
                // 方法3: 简单的字符串处理
                if responseString.contains("token") {
                    let tokenParts = responseString.components(separatedBy: "\"token\":\"")
                    if tokenParts.count > 1, let endIndex = tokenParts[1].firstIndex(of: "\"") {
                        let token = tokenParts[1][..<endIndex]
                        self?.csrfToken = String(token)
                        print("成功从字符串处理提取CSRF-Token: \(token)")
                        completion(true)
                        return
                    }
                }
                
                // 如果响应中直接包含一个UUID格式的令牌（没有JSON包装）
                let uuidPattern = "[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}"
                if let regex = try? NSRegularExpression(pattern: uuidPattern, options: .caseInsensitive),
                   let match = regex.firstMatch(in: responseString, range: NSRange(responseString.startIndex..., in: responseString)),
                   let matchRange = Range(match.range, in: responseString) {
                    
                    let token = String(responseString[matchRange])
                    self?.csrfToken = token
                    print("成功从UUID模式提取CSRF-Token: \(token)")
                    completion(true)
                    return
                }
            }
            
            print("无法从响应中提取CSRF-Token")
            completion(false)
        }
        
        task.resume()
    }
    
    // 在请求前确保CSRF-Token有效
    private func ensureCSRFToken(forceRefresh: Bool = false, retryCount: Int = 0, completion: @escaping (Bool) -> Void) {
        // 最多重试2次
        if retryCount >= 2 {
            print("CSRF-Token获取失败，已达到最大重试次数")
            
            // 如果重试失败但已有默认token，仍然可以尝试继续
            if self.csrfToken == nil {
                self.csrfToken = "29afa5f6-ccfa-44d2-b4d6-6bdd98c7ebb6"
                print("使用默认CSRF-Token: 29afa5f6-ccfa-44d2-b4d6-6bdd98c7ebb6")
            }
            
            // 如果重试失败但已有默认JSESSIONID，仍然可以尝试继续
            if self.jsessionId == nil {
                self.jsessionId = "649D78A6D655503161761FE2A7A6E150"
                print("使用默认JSESSIONID: 649D78A6D655503161761FE2A7A6E150")
            }
            
            completion(true) // 使用默认值继续
            return
        }
        
        // 如果不是强制刷新且已经有CSRF-Token，检查是否需要重新获取
        if !forceRefresh && csrfToken != nil && jsessionId != nil {
            print("已有CSRF-Token: \(csrfToken!) 和 JSESSIONID: \(jsessionId!)")
            completion(true)
            return
        }
        
        print("🔄 需要获取新的CSRF-Token和JSESSIONID配对 (强制刷新: \(forceRefresh))")
        
        // 使用信号量确保同一时间只有一个线程在更新CSRF-Token
        csrfTokenLock.wait()
        
        // 刷新Token
        refreshCSRFToken { [weak self] success in
            self?.csrfTokenLock.signal()
            
            if success {
                print("✅ CSRF-Token和JSESSIONID获取成功")
                completion(true)
            } else {
                // 如果获取失败，稍等后重试
                print("CSRF-Token获取失败，正在重试...(第\(retryCount + 1)次)")
                DispatchQueue.global().asyncAfter(deadline: .now() + 0.5) {
                    self?.ensureCSRFToken(forceRefresh: forceRefresh, retryCount: retryCount + 1, completion: completion)
                }
            }
        }
    }
    
    // 从HTTP响应中更新会话信息
    private func updateSessionFromResponse(_ httpResponse: HTTPURLResponse) {
        // 检查Set-Cookie头中的JSESSIONID
        for (headerKey, headerValue) in httpResponse.allHeaderFields {
            if let key = headerKey as? String,
               key.lowercased() == "set-cookie",
               let value = headerValue as? String,
               value.contains("JSESSIONID") {
                
                print("发现Set-Cookie头中的JSESSIONID: \(value)")
                
                // 使用正则表达式提取JSESSIONID值
                let pattern = "JSESSIONID=([^;]+)"
                if let regex = try? NSRegularExpression(pattern: pattern),
                   let match = regex.firstMatch(in: value, range: NSRange(value.startIndex..., in: value)),
                   match.numberOfRanges > 1,
                   let valueRange = Range(match.range(at: 1), in: value) {
                    
                    let newJsessionId = String(value[valueRange])
                    let oldJsessionId = self.jsessionId
                    self.jsessionId = newJsessionId
                    print("🔄 已更新JSESSIONID: 从 \(oldJsessionId ?? "无") 更新为 \(newJsessionId)")
                }
            }
        }
    }

    // 为请求添加必要的头信息
    private func addHeaders(to request: inout URLRequest) {
        // 添加基本头信息，不包括CSRF-Token和Cookie
        for (key, value) in headers {
            if key != "X-CSRF-TOKEN" && key != "Cookie" {
                request.addValue(value, forHTTPHeaderField: key)
            }
        }
        
        // 添加CSRF-Token（如果有）
        if let token = csrfToken {
            request.addValue(token, forHTTPHeaderField: "X-CSRF-TOKEN")
            print("添加CSRF-Token到请求头: \(token)")
        } else {
            // 使用默认的CSRF-Token，参考登录的实际请求代码
            request.addValue("29afa5f6-ccfa-44d2-b4d6-6bdd98c7ebb6", forHTTPHeaderField: "X-CSRF-TOKEN")
            print("使用默认CSRF-Token: 29afa5f6-ccfa-44d2-b4d6-6bdd98c7ebb6")
        }
        
        // 添加JSESSIONID（如果有）
        if let sessionId = jsessionId {
            request.addValue("JSESSIONID=\(sessionId); tgw_l7_route=5f4f12d20b9b450e602a4f2ad371ca95", forHTTPHeaderField: "Cookie")
            print("✅ 添加最新JSESSIONID到请求头: \(sessionId)")
        } else {
            // 使用默认的Cookie，参考登录的实际请求代码
            request.addValue("JSESSIONID=649D78A6D655503161761FE2A7A6E150; tgw_l7_route=5f4f12d20b9b450e602a4f2ad371ca95", forHTTPHeaderField: "Cookie")
            print("⚠️ 使用默认JSESSIONID: 649D78A6D655503161761FE2A7A6E150")
        }
        
        // 添加调试日志
        #if DEBUG
        print("完整请求头: \(request.allHTTPHeaderFields ?? [:])")
        #endif
    }
    
    // 发送验证码
    func sendVerificationCode(email: String, completion: @escaping (Result<VerificationCodeResponse, Error>) -> Void) {
        // 强制刷新CSRF-Token和JSESSIONID，确保会话同步
        ensureCSRFToken(forceRefresh: true) { [weak self] success in
            guard let self = self else { return }
            
            if !success {
                print("警告: 无法获取CSRF-Token，继续尝试发送验证码")
            }
            
            print("🔐 发送验证码使用的会话信息 - CSRF-Token: \(self.csrfToken ?? "无"), JSESSIONID: \(self.jsessionId ?? "无")")
            
            let endpoint = "\(self.baseURL)/email-register/j/send-code"
            
            guard let url = URL(string: endpoint) else {
                completion(.failure(NSError(domain: "InvalidURL", code: 0, userInfo: nil)))
                return
            }
            
            let sendCodeRequest = SendCodeRequest(identity: email, username: nil) // 如果有用户名可以在这里传入
            
            var request = URLRequest(url: url)
            request.httpMethod = "POST"
            
            // 添加必要的头信息
            self.addHeaders(to: &request)
            
            do {
                let jsonData = try JSONEncoder().encode(sendCodeRequest)
                request.httpBody = jsonData
                
                print("发送验证码请求参数: \(String(data: jsonData, encoding: .utf8) ?? "")")
                
                let task = URLSession.shared.dataTask(with: request) { data, response, error in
                    if let error = error {
                        DispatchQueue.main.async {
                            print("网络错误: \(error.localizedDescription)")
                            completion(.failure(error))
                        }
                        return
                    }
                    
                    if let httpResponse = response as? HTTPURLResponse {
                        print("验证码API HTTP状态码: \(httpResponse.statusCode)")
                        
                        // 打印完整响应头，帮助调试
                        print("响应头: \(httpResponse.allHeaderFields)")
                        
                        // 更新JSESSIONID - 即使是错误响应也要更新会话ID
                        self.updateSessionFromResponse(httpResponse)
                        
                        // 如果状态码不是2xx，返回错误
                        if !(200...299).contains(httpResponse.statusCode) {
                            if let data = data, let responseString = String(data: data, encoding: .utf8) {
                                print("错误响应内容: \(responseString)")
                            }
                            
                            DispatchQueue.main.async {
                                let errorMessage = "服务器返回错误码: \(httpResponse.statusCode)"
                                completion(.failure(NSError(domain: "HTTPError", code: httpResponse.statusCode, userInfo: [NSLocalizedDescriptionKey: errorMessage])))
                            }
                            return
                        }
                    }
                    
                    guard let data = data else {
                        DispatchQueue.main.async {
                            completion(.failure(NSError(domain: "NoData", code: 0, userInfo: nil)))
                        }
                        return
                    }
                    
                    // 打印响应内容，便于调试
                    if let responseString = String(data: data, encoding: .utf8) {
                        print("验证码API响应: \(responseString)")
                    }
                    
                    do {
                        // 先尝试直接解析为VerificationCodeResponse
                        let directResponse = try JSONDecoder().decode(VerificationCodeResponse.self, from: data)
                        DispatchQueue.main.async {
                            completion(.success(directResponse))
                        }
                    } catch {
                        print("直接解析为VerificationCodeResponse失败: \(error)")
                        
                        // 尝试解析为APIResponse<VerificationCodeResponse>
                        do {
                            let response = try JSONDecoder().decode(APIResponse<VerificationCodeResponse>.self, from: data)
                            
                            DispatchQueue.main.async {
                                if let responseData = response.data {
                                    completion(.success(responseData))
                                } else if let message = response.message {
                                    completion(.failure(NSError(domain: "APIError", code: response.status ?? 400, userInfo: [NSLocalizedDescriptionKey: message])))
                                } else {
                                    completion(.failure(NSError(domain: "UnknownError", code: 0, userInfo: nil)))
                                }
                            }
                        } catch {
                            DispatchQueue.main.async {
                                print("APIResponse<VerificationCodeResponse>解析失败: \(error)")
                                
                                // 如果所有解析方法都失败，尝试创建一个基于响应字符串的模拟对象
                                if let responseString = String(data: data, encoding: .utf8),
                                   responseString.contains("identity") && responseString.contains("expiredAt") {
                                    // 创建一个基本的响应对象
                                    let mockResponse = VerificationCodeResponse(
                                        identity: email,
                                        expiredAt: nil,
                                        validated: false,
                                        code: nil,
                                        scene: nil,
                                        sequence: nil,
                                        validatedTime: nil
                                    )
                                    completion(.success(mockResponse))
                                } else {
                                    completion(.failure(error))
                                }
                            }
                        }
                    }
                }
                
                task.resume()
            } catch {
                completion(.failure(error))
            }
        }
    }
    
    // 注册用户
    func registerUser(nickname: String, email: String, password: String, code: String, completion: @escaping (Result<RegisterResponse, Error>) -> Void) {
        // 注册时不强制刷新CSRF-Token，使用发送验证码时获取的相同会话
        // 这确保了验证码和注册使用相同的JSESSIONID
        ensureCSRFToken(forceRefresh: false) { [weak self] success in
            guard let self = self else { return }
            
            if !success {
                print("警告: 无法获取CSRF-Token，继续尝试注册用户")
            }
            
            print("🔐 注册用户使用的会话信息 - CSRF-Token: \(self.csrfToken ?? "无"), JSESSIONID: \(self.jsessionId ?? "无")")
            
            // 验证会话一致性
            if self.jsessionId == nil {
                print("⚠️ 警告：注册时JSESSIONID为空，这可能导致验证码验证失败")
            }
            
            let endpoint = "https://fsai-auth.pickgoodspro.com/api/email-register"
            
            guard let url = URL(string: endpoint) else {
                completion(.failure(NSError(domain: "InvalidURL", code: 0, userInfo: nil)))
                return
            }
            
            let registerRequest = EmailRegisterRequest(email: email, password: password, code: code, nickname: nickname)
            
            var request = URLRequest(url: url)
            request.httpMethod = "POST"
            
            // 添加所有必要的头信息
            self.addHeaders(to: &request)
            
            do {
                let jsonData = try JSONEncoder().encode(registerRequest)
                request.httpBody = jsonData
                
                print("注册请求参数: \(String(data: jsonData, encoding: .utf8) ?? "")")
                
                let task = URLSession.shared.dataTask(with: request) { data, response, error in
                    if let error = error {
                        DispatchQueue.main.async {
                            completion(.failure(error))
                        }
                        return
                    }
                    
                    if let httpResponse = response as? HTTPURLResponse {
                        print("注册API HTTP状态码: \(httpResponse.statusCode)")
                        
                        // 如果状态码不是2xx，返回错误
                        if !(200...299).contains(httpResponse.statusCode) {
                            if let data = data, let responseString = String(data: data, encoding: .utf8) {
                                print("注册错误响应内容: \(responseString)")
                                
                                // 检查是否是账号已注册的错误
                                if responseString.contains("已注册") || responseString.contains("already registered") || responseString.contains("already exists") {
                                    DispatchQueue.main.async {
                                        let error = NSError(domain: "RegisterError", code: 409, userInfo: [NSLocalizedDescriptionKey: "This email address is already registered"])
                                        completion(.failure(error))
                                    }
                                    return
                                }
                            }
                            
                            DispatchQueue.main.async {
                                let errorMessage = "服务器返回错误码: \(httpResponse.statusCode)"
                                completion(.failure(NSError(domain: "HTTPError", code: httpResponse.statusCode, userInfo: [NSLocalizedDescriptionKey: errorMessage])))
                            }
                            return
                        }
                    }
                    
                    guard let data = data else {
                        DispatchQueue.main.async {
                            completion(.failure(NSError(domain: "NoData", code: 0, userInfo: nil)))
                        }
                        return
                    }
                    
                    // 打印响应内容，便于调试
                    if let responseString = String(data: data, encoding: .utf8) {
                        print("注册API响应: \(responseString)")
                    }
                    
                    do {
                        // 尝试解析为RegisterResponse
                        let registerResponse = try JSONDecoder().decode(RegisterResponse.self, from: data)
                        DispatchQueue.main.async {
                            completion(.success(registerResponse))
                        }
                    } catch {
                        print("注册响应解析失败: \(error)")
                        
                        // 如果解析失败，尝试检查响应内容是否表示成功
                        if let responseString = String(data: data, encoding: .utf8) {
                            if responseString.contains("success") || responseString.contains("成功") {
                                                                 // 创建一个模拟的成功响应
                                 let mockResponse = RegisterResponse(
                                     loginType: "NS",
                                     userId: nil,
                                     username: nickname,
                                     mobile: nil,
                                     email: email
                                 )
                                DispatchQueue.main.async {
                                    completion(.success(mockResponse))
                                }
                                return
                            }
                            
                            // 检查是否是账号已注册的错误
                            if responseString.contains("已注册") || responseString.contains("already registered") || responseString.contains("already exists") {
                        DispatchQueue.main.async {
                                    let error = NSError(domain: "RegisterError", code: 409, userInfo: [NSLocalizedDescriptionKey: "This email address is already registered"])
                            completion(.failure(error))
                                }
                                return
                            }
                        }
                        
                        DispatchQueue.main.async {
                            completion(.failure(error))
                        }
                    }
                }
                
                task.resume()
            } catch {
                completion(.failure(error))
            }
        }
    }
    
    
    #if DEBUG
    // Debug辅助方法：测试SendCodeRequest的JSON编码
    func testSendCodeRequestEncoding() {
        let email = "<EMAIL>"
        let request = SendCodeRequest(identity: email)
        do {
            let jsonData = try JSONEncoder().encode(request)
            if let jsonString = String(data: jsonData, encoding: .utf8) {
                print("编码后的SendCodeRequest JSON: \(jsonString)")
            }
        } catch {
            print("编码SendCodeRequest失败: \(error)")
        }
    }

    // 测试验证码请求编码
    func testValidateCodeRequestEncoding() {
        let email = "<EMAIL>"
        let code = "123456"
        let request = ValidateCodeRequest(identity: email, code: code)
        do {
            let jsonData = try JSONEncoder().encode(request)
            if let jsonString = String(data: jsonData, encoding: .utf8) {
                print("编码后的ValidateCodeRequest JSON: \(jsonString)")
            }
        } catch {
            print("编码ValidateCodeRequest失败: \(error)")
        }
    }
    
    // 测试直接登录接口
    func testDirectLogin(email: String, password: String) {
        print("测试直接登录接口 - 开始")
        directLogin(email: email, password: password) { result in
            switch result {
            case .success(let loginData):
                print("直接登录成功!")
                print("用户名: \(loginData.userName ?? "未知用户名")")
                print("用户ID: \(loginData.userId ?? "未知ID")")
                print("Access Token: \(loginData.accessToken ?? "未获取到Token")")
            case .failure(let error):
                print("直接登录失败: \(error.localizedDescription)")
            }
        }
    }

    // 测试API连接状态
    func testAPIConnection() {
        print("正在测试API连接状态...")
        
        // 首先检查基本URL是否可连接
        let endpoint = "https://fsai-auth.pickgoodspro.com"
        
        guard let url = URL(string: endpoint) else {
            print("URL无效")
            return
        }
        
        var request = URLRequest(url: url)
        request.httpMethod = "GET"
        
        let task = URLSession.shared.dataTask(with: request) { data, response, error in
            if let error = error {
                print("连接错误: \(error.localizedDescription)")
                return
            }
            
            if let httpResponse = response as? HTTPURLResponse {
                print("API基本连接HTTP状态码: \(httpResponse.statusCode)")
                
                if (200...299).contains(httpResponse.statusCode) {
                    print("API基本连接成功")
                } else {
                    print("API基本连接失败，状态码: \(httpResponse.statusCode)")
                }
            }
            
            // 测试登录API
            self.testLoginAPI()
        }
        
        task.resume()
    }
    
    // 测试登录API
    private func testLoginAPI() {
        print("正在测试登录API...")
        
        let endpoint = "https://fsai-auth.pickgoodspro.com/direct/login"
        
        guard let url = URL(string: endpoint) else {
            print("登录URL无效")
            return
        }
        
        // 创建一个测试请求
        var request = URLRequest(url: url)
        request.httpMethod = "OPTIONS" // 使用OPTIONS方法检查API支持的方法
        
        let task = URLSession.shared.dataTask(with: request) { data, response, error in
            if let error = error {
                print("登录API连接错误: \(error.localizedDescription)")
                return
            }
            
            if let httpResponse = response as? HTTPURLResponse {
                print("登录API HTTP状态码: \(httpResponse.statusCode)")
                print("登录API 响应头: \(httpResponse.allHeaderFields)")
                
                if let allowMethods = httpResponse.allHeaderFields["Allow"] as? String {
                    print("支持的HTTP方法: \(allowMethods)")
                }
                
                if (200...299).contains(httpResponse.statusCode) {
                    print("登录API连接成功")
                } else {
                    print("登录API连接失败，状态码: \(httpResponse.statusCode)")
                }
            }
        }
        
        task.resume()
    }

    // 打印所有API端点
    func printAPIEndpoints() {
        print("=== NetworkService API端点 ===")
        print("发送验证码: \(baseURL)/email-register/j/send-code")
        print("验证验证码: \(baseURL)/email-register/j/validate")
        print("发送第二步验证码: \(baseURL)/email-register/j/send-next")
        print("注册用户: \(baseURL)/email-register")
        print("直接登录: https://fsai-auth.pickgoodspro.com/direct/login")
        print("==========================")
    }
    #endif



    // 直接登录方法
    func directLogin(email: String, password: String, completion: @escaping (Result<DirectLoginData, Error>) -> Void) {
        // 登录时总是重新获取CSRF-Token，确保使用最新的会话
        refreshCSRFToken { [weak self] success in
            guard let self = self else { return }
            
            if !success {
                print("警告: 无法获取CSRF-Token，继续尝试登录")
            }
            
            // 继续登录流程
            self.performLogin(email: email, password: password, completion: completion)
        }
    }
    
    // 实际执行登录请求的方法
    private func performLogin(email: String, password: String, completion: @escaping (Result<DirectLoginData, Error>) -> Void) {
        // 后端提供的API端点，保持与其他接口一致的baseURL
        let endpoint = "https://fsai-auth.pickgoodspro.com/direct/app/login"
        
        guard let url = URL(string: endpoint) else {
            completion(.failure(NSError(domain: "InvalidURL", code: 0, userInfo: nil)))
            return
        }
        
        // 创建请求参数字符串，去掉验证码参数
        let parametersArray = [
            "loginType=NS",
            "clientId=4072cc9e-b4a3-4ab7-bd92-724783a65349",
            "authorityScope=user",
            "username=\(email.addingPercentEncoding(withAllowedCharacters: .urlQueryAllowed) ?? email)",
            "password=\(password.addingPercentEncoding(withAllowedCharacters: .urlQueryAllowed) ?? password)",
            "namespace=fsai",
            "userspace=fsai"
        ]
        
        let parameters = parametersArray.joined(separator: "&")
        let postData = parameters.data(using: .utf8)
        
        print("登录请求参数: \(parameters)")
        
        // 创建请求
        var request = URLRequest(url: url, timeoutInterval: Double.infinity)
        request.httpMethod = "POST"
        
        // 添加Content-Type头，确保使用正确的格式
        request.setValue("application/x-www-form-urlencoded", forHTTPHeaderField: "Content-Type")
        
        // 添加CSRF-Token头
        if let token = csrfToken {
            request.setValue(token, forHTTPHeaderField: "X-CSRF-TOKEN")
            print("添加CSRF-Token到登录请求: \(token)")
        } else {
            // 使用默认的CSRF-Token，参考登录的实际请求代码
            request.setValue("29afa5f6-ccfa-44d2-b4d6-6bdd98c7ebb6", forHTTPHeaderField: "X-CSRF-TOKEN")
            print("使用默认CSRF-Token: 29afa5f6-ccfa-44d2-b4d6-6bdd98c7ebb6")
        }
        
        // 添加Cookie头，使用之前验证码请求获取的JSESSIONID
        if let sessionId = jsessionId {
            request.setValue("JSESSIONID=\(sessionId); tgw_l7_route=5f4f12d20b9b450e602a4f2ad371ca95", forHTTPHeaderField: "Cookie")
            print("添加JSESSIONID到登录请求: \(sessionId)")
        } else {
            // 使用默认的Cookie
            request.setValue("JSESSIONID=649D78A6D655503161761FE2A7A6E150; tgw_l7_route=5f4f12d20b9b450e602a4f2ad371ca95", forHTTPHeaderField: "Cookie")
            print("使用默认JSESSIONID: 649D78A6D655503161761FE2A7A6E150")
        }
        
        // 添加其他标准请求头
        request.setValue("*/*", forHTTPHeaderField: "Accept")
        request.setValue("fsai-auth.pickgoodspro.com", forHTTPHeaderField: "Host")
        request.setValue("keep-alive", forHTTPHeaderField: "Connection")
        
        request.httpBody = postData
        
        print("直接登录请求头: \(request.allHTTPHeaderFields ?? [:])")
        
        // 创建并执行任务
        let task = URLSession.shared.dataTask(with: request) { [weak self] data, response, error in
            if let error = error {
                DispatchQueue.main.async {
                    completion(.failure(error))
                }
                return
            }
            
            // 检查HTTP响应状态码
            if let httpResponse = response as? HTTPURLResponse {
                print("HTTP 状态码: \(httpResponse.statusCode)")
                print("响应头: \(httpResponse.allHeaderFields)")
                
                // 保存Cookie信息，如果有
                if let setCookieHeader = httpResponse.allHeaderFields["Set-Cookie"] as? String {
                    // 使用NSRegularExpression直接提取JSESSIONID值，避免Range转换问题
                    let pattern = "JSESSIONID=([^;]+)"
                    if let regex = try? NSRegularExpression(pattern: pattern),
                       let match = regex.firstMatch(in: setCookieHeader, range: NSRange(setCookieHeader.startIndex..., in: setCookieHeader)),
                       match.numberOfRanges > 1,
                       let valueRange = Range(match.range(at: 1), in: setCookieHeader) {
                        let jsessionIdValue = String(setCookieHeader[valueRange])
                        self?.jsessionId = jsessionIdValue
                        print("从登录响应提取的JSESSIONID: \(jsessionIdValue)")
                    }
                }
                
                // 如果状态码不是2xx，返回错误
                if !(200...299).contains(httpResponse.statusCode) {
                    if let data = data, let responseString = String(data: data, encoding: .utf8) {
                        print("登录错误响应内容: \(responseString)")
                        
                        // 尝试将错误响应解析为JSON
                        do {
                            if let jsonError = try JSONSerialization.jsonObject(with: data) as? [String: Any] {
                                // 从JSON中提取错误信息
                                var errorMessage = "登录失败"
                                if let message = jsonError["message"] as? String {
                                    errorMessage = message
                                } else if let error = jsonError["error"] as? String {
                                    errorMessage = error
                                }
                                
                                DispatchQueue.main.async {
                                    completion(.failure(NSError(domain: "LoginError", code: httpResponse.statusCode, userInfo: [NSLocalizedDescriptionKey: errorMessage])))
                                }
                            } else {
                                // 如果不是JSON格式，使用原始响应字符串
                                DispatchQueue.main.async {
                                    let errorMessage = "登录失败: \(responseString)"
                                    completion(.failure(NSError(domain: "LoginError", code: httpResponse.statusCode, userInfo: [NSLocalizedDescriptionKey: errorMessage])))
                                }
                            }
                        } catch {
                            // JSON解析失败，使用原始响应字符串
                            DispatchQueue.main.async {
                                let errorMessage = "登录失败: \(responseString)"
                                completion(.failure(NSError(domain: "LoginError", code: httpResponse.statusCode, userInfo: [NSLocalizedDescriptionKey: errorMessage])))
                            }
                        }
                    }
                    
                    // 如果是权限错误，给予通用的错误提示
                    if httpResponse.statusCode == 403 {
                        DispatchQueue.main.async {
                            let errorMessage = "登录权限错误: 请检查账号和密码是否正确，或确认账号是否有权限"
                            completion(.failure(NSError(domain: "AuthorizationError", code: 403, userInfo: [NSLocalizedDescriptionKey: errorMessage])))
                        }
                        return
                    }
                    
                    DispatchQueue.main.async {
                        let errorMessage = "服务器返回错误码: \(httpResponse.statusCode)"
                        completion(.failure(NSError(domain: "HTTPError", code: httpResponse.statusCode, userInfo: [NSLocalizedDescriptionKey: errorMessage])))
                    }
                    return
                }
            }
            
            guard let data = data else {
                DispatchQueue.main.async {
                    completion(.failure(NSError(domain: "NoData", code: 0, userInfo: nil)))
                }
                return
            }
            
            // 打印响应内容，便于调试
            if let responseString = String(data: data, encoding: .utf8) {
                print("直接登录API响应: \(responseString)")
            }
            
            // 解析JSON响应
            do {
                // 尝试将响应解析为JSON字典
                if let jsonResponse = try JSONSerialization.jsonObject(with: data) as? [String: Any] {
                    print("解析为JSON成功: \(jsonResponse)")
                    
                    // 检查所需的关键字段是否存在
                    if let userId = jsonResponse["userId"] as? String,
                       let accessToken = jsonResponse["accessToken"] as? String {
                        
                        // 使用通用方法获取最终的expiresIn值
                        let finalExpiresIn = self!.getFinalExpiresIn(
                            accessToken: accessToken,
                            apiExpiresIn: jsonResponse["expireAt"] as? Int,
                            defaultValue: 3600
                        )
                        
                        // 创建手动封装的DirectLoginData
                        let loginData = DirectLoginData(
                            accessToken: accessToken,
                            tokenType: "Bearer", // 默认为Bearer类型
                            refreshToken: jsonResponse["refreshToken"] as? String,
                            expiresIn: finalExpiresIn,
                            scope: (jsonResponse["scopes"] as? [String])?.joined(separator: ","),
                            userId: userId,
                            userType: jsonResponse["loginType"] as? String,
                            userName: nil, // API返回中可能没有userName
                            userInfo: nil  // API返回中可能没有userInfo
                        )
                        
                        DispatchQueue.main.async {
                            completion(.success(loginData))
                        }
                        return
                    } else if let dataObj = jsonResponse["data"] as? [String: Any],
                              let success = jsonResponse["success"] as? Bool, success {
                        // 如果响应中有嵌套的data对象，尝试从中获取数据
                        let accessToken = dataObj["accessToken"] as? String
                        
                        // 使用通用方法获取最终的expiresIn值
                        let finalExpiresIn = self!.getFinalExpiresIn(
                            accessToken: accessToken,
                            apiExpiresIn: dataObj["expiresIn"] as? Int,
                            defaultValue: 3600
                        )
                        
                        let loginData = DirectLoginData(
                            accessToken: accessToken,
                            tokenType: dataObj["tokenType"] as? String,
                            refreshToken: dataObj["refreshToken"] as? String,
                            expiresIn: finalExpiresIn,
                            scope: dataObj["scope"] as? String,
                            userId: dataObj["userId"] as? String,
                            userType: dataObj["userType"] as? String,
                            userName: dataObj["userName"] as? String,
                            userInfo: self?.parseUserInfo(dataObj["userInfo"] as? [String: Any])
                        )
                        
                        DispatchQueue.main.async {
                            completion(.success(loginData))
                        }
                        return
                    } else {
                        // 检查是否有错误信息
                        var errorMessage = "登录失败"
                        var errorCode = 0
                        
                        if let code = jsonResponse["code"] as? Int {
                            errorCode = code
                        } else if let status = jsonResponse["status"] as? Int {
                            errorCode = status
                        }
                        
                        if let message = jsonResponse["message"] as? String {
                            errorMessage = message
                        } else if let error = jsonResponse["error"] as? String {
                            errorMessage = error
                        }
                        
                        // 如果HTTP状态码是200但没有找到必要字段，可能是返回格式与预期不同
                        // 尝试手动处理这种情况，将其视为成功
                        if let authenticated = jsonResponse["authenticated"] as? Bool,
                           authenticated == true {
                            
                            let accessToken = jsonResponse["accessToken"] as? String
                            
                            // 使用通用方法获取最终的expiresIn值
                            let finalExpiresIn = self!.getFinalExpiresIn(
                                accessToken: accessToken,
                                apiExpiresIn: jsonResponse["expireAt"] as? Int,
                                defaultValue: 3600
                            )
                            
                            // 创建一个基本的登录数据对象，使用能够找到的信息
                            let loginData = DirectLoginData(
                                accessToken: accessToken,
                                tokenType: "Bearer", // 默认值
                                refreshToken: jsonResponse["refreshToken"] as? String,
                                expiresIn: finalExpiresIn,
                                scope: (jsonResponse["scopes"] as? [String])?.joined(separator: ","),
                                userId: jsonResponse["userId"] as? String,
                                userType: jsonResponse["loginType"] as? String,
                                userName: nil,
                                userInfo: nil
                            )
                            
                            DispatchQueue.main.async {
                                completion(.success(loginData))
                            }
                            return
                        }
                        
                        DispatchQueue.main.async {
                            completion(.failure(NSError(domain: "APIError", code: errorCode, userInfo: [NSLocalizedDescriptionKey: errorMessage])))
                        }
                    }
                } else {
                    // 如果不是标准JSON，使用JSONDecoder尝试解析标准模型
                    let decoder = JSONDecoder()
                    let response = try decoder.decode(DirectLoginResponse.self, from: data)
                    
                    DispatchQueue.main.async {
                        if response.isSuccess, let responseData = response.data {
                            completion(.success(responseData))
                        } else {
                            let errorMessage = response.errorMessage
                            let errorCode = response.statusCode
                            completion(.failure(NSError(domain: "APIError", code: errorCode, userInfo: [NSLocalizedDescriptionKey: errorMessage])))
                        }
                    }
                }
            } catch {
                print("JSON解析错误: \(error.localizedDescription)")
                
                // 如果解析失败但HTTP状态码是200，尝试手动构建一个成功响应
                if let httpResponse = response as? HTTPURLResponse,
                   (200...299).contains(httpResponse.statusCode),
                   let responseString = String(data: data, encoding: .utf8),
                   responseString.contains("accessToken") {
                    
                    do {
                        if let jsonResponse = try JSONSerialization.jsonObject(with: data) as? [String: Any] {
                            // 创建一个基本的登录数据对象
                            let accessToken = jsonResponse["accessToken"] as? String
                            let finalExpiresIn = self!.getFinalExpiresIn(
                                accessToken: accessToken,
                                apiExpiresIn: nil,
                                defaultValue: 3600
                            )
                            
                            let loginData = DirectLoginData(
                                accessToken: accessToken,
                                tokenType: "Bearer", // 默认值
                                refreshToken: jsonResponse["refreshToken"] as? String,
                                expiresIn: finalExpiresIn,
                                scope: nil,
                                userId: jsonResponse["userId"] as? String,
                                userType: nil,
                                userName: nil,
                                userInfo: nil
                            )
                            
                            DispatchQueue.main.async {
                                completion(.success(loginData))
                            }
                            return
                        }
                    } catch {
                        print("二次JSON解析错误: \(error.localizedDescription)")
                    }
                }
                
                // 所有尝试都失败，返回原始错误
                DispatchQueue.main.async {
                    completion(.failure(error))
                }
            }
        }
        
        task.resume()
    }

    private func parseUserInfo(_ userInfoDict: [String: Any]?) -> DirectLoginData.UserInfo? {
        guard let userInfoDict = userInfoDict else { return nil }
        return DirectLoginData.UserInfo(
            id: userInfoDict["id"] as? String,
            name: userInfoDict["name"] as? String,
            nickname: userInfoDict["nickname"] as? String,
            email: userInfoDict["email"] as? String,
            phone: userInfoDict["phone"] as? String,
            avatar: userInfoDict["avatar"] as? String
        )
    }

    // 发送忘记密码验证码
    func sendForgotPasswordCode(email: String, completion: @escaping (Result<VerificationCodeResponse, Error>) -> Void) {
        // 强制刷新CSRF-Token和JSESSIONID，确保会话同步
        ensureCSRFToken(forceRefresh: true) { [weak self] success in
            guard let self = self else { return }
            
            if !success {
                print("警告: 无法获取CSRF-Token，继续尝试发送忘记密码验证码")
            }
            
            print("🔐 发送忘记密码验证码使用的会话信息 - CSRF-Token: \(self.csrfToken ?? "无"), JSESSIONID: \(self.jsessionId ?? "无")")
            
            let endpoint = "\(self.baseURL)/email-password-forget/j/send-code"
            
            guard let url = URL(string: endpoint) else {
                completion(.failure(NSError(domain: "InvalidURL", code: 0, userInfo: nil)))
                return
            }
            
            // 确保loginType是"NS"而不是"user"
            let sendCodeRequest = ForgotPasswordSendCodeRequest(identity: email, loginType: "NS")
            
            var request = URLRequest(url: url)
            request.httpMethod = "POST"
            
            // 添加必要的头信息
            self.addHeaders(to: &request)
            
            do {
                let jsonEncoder = JSONEncoder()
                // 确保不使用任何特殊的键命名策略
                let jsonData = try jsonEncoder.encode(sendCodeRequest)
                
                // 检查最终JSON
                if let jsonString = String(data: jsonData, encoding: .utf8) {
                    print("发送忘记密码验证码请求参数: \(jsonString)")
                    // 确保loginType是"NS"
                    assert(jsonString.contains("NS"), "loginType必须是NS")
                }
                
                request.httpBody = jsonData
                
                let task = URLSession.shared.dataTask(with: request) { data, response, error in
                    if let error = error {
                        DispatchQueue.main.async {
                            print("网络错误: \(error.localizedDescription)")
                            completion(.failure(error))
                        }
                        return
                    }
                    
                    if let httpResponse = response as? HTTPURLResponse {
                        print("忘记密码验证码API HTTP状态码: \(httpResponse.statusCode)")
                        
                        // 打印完整响应头，帮助调试
                        print("响应头: \(httpResponse.allHeaderFields)")
                        
                        // 更新JSESSIONID - 即使是错误响应也要更新会话ID
                        self.updateSessionFromResponse(httpResponse)
                        
                        // 如果状态码不是2xx，返回错误
                        if !(200...299).contains(httpResponse.statusCode) {
                            if let data = data, let responseString = String(data: data, encoding: .utf8) {
                                print("错误响应内容: \(responseString)")
                            }
                            
                            DispatchQueue.main.async {
                                let errorMessage = "服务器返回错误码: \(httpResponse.statusCode)"
                                completion(.failure(NSError(domain: "HTTPError", code: httpResponse.statusCode, userInfo: [NSLocalizedDescriptionKey: errorMessage])))
                            }
                            return
                        }
                    }
                    
                    guard let data = data else {
                        DispatchQueue.main.async {
                            completion(.failure(NSError(domain: "NoData", code: 0, userInfo: nil)))
                        }
                        return
                    }
                    
                    // 打印响应内容，便于调试
                    if let responseString = String(data: data, encoding: .utf8) {
                        print("忘记密码验证码API响应: \(responseString)")
                    }
                    
                    do {
                        // 先尝试直接解析为VerificationCodeResponse
                        let directResponse = try JSONDecoder().decode(VerificationCodeResponse.self, from: data)
                        DispatchQueue.main.async {
                            completion(.success(directResponse))
                        }
                    } catch {
                        print("直接解析为VerificationCodeResponse失败: \(error)")
                        
                        // 尝试解析为APIResponse<VerificationCodeResponse>
                        do {
                            let response = try JSONDecoder().decode(APIResponse<VerificationCodeResponse>.self, from: data)
                            
                            DispatchQueue.main.async {
                                if let responseData = response.data {
                                    completion(.success(responseData))
                                } else if let message = response.message {
                                    completion(.failure(NSError(domain: "APIError", code: response.status ?? 400, userInfo: [NSLocalizedDescriptionKey: message])))
                                } else {
                                    completion(.failure(NSError(domain: "UnknownError", code: 0, userInfo: nil)))
                                }
                            }
                        } catch {
                            DispatchQueue.main.async {
                                print("APIResponse<VerificationCodeResponse>解析失败: \(error)")
                                
                                // 如果所有解析方法都失败，尝试创建一个基于响应字符串的模拟对象
                                if let responseString = String(data: data, encoding: .utf8),
                                   responseString.contains("identity") && responseString.contains("expiredAt") {
                                    // 创建一个基本的响应对象
                                    let mockResponse = VerificationCodeResponse(
                                        identity: email,
                                        expiredAt: nil,
                                        validated: false,
                                        code: nil,
                                        scene: nil,
                                        sequence: nil,
                                        validatedTime: nil
                                    )
                                    completion(.success(mockResponse))
                                } else {
                                    completion(.failure(error))
                                }
                            }
                        }
                    }
                }
                
                task.resume()
            } catch {
                completion(.failure(error))
            }
        }
    }

    // 执行忘记密码重置
    func resetForgotPassword(email: String, code: String, newPassword: String, completion: @escaping (Result<Bool, Error>) -> Void) {
        // 重置密码时不强制刷新CSRF-Token，使用发送验证码时获取的相同会话
        // 这确保了验证码和重置密码使用相同的JSESSIONID
        ensureCSRFToken(forceRefresh: false) { [weak self] success in
            guard let self = self else { return }
            
            if !success {
                print("警告: 无法获取CSRF-Token，继续尝试重置密码")
            }
            
            print("🔐 重置密码使用的会话信息 - CSRF-Token: \(self.csrfToken ?? "无"), JSESSIONID: \(self.jsessionId ?? "无")")
            
            // 验证会话一致性
            if self.jsessionId == nil {
                print("⚠️ 警告：重置密码时JSESSIONID为空，这可能导致验证码验证失败")
            }
            
            // 修正端点URL
            let endpoint = "https://fsai-auth.pickgoodspro.com/api/email-password-forget/j"
            
            guard let url = URL(string: endpoint) else {
                completion(.failure(NSError(domain: "InvalidURL", code: 0, userInfo: nil)))
                return
            }
            
            // 创建请求
            var request = URLRequest(url: url)
            request.httpMethod = "POST"
            
            // 添加请求头
            self.addHeaders(to: &request)
            
            // 手动设置Content-Type为JSON
            request.setValue("application/json", forHTTPHeaderField: "Content-Type")
            
            // 创建请求体
            let resetRequest = ForgotPasswordResetRequest(
                identity: email,
                code: code,
                password: newPassword,
                loginType: "NS",
                username: nil
            )
            
            do {
                let jsonEncoder = JSONEncoder()
                let jsonData = try jsonEncoder.encode(resetRequest)
                
                // 检查最终JSON
                if let jsonString = String(data: jsonData, encoding: .utf8) {
                    print("忘记密码重置请求参数: \(jsonString)")
                }
                
                request.httpBody = jsonData
                
                // 打印完整请求信息用于调试
                print("重置密码请求URL: \(endpoint)")
                print("重置密码请求方法: \(request.httpMethod ?? "未知")")
                print("重置密码请求头: \(request.allHTTPHeaderFields ?? [:])")
                
                // 发送请求
                let task = URLSession.shared.dataTask(with: request) { data, response, error in
                    DispatchQueue.main.async {
                        if let error = error {
                            print("重置密码网络错误: \(error)")
                            completion(.failure(error))
                            return
                        }
                        
                        // 打印HTTP响应详情
                        if let httpResponse = response as? HTTPURLResponse {
                            print("HTTP 状态码: \(httpResponse.statusCode)")
                            print("响应头: \(httpResponse.allHeaderFields)")
                            
                            // 检查是否有错误状态码
                            if httpResponse.statusCode >= 400 {
                                if let data = data, let responseString = String(data: data, encoding: .utf8) {
                                    print("错误响应内容: \(responseString)")
                                    completion(.failure(NSError(domain: "HTTPError", code: httpResponse.statusCode, userInfo: [NSLocalizedDescriptionKey: responseString])))
                                    return
                                }
                            }
                        }
                        
                        guard let data = data else {
                            print("重置密码无响应数据")
                            completion(.failure(NSError(domain: "NoData", code: 0, userInfo: nil)))
                            return
                        }
                        
                        // 打印响应内容，便于调试
                        if let responseString = String(data: data, encoding: .utf8) {
                            print("重置密码API响应: \(responseString)")
                        }
                        
                        do {
                            // 尝试直接解析用户信息对象
                            let jsonDecoder = JSONDecoder()
                            _ = try jsonDecoder.decode(User.self, from: data)
                            completion(.success(true))
                            return
                        } catch {
                            print("直接解析为User对象失败: \(error)")
                            
                            // 尝试解析为API通用响应
                            do {
                                if let json = try JSONSerialization.jsonObject(with: data) as? [String: Any] {
                                    // 检查是否包含id字段，说明可能是用户对象
                                    if json["id"] != nil {
                                        // 如果包含id，认为是成功的用户响应
                                        completion(.success(true))
                                        return
                                    }
                                    
                                    // 检查状态码字段
                                    if let status = json["status"] as? Int {
                                        if status == 200 || status == 0 {
                                            // 状态码表示成功
                                            completion(.success(true))
                                            return
                                        } else {
                                            // 提取错误消息
                                            let message = json["message"] as? String ?? "未知错误"
                                            completion(.failure(NSError(domain: "ResetPasswordError", code: status, userInfo: [NSLocalizedDescriptionKey: message])))
                                            return
                                        }
                                    }
                                }
                            } catch {
                                print("JSON对象解析失败: \(error)")
                            }
                            
                            // 如果所有尝试都失败，但响应包含成功指示符（如用户ID或邮箱），则认为成功
                            if let responseString = String(data: data, encoding: .utf8),
                               responseString.contains("email") || responseString.contains("id") || responseString.contains("createdDate") {
                                completion(.success(true))
                            } else {
                                // 其他所有情况都报告失败
                                completion(.failure(NSError(domain: "ParseError", code: 0, userInfo: [NSLocalizedDescriptionKey: "解析响应失败"])))
                            }
                        }
                    }
                }
                
                task.resume()
            } catch {
                completion(.failure(error))
            }
        }
    }

    // 退出登录API调用
    func logout(completion: @escaping (Result<Bool, Error>) -> Void) {
        // 首先获取CSRF token
        ensureCSRFToken { [weak self] success in
            guard let self = self else { return }
            
            if !success {
                print("警告: 无法获取CSRF-Token，继续尝试退出登录")
            }
            
            // 调用退出登录API
            self.callLogoutAPI(completion: completion)
        }
    }
    
    // 调用退出登录API
    private func callLogoutAPI(completion: @escaping (Result<Bool, Error>) -> Void) {
        let endpoint = "https://fsai-auth.pickgoodspro.com/logout"
        
        guard let url = URL(string: endpoint) else {
            let error = NSError(domain: "NetworkServiceError", code: 0, userInfo: [NSLocalizedDescriptionKey: "无效的退出登录URL"])
            completion(.failure(error))
            return
        }
        
        var request = URLRequest(url: url, timeoutInterval: Double.infinity)
        request.httpMethod = "POST"
        
        // 添加CSRF token到请求头
        if let token = csrfToken {
            request.addValue(token, forHTTPHeaderField: "X-CSRF-TOKEN")
            print("添加CSRF-Token到退出登录请求: \(token)")
        }
        
        // 添加JSESSIONID（如果有）
        if let sessionId = jsessionId {
            request.addValue("JSESSIONID=\(sessionId); tgw_l7_route=5f4f12d20b9b450e602a4f2ad371ca95", forHTTPHeaderField: "Cookie")
            print("添加JSESSIONID到退出登录请求: \(sessionId)")
        }
        
        let task = URLSession.shared.dataTask(with: request) { data, response, error in
            if let error = error {
                print("调用退出登录API失败: \(error.localizedDescription)")
                completion(.failure(error))
                return
            }
            
            // 检查HTTP响应状态码
            if let httpResponse = response as? HTTPURLResponse {
                print("退出登录API HTTP状态码: \(httpResponse.statusCode)")
                
                if (200...299).contains(httpResponse.statusCode) {
                    // 退出登录成功
                    print("退出登录API调用成功")
                    completion(.success(true))
                } else {
                    // 退出登录失败
                    var errorMessage = "退出登录失败，状态码: \(httpResponse.statusCode)"
                    
                    if let data = data, let responseString = String(data: data, encoding: .utf8) {
                        errorMessage += ", 响应: \(responseString)"
                    }
                    
                    let error = NSError(domain: "NetworkServiceError", code: httpResponse.statusCode, userInfo: [NSLocalizedDescriptionKey: errorMessage])
                    completion(.failure(error))
                }
            } else {
                let error = NSError(domain: "NetworkServiceError", code: 0, userInfo: [NSLocalizedDescriptionKey: "无法获取HTTP响应"])
                completion(.failure(error))
            }
        }
        
        task.resume()
    }

    // 苹果登录 - 获取CSRF Token后发送请求
    func appleLogin(userId: String, identityToken: String, authorizationCode: String, completion: @escaping (Result<DirectLoginData, Error>) -> Void) {
        let endpoint = "https://fsai-auth.pickgoodspro.com/apple/login"
        print("📡 准备发送苹果登录API请求到: \(endpoint)")
        
        // 保存授权信息到UserDefaults，以便排查问题
        UserDefaults.standard.set(userId, forKey: "appleAuthUserId")
        UserDefaults.standard.set(authorizationCode, forKey: "appleAuthCode")
        UserDefaults.standard.set(identityToken.count, forKey: "appleTokenLength")
        
        // 在每次登录前主动重置会话状态，确保获取新的CSRF Token和JSESSIONID
        print("🔄 苹果登录前重置会话状态，强制获取新的CSRF令牌")
        resetSessionState()
        
        // 首先确保有有效的CSRF Token和JSESSIONID
        ensureCSRFToken { [weak self] success in
            guard let self = self else { return }
            
            if !success {
                print("⚠️ 无法获取CSRF Token，将使用默认值继续尝试苹果登录")
            }
            
            print("✅ 已获取CSRF Token: \(self.csrfToken ?? "未知")")
            print("✅ 已获取JSESSIONID: \(self.jsessionId ?? "未知")")
            
            // 只使用直接JSON格式发送请求
            self.sendAppleLoginWithDirectJSON(userId: userId, identityToken: identityToken, authorizationCode: authorizationCode, endpoint: endpoint, completion: completion)
        }
    }

    // 使用JSON格式发送苹果登录请求
    private func sendAppleLoginWithJSON(userId: String, identityToken: String, authorizationCode: String, endpoint: String, completion: @escaping (Result<DirectLoginData, Error>) -> Void) {
        // 创建请求对象
        let request = AppleLoginRequest(
            userId: userId,
            identityToken: identityToken,
            authorizationCode: authorizationCode
        )
        
        // 打印详细的请求参数信息
        print("🔑 苹果登录JSON参数详情:")
        print("  - userId: \(userId)")
        print("  - bundleId: \(request.bundleId)")
        print("  - clientId: \(request.clientId)")
        print("  - loginType: \(request.loginType)")
        print("  - namespace: \(request.namespace)")
        print("  - userspace: \(request.userspace)")
        print("  - identityToken长度: \(identityToken.count)")
        print("  - authorizationCode长度: \(authorizationCode.count)")
        
        guard let requestData = try? JSONEncoder().encode(request) else {
            print("❌ 请求数据编码失败")
            completion(.failure(NSError(domain: "AppleLoginError", code: 0, userInfo: [NSLocalizedDescriptionKey: "无法编码请求数据"])))
            return
        }
        
        print("📦 JSON请求数据准备完成，请求体大小: \(requestData.count) 字节")
        if let requestStr = String(data: requestData, encoding: .utf8) {
            print("📝 JSON请求内容: \(requestStr)")
        }
        
        var urlRequest = URLRequest(url: URL(string: endpoint)!)
        urlRequest.httpMethod = "POST"
        urlRequest.httpBody = requestData
        urlRequest.addValue("application/json", forHTTPHeaderField: "Content-Type")
        
        print("🚀 开始发送苹果登录JSON API请求")
        let task = URLSession.shared.dataTask(with: urlRequest) { data, response, error in
            if let error = error {
                print("❌ 网络请求错误: \(error.localizedDescription)")
                completion(.failure(error))
                return
            }
            
            guard let httpResponse = response as? HTTPURLResponse else {
                print("❌ 无效的HTTP响应")
                completion(.failure(NSError(domain: "AppleLoginError", code: 0, userInfo: [NSLocalizedDescriptionKey: "无效的HTTP响应"])))
                return
            }
            
            print("📊 收到HTTP响应，状态码: \(httpResponse.statusCode)")
            print("📋 响应头: \(httpResponse.allHeaderFields)")
            
            guard let data = data else {
                print("❌ 响应数据为空")
                completion(.failure(NSError(domain: "AppleLoginError", code: 0, userInfo: [NSLocalizedDescriptionKey: "没有接收到数据"])))
                return
            }
            
            // 打印响应数据以便调试
            if let responseString = String(data: data, encoding: .utf8) {
                print("📄 响应数据: \(responseString)")
            }
            
            if httpResponse.statusCode != 200 {
                print("⚠️ HTTP状态码非200: \(httpResponse.statusCode)")
                do {
                    if let jsonResponse = try JSONSerialization.jsonObject(with: data, options: []) as? [String: Any],
                       let message = jsonResponse["message"] as? String {
                        print("❌ 服务器返回错误信息: \(message)")
                        completion(.failure(NSError(domain: "AppleLoginError", code: httpResponse.statusCode, userInfo: [NSLocalizedDescriptionKey: message])))
                    } else {
                        print("❌ 无法解析错误响应")
                        completion(.failure(NSError(domain: "AppleLoginError", code: httpResponse.statusCode, userInfo: [NSLocalizedDescriptionKey: "登录失败，状态码: \(httpResponse.statusCode)"])))
                    }
                } catch {
                    print("❌ 解析错误响应时发生异常: \(error)")
                    completion(.failure(NSError(domain: "AppleLoginError", code: httpResponse.statusCode, userInfo: [NSLocalizedDescriptionKey: "登录失败，状态码: \(httpResponse.statusCode)"])))
                }
                return
            }
            
            print("✅ 成功接收HTTP 200响应，开始解析数据")
            do {
                if let jsonResponse = try JSONSerialization.jsonObject(with: data, options: []) as? [String: Any] {
                    print("📊 响应JSON解析成功，字段: \(jsonResponse.keys.joined(separator: ", "))")
                    if let loginData = self.parseLoginResponse(jsonResponse) {
                        print("✅ 成功解析登录数据，AccessToken: \(loginData.accessToken?.prefix(10) ?? "无")...")
                        completion(.success(loginData))
                    } else {
                        print("❌ 无法从JSON创建DirectLoginData对象")
                        completion(.failure(NSError(domain: "AppleLoginError", code: 0, userInfo: [NSLocalizedDescriptionKey: "无法解析登录响应"])))
                    }
                } else {
                    print("❌ 响应不是有效的JSON对象")
                    completion(.failure(NSError(domain: "AppleLoginError", code: 0, userInfo: [NSLocalizedDescriptionKey: "无法解析登录响应为JSON"])))
                }
            } catch {
                print("❌ JSON解析异常: \(error)")
                completion(.failure(error))
            }
        }
        
        task.resume()
        print("🔄 苹果登录JSON API请求已发送，等待响应")
    }

    // 使用表单格式发送苹果登录请求
    private func sendAppleLoginWithForm(userId: String, identityToken: String, authorizationCode: String, endpoint: String, completion: @escaping (Result<DirectLoginData, Error>) -> Void) {
        // 获取应用的实际Bundle ID
        let actualBundleId = Bundle.main.bundleIdentifier ?? "com.fitscanai.app"
        
        // 构建表单参数
        let parameters = [
            "userId": userId,
            "identityToken": identityToken,
            "authorizationCode": authorizationCode,
            "bundleId": actualBundleId,
            "clientId": "4072cc9e-b4a3-4ab7-bd92-724783a65349",
            "loginType": "NS",
            "namespace": "fsai",
            "userspace": "fsai"
        ]
        
        // 打印详细的请求参数信息
        print("🔑 苹果登录表单参数详情:")
        print("  - userId: \(userId)")
        print("  - bundleId: \(actualBundleId)")
        print("  - clientId: 4072cc9e-b4a3-4ab7-bd92-724783a65349")
        print("  - loginType: NS")
        print("  - namespace: fsai")
        print("  - userspace: fsai")
        print("  - identityToken长度: \(identityToken.count)")
        print("  - authorizationCode长度: \(authorizationCode.count)")
        
        // 构建表单数据
        var components = URLComponents()
        components.queryItems = parameters.map { URLQueryItem(name: $0.key, value: $0.value) }
        
        guard let formData = components.query?.data(using: .utf8) else {
            print("❌ 表单数据编码失败")
            completion(.failure(NSError(domain: "AppleLoginError", code: 0, userInfo: [NSLocalizedDescriptionKey: "无法编码表单数据"])))
            return
        }
        
        print("📦 表单请求数据准备完成，请求体大小: \(formData.count) 字节")
        if let formString = String(data: formData, encoding: .utf8) {
            print("📝 表单请求内容: \(formString)")
        }
        
        var urlRequest = URLRequest(url: URL(string: endpoint)!)
        urlRequest.httpMethod = "POST"
        urlRequest.httpBody = formData
        urlRequest.addValue("application/x-www-form-urlencoded", forHTTPHeaderField: "Content-Type")
        
        print("🚀 开始发送苹果登录表单API请求")
        let task = URLSession.shared.dataTask(with: urlRequest) { data, response, error in
            if let error = error {
                print("❌ 网络请求错误: \(error.localizedDescription)")
                completion(.failure(error))
                return
            }
            
            guard let httpResponse = response as? HTTPURLResponse else {
                print("❌ 无效的HTTP响应")
                completion(.failure(NSError(domain: "AppleLoginError", code: 0, userInfo: [NSLocalizedDescriptionKey: "无效的HTTP响应"])))
                return
            }
            
            print("📊 收到HTTP响应，状态码: \(httpResponse.statusCode)")
            print("📋 响应头: \(httpResponse.allHeaderFields)")
            
            guard let data = data else {
                print("❌ 响应数据为空")
                completion(.failure(NSError(domain: "AppleLoginError", code: 0, userInfo: [NSLocalizedDescriptionKey: "没有接收到数据"])))
                return
            }
            
            // 打印响应数据以便调试
            if let responseString = String(data: data, encoding: .utf8) {
                print("📄 响应数据: \(responseString)")
            }
            
            if httpResponse.statusCode != 200 {
                print("⚠️ HTTP状态码非200: \(httpResponse.statusCode)")
                do {
                    if let jsonResponse = try JSONSerialization.jsonObject(with: data, options: []) as? [String: Any],
                       let message = jsonResponse["message"] as? String {
                        print("❌ 服务器返回错误信息: \(message)")
                        completion(.failure(NSError(domain: "AppleLoginError", code: httpResponse.statusCode, userInfo: [NSLocalizedDescriptionKey: message])))
                    } else {
                        print("❌ 无法解析错误响应")
                        completion(.failure(NSError(domain: "AppleLoginError", code: httpResponse.statusCode, userInfo: [NSLocalizedDescriptionKey: "登录失败，状态码: \(httpResponse.statusCode)"])))
                    }
                } catch {
                    print("❌ 解析错误响应时发生异常: \(error)")
                    completion(.failure(NSError(domain: "AppleLoginError", code: httpResponse.statusCode, userInfo: [NSLocalizedDescriptionKey: "登录失败，状态码: \(httpResponse.statusCode)"])))
                }
                return
            }
            
            print("✅ 成功接收HTTP 200响应，开始解析数据")
            do {
                if let jsonResponse = try JSONSerialization.jsonObject(with: data, options: []) as? [String: Any] {
                    print("📊 响应JSON解析成功，字段: \(jsonResponse.keys.joined(separator: ", "))")
                    if let loginData = self.parseLoginResponse(jsonResponse) {
                        print("✅ 成功解析登录数据，AccessToken: \(loginData.accessToken?.prefix(10) ?? "无")...")
                        completion(.success(loginData))
                    } else {
                        print("❌ 无法从JSON创建DirectLoginData对象")
                        completion(.failure(NSError(domain: "AppleLoginError", code: 0, userInfo: [NSLocalizedDescriptionKey: "无法解析登录响应"])))
                    }
                } else {
                    print("❌ 响应不是有效的JSON对象")
                    completion(.failure(NSError(domain: "AppleLoginError", code: 0, userInfo: [NSLocalizedDescriptionKey: "无法解析登录响应为JSON"])))
                }
            } catch {
                print("❌ JSON解析异常: \(error)")
                completion(.failure(error))
            }
        }
        
        task.resume()
        print("🔄 苹果登录表单API请求已发送，等待响应")
    }

    // 解析登录响应
    private func parseLoginResponse(_ jsonResponse: [String: Any]) -> DirectLoginData? {
        // 检查必要的字段是否存在
        guard let accessToken = jsonResponse["accessToken"] as? String else {
            print("❌ 响应中缺少必要的accessToken字段")
            return nil
        }
        
        print("🔑 从响应中获取到accessToken")
        
        // 使用通用方法获取最终的expiresIn值
        let finalExpiresIn = getFinalExpiresIn(
            accessToken: accessToken,
            apiExpiresIn: jsonResponse["expiresIn"] as? Int,
            defaultValue: 3600
        )
        
        // 创建DirectLoginData对象
        let loginData = DirectLoginData(
            accessToken: accessToken,
            tokenType: jsonResponse["tokenType"] as? String ?? "Bearer",
            refreshToken: jsonResponse["refreshToken"] as? String,
            expiresIn: finalExpiresIn,
            scope: jsonResponse["scope"] as? String,
            userId: jsonResponse["userId"] as? String,
            userType: jsonResponse["userType"] as? String,
            userName: jsonResponse["userName"] as? String,
            userInfo: parseUserInfo(jsonResponse["userInfo"] as? [String: Any])
        )
        
        print("📋 已创建DirectLoginData对象，userId: \(loginData.userId ?? "无"), userName: \(loginData.userName ?? "无"), expiresIn: \(finalExpiresIn)秒")
        return loginData
    }

    // 直接使用JSON对象发送苹果登录请求
    private func sendAppleLoginWithDirectJSON(userId: String, identityToken: String, authorizationCode: String, endpoint: String, completion: @escaping (Result<DirectLoginData, Error>) -> Void) {
        // 获取应用的实际Bundle ID
        let actualBundleId = Bundle.main.bundleIdentifier ?? "com.fitscanai.app"
        
        // 构建JSON数据
        let requestDict: [String: Any] = [
            "userId": userId,
            "identityToken": identityToken,
            "authorizationCode": authorizationCode,
            "bundleId": actualBundleId,
            "clientId": "4072cc9e-b4a3-4ab7-bd92-724783a65349",
            "loginType": "NS",
            "namespace": "fsai",
            "userspace": "fsai"
        ]
        
        // 打印详细的请求参数信息
        print("🔑 苹果登录直接JSON参数详情:")
        print("  - userId: \(userId)")
        print("  - bundleId: \(actualBundleId)")
        print("  - clientId: 4072cc9e-b4a3-4ab7-bd92-724783a65349")
        print("  - identityToken长度: \(identityToken.count)")
        print("  - authorizationCode长度: \(authorizationCode.count)")
        
        guard let requestData = try? JSONSerialization.data(withJSONObject: requestDict) else {
            print("❌ 直接JSON请求数据编码失败")
            completion(.failure(NSError(domain: "AppleLoginError", code: 0, userInfo: [NSLocalizedDescriptionKey: "无法编码JSON请求数据"])))
            return
        }
        
        print("📦 直接JSON请求数据准备完成，请求体大小: \(requestData.count) 字节")
        if let requestStr = String(data: requestData, encoding: .utf8) {
            print("📝 直接JSON请求内容: \(requestStr)")
        }
        
        var urlRequest = URLRequest(url: URL(string: endpoint)!)
        urlRequest.httpMethod = "POST"
        urlRequest.httpBody = requestData
        urlRequest.addValue("application/json", forHTTPHeaderField: "Content-Type")
        
        // 添加CSRF Token到请求头
        if let token = csrfToken {
            urlRequest.addValue(token, forHTTPHeaderField: "X-CSRF-TOKEN")
            print("✅ 添加CSRF Token到请求头: \(token)")
        } else {
            // 使用默认Token
            urlRequest.addValue("29afa5f6-ccfa-44d2-b4d6-6bdd98c7ebb6", forHTTPHeaderField: "X-CSRF-TOKEN")
            print("⚠️ 使用默认CSRF Token: 29afa5f6-ccfa-44d2-b4d6-6bdd98c7ebb6")
        }
        
        // 添加JSESSIONID到Cookie
        if let sessionId = jsessionId {
            urlRequest.addValue("JSESSIONID=\(sessionId); tgw_l7_route=5f4f12d20b9b450e602a4f2ad371ca95", forHTTPHeaderField: "Cookie")
            print("✅ 添加JSESSIONID到Cookie: \(sessionId)")
        } else {
            // 使用默认JSESSIONID
            urlRequest.addValue("JSESSIONID=649D78A6D655503161761FE2A7A6E150; tgw_l7_route=5f4f12d20b9b450e602a4f2ad371ca95", forHTTPHeaderField: "Cookie")
            print("⚠️ 使用默认JSESSIONID: 649D78A6D655503161761FE2A7A6E150")
        }
        
        // 添加其他通用头
        urlRequest.addValue("*/*", forHTTPHeaderField: "Accept")
        urlRequest.addValue("fsai-auth.pickgoodspro.com", forHTTPHeaderField: "Host")
        urlRequest.addValue("keep-alive", forHTTPHeaderField: "Connection")
        
        // 打印完整的请求头信息
        print("📋 完整请求头: \(urlRequest.allHTTPHeaderFields ?? [:])")
        
        print("🚀 开始发送苹果登录直接JSON API请求")
        let task = URLSession.shared.dataTask(with: urlRequest) { [weak self] data, response, error in
            guard let self = self else { return }
            
            if let error = error {
                print("❌ 网络请求错误: \(error.localizedDescription)")
                completion(.failure(error))
                return
            }
            
            guard let httpResponse = response as? HTTPURLResponse else {
                print("❌ 无效的HTTP响应")
                completion(.failure(NSError(domain: "AppleLoginError", code: 0, userInfo: [NSLocalizedDescriptionKey: "无效的HTTP响应"])))
                return
            }
            
            print("📊 收到HTTP响应，状态码: \(httpResponse.statusCode)")
            print("📋 响应头: \(httpResponse.allHeaderFields)")
            
            // 处理Set-Cookie响应头，提取新的JSESSIONID
            var newJsessionId: String? = nil
            if let allHeaders = httpResponse.allHeaderFields as? [String: Any] {
                for (key, value) in allHeaders {
                    if key.lowercased() == "set-cookie", 
                       let cookieStr = value as? String, 
                       cookieStr.contains("JSESSIONID") {
                        
                        // 提取JSESSIONID
                        let pattern = "JSESSIONID=([^;]+)"
                        if let regex = try? NSRegularExpression(pattern: pattern),
                           let match = regex.firstMatch(in: cookieStr, range: NSRange(cookieStr.startIndex..., in: cookieStr)),
                           match.numberOfRanges > 1,
                           let valueRange = Range(match.range(at: 1), in: cookieStr) {
                            
                            newJsessionId = String(cookieStr[valueRange])
                            print("✅ 从响应中提取到新的JSESSIONID: \(newJsessionId!)")
                            
                            // 保存新的JSESSIONID
                            self.jsessionId = newJsessionId
                        }
                    }
                }
            }
            
            guard let data = data else {
                print("❌ 响应数据为空")
                completion(.failure(NSError(domain: "AppleLoginError", code: 0, userInfo: [NSLocalizedDescriptionKey: "没有接收到数据"])))
                return
            }
            
            // 打印响应数据以便调试
            if let responseString = String(data: data, encoding: .utf8) {
                print("📄 响应数据: \(responseString)")
            }
            
            // 如果状态码是403且收到了新的JSESSIONID，尝试使用新的JSESSIONID重试一次
            if httpResponse.statusCode == 403 && newJsessionId != nil {
                print("⚠️ 收到403状态码，但获取到了新的JSESSIONID: \(newJsessionId!)，尝试重新获取CSRF Token并重试...")
                
                // 使用新的JSESSIONID获取CSRF Token
                self.refreshCSRFToken { success in
                    if success {
                        print("✅ 使用新JSESSIONID获取CSRF Token成功，正在重试苹果登录...")
                        // 递归调用自身重试登录
                        self.sendAppleLoginWithDirectJSON(userId: userId, identityToken: identityToken, authorizationCode: authorizationCode, endpoint: endpoint, completion: completion)
                    } else {
                        print("❌ 使用新JSESSIONID获取CSRF Token失败")
                        completion(.failure(NSError(domain: "AppleLoginError", code: httpResponse.statusCode, userInfo: [NSLocalizedDescriptionKey: "登录失败，无法获取有效的CSRF Token"])))
                    }
                }
                return
            }
            
            if httpResponse.statusCode != 200 {
                print("⚠️ HTTP状态码非200: \(httpResponse.statusCode)")
                do {
                    if let jsonResponse = try JSONSerialization.jsonObject(with: data, options: []) as? [String: Any],
                       let message = jsonResponse["message"] as? String {
                        print("❌ 服务器返回错误信息: \(message)")
                        completion(.failure(NSError(domain: "AppleLoginError", code: httpResponse.statusCode, userInfo: [NSLocalizedDescriptionKey: message])))
                    } else {
                        print("❌ 无法解析错误响应")
                        completion(.failure(NSError(domain: "AppleLoginError", code: httpResponse.statusCode, userInfo: [NSLocalizedDescriptionKey: "登录失败，状态码: \(httpResponse.statusCode)"])))
                    }
                } catch {
                    print("❌ 解析错误响应时发生异常: \(error)")
                    completion(.failure(NSError(domain: "AppleLoginError", code: httpResponse.statusCode, userInfo: [NSLocalizedDescriptionKey: "登录失败，状态码: \(httpResponse.statusCode)"])))
                }
                return
            }
            
            print("✅ 成功接收HTTP 200响应，开始解析数据")
            do {
                if let jsonResponse = try JSONSerialization.jsonObject(with: data, options: []) as? [String: Any] {
                    print("📊 响应JSON解析成功，字段: \(jsonResponse.keys.joined(separator: ", "))")
                    if let loginData = self.parseLoginResponse(jsonResponse) {
                        print("✅ 成功解析登录数据，AccessToken: \(loginData.accessToken?.prefix(10) ?? "无")...")
                        completion(.success(loginData))
                    } else {
                        print("❌ 无法从JSON创建DirectLoginData对象")
                        completion(.failure(NSError(domain: "AppleLoginError", code: 0, userInfo: [NSLocalizedDescriptionKey: "无法解析登录响应"])))
                    }
                } else {
                    print("❌ 响应不是有效的JSON对象")
                    completion(.failure(NSError(domain: "AppleLoginError", code: 0, userInfo: [NSLocalizedDescriptionKey: "无法解析登录响应为JSON"])))
                }
            } catch {
                print("❌ JSON解析异常: \(error)")
                completion(.failure(error))
            }
        }
        
        task.resume()
        print("🔄 苹果登录直接JSON API请求已发送，等待响应")
    }

    // 谷歌登录方法
    func googleLogin(appId: String, idToken: String, clientId: String, completion: @escaping (Result<DirectLoginData, Error>) -> Void) {
        let endpoint = "https://fsai-auth.pickgoodspro.com/google/login"
        print("📡 准备发送谷歌登录API请求到: \(endpoint)")
        
        // 在每次登录前主动重置会话状态，确保获取新的CSRF Token和JSESSIONID
        print("🔄 谷歌登录前重置会话状态，强制获取新的CSRF令牌")
        resetSessionState()
        
        // 首先确保有有效的CSRF Token和JSESSIONID
        ensureCSRFToken { [weak self] success in
            guard let self = self else { return }
            
            if !success {
                print("⚠️ 无法获取CSRF Token，将使用默认值继续尝试谷歌登录")
            }
            
            print("✅ 已获取CSRF Token: \(self.csrfToken ?? "未知")")
            print("✅ 已获取JSESSIONID: \(self.jsessionId ?? "未知")")
            
            // 发送谷歌登录请求
            self.sendGoogleLoginRequest(appId: appId, idToken: idToken, clientId: clientId, endpoint: endpoint, completion: completion)
        }
    }
    
    // 发送谷歌登录请求
    private func sendGoogleLoginRequest(appId: String, idToken: String, clientId: String, endpoint: String, completion: @escaping (Result<DirectLoginData, Error>) -> Void) {
        // 构建JSON数据
        let requestDict: [String: Any] = [
            "appId": appId,
            "idToken": idToken,
            "clientId": clientId
        ]
        
        print("🔑 谷歌登录JSON参数详情:")
        print("  - appId: \(appId)")
        print("  - clientId: \(clientId)")
        print("  - idToken长度: \(idToken.count)")
        
        guard let requestData = try? JSONSerialization.data(withJSONObject: requestDict) else {
            print("❌ 谷歌登录请求数据编码失败")
            completion(.failure(NSError(domain: "GoogleLoginError", code: 0, userInfo: [NSLocalizedDescriptionKey: "无法编码JSON请求数据"])))
            return
        }
        
        print("📦 谷歌登录JSON请求数据准备完成，请求体大小: \(requestData.count) 字节")
        if let requestStr = String(data: requestData, encoding: .utf8) {
            print("📝 谷歌登录JSON请求内容: \(requestStr)")
        }
        
        var urlRequest = URLRequest(url: URL(string: endpoint)!)
        urlRequest.httpMethod = "POST"
        urlRequest.httpBody = requestData
        urlRequest.addValue("application/json", forHTTPHeaderField: "Content-Type")
        
        // 添加CSRF Token到请求头
        if let token = csrfToken {
            urlRequest.addValue(token, forHTTPHeaderField: "X-CSRF-TOKEN")
            print("✅ 添加CSRF Token到谷歌登录请求头: \(token)")
        } else {
            // 使用默认Token
            urlRequest.addValue("29afa5f6-ccfa-44d2-b4d6-6bdd98c7ebb6", forHTTPHeaderField: "X-CSRF-TOKEN")
            print("⚠️ 使用默认CSRF Token: 29afa5f6-ccfa-44d2-b4d6-6bdd98c7ebb6")
        }
        
        // 添加JSESSIONID到Cookie
        if let sessionId = jsessionId {
            urlRequest.addValue("JSESSIONID=\(sessionId); tgw_l7_route=5f4f12d20b9b450e602a4f2ad371ca95", forHTTPHeaderField: "Cookie")
            print("✅ 添加JSESSIONID到谷歌登录Cookie: \(sessionId)")
        } else {
            // 使用默认JSESSIONID
            urlRequest.addValue("JSESSIONID=649D78A6D655503161761FE2A7A6E150; tgw_l7_route=5f4f12d20b9b450e602a4f2ad371ca95", forHTTPHeaderField: "Cookie")
            print("⚠️ 使用默认JSESSIONID: 649D78A6D655503161761FE2A7A6E150")
        }
        
        // 添加其他通用头
        urlRequest.addValue("*/*", forHTTPHeaderField: "Accept")
        urlRequest.addValue("fsai-auth.pickgoodspro.com", forHTTPHeaderField: "Host")
        urlRequest.addValue("keep-alive", forHTTPHeaderField: "Connection")
        
        // 打印完整的请求头信息
        print("📋 完整谷歌登录请求头: \(urlRequest.allHTTPHeaderFields ?? [:])")
        
        print("🚀 开始发送谷歌登录API请求")
        let task = URLSession.shared.dataTask(with: urlRequest) { [weak self] data, response, error in
            guard let self = self else { return }
            
            if let error = error {
                print("❌ 谷歌登录网络请求错误: \(error.localizedDescription)")
                completion(.failure(error))
                return
            }
            
            guard let httpResponse = response as? HTTPURLResponse else {
                print("❌ 无效的HTTP响应")
                completion(.failure(NSError(domain: "GoogleLoginError", code: 0, userInfo: [NSLocalizedDescriptionKey: "无效的HTTP响应"])))
                return
            }
            
            print("📊 收到谷歌登录HTTP响应，状态码: \(httpResponse.statusCode)")
            print("📋 响应头: \(httpResponse.allHeaderFields)")
            
            // 处理Set-Cookie响应头，提取新的JSESSIONID
            self.updateSessionFromResponse(httpResponse)
            
            guard let data = data else {
                print("❌ 谷歌登录响应数据为空")
                completion(.failure(NSError(domain: "GoogleLoginError", code: 0, userInfo: [NSLocalizedDescriptionKey: "没有接收到数据"])))
                return
            }
            
            // 打印响应数据以便调试
            if let responseString = String(data: data, encoding: .utf8) {
                print("📄 谷歌登录响应数据: \(responseString)")
            }
            
            if httpResponse.statusCode != 200 {
                print("⚠️ 谷歌登录HTTP状态码非200: \(httpResponse.statusCode)")
                do {
                    if let jsonResponse = try JSONSerialization.jsonObject(with: data, options: []) as? [String: Any],
                       let message = jsonResponse["message"] as? String {
                        print("❌ 服务器返回错误信息: \(message)")
                        completion(.failure(NSError(domain: "GoogleLoginError", code: httpResponse.statusCode, userInfo: [NSLocalizedDescriptionKey: message])))
                    } else {
                        print("❌ 无法解析错误响应")
                        completion(.failure(NSError(domain: "GoogleLoginError", code: httpResponse.statusCode, userInfo: [NSLocalizedDescriptionKey: "登录失败，状态码: \(httpResponse.statusCode)"])))
                    }
                } catch {
                    print("❌ 解析错误响应时发生异常: \(error)")
                    completion(.failure(NSError(domain: "GoogleLoginError", code: httpResponse.statusCode, userInfo: [NSLocalizedDescriptionKey: "登录失败，状态码: \(httpResponse.statusCode)"])))
                }
                return
            }
            
            print("✅ 成功接收谷歌登录HTTP 200响应，开始解析数据")
            do {
                if let jsonResponse = try JSONSerialization.jsonObject(with: data, options: []) as? [String: Any] {
                    print("📊 谷歌登录响应JSON解析成功，字段: \(jsonResponse.keys.joined(separator: ", "))")
                    if let loginData = self.parseLoginResponse(jsonResponse) {
                        print("✅ 成功解析谷歌登录数据，AccessToken: \(loginData.accessToken?.prefix(10) ?? "无")...")
                        completion(.success(loginData))
                    } else {
                        print("❌ 无法从JSON创建DirectLoginData对象")
                        completion(.failure(NSError(domain: "GoogleLoginError", code: 0, userInfo: [NSLocalizedDescriptionKey: "无法解析登录响应"])))
                    }
                } else {
                    print("❌ 响应不是有效的JSON对象")
                    completion(.failure(NSError(domain: "GoogleLoginError", code: 0, userInfo: [NSLocalizedDescriptionKey: "无法解析登录响应为JSON"])))
                }
            } catch {
                print("❌ JSON解析异常: \(error)")
                completion(.failure(error))
            }
        }
        
        task.resume()
        print("🔄 谷歌登录API请求已发送，等待响应")
    }

    // 刷新AccessToken
    func refreshAccessToken(refreshToken: String, completion: @escaping (Result<RefreshTokenResponse, Error>) -> Void) {
        // 首先确保有有效的CSRF-Token
        ensureCSRFToken { [weak self] success in
            guard let self = self else { return }
            
            if !success {
                print("⚠️ 无法获取CSRF-Token，将使用默认值继续尝试刷新Token")
            }
            
            // 执行实际的刷新请求
            self.performRefreshTokenRequest(refreshToken: refreshToken, completion: completion)
        }
    }
    
    // 执行实际的刷新Token请求
    private func performRefreshTokenRequest(refreshToken: String, completion: @escaping (Result<RefreshTokenResponse, Error>) -> Void) {
        // 使用文档中的URL
        let endpoint = "https://fsai-auth.pickgoodspro.com/api/refresh-token"
        
        guard let url = URL(string: endpoint) else {
            completion(.failure(NSError(domain: "InvalidURL", code: 0, userInfo: nil)))
            return
        }
        
        let refreshRequest = RefreshTokenRequest(refreshToken: refreshToken)
        
        var request = URLRequest(url: url, timeoutInterval: Double.infinity)
        request.httpMethod = "POST"
        request.addValue("application/json", forHTTPHeaderField: "Content-Type")
        
        // 添加CSRF-Token头信息
        if let token = csrfToken {
            request.addValue(token, forHTTPHeaderField: "X-CSRF-TOKEN")
            print("✅ 添加CSRF-Token到刷新请求: \(token)")
        } else {
            // 使用默认Token
            request.addValue("29afa5f6-ccfa-44d2-b4d6-6bdd98c7ebb6", forHTTPHeaderField: "X-CSRF-TOKEN")
            print("⚠️ 使用默认CSRF-Token: 29afa5f6-ccfa-44d2-b4d6-6bdd98c7ebb6")
        }
        
        // 添加Cookie头信息
        if let sessionId = jsessionId {
            request.addValue("JSESSIONID=\(sessionId); tgw_l7_route=5f4f12d20b9b450e602a4f2ad371ca95", forHTTPHeaderField: "Cookie")
            print("✅ 添加JSESSIONID到刷新请求: \(sessionId)")
        } else {
            // 使用默认Cookie
            request.addValue("JSESSIONID=649D78A6D655503161761FE2A7A6E150; tgw_l7_route=5f4f12d20b9b450e602a4f2ad371ca95", forHTTPHeaderField: "Cookie")
            print("⚠️ 使用默认JSESSIONID: 649D78A6D655503161761FE2A7A6E150")
        }
        
        // 添加其他标准头信息
        request.addValue("*/*", forHTTPHeaderField: "Accept")
        request.addValue("fsai-auth.pickgoodspro.com", forHTTPHeaderField: "Host")
        request.addValue("keep-alive", forHTTPHeaderField: "Connection")
        
        do {
            let jsonData = try JSONEncoder().encode(refreshRequest)
            request.httpBody = jsonData
            
            print("刷新Token请求参数: \(String(data: jsonData, encoding: .utf8) ?? "")")
            print("刷新Token请求头: \(request.allHTTPHeaderFields ?? [:])")
            
            let task = URLSession.shared.dataTask(with: request) { data, response, error in
                if let error = error {
                    DispatchQueue.main.async {
                        print("刷新Token网络错误: \(error.localizedDescription)")
                        completion(.failure(error))
                    }
                    return
                }
                
                if let httpResponse = response as? HTTPURLResponse {
                    print("刷新Token API HTTP状态码: \(httpResponse.statusCode)")
                    
                    if !(200...299).contains(httpResponse.statusCode) {
                        if let data = data, let responseString = String(data: data, encoding: .utf8) {
                            print("刷新Token错误响应内容: \(responseString)")
                        }
                        
                        DispatchQueue.main.async {
                            let errorMessage = "服务器返回错误码: \(httpResponse.statusCode)"
                            completion(.failure(NSError(domain: "HTTPError", code: httpResponse.statusCode, userInfo: [NSLocalizedDescriptionKey: errorMessage])))
                        }
                        return
                    }
                }
                
                guard let data = data else {
                    DispatchQueue.main.async {
                        completion(.failure(NSError(domain: "NoData", code: 0, userInfo: nil)))
                    }
                    return
                }
                
                // 打印响应内容，便于调试
                if let responseString = String(data: data, encoding: .utf8) {
                    print("刷新Token API响应: \(responseString)")
                }
                
                do {
                    // 先尝试解析为JSON字典
                    if let jsonResponse = try JSONSerialization.jsonObject(with: data) as? [String: Any] {
                        // 创建RefreshTokenResponse对象
                        let refreshResponse = RefreshTokenResponse(
                            accessToken: jsonResponse["accessToken"] as? String,
                            tokenType: jsonResponse["tokenType"] as? String ?? "Bearer",
                            refreshToken: jsonResponse["refreshToken"] as? String,
                            expiresIn: jsonResponse["expiresIn"] as? Int ?? 86400, // 默认24小时
                            scope: jsonResponse["scope"] as? String,
                            userId: jsonResponse["userId"] as? String,
                            userType: jsonResponse["userType"] as? String,
                            userName: jsonResponse["userName"] as? String
                        )
                        
                        DispatchQueue.main.async {
                            completion(.success(refreshResponse))
                        }
                    } else {
                        // 尝试直接解码
                        let refreshResponse = try JSONDecoder().decode(RefreshTokenResponse.self, from: data)
                        DispatchQueue.main.async {
                            completion(.success(refreshResponse))
                        }
                    }
                } catch {
                    DispatchQueue.main.async {
                        print("刷新Token响应解析失败: \(error)")
                        completion(.failure(error))
                    }
                }
            }
            
            task.resume()
        } catch {
            completion(.failure(error))
        }
    }

    // 从JWT token中解析过期时间
    private func parseExpiresInFromJWT(_ accessToken: String) -> Int? {
        guard accessToken.hasPrefix("ey") && accessToken.contains(".") else {
            print("⚠️ Token不是标准JWT格式")
            return nil
        }
        
        let components = accessToken.components(separatedBy: ".")
        guard components.count >= 2 else {
            print("⚠️ JWT token格式异常，组件数量不足")
            return nil
        }
        
        let base64String = components[1].padding(toLength: ((components[1].count + 3) / 4) * 4, withPad: "=", startingAt: 0)
        
        guard let payloadData = Data(base64Encoded: base64String),
              let payload = try? JSONSerialization.jsonObject(with: payloadData, options: []) as? [String: Any],
              let exp = payload["exp"] as? TimeInterval else {
            print("⚠️ 无法解析JWT token的payload或exp字段")
            return nil
        }
        
        let expirationDate = Date(timeIntervalSince1970: exp)
        let currentTime = Date()
        let timeUntilExpiry = expirationDate.timeIntervalSince(currentTime)
        
        if timeUntilExpiry > 0 {
            let calculatedExpiresIn = Int(timeUntilExpiry)
            print("✅ 从JWT token解析到过期时间: \(expirationDate), 剩余时间: \(timeUntilExpiry)秒")
            return calculatedExpiresIn
        } else {
            print("⚠️ JWT token已过期，过期时间: \(expirationDate)")
            return nil
        }
    }
    
    // 获取最终的expiresIn值（优先使用JWT解析，其次API返回，最后默认值）
    private func getFinalExpiresIn(accessToken: String?, apiExpiresIn: Int?, defaultValue: Int = 3600) -> Int {
        // 优先从JWT token解析
        if let accessToken = accessToken,
           let jwtExpiresIn = parseExpiresInFromJWT(accessToken) {
            print("📅 使用从JWT解析的过期时间: \(jwtExpiresIn)秒")
            return jwtExpiresIn
        }
        
        // 其次使用API返回的值
        if let apiValue = apiExpiresIn {
            print("📅 使用API返回的expiresIn: \(apiValue)秒")
            return apiValue
        }
        
        // 最后使用默认值
        print("⚠️ 使用默认过期时间: \(defaultValue)秒")
        return defaultValue
    }
}

// 全局网络服务实例
let NS = NetworkService.shared

// 全局苹果登录方法，使用回调替代Promise
func net_appleLogin(userId: String, identityToken: String, authorizationCode: String, completion: @escaping (Result<DirectLoginData, Error>) -> Void) {
    NS.appleLogin(userId: userId, identityToken: identityToken, authorizationCode: authorizationCode, completion: completion)
}

// 全局谷歌登录方法
func net_googleLogin(appId: String, idToken: String, clientId: String, completion: @escaping (Result<DirectLoginData, Error>) -> Void) {
    NS.googleLogin(appId: appId, idToken: idToken, clientId: clientId, completion: completion)
}
