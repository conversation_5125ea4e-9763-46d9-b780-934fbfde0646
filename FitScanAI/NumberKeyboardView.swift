import SwiftUI
import UIKit

// 数字键盘输入视图
struct NumberKeyboardView: View {
    @EnvironmentObject var userData: UserData
    @Binding var value: String
    @Binding var showKeyboard: Bool
    @Binding var selectedUnit: String
    let title: String
    let units: [String]
    let onConfirm: (String, String) -> Void
    
    // 添加避免在iOS 15以下出现约束冲突的状态
    @State private var keyboardHeight: CGFloat = UIScreen.main.bounds.height * 0.35
    
    // 获取屏幕尺寸信息
    private var screenWidth: CGFloat { UIScreen.main.bounds.width }
    private var screenHeight: CGFloat { UIScreen.main.bounds.height }
    private var isSmallScreen: Bool { screenHeight < 700 } // iPhone SE等小屏设备
    
    // 自适应尺寸计算
    private var dialogWidth: CGFloat {
        if isSmallScreen {
            return screenWidth * 0.95 // 小屏幕使用更大比例
        } else {
            return screenWidth * 0.85
        }
    }
    
    private var numberDisplayFontSize: CGFloat {
        if isSmallScreen {
            return min(32, screenWidth * 0.08) // 小屏幕动态缩放字体
        } else {
            return 36
        }
    }
    
    private var keyboardButtonSize: CGFloat {
        if isSmallScreen {
            return min(50, screenWidth * 0.12) // 小屏幕按钮稍小
        } else {
            return 60
        }
    }
    
    private var keyboardButtonSpacing: CGFloat {
        if isSmallScreen {
            return screenWidth * 0.05 // 小屏幕动态间距
        } else {
            return 30
        }
    }
    
    private var keyboardButtonFont: Font {
        if isSmallScreen {
            return .title2 // 小屏幕使用稍小字体
        } else {
            return .title
        }
    }
    
    private var adaptiveKeyboardHeight: CGFloat {
        if isSmallScreen {
            return min(screenHeight * 0.45, 380) // 小屏幕限制最大高度
        } else {
            return screenHeight * 0.35
        }
    }
    
    var body: some View {
        ZStack {
            // 半透明背景
            Color.black.opacity(0.4)
                .edgesIgnoringSafeArea(.all)
                .onTapGesture {
                    showKeyboard = false
                }
            
            // 弹窗内容
            VStack(spacing: 0) {
                // 标题栏
                HStack {
                    Text(title)
                        .font(.headline)
                    
                    Spacer()
                    
                    Button(action: {
                        showKeyboard = false
                    }) {
                        Image(systemName: "xmark")
                            .foregroundColor(.gray)
                    }
                }
                .padding()
                .background(Color.white)
                
                // 体重显示区域
                HStack(alignment: .firstTextBaseline, spacing: 4) {
                    Spacer()
                    
                    Text(value)
                        .font(.system(size: numberDisplayFontSize, weight: .bold))
                        .minimumScaleFactor(0.7) // 允许字体缩放以适应小屏幕
                        .lineLimit(1)
                    
                    // 单位选择 - 下拉菜单
                    Menu {
                        ForEach(units, id: \.self) { unit in
                            Button(unit) {
                                if unit != selectedUnit {
                                    // 转换单位
                                    convertValue(from: selectedUnit, to: unit)
                                    selectedUnit = unit
                                    
                                    // 同步更新到用户数据
                                    if units.contains("kg") { // 如果是体重单位
                                        userData.weightUnit = unit
                                    } else if units.contains("cm") { // 如果是身高单位
                                        userData.heightUnit = unit
                                    }
                                }
                            }
                        }
                    } label: {
                        HStack(spacing: 2) {
                            Text(selectedUnit)
                                .font(.headline)
                                .foregroundColor(.green)
                            Image(systemName: "chevron.down")
                                .font(.caption)
                                .foregroundColor(.green)
                        }
                    }
                    
                    Spacer()
                }
                .padding(.vertical, isSmallScreen ? 8 : 10)
                .background(Color.white)
                
                // 数字键盘 - 3x4网格布局
                VStack(spacing: isSmallScreen ? 10 : 15) {
                    // 第一行: 1 2 3
                    HStack(spacing: keyboardButtonSpacing) {
                        numberButton("1")
                        numberButton("2")
                        numberButton("3")
                    }
                    .frame(height: keyboardButtonSize)
                    
                    // 第二行: 4 5 6
                    HStack(spacing: keyboardButtonSpacing) {
                        numberButton("4")
                        numberButton("5")
                        numberButton("6")
                    }
                    .frame(height: keyboardButtonSize)
                    
                    // 第三行: 7 8 9
                    HStack(spacing: keyboardButtonSpacing) {
                        numberButton("7")
                        numberButton("8")
                        numberButton("9")
                    }
                    .frame(height: keyboardButtonSize)
                    
                    // 第四行: . 0 删除
                    HStack(spacing: keyboardButtonSpacing) {
                        numberButton(".")
                        numberButton("0")
                        Button(action: {
                            removeLastDigit()
                        }) {
                            Image(systemName: "delete.left")
                                .font(keyboardButtonFont)
                                .frame(width: keyboardButtonSize, height: keyboardButtonSize)
                                .foregroundColor(.primary)
                        }
                    }
                    .frame(height: keyboardButtonSize)
                }
                .padding(.horizontal, 16)
                .padding(.vertical, isSmallScreen ? 12 : 16)
                
                // 确认按钮
                Button(action: {
                    onConfirm(value, selectedUnit)
                    showKeyboard = false
                }) {
                    Text("Confirm")
                        .font(.headline)
                        .foregroundColor(.white)
                        .frame(maxWidth: .infinity)
                        .padding(.vertical, 12)
                        .background(Color.green)
                        .cornerRadius(8)
                }
                .padding()
                .background(Color.white)
            }
            .background(Color.white)
            .cornerRadius(12)
            .shadow(radius: 5)
            .frame(width: dialogWidth)
            .frame(maxHeight: adaptiveKeyboardHeight)
            .accessibilityIdentifier("CustomKeyboard") // 添加标识符
            .onAppear {
                // 修复约束问题
                DispatchQueue.main.asyncAfter(deadline: .now() + 0.1) {
                    fixInputViewConstraints()
                }
            }
        }
    }
    
    // 数字按钮
    private func numberButton(_ text: String) -> some View {
        Button(action: {
            appendDigit(text)
        }) {
            Text(text)
                .font(keyboardButtonFont)
                .frame(width: keyboardButtonSize, height: keyboardButtonSize)
                .foregroundColor(.primary)
        }
    }
    
    // 添加数字
    private func appendDigit(_ digit: String) {
        // 如果是小数点，确保只有一个小数点
        if digit == "." && value.contains(".") {
            return
        }
        
        // 当值为"0.0"时，如果输入数字，清空并重新开始
        if value == "0.0" && digit != "." && digit.rangeOfCharacter(from: CharacterSet.decimalDigits) != nil {
            value = digit
            return
        }
        
        // 限制输入逻辑 - 修复版本：小数点前3位，小数点后1位
        if digit == "." && value.isEmpty {
            value = "0."
        } else if value == "0" && digit != "." {
            value = digit
        } else {
            // 检查当前值的格式
            let parts = value.components(separatedBy: ".")
            
            if digit == "." {
                // 添加小数点：只有在整数部分不超过3位且还没有小数点时才允许
                if parts.count == 1 && parts[0].count <= 3 {
                    value += digit
                }
            } else {
                // 添加数字
                if parts.count == 1 {
                    // 还没有小数点，检查整数部分是否已达到3位
                    if parts[0].count < 3 {
                        value += digit
                    }
                } else if parts.count == 2 {
                    // 已有小数点，检查小数部分是否已达到1位
                    if parts[1].count < 1 {
                        value += digit
                    }
                } else if parts.count == 0 {
                    // 空值情况
                    value += digit
                }
            }
        }
    }
    
    // 删除最后一位
    private func removeLastDigit() {
        if !value.isEmpty {
            value.removeLast()
        }
        
        // 如果删空了，设置为0
        if value.isEmpty {
            value = "0"
        }
    }
    
    // 转换单位
    private func convertValue(from sourceUnit: String, to targetUnit: String) {
        guard let numValue = Double(value) else { return }
        
        if sourceUnit == "kg" && targetUnit == "lbs" {
            // 从千克转换为磅
            value = String(format: "%.1f", numValue * 2.20462)
        } else if sourceUnit == "lbs" && targetUnit == "kg" {
            // 从磅转换为千克
            value = String(format: "%.1f", numValue / 2.20462)
        } else if sourceUnit == "cm" && targetUnit == "ft" {
            // 从厘米转换为英尺
            let inches = numValue / 2.54
            value = String(format: "%.1f", inches / 12)
        } else if sourceUnit == "ft" && targetUnit == "cm" {
            // 从英尺转换为厘米
            value = String(format: "%.1f", numValue * 12 * 2.54)
        }
    }
    
    // 添加修复约束的方法
    private func fixInputViewConstraints() {
        // 设置合适的键盘高度，避免约束冲突
        keyboardHeight = min(350, UIScreen.main.bounds.height * 0.35)
        
        // 查找和修复所有相关约束
        if #available(iOS 15.0, *) {
            // 使用UIWindowScene.windows
            if let scene = UIApplication.shared.connectedScenes.first as? UIWindowScene,
               let keyWindow = scene.windows.first(where: { $0.isKeyWindow }) {
                findAndFixConstraints(in: keyWindow)
            }
        } else {
            // 兼容iOS 15以下版本
            if let keyWindow = UIApplication.shared.windows.first(where: { $0.isKeyWindow }) {
                findAndFixConstraints(in: keyWindow)
            }
        }
    }
    
    // 递归查找视图层次结构中的约束问题
    private func findAndFixConstraints(in view: UIView) {
        // 检查视图类名
        let className = NSStringFromClass(type(of: view))
        
        // 如果找到SystemInputAssistantView，修复它的高度约束
        if className.contains("SystemInputAssistant") {
            // 查找可能引起冲突的约束
            var constraintToRemove: NSLayoutConstraint? = nil
            
            for constraint in view.constraints {
                if constraint.identifier == "assistantHeight" || 
                   (constraint.firstAttribute == .height && constraint.relation == .equal) {
                    constraintToRemove = constraint
                    break
                }
            }
            
            // 如果找到了约束，移除它并添加新的约束
            if let constraint = constraintToRemove {
                view.removeConstraint(constraint)
                
                // 添加高度为0的约束
                let zeroHeightConstraint = NSLayoutConstraint(
                    item: view,
                    attribute: .height,
                    relatedBy: .equal,
                    toItem: nil,
                    attribute: .notAnAttribute,
                    multiplier: 1.0,
                    constant: 0
                )
                zeroHeightConstraint.priority = .required
                zeroHeightConstraint.identifier = "fixedHeight"
                view.addConstraint(zeroHeightConstraint)
            }
        }
        
        // 递归检查子视图
        for subview in view.subviews {
            findAndFixConstraints(in: subview)
        }
    }
}