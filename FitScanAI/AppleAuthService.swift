import SwiftUI
import AuthenticationServices

// 苹果登录协调器类
class SignInWithAppleCoordinator: NSObject, ASAuthorizationControllerDelegate, ASAuthorizationControllerPresentationContextProviding {
    private var userData: UserData?
    private var shouldSendNotification: Bool
    private var completion: ((Bool) -> Void)?
    
    // 定义Keychain的键名常量
    private enum KeychainKeys {
        static let appleAuthUserId = "appleAuthUserId"
        static let appleAuthIdentityToken = "appleAuthIdentityToken"
        static let appleAuthAuthorizationCode = "appleAuthAuthorizationCode"
    }
    
    init(userData: UserData? = nil, shouldSendNotification: Bool = true, completion: ((Bool) -> Void)? = nil) {
        self.userData = userData
        self.shouldSendNotification = shouldSendNotification
        self.completion = completion
        super.init()
    }

    // 处理授权结果
    func authorizationController(controller: ASAuthorizationController, didCompleteWithAuthorization authorization: ASAuthorization) {
        if let appleIDCredential = authorization.credential as? ASAuthorizationAppleIDCredential {
            let userId = appleIDCredential.user
            
            // 注意：iOS 17+中由于苹果隐私政策限制，无法获取email和fullName
            // 这些信息只在首次授权时提供，后续授权时可能为nil
            // 因此我们不再依赖这些信息
            
            // 获取identityToken数据
            guard let identityTokenData = appleIDCredential.identityToken else {
                print("❌ 苹果登录授权失败：无法获取identityToken")
                return
            }
            
            // 获取authorizationCode数据
            guard let authorizationCodeData = appleIDCredential.authorizationCode else {
                print("❌ 苹果登录授权失败：无法获取authorizationCode")
                return
            }
            
            // 转换为字符串
            guard let identityToken = String(data: identityTokenData, encoding: .utf8) else {
                print("❌ 苹果登录授权失败：无法将identityToken转换为字符串")
                return
            }
            
            guard let authorizationCode = String(data: authorizationCodeData, encoding: .utf8) else {
                print("❌ 苹果登录授权失败：无法将authorizationCode转换为字符串")
                return
            }

            // 将授权数据传递给后端服务器
            print("🍎 苹果登录授权成功，获取到以下信息:")
            print("👤 User ID: \(userId)")
            print("🔑 Identity Token: \(identityToken.prefix(20))...(长度:\(identityToken.count))")
            print("🔐 Authorization Code: \(authorizationCode.prefix(20))...(长度:\(authorizationCode.count))")
            print("📝 注意：由于iOS 17+隐私政策，无法获取email和fullName信息")
            
            
            
            // 保存授权信息到Keychain
            do {
                try KeychainHelper.shared.saveString(userId, forKey: KeychainKeys.appleAuthUserId)
                try KeychainHelper.shared.saveString(identityToken, forKey: KeychainKeys.appleAuthIdentityToken)
                try KeychainHelper.shared.saveString(authorizationCode, forKey: KeychainKeys.appleAuthAuthorizationCode)
                print("✅ 苹果登录信息已成功保存到Keychain")
            } catch {
                print("❌ 保存苹果登录信息到Keychain失败: \(error)")
            }
            
            // 首先强制重置会话并刷新CSRF令牌，确保每次都能获取新的令牌
            print("🔄 强制重置会话状态并获取新的CSRF令牌")
            NetworkService.shared.resetSessionState()
            
            // 向后端发送请求，执行苹果登录
            print("🚀 正在向后端发送苹果登录请求...")
            net_appleLogin(userId: userId, identityToken: identityToken, authorizationCode: authorizationCode) { [weak self] result in
                // 确保在主线程处理UI更新
                DispatchQueue.main.async {
                    switch result {
                    case .success(let loginData):
                        print("✅ 苹果登录API请求成功，获取到token: \(loginData.accessToken ?? "无")")
                        
                        // 设置标记，表示这是通过苹果登录的用户
                        UserDefaults.standard.set(true, forKey: "isAppleLogin")
                        print("✅ 已将isAppleLogin设置为true")
                        
                        // 为确保访问令牌一定被保存，总是保存到UserDefaults
                        if let accessToken = loginData.accessToken {
                            UserDefaults.standard.set(accessToken, forKey: "accessToken")
                            print("✅ 已直接保存accessToken到UserDefaults: \(accessToken.prefix(10))...")
                            
                            // 同时保存到Keychain，用于token过期时的自动恢复
                            do {
                                try KeychainHelper.shared.saveString(accessToken, forKey: "appleLoginAccessToken")
                                print("✅ 已将accessToken保存到Keychain")
                            } catch {
                                print("⚠️ 保存accessToken到Keychain失败: \(error)")
                            }
                        }
                        
                        if let refreshToken = loginData.refreshToken {
                            UserDefaults.standard.set(refreshToken, forKey: "refreshToken")
                            print("✅ 已直接保存refreshToken到UserDefaults")
                            
                            // 同时保存到Keychain，用于token过期时的自动恢复
                            do {
                                try KeychainHelper.shared.saveString(refreshToken, forKey: "appleLoginRefreshToken")
                                print("✅ 已将refreshToken保存到Keychain")
                            } catch {
                                print("⚠️ 保存refreshToken到Keychain失败: \(error)")
                            }
                        }
                        
                        // 确保正确保存令牌过期时间
                        if let expiresIn = loginData.expiresIn {
                            let expiresAt = Date().addingTimeInterval(TimeInterval(expiresIn))
                            UserDefaults.standard.set(expiresAt, forKey: "tokenExpiresAt")
                            print("✅ 已设置令牌过期时间: \(expiresAt)")
                        }
                        
                        if let sharedUserData = self?.userData {
                            // 使用传入的共享UserData实例
                            print("✅ 使用共享的userData实例保存登录信息")
                            sharedUserData.setLogin(loginData)
                            
                            // 手动设置登录状态标志
                            UserDefaults.standard.set(true, forKey: "isLoggedIn")
                            print("✅ 已将isLoggedIn设置为true")
                            
                            // 根据shouldSendNotification参数决定是否发送通知
                            if self?.shouldSendNotification == true {
                            NotificationCenter.default.post(name: Notification.Name("UserLoggedIn"), object: nil)
                            print("✅ 已发送UserLoggedIn通知")
                            
                            // 延迟执行后续操作，确保状态已更新
                            DispatchQueue.main.asyncAfter(deadline: .now() + 0.5) {
                                print("✅ 执行登录成功后续操作")
                                sharedUserData.doSthAfterLoginSuccess()
                                    
                                    // 调用completion回调，表示登录成功
                                    self?.completion?(true)
                                }
                            } else {
                                print("⚠️ 根据shouldSendNotification参数，跳过发送UserLoggedIn通知")
                                // 即使不发送通知，也要执行后续操作
                                DispatchQueue.main.asyncAfter(deadline: .now() + 0.5) {
                                    print("✅ 执行登录成功后续操作（无通知模式）")
                                    sharedUserData.doSthAfterLoginSuccess()
                                    
                                    // 调用completion回调，表示登录成功
                                    self?.completion?(true)
                                }
                            }
                        } else {
                            // 创建一个新的UserData实例作为后备方案
                            print("⚠️ 警告：未找到共享的userData实例，创建新实例")
                            let userData = UserData()
                            userData.setLogin(loginData)
                            
                            // 手动设置登录状态标志
                            UserDefaults.standard.set(true, forKey: "isLoggedIn")
                            print("✅ 已将isLoggedIn设置为true")
                            
                            // 根据shouldSendNotification参数决定是否发送通知
                            if self?.shouldSendNotification == true {
                            NotificationCenter.default.post(name: Notification.Name("UserLoggedIn"), object: nil)
                            print("✅ 已发送UserLoggedIn通知")
                            
                            // 延迟执行后续操作，确保状态已更新
                            DispatchQueue.main.asyncAfter(deadline: .now() + 0.5) {
                                print("✅ 执行登录成功后续操作")
                                userData.doSthAfterLoginSuccess()
                                    
                                    // 调用completion回调，表示登录成功
                                    self?.completion?(true)
                                }
                            } else {
                                print("⚠️ 根据shouldSendNotification参数，跳过发送UserLoggedIn通知")
                                // 即使不发送通知，也要执行后续操作
                                DispatchQueue.main.asyncAfter(deadline: .now() + 0.5) {
                                    print("✅ 执行登录成功后续操作（无通知模式）")
                                    userData.doSthAfterLoginSuccess()
                                    
                                    // 调用completion回调，表示登录成功
                                    self?.completion?(true)
                                }
                            }
                        }
                    case .failure(let error):
                        print("❌ 苹果登录API请求失败: \(error.localizedDescription)")
                        
                        // 保存错误信息到UserDefaults，便于排查
                        UserDefaults.standard.set(error.localizedDescription, forKey: "appleLoginError")
                        
                        // 清除苹果登录标记 (如果需要，也可以考虑从Keychain中删除相关信息)
                        // 例如：
                        // do {
                        //     try KeychainHelper.shared.deleteString(forKey: SignInWithAppleCoordinator.KeychainKeys.appleAuthUserId)
                        //     try KeychainHelper.shared.deleteString(forKey: SignInWithAppleCoordinator.KeychainKeys.appleAuthIdentityToken)
                        //     try KeychainHelper.shared.deleteString(forKey: SignInWithAppleCoordinator.KeychainKeys.appleAuthAuthorizationCode)
                        // } catch {
                        //     print("❌ 清除Keychain中的苹果登录信息失败: \(error)")
                        // }
                        UserDefaults.standard.set(false, forKey: "isAppleLogin")
                        
                        // 调用completion回调，表示登录失败
                        self?.completion?(false)
                    }
                }
            }
        } else {
            print("❌ 苹果登录授权失败：获取凭据类型错误")
        }
    }

    func authorizationController(controller: ASAuthorizationController, didCompleteWithError error: Error) {
        // 记录错误详情
        print("❌ 苹果登录授权失败: \(error.localizedDescription)")
        
        // 保存错误信息到UserDefaults，便于调试
        UserDefaults.standard.set(error.localizedDescription, forKey: "appleAuthError")
        
        // 根据错误类型提供更具体的信息
        if let authError = error as? ASAuthorizationError {
            switch authError.code {
            case .canceled:
                print("⚠️ 用户取消了授权")
            case .invalidResponse:
                print("❌ 无效的响应")
            case .notHandled:
                print("❌ 授权请求未处理")
            case .failed:
                print("❌ 授权失败")
            case .unknown:
                print("❌ 未知错误")
            default:
                print("❌ 其他错误: \(authError.code.rawValue)")
            }
        }
        
        // 清除苹果登录标记 (如果需要，也可以考虑从Keychain中删除相关信息)
        // 例如：
        // do {
        //     try KeychainHelper.shared.deleteString(forKey: SignInWithAppleCoordinator.KeychainKeys.appleAuthUserId)
        //     try KeychainHelper.shared.deleteString(forKey: SignInWithAppleCoordinator.KeychainKeys.appleAuthIdentityToken)
        //     try KeychainHelper.shared.deleteString(forKey: SignInWithAppleCoordinator.KeychainKeys.appleAuthAuthorizationCode)
        // } catch {
        //     print("❌ 清除Keychain中的苹果登录信息失败: \(error)")
        // }
        UserDefaults.standard.set(false, forKey: "isAppleLogin")
        
        // 调用completion回调，表示登录失败
        self.completion?(false)
    }

    // 指定展示的视图
    func presentationAnchor(for controller: ASAuthorizationController) -> ASPresentationAnchor {
        // 使用新的iOS 15+推荐的方式获取窗口
        if #available(iOS 15.0, *) {
            // 获取连接到场景的第一个窗口
            let scenes = UIApplication.shared.connectedScenes
            let windowScene = scenes.first as? UIWindowScene
            return windowScene?.windows.first ?? UIWindow()
        } else {
            // 旧方式，iOS 15之前
            return UIApplication.shared.windows.first { $0.isKeyWindow } ?? UIWindow()
        }
    }
}

// 苹果登录服务类
class AppleAuthService {
    static let shared = AppleAuthService()
    
    // 添加一个属性来保持coordinator的引用
    private var activeCoordinator: SignInWithAppleCoordinator?
    
    // 开始苹果登录流程
    func startSignInWithApple(userData: UserData? = nil, shouldSendNotification: Bool = true, completion: ((Bool) -> Void)? = nil) {
        print("🍎 创建苹果登录授权请求")
        let request = ASAuthorizationAppleIDProvider().createRequest()
        // 注意：iOS 17+中由于隐私政策限制，不再请求email和fullName权限
        // request.requestedScopes = [.fullName, .email]  // 已移除
        
        // 显示传入的userData实例状态
        if let userDataInstance = userData {
            print("🍎 已传入UserData实例，当前accessToken长度: \(userDataInstance.accessToken.count)")
            print("🍎 UserData实例ID: \(userDataInstance.id)")
        } else {
            print("⚠️ 警告: 未传入UserData实例，将尝试创建新实例")
        }
        
        // 创建并保存coordinator实例
        let coordinator = SignInWithAppleCoordinator(userData: userData, shouldSendNotification: shouldSendNotification, completion: completion)
        self.activeCoordinator = coordinator
        print("🍎 创建SignInWithAppleCoordinator实例并保存引用")

        let controller = ASAuthorizationController(authorizationRequests: [request])
        controller.delegate = coordinator
        controller.presentationContextProvider = coordinator
        print("🍎 开始执行苹果登录请求")
        controller.performRequests()
    }
} 