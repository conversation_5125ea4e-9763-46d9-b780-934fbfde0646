import Foundation
import SwiftUI

// MARK: - 数据模型

/// 打卡日志类型枚举
enum LogsType: String, CaseIterable {
    case morningWeight = "MORNING_WEIGHT"
    case breakfast = "LOG_BREAKFAST"
    case lunch = "LOG_LUNCH"
    case dinner = "LOG_DINNER"
    case eveningWeight = "EVENING_WEIGHT"
    
    var displayName: String {
        switch self {
        case .morningWeight:
            return "Morning Weight"
        case .eveningWeight:
            return "Evening Weight"
        case .breakfast:
            return "Breakfast Diary"
        case .lunch:
            return "Lunch Diary"
        case .dinner:
            return "Dinner Diary"
        }
    }
}

/// 打卡任务模型
struct CheckInTask: Identifiable {
    let id = UUID()
    let type: LogsType
    let isCompleted: Bool
    
    var displayName: String {
        return type.displayName
    }
}

/// 每日打卡状态模型
struct DailyCheckInStatus {
    let date: Date
    let tasks: [CheckInTask]
    
    var allCompleted: Bool {
        return !tasks.isEmpty && tasks.allSatisfy { $0.isCompleted }
    }
    
    var completedCount: Int {
        return tasks.filter { $0.isCompleted }.count
    }
    
    var totalCount: Int {
        return tasks.count
    }
}

/// 周打卡视图数据模型
struct WeeklyCheckInData {
    let weekDays: [Date]
    var dailyStatuses: [Date: DailyCheckInStatus]
    
    init(startDate: Date) {
        let calendar = Calendar.current
        var days: [Date] = []
        
        // 生成一周的日期（从周一开始）
        for i in 0..<7 {
            if let date = calendar.date(byAdding: .day, value: i, to: startDate) {
                days.append(date)
            }
        }
        
        self.weekDays = days
        self.dailyStatuses = [:]
    }
    
    mutating func updateDailyStatus(_ status: DailyCheckInStatus) {
        dailyStatuses[status.date] = status
    }
}

/// 获取单个打卡日志响应（用于获取单日数据）
struct GetLogsResponse: Codable {
    let code: Int
    let message: String?
    let data: UserPlansLogsResponse?
    let timestamp: Int64?
}

// MARK: - API响应模型

/// 设置打卡日志请求
struct UserPlansLogsRequest: Codable {
    let dateStr: String
    let logsTypeList: [String]
}

struct SetLogsResponse: Codable {
    let code: Int
    let message: String?
    let data: Bool?
    let timestamp: Int64?
}

// MARK: - 打卡服务类

class CheckInService: ObservableObject {
    static let shared = CheckInService()
    
    private let baseURL = "https://fsai.pickgoodspro.com"
    
    @Published var isLoading = false
    
    private init() {
        // 简化初始化
    }
    
    // MARK: - API调用方法
    
    /// 获取指定日期的打卡日志
    func getCheckInLogs(for date: Date, userData: UserData, completion: @escaping (Result<[LogsType], Error>) -> Void) {
        let dateFormatter = DateFormatter()
        dateFormatter.dateFormat = "yyyyMMdd"
        let dateStr = dateFormatter.string(from: date)
        
        guard let url = URL(string: "\(baseURL)/ns/app/user-plans-logs/user?dateStr=\(dateStr)") else {
            completion(.failure(CheckInError.invalidURL))
            return
        }
        
        var request = URLRequest(url: url)
        request.httpMethod = "GET"
        request.addValue("Bearer \(userData.accessToken)", forHTTPHeaderField: "Authorization")
        
        print("🌐 获取打卡日志 [GET] \(url)")
        
        URLSession.shared.dataTask(with: request) { data, response, error in
            if let error = error {
                print("❌ 获取打卡日志错误: \(error.localizedDescription)")
                DispatchQueue.main.async {
                    completion(.failure(error))
                }
                return
            }
            
            guard let data = data else {
                DispatchQueue.main.async {
                    completion(.failure(CheckInError.noData))
                }
                return
            }
            
            if let responseString = String(data: data, encoding: .utf8) {
                print("📄 获取打卡日志响应: \(responseString)")
            }
            
            do {
                let response = try JSONDecoder().decode(GetLogsResponse.self, from: data)
                
                if response.code == 0, let logsData = response.data {
                    let logTypes = logsData.logsTypeList.compactMap { LogsType(rawValue: $0) }
                    print("✅ 获取打卡日志成功: \(logTypes.map { $0.rawValue })")
                    DispatchQueue.main.async {
                        completion(.success(logTypes))
                    }
                } else {
                    let errorMessage = response.message ?? "获取打卡日志失败"
                    DispatchQueue.main.async {
                        completion(.failure(CheckInError.apiError(errorMessage)))
                    }
                }
            } catch {
                print("❌ 解析打卡日志响应失败: \(error.localizedDescription)")
                DispatchQueue.main.async {
                    completion(.failure(error))
                }
            }
        }.resume()
    }
    
    /// 设置打卡日志
    func setCheckInLogs(for date: Date, logTypes: [LogsType], userData: UserData, completion: @escaping (Result<Bool, Error>) -> Void) {
        let dateFormatter = DateFormatter()
        dateFormatter.dateFormat = "yyyyMMdd"
        let dateStr = dateFormatter.string(from: date)
        
        guard let url = URL(string: "\(baseURL)/ns/app/user-plans-logs/edit") else {
            completion(.failure(CheckInError.invalidURL))
            return
        }
        
        var request = URLRequest(url: url)
        request.httpMethod = "POST"
        request.addValue("application/json", forHTTPHeaderField: "Content-Type")
        request.addValue("Bearer \(userData.accessToken)", forHTTPHeaderField: "Authorization")
        
        let requestBody = UserPlansLogsRequest(
            dateStr: dateStr,
            logsTypeList: logTypes.map { $0.rawValue }
        )
        
        do {
            let jsonData = try JSONEncoder().encode(requestBody)
            request.httpBody = jsonData
            
            print("🌐 设置打卡日志 [POST] \(url)")
            if let bodyString = String(data: jsonData, encoding: .utf8) {
                print("📦 请求体: \(bodyString)")
            }
            
            URLSession.shared.dataTask(with: request) { data, response, error in
                if let error = error {
                    print("❌ 设置打卡日志错误: \(error.localizedDescription)")
                    DispatchQueue.main.async {
                        completion(.failure(error))
                    }
                    return
                }
                
                guard let data = data else {
                    DispatchQueue.main.async {
                        completion(.failure(CheckInError.noData))
                    }
                    return
                }
                
                if let responseString = String(data: data, encoding: .utf8) {
                    print("📄 设置打卡日志响应: \(responseString)")
                }
                
                do {
                    let response = try JSONDecoder().decode(SetLogsResponse.self, from: data)
                    
                    if response.code == 0, let success = response.data, success {
                        print("✅ 设置打卡日志成功")
                        DispatchQueue.main.async {
                            completion(.success(true))
                        }
                    } else {
                        let errorMessage = response.message ?? "设置打卡日志失败"
                        DispatchQueue.main.async {
                            completion(.failure(CheckInError.apiError(errorMessage)))
                        }
                    }
                } catch {
                    print("❌ 解析设置打卡日志响应失败: \(error.localizedDescription)")
                    DispatchQueue.main.async {
                        completion(.failure(error))
                    }
                }
            }.resume()
        } catch {
            completion(.failure(error))
        }
    }
    
    // MARK: - 业务逻辑方法
    
    /// 检查当前状态并获取应该打卡的项目
    func getCurrentCheckInStatus(for date: Date, userData: UserData, completion: @escaping (DailyCheckInStatus) -> Void) {
        let dateFormatter = DateFormatter()
        dateFormatter.dateFormat = "yyyyMMdd"
        let dateStr = dateFormatter.string(from: date)
        
        print("🔍 [CheckIn] 开始检查日期 \(dateStr) 的打卡状态")
        
        // 如果用户已登录，优先使用打卡日志API的数据
        if !userData.accessToken.isEmpty {
            print("🌐 [CheckIn] 用户已登录，优先获取打卡日志API数据")
            
            getCheckInLogs(for: date, userData: userData) { result in
                print("📋 [CheckIn] 打卡日志API响应完成")
                
                switch result {
                case .success(let completedLogs):
                    print("✅ [CheckIn] 获取到API打卡记录: \(completedLogs.map { $0.rawValue })")
                    
                    // 基于API数据创建任务列表
                    var tasks: [CheckInTask] = []
                    for logType in LogsType.allCases {
                        let isCompleted = completedLogs.contains(logType)
                        tasks.append(CheckInTask(type: logType, isCompleted: isCompleted))
                        print("📝 [CheckIn] 任务 \(logType.displayName): \(isCompleted ? "✅" : "❌")")
                    }
                    
                    let apiStatus = DailyCheckInStatus(date: date, tasks: tasks)
                    print("📊 [CheckIn] API状态 - 完成: \(apiStatus.completedCount)/\(apiStatus.totalCount)")
                    
                    // 立即返回API状态
                    DispatchQueue.main.async {
                        completion(apiStatus)
                    }
                    
                    // 异步检查实际完成状态，用于补充API可能遗漏的数据
                    self.checkActualCompletionStatus(for: date, dateStr: dateStr, userData: userData) { actualStatus in
                        print("🔍 [CheckIn] 实际状态检查完成: \(actualStatus)")
                        
                        // 合并API数据和实际状态
                        var finalTasks: [CheckInTask] = []
                        var hasChanges = false
                        
                        for logType in LogsType.allCases {
                            // API数据优先，但如果实际状态显示完成而API没有，则补充
                            let apiCompleted = completedLogs.contains(logType)
                            let actualCompleted = actualStatus[logType] == true
                            let isCompleted = apiCompleted || actualCompleted
                            
                            finalTasks.append(CheckInTask(type: logType, isCompleted: isCompleted))
                            
                            // 检查是否有变化
                            if isCompleted != apiCompleted {
                                hasChanges = true
                                print("🔄 [CheckIn] 状态变化: \(logType.displayName) API:\(apiCompleted) -> 最终:\(isCompleted)")
                            }
                        }
                        
                        // 如果有变化，再次更新UI
                        if hasChanges {
                            let finalStatus = DailyCheckInStatus(date: date, tasks: finalTasks)
                            print("📊 [CheckIn] 最终状态 - 完成: \(finalStatus.completedCount)/\(finalStatus.totalCount)")
                            
                            DispatchQueue.main.async {
                                completion(finalStatus)
                            }
                        }
                    }
                    
                case .failure(let error):
                    print("❌ [CheckIn] 获取打卡日志失败: \(error.localizedDescription)，使用实际状态")
                    
                    // API失败时，回退到实际状态检查
                    self.checkActualCompletionStatus(for: date, dateStr: dateStr, userData: userData) { actualStatus in
                        var tasks: [CheckInTask] = []
                        for logType in LogsType.allCases {
                            let isCompleted = actualStatus[logType] == true
                            tasks.append(CheckInTask(type: logType, isCompleted: isCompleted))
                        }
                        
                        let status = DailyCheckInStatus(date: date, tasks: tasks)
                        DispatchQueue.main.async {
                            completion(status)
                        }
                    }
                }
            }
        } else {
            print("🔒 [CheckIn] 用户未登录，使用本地数据")
            
            // 未登录状态，使用本地数据
            checkActualCompletionStatus(for: date, dateStr: dateStr, userData: userData) { actualStatus in
                var tasks: [CheckInTask] = []
                for logType in LogsType.allCases {
                    let isCompleted = actualStatus[logType] == true
                    tasks.append(CheckInTask(type: logType, isCompleted: isCompleted))
                }
                
                let status = DailyCheckInStatus(date: date, tasks: tasks)
                DispatchQueue.main.async {
                    completion(status)
                }
            }
        }
    }
    
    /// 检查实际完成状态（体重记录和食物记录）
    private func checkActualCompletionStatus(for date: Date, dateStr: String, userData: UserData, completion: @escaping ([LogsType: Bool]) -> Void) {
        var status: [LogsType: Bool] = [:]
        
        // 如果用户已登录，优先从API获取数据
        if !userData.accessToken.isEmpty {
            let group = DispatchGroup()
            
            // 检查体重记录（从体重记录接口获取）
            group.enter()
            WeightRecordService.shared.getUserWeightRecords(date: dateStr, timeType: "AM", userData: userData) { result in
                status[.morningWeight] = result.success?.isEmpty == false
                group.leave()
            }
            
            group.enter()
            WeightRecordService.shared.getUserWeightRecords(date: dateStr, timeType: "PM", userData: userData) { result in
                status[.eveningWeight] = result.success?.isEmpty == false
                group.leave()
            }
            
            // 检查食物记录（从食物日记接口获取）
            group.enter()
            ImageUploadService.shared.getFoodRecords(dateStr: dateStr, authToken: userData.accessToken) { result in
                defer { group.leave() }
                
                guard let records = result.success else {
                    status[.breakfast] = false
                    status[.lunch] = false
                    status[.dinner] = false
                    return
                }
                
                var hasBreakfast = false
                var hasLunch = false
                var hasDinner = false
                
                for record in records {
                    let components = record.timeStr.components(separatedBy: " ")
                    guard components.count >= 2,
                          let hourStr = components[0].components(separatedBy: ":").first,
                          var hour = Int(hourStr) else { continue }
                    
                    let ampm = components[1].uppercased()
                    if ampm == "PM" && hour != 12 { hour += 12 }
                    else if ampm == "AM" && hour == 12 { hour = 0 }
                    
                    if (hour >= 0 && hour < 5) || (hour >= 5 && hour < 11) {
                        hasBreakfast = true
                    } else if hour >= 11 && hour < 17 {
                        hasLunch = true
                    } else if hour >= 17 {
                        hasDinner = true
                    }
                }
                
                status[.breakfast] = hasBreakfast
                status[.lunch] = hasLunch
                status[.dinner] = hasDinner
            }
            
            group.notify(queue: .main) {
                completion(status)
            }
        } else {
            // 未登录状态，使用本地数据作为备份
            checkLocalData(for: date, userData: userData, status: &status)
            completion(status)
        }
    }
    
    /// 检查本地数据（作为API的备份）
    private func checkLocalData(for date: Date, userData: UserData, status: inout [LogsType: Bool]) {
        let calendar = Calendar.current
        let startOfDay = calendar.startOfDay(for: date)
        let endOfDay = calendar.date(byAdding: .day, value: 1, to: startOfDay)!
        
        // 检查本地体重记录
        let isToday = calendar.isDate(date, inSameDayAs: Date())
        if isToday {
            status[.morningWeight] = userData.morningWeight > 0
            status[.eveningWeight] = userData.eveningWeight > 0
        } else {
            // 检查历史体重记录
            let morningWeight = userData.weightHistory.first { record in
                calendar.isDate(record.date, inSameDayAs: date) && record.isMorning
            }
            let eveningWeight = userData.weightHistory.first { record in
                calendar.isDate(record.date, inSameDayAs: date) && !record.isMorning
            }
            status[.morningWeight] = morningWeight != nil
            status[.eveningWeight] = eveningWeight != nil
        }
        
        // 检查本地食物记录
        let localFoodEntries = userData.foodEntries.filter { entry in
            entry.time >= startOfDay && entry.time < endOfDay
        }
        
        var hasBreakfast = false
        var hasLunch = false
        var hasDinner = false
        
        for entry in localFoodEntries {
            let hour = calendar.component(.hour, from: entry.time)
            if (hour >= 0 && hour < 5) || (hour >= 5 && hour < 11) {
                hasBreakfast = true
            } else if hour >= 11 && hour < 17 {
                hasLunch = true
            } else if hour >= 17 {
                hasDinner = true
            }
        }
        
        status[.breakfast] = hasBreakfast
        status[.lunch] = hasLunch
        status[.dinner] = hasDinner
    }
    
    /// 更新打卡状态
    func updateCheckInStatus(for date: Date, userData: UserData, completion: @escaping (Bool) -> Void) {
        getCurrentCheckInStatus(for: date, userData: userData) { status in
            let completedLogs = status.tasks.filter { $0.isCompleted }.map { $0.type }
            
            if !userData.accessToken.isEmpty {
                self.setCheckInLogs(for: date, logTypes: completedLogs, userData: userData) { result in
                    completion(result.success ?? false)
                }
            } else {
                // 未登录状态，仅更新本地状态
                userData.updateCheckInStatus(for: date, isCompleted: status.allCompleted)
                completion(true)
            }
        }
    }
}

// MARK: - 错误类型

enum CheckInError: LocalizedError {
    case invalidURL
    case noData
    case apiError(String)
    
    var errorDescription: String? {
        switch self {
        case .invalidURL:
            return "无效的URL"
        case .noData:
            return "服务器返回空数据"
        case .apiError(let message):
            return message
        }
    }
}

// MARK: - Result扩展

extension Result {
    var success: Success? {
        switch self {
        case .success(let value):
            return value
        case .failure:
            return nil
        }
    }
} 