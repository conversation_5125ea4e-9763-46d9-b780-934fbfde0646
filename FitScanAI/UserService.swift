import Foundation
import SwiftUI
import Combine

// 直接表示用户信息的结构体，与API返回格式匹配
struct UserInfo: Codable {
    let id: Int
    let loginType: String?
    let type: String?
    let identity: String?
    let username: String?
    let mobile: String?
    let email: String?
    let name: String?
    let nickname: String?
    let hasPassword: Bool?
    let avatar: String?
    let state: String?
    let active: Bool?
    let createdDate: String?
    let special: Bool?
    // 其他可能的字段可以根据需要添加
    
    // 添加自定义编码键，处理API响应中字段名与Swift命名规范不匹配的情况
    enum CodingKeys: String, CodingKey {
        case id
        case loginType = "loginType"
        case type
        case identity
        case username
        case mobile
        case email
        case name
        case nickname
        case hasPassword = "hasPassword"
        case avatar
        case state
        case active
        case createdDate = "createdDate"
        case special
    }
    
    // 添加自定义解码初始化方法，使其更灵活地处理可能缺失或类型不匹配的字段
    init(from decoder: Decoder) throws {
        let container = try decoder.container(keyedBy: CodingKeys.self)
        
        // 必须字段
        do {
            id = try container.decode(Int.self, forKey: .id)
        } catch {
            print("⚠️ 解码id字段失败: \(error)")
            // 尝试从字符串转换为Int
            if let idString = try? container.decode(String.self, forKey: .id),
               let idInt = Int(idString) {
                id = idInt
            } else {
                throw error // 如果无法转换，重新抛出原始错误
            }
        }
        
        // 可选字段
        loginType = try? container.decodeIfPresent(String.self, forKey: .loginType)
        type = try? container.decodeIfPresent(String.self, forKey: .type)
        identity = try? container.decodeIfPresent(String.self, forKey: .identity)
        username = try? container.decodeIfPresent(String.self, forKey: .username)
        mobile = try? container.decodeIfPresent(String.self, forKey: .mobile)
        email = try? container.decodeIfPresent(String.self, forKey: .email)
        name = try? container.decodeIfPresent(String.self, forKey: .name)
        nickname = try? container.decodeIfPresent(String.self, forKey: .nickname)
        
        // 布尔字段处理，支持布尔值或字符串形式的布尔值
        if let hasPasswordBool = try? container.decodeIfPresent(Bool.self, forKey: .hasPassword) {
            hasPassword = hasPasswordBool
        } else if let hasPasswordString = try? container.decodeIfPresent(String.self, forKey: .hasPassword) {
            hasPassword = hasPasswordString.lowercased() == "true"
        } else {
            hasPassword = nil
        }
        
        avatar = try? container.decodeIfPresent(String.self, forKey: .avatar)
        state = try? container.decodeIfPresent(String.self, forKey: .state)
        
        // 布尔字段处理
        if let activeBool = try? container.decodeIfPresent(Bool.self, forKey: .active) {
            active = activeBool
        } else if let activeString = try? container.decodeIfPresent(String.self, forKey: .active) {
            active = activeString.lowercased() == "true"
        } else {
            active = nil
        }
        
        createdDate = try? container.decodeIfPresent(String.self, forKey: .createdDate)
        
        // 布尔字段处理
        if let specialBool = try? container.decodeIfPresent(Bool.self, forKey: .special) {
            special = specialBool
        } else if let specialString = try? container.decodeIfPresent(String.self, forKey: .special) {
            special = specialString.lowercased() == "true"
        } else {
            special = nil
        }
    }
}

class UserService {
    static let shared = UserService()
    
    private init() {}
    
    // 发布者 - 用于通知用户信息变化
    private let userInfoSubject = PassthroughSubject<UserInfo?, Error>()
    var userInfoPublisher: AnyPublisher<UserInfo?, Error> {
        return userInfoSubject.eraseToAnyPublisher()
    }
    
    // 获取用户信息
    func fetchUserInfo(accessToken: String, completion: ((Result<UserInfo?, Error>) -> Void)? = nil) {
        // 创建URL请求
        let urlString = "https://fsai.pickgoodspro.com/auth-resource-api/oauth/user"
        guard let url = URL(string: urlString) else {
            let error = NSError(domain: "UserServiceError", code: 0, userInfo: [NSLocalizedDescriptionKey: "无效的URL"])
            print("❌ 创建用户信息URL失败: \(urlString)")
            completion?(.failure(error))
            return
        }
        
        var request = URLRequest(url: url, timeoutInterval: 10.0)
        
        // 添加Authorization头，包含Bearer token
        request.addValue("Bearer \(accessToken)", forHTTPHeaderField: "Authorization")
        
        // 设置HTTP方法
        request.httpMethod = "GET"
        
        // 日志：请求信息
        print("🌐 请求用户信息 [GET] \(request.url?.absoluteString ?? "")")
        print("🔑 请求头: \(request.allHTTPHeaderFields ?? [:])")
        
        // 创建任务
        let task = URLSession.shared.dataTask(with: request) { [weak self] data, response, error in
            // 日志：响应信息
            if let httpResponse = response as? HTTPURLResponse {
                print("📥 用户信息API响应状态码: \(httpResponse.statusCode)")
                print("📥 用户信息API响应头: \(httpResponse.allHeaderFields)")
            }
            
            // 检查是否有错误
            if let error = error {
                print("❌ 获取用户信息失败: \(error.localizedDescription)")
                self?.userInfoSubject.send(completion: .failure(error))
                completion?(.failure(error))
                return
            }
            
            // 检查是否有数据
            guard let data = data else {
                let error = NSError(domain: "UserServiceError", code: 0, userInfo: [NSLocalizedDescriptionKey: "没有返回数据"])
                print("❌ 获取用户信息失败: 没有返回数据")
                self?.userInfoSubject.send(completion: .failure(error))
                completion?(.failure(error))
                return
            }
            
            // 检查响应状态码
            if let httpResponse = response as? HTTPURLResponse {
                if httpResponse.statusCode != 200 {
                    let errorMessage = "服务器返回错误状态码: \(httpResponse.statusCode)"
                    if let responseString = String(data: data, encoding: .utf8) {
                        print("❌ 用户信息API错误响应: \(responseString)")
                        
                        // 尝试解析错误响应为JSON
                        do {
                            if let errorDict = try JSONSerialization.jsonObject(with: data, options: []) as? [String: Any] {
                                print("❌ 错误响应详情: \(errorDict)")
                            }
                        } catch {
                            print("❌ 无法解析错误响应为JSON: \(error.localizedDescription)")
                        }
                    }
                    
                    let error = NSError(domain: "UserServiceError", code: httpResponse.statusCode, userInfo: [NSLocalizedDescriptionKey: errorMessage])
                    self?.userInfoSubject.send(completion: .failure(error))
                    completion?(.failure(error))
                    return
                }
            }
            
            // 尝试解析响应数据
            do {
                // 先打印原始JSON以进行调试
                if let jsonString = String(data: data, encoding: .utf8) {
                    print("📊 用户信息原始JSON: \(jsonString)")
                    
                    // 检查JSON是否为空
                    if jsonString.trimmingCharacters(in: .whitespacesAndNewlines).isEmpty {
                        print("⚠️ 用户信息JSON为空")
                        let error = NSError(domain: "UserServiceError", code: 0, userInfo: [NSLocalizedDescriptionKey: "服务器返回了空数据"])
                        self?.userInfoSubject.send(completion: .failure(error))
                        completion?(.failure(error))
                        return
                    }
                }
                
                // 尝试将数据解析为通用JSON格式，查看数据结构
                do {
                    if let jsonDict = try JSONSerialization.jsonObject(with: data, options: []) as? [String: Any] {
                        print("📊 解析为字典: \(jsonDict)")
                    }
                } catch {
                    print("⚠️ 无法解析为通用JSON: \(error.localizedDescription)")
                }
                
                // 直接将JSON解析为UserInfo对象
                let decoder = JSONDecoder()
                decoder.keyDecodingStrategy = .useDefaultKeys
                
                let userInfo = try decoder.decode(UserInfo.self, from: data)
                
                // 发布用户信息
                self?.userInfoSubject.send(userInfo)
                completion?(.success(userInfo))
                
                // 打印获取到的用户名和头像URL
                if let userName = userInfo.name {
                    print("✅ 成功获取用户名: \(userName)")
                }
                
                if let avatarUrl = userInfo.avatar {
                    print("✅ 成功获取用户头像URL: \(avatarUrl)")
                    
                    // 如果获取到头像URL，尝试下载头像
                    if !avatarUrl.isEmpty, let imageUrl = URL(string: avatarUrl) {
                        print("🌐 正在下载用户头像: \(avatarUrl)")
                        
                        URLSession.shared.dataTask(with: imageUrl) { imageData, imageResponse, imageError in
                            if let imageError = imageError {
                                print("❌ 下载用户头像失败: \(imageError.localizedDescription)")
                                return
                            }
                            
                            if let imageData = imageData, let image = UIImage(data: imageData) {
                                print("✅ 成功下载用户头像")
                                
                                // 通过通知更新头像，不保存到UserDefaults
                                DispatchQueue.main.async {
                                    NotificationCenter.default.post(
                                        name: Notification.Name("UserAvatarUpdated"),
                                        object: nil,
                                        userInfo: ["avatarImage": image]
                                    )
                                }
                            } else {
                                print("❌ 无法将下载的数据转换为图片")
                            }
                        }.resume()
                    } else {
                        print("⚠️ 头像URL为空或无效: \(avatarUrl)")
                    }
                } else {
                    print("⚠️ 用户信息中没有头像URL")
                }
            } catch {
                print("❌ 解析用户信息失败: \(error.localizedDescription)")
                
                // 提供更详细的解码错误信息
                if let decodingError = error as? DecodingError {
                    switch decodingError {
                    case .keyNotFound(let key, let context):
                        print("❌ 解码错误 - 未找到键: \(key), 路径: \(context.codingPath)")
                    case .valueNotFound(let type, let context):
                        print("❌ 解码错误 - 未找到值: \(type), 路径: \(context.codingPath)")
                    case .typeMismatch(let type, let context):
                        print("❌ 解码错误 - 类型不匹配: \(type), 路径: \(context.codingPath)")
                    case .dataCorrupted(let context):
                        print("❌ 解码错误 - 数据损坏: \(context)")
                    @unknown default:
                        print("❌ 解码错误 - 未知错误: \(decodingError)")
                    }
                }
                
                self?.userInfoSubject.send(completion: .failure(error))
                completion?(.failure(error))
            }
        }
        
        // 开始任务
        task.resume()
    }
    
    // 更新UserData中的用户名
    func updateUserDataWithFetchedInfo(userData: UserData) {
        if let accessToken = UserDefaults.standard.string(forKey: "accessToken") {
            fetchUserInfo(accessToken: accessToken) { result in
                DispatchQueue.main.async {
                    switch result {
                    case .success(let userInfo):
                        // 更新昵称，但需要过滤虚假格式
                        if let nickname = userInfo?.nickname {
                            let filteredNickname = userData.filterNickname(nickname)
                            userData.nickname = filteredNickname
                            print("成功更新用户昵称到UserData: \(nickname) -> 过滤后: \(filteredNickname)")
                        } else if let name = userInfo?.name {
                            // 如果后端没有返回nickname，则使用name作为nickname，但也需要过滤
                            let filteredNickname = userData.filterNickname(name)
                            userData.nickname = filteredNickname
                            print("后端未提供昵称，使用name作为nickname: \(name) -> 过滤后: \(filteredNickname)")
                        }
                        
                        // 更新头像URL和下载头像
                        if let avatarUrl = userInfo?.avatar, !avatarUrl.isEmpty {
                            print("获取到头像URL: \(avatarUrl)")
                            
                            // 输出URL的每个字符的编码，用于诊断
                            print("URL字符诊断:")
                            for (index, char) in avatarUrl.enumerated() {
                                print("  位置[\(index)]: '\(char)' (Unicode: \(char.unicodeScalars.first?.value ?? 0))")
                            }
                            
                            // 检查URL是否有效
                            if URL(string: avatarUrl) == nil {
                                print("❌ 无效的URL格式: \(avatarUrl)")
                                
                                // 尝试修复URL
                                let escapedUrl = avatarUrl.addingPercentEncoding(withAllowedCharacters: .urlQueryAllowed)
                                print("尝试转义后的URL: \(escapedUrl ?? "转义失败")")
                                
                                if let escapedUrl = escapedUrl, let url = URL(string: escapedUrl) {
                                    print("✅ 转义后的URL有效: \(url.absoluteString)")
                                    
                                    // 使用转义后的URL下载头像
                                    URLSession.shared.dataTask(with: url) { data, response, error in
                                        if let error = error {
                                            print("下载头像图片失败: \(error.localizedDescription)")
                                            return
                                        }
                                        
                                        if let data = data, let image = UIImage(data: data) {
                                            DispatchQueue.main.async {
                                                // 通过通知更新头像，不保存到UserDefaults
                                                NotificationCenter.default.post(
                                                    name: Notification.Name("UserAvatarUpdated"),
                                                    object: nil,
                                                    userInfo: ["avatarImage": image]
                                                )
                                                print("成功下载并设置用户头像(使用转义URL)")
                                            }
                                        }
                                    }.resume()
                                } else {
                                    print("❌ 即使转义后URL仍然无效")
                                }
                            } else {
                                // 下载头像图片
                                if let url = URL(string: avatarUrl) {
                                    print("准备下载头像: \(url.absoluteString)")
                                    URLSession.shared.dataTask(with: url) { data, response, error in
                                        if let error = error {
                                            print("下载头像图片失败: \(error.localizedDescription)")
                                            return
                                        }
                                        
                                        if let data = data, let image = UIImage(data: data) {
                                            DispatchQueue.main.async {
                                                // 通过通知更新头像，不保存到UserDefaults
                                                NotificationCenter.default.post(
                                                    name: Notification.Name("UserAvatarUpdated"),
                                                    object: nil,
                                                    userInfo: ["avatarImage": image]
                                                )
                                                print("成功下载并设置用户头像")
                                            }
                                        }
                                    }.resume()
                                }
                            }
                        } else {
                            print("未获取到头像URL或URL为空")
                        }
                        
                        // 保存更新后的设置
                        userData.saveSettings()
                    case .failure(let error):
                        print("更新UserData中的用户信息失败: \(error.localizedDescription)")
                    }
                }
            }
        } else {
            print("无法获取访问令牌，请确保用户已登录")
        }
    }
    
    // 修改用户昵称
    func updateUserNickname(newNickname: String, accessToken: String, completion: @escaping (Result<Bool, Error>) -> Void) {
        // 创建URL请求
        let urlString = "https://fsai.pickgoodspro.com/auth-resource-api/oauth/nickname"
        guard let url = URL(string: urlString) else {
            let error = NSError(domain: "UserServiceError", code: 0, userInfo: [NSLocalizedDescriptionKey: "无效的URL"])
            print("❌ 创建修改昵称URL失败: \(urlString)")
            completion(.failure(error))
            return
        }
        
        var request = URLRequest(url: url, timeoutInterval: 10.0)
        
        // 设置请求方法为PUT
        request.httpMethod = "PUT"
        
        // 添加请求头
        request.addValue("application/json", forHTTPHeaderField: "Content-Type")
        request.addValue("Bearer \(accessToken)", forHTTPHeaderField: "Authorization")
        
        // 准备请求体数据 - 包含新昵称
        let parameters: [String: String] = ["nickname": newNickname]
        
        do {
            // 将字典转换为JSON数据
            let jsonData = try JSONSerialization.data(withJSONObject: parameters, options: [])
            request.httpBody = jsonData
            
            // 日志：请求信息
            print("🌐 修改用户昵称 [PUT] \(url.absoluteString)")
            print("🔑 请求头: \(request.allHTTPHeaderFields ?? [:])")
            if let bodyString = String(data: jsonData, encoding: .utf8) {
                print("📦 请求体: \(bodyString)")
            }
            
            // 创建任务
            let task = URLSession.shared.dataTask(with: request) { data, response, error in
                // 日志：响应信息
                if let httpResponse = response as? HTTPURLResponse {
                    print("📥 修改昵称API响应状态码: \(httpResponse.statusCode)")
                    print("📥 修改昵称API响应头: \(httpResponse.allHeaderFields)")
                }
                
                // 检查是否有错误
                if let error = error {
                    print("❌ 修改昵称失败: \(error.localizedDescription)")
                    completion(.failure(error))
                    return
                }
                
                // 检查HTTP响应状态码
                if let httpResponse = response as? HTTPURLResponse {
                    print("📥 修改昵称API状态码: \(httpResponse.statusCode)")
                    
                    // 打印响应数据，无论成功与否
                    if let data = data, let responseString = String(data: data, encoding: .utf8) {
                        print("📄 修改昵称API响应内容: \(responseString)")
                        
                        // 尝试解析为JSON，以便更好地了解响应结构
                        do {
                            if let jsonDict = try JSONSerialization.jsonObject(with: data, options: []) as? [String: Any] {
                                print("📊 修改昵称响应解析为字典: \(jsonDict)")
                            }
                        } catch {
                            print("⚠️ 修改昵称响应无法解析为JSON: \(error.localizedDescription)")
                        }
                    }
                    
                    if (200...299).contains(httpResponse.statusCode) {
                        // 成功修改昵称
                        DispatchQueue.main.async {
                            print("✅ 成功修改用户昵称")
                            completion(.success(true))
                        }
                    } else {
                        // 处理服务器错误响应
                        var errorMessage = "服务器返回错误：状态码 \(httpResponse.statusCode)"
                        
                        if let data = data, let responseString = String(data: data, encoding: .utf8) {
                            errorMessage += ", 响应内容: \(responseString)"
                            print("❌ 修改昵称API错误响应: \(responseString)")
                        }
                        
                        let error = NSError(domain: "UserServiceError", code: httpResponse.statusCode, userInfo: [NSLocalizedDescriptionKey: errorMessage])
                        completion(.failure(error))
                    }
                } else {
                    let error = NSError(domain: "UserServiceError", code: 0, userInfo: [NSLocalizedDescriptionKey: "无法获取HTTP响应"])
                    print("❌ 修改昵称失败: 无法获取HTTP响应")
                    completion(.failure(error))
                }
            }
            
            // 开始任务
            task.resume()
            
        } catch {
            print("❌ 准备请求数据失败: \(error.localizedDescription)")
            completion(.failure(error))
        }
    }
    
    // 修改用户昵称并更新本地UserData
    func changeNickname(newNickname: String, userData: UserData, completion: @escaping (Result<Bool, Error>) -> Void) {
        // 检查是否有访问令牌
        guard let accessToken = UserDefaults.standard.string(forKey: "accessToken"), !accessToken.isEmpty else {
            let error = NSError(domain: "UserServiceError", code: 401, userInfo: [NSLocalizedDescriptionKey: "未登录或访问令牌无效"])
            completion(.failure(error))
            return
        }
        
        // 调用修改昵称API
        updateUserNickname(newNickname: newNickname, accessToken: accessToken) { result in
            switch result {
            case .success(_):
                // API调用成功，更新本地数据
                DispatchQueue.main.async {
                    userData.nickname = newNickname
                    userData.saveSettings()
                    print("昵称修改成功并已更新到本地: \(newNickname)")
                    completion(.success(true))
                }
                
            case .failure(let error):
                // API调用失败
                DispatchQueue.main.async {
                    completion(.failure(error))
                }
            }
        }
    }
    
    // 修改用户密码
    func updatePassword(currentPassword: String, newPassword: String, accessToken: String, completion: @escaping (Result<Bool, Error>) -> Void) {
        // 创建URL请求
        guard let url = URL(string: "https://fsai.pickgoodspro.com/auth-resource-api/oauth/password") else {
            let error = NSError(domain: "UserServiceError", code: 0, userInfo: [NSLocalizedDescriptionKey: "无效的URL"])
            completion(.failure(error))
            return
        }
        
        var request = URLRequest(url: url, timeoutInterval: 10.0)
        
        // 设置请求方法为PUT
        request.httpMethod = "PUT"
        
        // 添加请求头
        request.addValue("application/json", forHTTPHeaderField: "Content-Type")
        request.addValue("Bearer \(accessToken)", forHTTPHeaderField: "Authorization")
        
        // 准备请求体数据 - 包含当前密码和新密码
        let parameters: [String: Any] = [
            "id": 0, // 使用默认值0
            "password": currentPassword,
            "newPassword": newPassword
        ]
        
        do {
            // 将字典转换为JSON数据
            let jsonData = try JSONSerialization.data(withJSONObject: parameters, options: [])
            request.httpBody = jsonData
            
            // 日志：请求信息
            print("🌐 修改用户密码 [PUT] \(url.absoluteString)")
            print("🔑 请求头: \(request.allHTTPHeaderFields ?? [:])")
            if let bodyString = String(data: jsonData, encoding: .utf8) {
                print("📦 请求体: \(bodyString)")
            }
            
            // 创建任务
            let task = URLSession.shared.dataTask(with: request) { data, response, error in
                // 日志：响应信息
                if let httpResponse = response as? HTTPURLResponse {
                    print("📥 修改密码API响应状态码: \(httpResponse.statusCode)")
                }
                
                // 检查是否有错误
                if let error = error {
                    print("❌ 修改密码失败: \(error.localizedDescription)")
                    completion(.failure(error))
                    return
                }
                
                // 检查HTTP响应状态码
                if let httpResponse = response as? HTTPURLResponse {
                    print("📥 修改密码API状态码: \(httpResponse.statusCode)")
                    
                    if (200...299).contains(httpResponse.statusCode) {
                        // 成功修改密码
                        DispatchQueue.main.async {
                            print("✅ 成功修改用户密码")
                            completion(.success(true))
                        }
                    } else {
                        // 处理服务器错误响应
                        var errorMessage = "服务器返回错误：状态码 \(httpResponse.statusCode)"
                        
                        if let data = data, let responseString = String(data: data, encoding: .utf8) {
                            errorMessage += ", 响应内容: \(responseString)"
                            print("❌ 修改密码API错误响应: \(responseString)")
                        }
                        
                        let error = NSError(domain: "UserServiceError", code: httpResponse.statusCode, userInfo: [NSLocalizedDescriptionKey: errorMessage])
                        completion(.failure(error))
                    }
                } else {
                    let error = NSError(domain: "UserServiceError", code: 0, userInfo: [NSLocalizedDescriptionKey: "无法获取HTTP响应"])
                    print("❌ 修改密码失败: 无法获取HTTP响应")
                    completion(.failure(error))
                }
            }
            
            // 开始任务
            task.resume()
            
        } catch {
            print("❌ 准备请求数据失败: \(error.localizedDescription)")
            completion(.failure(error))
        }
    }
    
    // 提供给外部调用的修改密码方法
    func changePassword(currentPassword: String, newPassword: String, userData: UserData, completion: @escaping (Result<Bool, Error>) -> Void) {
        // 检查是否有访问令牌
        guard let accessToken = UserDefaults.standard.string(forKey: "accessToken"), !accessToken.isEmpty else {
            let error = NSError(domain: "UserServiceError", code: 401, userInfo: [NSLocalizedDescriptionKey: "未登录或访问令牌无效"])
            completion(.failure(error))
            return
        }
        
        // 验证新密码长度（可根据需要调整）
        if newPassword.count < 6 {
            let error = NSError(domain: "UserServiceError", code: 400, userInfo: [NSLocalizedDescriptionKey: "新密码长度不能少于6个字符"])
            completion(.failure(error))
            return
        }
        
        // 调用API修改密码
        updatePassword(currentPassword: currentPassword, newPassword: newPassword, accessToken: accessToken) { result in
            switch result {
            case .success(_):
                // 密码修改成功后，通常需要用户重新登录
                // 清除登录状态和令牌
                DispatchQueue.main.async {
                    userData.logout() // 注销登录，清除令牌
                    print("密码修改成功，已清除登录状态，需要重新登录")
                    completion(.success(true))
                }
                
            case .failure(let error):
                // 密码修改失败
                DispatchQueue.main.async {
                    completion(.failure(error))
                }
            }
        }
    }
    
    // 注销用户账户 - 底层API调用
    func destroyUserAccount(password: String, accessToken: String, completion: @escaping (Result<Bool, Error>) -> Void) {
        // 创建URL请求
        guard let url = URL(string: "https://fsai.pickgoodspro.com/auth-resource-api/oauth/destroy") else {
            let error = NSError(domain: "UserServiceError", code: 0, userInfo: [NSLocalizedDescriptionKey: "无效的URL"])
            completion(.failure(error))
            return
        }
        
        var request = URLRequest(url: url, timeoutInterval: 10.0)
        
        // 设置请求方法为DELETE
        request.httpMethod = "DELETE"
        
        // 添加请求头
        request.addValue("application/json", forHTTPHeaderField: "Content-Type")
        request.addValue("Bearer \(accessToken)", forHTTPHeaderField: "Authorization")
        
        // 准备请求体数据
        let parameters: [String: Any] = [
            "id": 0, // 使用默认值0
            "password": password,
            "newPassword": "string" // 注销账户时不需要真正的新密码，但API可能要求这个字段存在
        ]
        
        do {
            // 将字典转换为JSON数据
            let jsonData = try JSONSerialization.data(withJSONObject: parameters, options: [])
            request.httpBody = jsonData
            
            // 日志：请求信息
            print("🌐 注销用户账户 [DELETE] \(url.absoluteString)")
            print("🔑 请求头: \(request.allHTTPHeaderFields ?? [:])")
            if let bodyString = String(data: jsonData, encoding: .utf8) {
                print("📦 请求体: \(bodyString)")
            }
            
            // 创建任务
            let task = URLSession.shared.dataTask(with: request) { data, response, error in
                // 日志：响应信息
                if let httpResponse = response as? HTTPURLResponse {
                    print("📥 注销账户API响应状态码: \(httpResponse.statusCode)")
                }
                
                // 检查是否有错误
                if let error = error {
                    print("❌ 注销账户失败: \(error.localizedDescription)")
                    completion(.failure(error))
                    return
                }
                
                // 检查HTTP响应状态码
                if let httpResponse = response as? HTTPURLResponse {
                    print("📥 注销账户API状态码: \(httpResponse.statusCode)")
                    
                    if (200...299).contains(httpResponse.statusCode) {
                        // 成功注销账户
                        DispatchQueue.main.async {
                            print("✅ 成功注销用户账户")
                            completion(.success(true))
                        }
                    } else {
                        // 处理服务器错误响应
                        var errorMessage = "服务器返回错误：状态码 \(httpResponse.statusCode)"
                        
                        if let data = data, let responseString = String(data: data, encoding: .utf8) {
                            errorMessage += ", 响应内容: \(responseString)"
                            print("❌ 注销账户API错误响应: \(responseString)")
                        }
                        
                        let error = NSError(domain: "UserServiceError", code: httpResponse.statusCode, userInfo: [NSLocalizedDescriptionKey: errorMessage])
                        completion(.failure(error))
                    }
                } else {
                    let error = NSError(domain: "UserServiceError", code: 0, userInfo: [NSLocalizedDescriptionKey: "无法获取HTTP响应"])
                    print("❌ 注销账户失败: 无法获取HTTP响应")
                    completion(.failure(error))
                }
            }
            
            // 开始任务
            task.resume()
            
        } catch {
            print("❌ 准备请求数据失败: \(error.localizedDescription)")
            completion(.failure(error))
        }
    }
    
    // 提供给外部调用的注销账户方法
    func deleteAccount(password: String, reason: String, userData: UserData, completion: @escaping (Result<Bool, Error>) -> Void) {
        // 检查是否有访问令牌
        guard let accessToken = UserDefaults.standard.string(forKey: "accessToken"), !accessToken.isEmpty else {
            let error = NSError(domain: "UserServiceError", code: 401, userInfo: [NSLocalizedDescriptionKey: "未登录或访问令牌无效"])
            completion(.failure(error))
            return
        }
        
        // 验证密码不为空
        if password.isEmpty {
            let error = NSError(domain: "UserServiceError", code: 400, userInfo: [NSLocalizedDescriptionKey: "请输入密码以确认注销账户"])
            completion(.failure(error))
            return
        }
        
        // 可以在这里记录注销原因，例如发送到日志服务器或分析平台
        print("用户注销原因: \(reason)")
        
        // 调用API注销账户
        destroyUserAccount(password: password, accessToken: accessToken) { result in
            switch result {
            case .success(_):
                // 账户注销成功，清除所有本地数据
                DispatchQueue.main.async {
                    userData.logout(isAccountDeletion: true) // 标记为账号删除，使用强制重置
                    
                    // 清除用户的所有其他数据
                    UserDefaults.standard.removePersistentDomain(forName: Bundle.main.bundleIdentifier!)
                    UserDefaults.standard.synchronize()
                    
                    print("账户注销成功，已清除所有本地数据")
                    completion(.success(true))
                }
                
            case .failure(let error):
                // 账户注销失败
                DispatchQueue.main.async {
                    completion(.failure(error))
                }
            }
        }
    }
    
    // 使用API实现真实的退出登录功能
    func logoutWithAPI(completion: @escaping (Result<Bool, Error>) -> Void) {
        // 调用NetworkService中的logout方法
        NetworkService.shared.logout(completion: completion)
    }
} 