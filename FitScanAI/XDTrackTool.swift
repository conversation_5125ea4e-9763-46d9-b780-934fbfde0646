//
//  XDTrackTool.swift
//  pick
//
//  Created by wxd on 2025/5/21.
//

import Foundation
import UIKit
import FirebaseCore
import FirebaseAnalytics
import AppsFlyerLib

class XDTrackTool: ObservableObject {
    // MARK: - Singleton Instance
    static let shared = XDTrackTool()

    // 事件发送缓存（保留用于其他用途）
    private var sentEvents: Set<String> = []

    // 用户行为统计
    private var sessionStartTime: Date?
    private var lastActiveTime: Date = Date()

    // 应用状态监听
    private var backgroundObserver: NSObjectProtocol?
    private var foregroundObserver: NSObjectProtocol?

    private init() {
        setupAppStateObservers()
    }

    /// 初始化
    func config() {
        FirebaseApp.configure()

        // 确保禁用广告个性化，符合隐私要求
        Analytics.setUserProperty("false", forName: "allow_ad_personalization_signals")

        // 启用分析收集（仅用于应用改进）
        Analytics.setAnalyticsCollectionEnabled(true)

        // 禁用自动收集的购买事件，减少重复事件
        Analytics.setUserProperty("false", forName: "automatic_event_logging_enabled")

        // 记录会话开始时间
        sessionStartTime = Date()

        // 设置用户属性
        print("📊 开始设置Firebase Analytics用户属性...")
        setUserProperties()
        print("✅ Firebase Analytics用户属性设置完成")

        print("📊 Firebase Analytics 已初始化（仅用于应用改进，不用于广告）")

        // 配置 AppsFlyer（不在此处启动，统一在前台激活时启动）
        let devKey = "m8RpWhxetPzmsCdGy4aMh6"
        AppsFlyerLib.shared().appsFlyerDevKey = devKey
        if let appId = Bundle.main.infoDictionary?["AppsFlyerAppleAppID"] as? String, !appId.isEmpty {
            AppsFlyerLib.shared().appleAppID = appId
            print("AppsFlyer 已配置: appId=\(appId)")
        }
    }

    /// 页面统计
    func appear(_ pageName: String) {
        Analytics.logEvent("page_view", parameters: [
            "page_name": pageName,
            "timestamp": Date().timeIntervalSince1970
        ])
        print("📊 统计页面访问: \(pageName)")

        AppsFlyerLib.shared().logEvent("af_content_view", withValues: [
            "page_name": pageName,
            "ts": Int(Date().timeIntervalSince1970)
        ])
    }

    /// 订阅事件统计
    func trackSubscriptionEvent(_ eventName: String, parameters: [String: Any] = [:]) {
        // 添加时间戳
        var finalParameters = parameters
        finalParameters["timestamp"] = Date().timeIntervalSince1970

        Analytics.logEvent(eventName, parameters: finalParameters)
        print("📊 统计订阅事件: \(eventName)")

        AppsFlyerLib.shared().logEvent(eventName, withValues: finalParameters)
    }



    // MARK: - 应用状态监听

    /// 设置应用状态观察者
    private func setupAppStateObservers() {
        backgroundObserver = NotificationCenter.default.addObserver(
            forName: UIApplication.didEnterBackgroundNotification,
            object: nil,
            queue: .main
        ) { [weak self] _ in
            self?.trackAppBackground()
        }

        foregroundObserver = NotificationCenter.default.addObserver(
            forName: UIApplication.willEnterForegroundNotification,
            object: nil,
            queue: .main
        ) { [weak self] _ in
            self?.trackAppForeground()
        }
    }

    /// 应用进入后台
    private func trackAppBackground() {
        let sessionDuration = sessionStartTime.map { Date().timeIntervalSince($0) } ?? 0

        Analytics.logEvent("app_background", parameters: [
            "session_duration": sessionDuration,
            "timestamp": Date().timeIntervalSince1970
        ])

        print("📊 应用进入后台，会话时长: \(Int(sessionDuration))秒")
    }

    /// 应用进入前台
    private func trackAppForeground() {
        sessionStartTime = Date()

        Analytics.logEvent("app_foreground", parameters: [
            "timestamp": Date().timeIntervalSince1970
        ])

        print("📊 应用进入前台")

        AppsFlyerLib.shared().logEvent("app_foreground", withValues: [
            "ts": Int(Date().timeIntervalSince1970)
        ])
    }

    // MARK: - 用户属性设置

    /// 设置用户属性
    private func setUserProperties() {
        print("📊 开始设置各项用户属性...")

        // 设置应用版本
        if let appVersion = Bundle.main.infoDictionary?["CFBundleShortVersionString"] as? String {
            Analytics.setUserProperty(appVersion, forName: "app_version")
            print("📊 已设置应用版本: \(appVersion)")
        } else {
            print("⚠️ 无法获取应用版本信息")
        }

        // 设置构建版本
        if let buildNumber = Bundle.main.infoDictionary?["CFBundleVersion"] as? String {
            Analytics.setUserProperty(buildNumber, forName: "build_number")
            print("📊 已设置构建版本: \(buildNumber)")
        } else {
            print("⚠️ 无法获取构建版本信息")
        }

        // 设置设备信息
        let iosVersion = UIDevice.current.systemVersion
        let deviceModel = UIDevice.current.model
        Analytics.setUserProperty(iosVersion, forName: "ios_version")
        Analytics.setUserProperty(deviceModel, forName: "device_model")
        print("📊 已设置iOS版本: \(iosVersion)")
        print("📊 已设置设备型号: \(deviceModel)")

        // 设置语言
        if let language = Locale.current.language.languageCode?.identifier {
            Analytics.setUserProperty(language, forName: "language")
            print("📊 已设置语言: \(language)")
        } else {
            print("⚠️ 无法获取语言信息")
        }

        // 设置设备标识符（符合Apple政策）
        let deviceId = getDeviceIdentifier()
        Analytics.setUserProperty(deviceId, forName: "device_id")
        print("📊 已设置Firebase Analytics用户属性 - device_id: \(deviceId)")

        print("✅ 所有用户属性设置完成")
    }

    /// 获取设备标识符（符合Apple政策的方案）
    private func getDeviceIdentifier() -> String {
        print("📱 开始获取设备标识符...")

        // 优先使用IDFV（同一开发商应用共享）
        if let idfv = UIDevice.current.identifierForVendor?.uuidString {
            print("✅ 成功获取IDFV: \(idfv)")
            print("📊 设备标识符来源: IDFV (identifierForVendor)")
            return idfv
        } else {
            print("⚠️ IDFV不可用，将使用备用方案")
        }

        // 如果IDFV不可用，使用持久化的UUID
        let key = "app_device_identifier"
        if let savedIdentifier = UserDefaults.standard.string(forKey: key) {
            print("✅ 从UserDefaults获取到已保存的设备标识符: \(savedIdentifier)")
            print("📊 设备标识符来源: UserDefaults (持久化UUID)")
            return savedIdentifier
        } else {
            print("ℹ️ UserDefaults中没有保存的设备标识符，将生成新的")
        }

        // 生成新的UUID并保存
        let newIdentifier = UUID().uuidString
        UserDefaults.standard.set(newIdentifier, forKey: key)
        print("🆕 生成新的设备标识符: \(newIdentifier)")
        print("💾 已保存到UserDefaults，key: \(key)")
        print("📊 设备标识符来源: 新生成的UUID")

        return newIdentifier
    }

    /// 公开方法：获取当前设备标识符（用于调试）
    func getCurrentDeviceId() -> String {
        let deviceId = getDeviceIdentifier()
        print("🔍 当前设备标识符: \(deviceId)")
        return deviceId
    }

    /// 公开方法：显示所有设备信息（用于调试）
    func logDeviceInfo() {
        print("📱 ========== 设备信息详情 ==========")
        print("📱 设备标识符: \(getCurrentDeviceId())")
        print("📱 iOS版本: \(UIDevice.current.systemVersion)")
        print("📱 设备型号: \(UIDevice.current.model)")
        print("📱 设备名称: \(UIDevice.current.name)")

        if let language = Locale.current.language.languageCode?.identifier {
            print("📱 语言代码: \(language)")
        }

        if let appVersion = Bundle.main.infoDictionary?["CFBundleShortVersionString"] as? String {
            print("📱 应用版本: \(appVersion)")
        }

        if let buildNumber = Bundle.main.infoDictionary?["CFBundleVersion"] as? String {
            print("📱 构建版本: \(buildNumber)")
        }

        // 检查IDFV是否可用
        if let idfv = UIDevice.current.identifierForVendor?.uuidString {
            print("📱 IDFV可用: \(idfv)")
        } else {
            print("📱 IDFV不可用")
        }

        print("📱 ================================")
    }

    // MARK: - 高级统计方法

    /// 用户行为事件统计
    func trackUserAction(_ action: String, parameters: [String: Any] = [:]) {
        var finalParameters = parameters
        finalParameters["timestamp"] = Date().timeIntervalSince1970
        finalParameters["user_id"] = UserDefaults.standard.string(forKey: "userId") ?? "anonymous"

        Analytics.logEvent("user_action", parameters: finalParameters.merging([
            "action_name": action
        ]) { _, new in new })

        print("📊 用户行为统计: \(action)")

        var afParams = finalParameters
        afParams["action_name"] = action
        AppsFlyerLib.shared().logEvent("user_action", withValues: afParams)
    }

    /// 功能使用统计
    func trackFeatureUsage(_ feature: String, parameters: [String: Any] = [:]) {
        var finalParameters = parameters
        finalParameters["timestamp"] = Date().timeIntervalSince1970
        finalParameters["feature_name"] = feature

        Analytics.logEvent("feature_usage", parameters: finalParameters)
        print("📊 功能使用统计: \(feature)")

        AppsFlyerLib.shared().logEvent("feature_usage", withValues: finalParameters)
    }

    /// 错误事件统计
    func trackError(_ error: String, context: String = "", parameters: [String: Any] = [:]) {
        var finalParameters = parameters
        finalParameters["timestamp"] = Date().timeIntervalSince1970
        finalParameters["error_message"] = error
        finalParameters["context"] = context

        Analytics.logEvent("app_error", parameters: finalParameters)

        print("📊 错误统计: \(error) - 上下文: \(context)")
    }

    /// 性能统计
    func trackPerformance(_ metric: String, value: Double, unit: String = "") {
        Analytics.logEvent("performance_metric", parameters: [
            "metric_name": metric,
            "value": value,
            "unit": unit,
            "timestamp": Date().timeIntervalSince1970
        ])

        print("📊 性能统计: \(metric) = \(value) \(unit)")
    }

    // MARK: - AppsFlyer 启动控制
    /// 在应用进入前台时启动 AppsFlyer
    func startAppsFlyerIfAvailable() {
        AppsFlyerLib.shared().start()
        print("AppsFlyer 已启动")
    }

    // MARK: - 清理资源

    deinit {
        if let observer = backgroundObserver {
            NotificationCenter.default.removeObserver(observer)
        }
        if let observer = foregroundObserver {
            NotificationCenter.default.removeObserver(observer)
        }
    }
}
