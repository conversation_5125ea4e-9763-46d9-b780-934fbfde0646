import Foundation

/**
 * UserDefaultsManager - UserDefaults数据大小管理器
 * 
 * 这个管理器负责监控和限制UserDefaults中的数据大小，
 * 防止超过CFPreferences的4MB限制，避免出现存储警告。
 * 
 * 主要功能:
 * 1. 监控UserDefaults数据大小
 * 2. 自动清理过大的数据
 * 3. 提供安全的数据存储方法
 * 4. 记录数据大小统计
 */
class UserDefaultsManager {
    static let shared = UserDefaultsManager()
    
    // 最大允许的总数据大小（3.5MB，留出安全边界）
    private let maxTotalSize = 3584 * 1024 // 3.5MB
    
    // 单个数据项的最大大小（500KB）
    private let maxItemSize = 512 * 1024 // 500KB
    
    private init() {}
    
    // 安全地设置数据到UserDefaults
    func safeSet<T: Codable>(_ object: T, forKey key: String) -> Bool {
        do {
            let data = try JSONEncoder().encode(object)
            
            // 检查单个数据项大小
            if data.count > maxItemSize {
                print("⚠️ 数据项过大，无法保存到UserDefaults: \(key), 大小: \(data.count) bytes")
                return false
            }
            
            // 检查总数据大小
            let currentSize = getTotalUserDefaultsSize()
            if currentSize + data.count > maxTotalSize {
                print("⚠️ UserDefaults总大小将超限，开始清理...")
                cleanupOldData()
                
                // 清理后再次检查
                let newSize = getTotalUserDefaultsSize()
                if newSize + data.count > maxTotalSize {
                    print("❌ 清理后仍然超限，无法保存: \(key)")
                    return false
                }
            }
            
            UserDefaults.standard.set(data, forKey: key)
            print("✅ 安全保存数据: \(key), 大小: \(data.count) bytes")
            return true
            
        } catch {
            print("❌ 编码数据失败: \(key), 错误: \(error)")
            return false
        }
    }
    
    // 安全地获取数据从UserDefaults
    func safeGet<T: Codable>(_ type: T.Type, forKey key: String) -> T? {
        guard let data = UserDefaults.standard.data(forKey: key) else {
            return nil
        }
        
        do {
            return try JSONDecoder().decode(type, from: data)
        } catch {
            print("❌ 解码数据失败: \(key), 错误: \(error)")
            // 如果解码失败，删除损坏的数据
            UserDefaults.standard.removeObject(forKey: key)
            return nil
        }
    }
    
    // 获取UserDefaults总数据大小（估算）
    func getTotalUserDefaultsSize() -> Int {
        let defaults = UserDefaults.standard
        var totalSize = 0
        
        // 获取所有键值对
        for (_, value) in defaults.dictionaryRepresentation() {
            if let data = value as? Data {
                totalSize += data.count
            } else if let string = value as? String {
                totalSize += string.data(using: .utf8)?.count ?? 0
            } else if let number = value as? NSNumber {
                // 处理数字类型
                totalSize += MemoryLayout.size(ofValue: number)
            } else if let bool = value as? Bool {
                // 处理布尔类型
                totalSize += MemoryLayout.size(ofValue: bool)
            } else if let array = value as? NSArray {
                // 处理数组类型
                do {
                    let data = try JSONSerialization.data(withJSONObject: array, options: [])
                    totalSize += data.count
                } catch {
                    // 如果序列化失败，使用估算大小
                    totalSize += array.count * 100 // 每个元素估算100字节
                    print("⚠️ 无法序列化数组，使用估算大小: \(error.localizedDescription)")
                }
            } else if let dict = value as? NSDictionary {
                // 处理字典类型
                do {
                    let data = try JSONSerialization.data(withJSONObject: dict, options: [])
                    totalSize += data.count
                } catch {
                    // 如果序列化失败，使用估算大小
                    totalSize += dict.count * 200 // 每个键值对估算200字节
                    print("⚠️ 无法序列化字典，使用估算大小: \(error.localizedDescription)")
                }
            } else {
                // 对于其他类型，尝试转换为字符串来估算大小
                let stringValue = String(describing: value)
                totalSize += stringValue.data(using: .utf8)?.count ?? 50 // 默认50字节
                print("⚠️ 未知类型，使用字符串估算: \(type(of: value))")
            }
        }
        
        return totalSize
    }
    
    // 清理旧数据
    private func cleanupOldData() {
        let defaults = UserDefaults.standard
        let allKeys = Array(defaults.dictionaryRepresentation().keys)
        
        // 按数据大小排序，优先清理大数据
        var keysSizeMap: [(String, Int)] = []
        
        for key in allKeys {
            if let data = defaults.data(forKey: key) {
                keysSizeMap.append((key, data.count))
            }
        }
        
        // 按大小降序排序
        keysSizeMap.sort { $0.1 > $1.1 }
        
        // 清理最大的几个数据项
        var cleanedSize = 0
        let targetCleanSize = maxTotalSize / 4 // 清理25%的空间
        
        for (key, size) in keysSizeMap {
            if cleanedSize >= targetCleanSize {
                break
            }
            
            // 不清理重要的系统数据
            if !isSystemKey(key) {
                defaults.removeObject(forKey: key)
                cleanedSize += size
                print("🗑️ 清理数据: \(key), 大小: \(size) bytes")
            }
        }
        
        print("✅ 清理完成，释放空间: \(cleanedSize) bytes")
    }
    
    // 检查是否为系统关键数据
    private func isSystemKey(_ key: String) -> Bool {
        let systemKeys = [
            "accessToken",
            "refreshToken", 
            "tokenExpiresAt",
            "userId",
            "userEmail",
            "userNickname",
            "isLoggedIn",
            "isAppleLogin",
            "lastFoodRecordId"  // 添加食物记录ID缓存
        ]
        
        return systemKeys.contains(key)
    }
    
    // MARK: - 食物记录ID缓存
    
    // 保存最后添加的食物记录ID
    func saveLastFoodRecordId(_ recordId: Int) {
        UserDefaults.standard.set(recordId, forKey: "lastFoodRecordId")
        print("💾 缓存最后添加的食物记录ID: \(recordId)")
    }
    
    // 获取最后添加的食物记录ID
    func getLastFoodRecordId() -> Int {
        let recordId = UserDefaults.standard.integer(forKey: "lastFoodRecordId")
        print("📥 获取缓存的食物记录ID: \(recordId)")
        return recordId
    }
    
    // 清除食物记录ID缓存
    func clearLastFoodRecordId() {
        UserDefaults.standard.removeObject(forKey: "lastFoodRecordId")
        print("🗑️ 清除食物记录ID缓存")
    }
    
    // 获取数据大小统计
    func getDataSizeStats() -> [String: Int] {
        let defaults = UserDefaults.standard
        var stats: [String: Int] = [:]
        
        for (key, value) in defaults.dictionaryRepresentation() {
            var size = 0
            
            if let data = value as? Data {
                size = data.count
            } else if let string = value as? String {
                size = string.data(using: .utf8)?.count ?? 0
            } else if let number = value as? NSNumber {
                size = MemoryLayout.size(ofValue: number)
            } else if let bool = value as? Bool {
                size = MemoryLayout.size(ofValue: bool)
            } else if let array = value as? NSArray {
                // 尝试序列化数组
                do {
                    let data = try JSONSerialization.data(withJSONObject: array, options: [])
                    size = data.count
                } catch {
                    size = array.count * 100 // 估算大小
                }
            } else if let dict = value as? NSDictionary {
                // 尝试序列化字典
                do {
                    let data = try JSONSerialization.data(withJSONObject: dict, options: [])
                    size = data.count
                } catch {
                    size = dict.count * 200 // 估算大小
                }
            } else {
                // 其他类型转换为字符串估算
                let stringValue = String(describing: value)
                size = stringValue.data(using: .utf8)?.count ?? 50
            }
            
            stats[key] = size
        }
        
        return stats.sorted { $0.value > $1.value }.reduce(into: [:]) { result, pair in
            result[pair.key] = pair.value
        }
    }
    
    // 打印数据大小报告
    func printSizeReport() {
        let totalSize = getTotalUserDefaultsSize()
        let stats = getDataSizeStats()
        
        print("📊 UserDefaults 数据大小报告:")
        print("总大小: \(totalSize) bytes (\(String(format: "%.2f", Double(totalSize) / 1024.0)) KB)")
        print("最大限制: \(maxTotalSize) bytes (\(String(format: "%.2f", Double(maxTotalSize) / 1024.0)) KB)")
        print("使用率: \(String(format: "%.1f", Double(totalSize) / Double(maxTotalSize) * 100))%")
        
        print("\n前10大数据项:")
        for (index, (key, size)) in stats.prefix(10).enumerated() {
            print("\(index + 1). \(key): \(size) bytes (\(String(format: "%.2f", Double(size) / 1024.0)) KB)")
        }
    }
} 