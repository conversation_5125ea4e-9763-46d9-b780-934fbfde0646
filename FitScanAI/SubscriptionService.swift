import Foundation
import StoreKit
import SwiftUI
import AppsFlyerLib

// StoreKit错误枚举
enum StoreError: Error {
    case failedVerification
}

// 订阅产品数据模型
struct SubscriptionProduct: Codable, Identifiable {
    let id = UUID()
    let productId: String
    let name: String
    let remark: String
    let price: Double
    let type: Int // 1:月订阅, 2:年订阅
    let hasTrial: Bool
    let trialDays: Int
    let currency: String
    
    enum CodingKeys: String, CodingKey {
        case productId, name, remark, price, type, hasTrial, trialDays, currency
    }
}

// 订阅创建请求模型（根据实际接口文档）
struct SubscriptionCreateRequest: Codable {
    let isSandbox: Bool
    let productId: String
    let originalTransactionId: String?  // 可选的原始交易ID
}

// 订阅创建响应模型（根据实际接口文档）
struct SubscriptionCreateResponse: Codable {
    let code: Int
    let message: String
    let data: SubscriptionCreateData?
    let timestamp: Int64
}

struct SubscriptionCreateData: Codable {
    let id: String
    let isSandbox: Bool
    let productId: String
    let scenario: String
    let isTrialPeriod: Bool?  // 设为可选，因为后端响应中可能没有这个字段
    let channelRequestId: String
    let originalPayId: String?
}

// 用户订阅响应模型 - 根据最新有效订阅接口
struct SubscriptionUserResponse: Codable {
    let id: String
    let startDate: String?  // 改为可选，因为INIT状态下可能没有这个字段
    let endDate: String?    // 改为可选，因为INIT状态下可能没有这个字段
    let status: String // INIT,ACTIVE,EXPIRED,CANCELED,IN_GRACE_PERIOD,IN_BILLING_RETRY
    let autoRenewal: Bool
    let channelRequestId: String
    let originalPayId: String?
    let renewPayId: String?
    let environment: String // Production
}

// 查询用户最新有效订阅响应模型
struct UserActiveSubscriptionResponse: Codable {
    let code: Int
    let message: String
    let data: SubscriptionUserResponse?
    let timestamp: Int64
}

// 订阅状态枚举
enum SubscriptionStatus {
    case firstTime          // 首次订阅，显示 "Start 3-day free trial"
    case hasActiveSubscription    // 有激活的订阅
    case hasExpiredSubscription   // 有过期的订阅，显示 "Subscribe now"
    case noSubscription     // 无订阅记录，显示 "Start 3-day free trial"
}

// 订阅信息结构
struct SubscriptionInfo {
    let status: SubscriptionStatus
    let subscription: SubscriptionUserResponse?
    let displayText: String
    let isSubscribed: Bool
    
    init(status: SubscriptionStatus, subscription: SubscriptionUserResponse? = nil) {
        self.status = status
        self.subscription = subscription
        self.isSubscribed = (status == .hasActiveSubscription)
        
        switch status {
        case .firstTime, .noSubscription:
            self.displayText = "Start 3-day free trial"
        case .hasActiveSubscription:
            self.displayText = "Active Subscription"
        case .hasExpiredSubscription:
            self.displayText = "Subscribe now"
        }
    }
}

// 新接口统一使用 SubscriptionUserResponse 结构
// 所有订阅相关的响应都采用这个统一的数据模型

// 订阅交易信息存储结构
struct SubscriptionTransactionInfo {
    let id: String
    let channelRequestId: String
    let scenario: String
    let isSandbox: Bool
    let productId: String
    let originalPayId: String?
    
    init(id: String, channelRequestId: String, scenario: String = "SUBSCRIPTION", isSandbox: Bool, productId: String, originalPayId: String? = nil) {
        self.id = id
        self.channelRequestId = channelRequestId
        self.scenario = scenario
        self.isSandbox = isSandbox
        self.productId = productId
        self.originalPayId = originalPayId
    }
}

// 订阅服务类
class SubscriptionService: ObservableObject {
    static let shared = SubscriptionService()

    @Published var subscriptionProducts: [SubscriptionProduct] = []
    @Published var isLoading = false
    @Published var errorMessage: String?


    
    // 使用实际的API地址，生产环境
    private let baseURL = "https://fsai.pickgoodspro.com"
    private let isSandbox = false // 生产环境标志
    
    // 存储当前订阅交易信息
    private var currentTransactionInfo: SubscriptionTransactionInfo?
    
    private init() {
        // 清理可能存在的旧沙箱数据
        cleanOldSandboxData()
        
        // 启动时尝试恢复保存的交易信息
        restoreTransactionInfoFromUserDefaults()
    }
    
    // 清理旧的沙箱数据
    private func cleanOldSandboxData() {
        // 清理可能存在的旧沙箱交易信息
        if let data = UserDefaults.standard.dictionary(forKey: "savedTransactionInfo"),
           let isSandbox = data["isSandbox"] as? Bool,
           isSandbox == true {
            print("🧹 SubscriptionService: 清理旧的沙箱交易数据")
            UserDefaults.standard.removeObject(forKey: "savedTransactionInfo")
        }
        
        // 清理其他可能存在的沙箱相关数据
        let keysToClean = [
            "lastOriginalTransactionId",
            "user_*_lastOriginalTransactionId"
        ]
        
        for key in keysToClean {
            if key.contains("*") {
                // 对于通配符key，我们需要特殊处理
                if key == "user_*_lastOriginalTransactionId" {
                    // 清理所有用户特定的原始交易ID
                    let allKeys = UserDefaults.standard.dictionaryRepresentation().keys
                    for userKey in allKeys {
                        if userKey.hasPrefix("user_") && userKey.hasSuffix("_lastOriginalTransactionId") {
                            print("🧹 SubscriptionService: 清理用户沙箱数据: \(userKey)")
                            UserDefaults.standard.removeObject(forKey: userKey)
                        }
                    }
                }
            } else {
                UserDefaults.standard.removeObject(forKey: key)
            }
        }
        
        print("✅ SubscriptionService: 旧沙箱数据清理完成")
    }
    
    // 查询当前Apple账号的原始交易ID
    @available(iOS 15.0, *)
    func getCurrentOriginalTransactionId() async -> String? {
        print("🔍 SubscriptionService: 开始查询当前Apple账号的原始交易ID...")
        
        // 从StoreKit的当前权益中查找订阅交易
        for await result in Transaction.currentEntitlements {
            switch result {
            case .verified(let transaction):
                // 检查是否是自动续订订阅产品
                if transaction.productType == .autoRenewable {
                    let originalTransactionId = String(transaction.originalID)
                    print("✅ SubscriptionService: 找到当前Apple账号的原始交易ID: \(originalTransactionId)")
                    return originalTransactionId
                }
            case .unverified(_, let error):
                print("❌ SubscriptionService: 交易验证失败: \(error)")
                continue
            }
        }
        
        print("ℹ️ SubscriptionService: 当前Apple账号没有找到原始交易ID")
        return nil
    }
    
    // 保存订阅交易信息
    func saveTransactionInfo(id: String, channelRequestId: String, productId: String, scenario: String = "SUBSCRIPTION", originalPayId: String? = nil) {
        currentTransactionInfo = SubscriptionTransactionInfo(
            id: id,
            channelRequestId: channelRequestId,
            scenario: scenario,
            isSandbox: isSandbox,
            productId: productId,
            originalPayId: originalPayId
        )
        
        // 同时保存到UserDefaults以便应用重启后能恢复
        saveTransactionInfoToUserDefaults()
        
        print("📱 SubscriptionService: 保存交易信息 - id: \(id), channelRequestId: \(channelRequestId), scenario: \(scenario), 环境: 生产")
    }
    
    // 保存交易信息到UserDefaults
    private func saveTransactionInfoToUserDefaults() {
        guard let transactionInfo = currentTransactionInfo else { return }
        
        let data: [String: Any] = [
            "id": transactionInfo.id,
            "channelRequestId": transactionInfo.channelRequestId,
            "scenario": transactionInfo.scenario,
            "isSandbox": transactionInfo.isSandbox,
            "productId": transactionInfo.productId,
            "originalPayId": transactionInfo.originalPayId as Any
        ]
        
        UserDefaults.standard.set(data, forKey: "savedTransactionInfo")
        print("📱 SubscriptionService: 交易信息已保存到UserDefaults")
    }
    
    // 从UserDefaults恢复交易信息
    private func restoreTransactionInfoFromUserDefaults() {
        guard let data = UserDefaults.standard.dictionary(forKey: "savedTransactionInfo"),
              let id = data["id"] as? String,
              let channelRequestId = data["channelRequestId"] as? String,
              let scenario = data["scenario"] as? String,
              let isSandbox = data["isSandbox"] as? Bool,
              let productId = data["productId"] as? String else {
            print("📱 SubscriptionService: 未找到保存的交易信息")
            return
        }
        
        let originalPayId = data["originalPayId"] as? String
        
        currentTransactionInfo = SubscriptionTransactionInfo(
            id: id,
            channelRequestId: channelRequestId,
            scenario: scenario,
            isSandbox: isSandbox,
            productId: productId,
            originalPayId: originalPayId
        )
        
        print("📱 SubscriptionService: 从UserDefaults恢复交易信息 - id: \(id), channelRequestId: \(channelRequestId)")
    }
    
    // 根据苹果原始交易ID获取订阅信息 - 使用新的刷新接口
    func fetchSubscriptionByOriginalTransactionId(originalTransactionId: String) async -> SubscriptionUserResponse? {
        // 如果没有保存的交易信息，返回错误
        guard let transactionInfo = currentTransactionInfo else {
            print("❌ SubscriptionService: 无法获取订阅信息 - 未找到保存的交易信息")
            print("❌ SubscriptionService: 请确保先调用订阅创建接口并保存交易信息")
            await MainActor.run {
                errorMessage = "Missing transaction info - Please create subscription first"
            }
            return nil
        }
        
        print("✅ SubscriptionService: 使用保存的交易信息进行查询")
        
        await MainActor.run {
            isLoading = true
            errorMessage = nil
        }
        
        // 构建URL和查询参数 - 使用新的接口地址
        var urlComponents = URLComponents(string: "\(baseURL)/ns/app/subscription/refresh/")!
        urlComponents.queryItems = [
            URLQueryItem(name: "id", value: transactionInfo.id),
            URLQueryItem(name: "channelRequestId", value: transactionInfo.channelRequestId),
            URLQueryItem(name: "scenario", value: transactionInfo.scenario),
            URLQueryItem(name: "isSandbox", value: String(transactionInfo.isSandbox)),
            URLQueryItem(name: "originalTransactionId", value: originalTransactionId)
        ]
        
        guard let url = urlComponents.url else {
            await MainActor.run {
                errorMessage = "Invalid URL"
                isLoading = false
            }
            return nil
        }
        
        print("📱 SubscriptionService: 根据苹果原始交易ID刷新订阅信息")
        print("  - URL: \(url.absoluteString)")
        print("  - id: \(transactionInfo.id)")
        print("  - channelRequestId: \(transactionInfo.channelRequestId)")
        print("  - scenario: \(transactionInfo.scenario)")
        print("  - 环境: 生产")
        print("  - originalTransactionId: \(originalTransactionId)")
        
        var request = URLRequest(url: url)
        request.httpMethod = "GET"
        request.setValue("application/x-www-form-urlencoded", forHTTPHeaderField: "Content-Type")
        
        // 添加认证头
        if let token = UserDefaults.standard.string(forKey: "accessToken") {
            request.setValue("Bearer \(token)", forHTTPHeaderField: "Authorization")
            print("📱 SubscriptionService: 刷新订阅信息请求已添加Authorization头，token前缀: \(token.prefix(10))...")
        } else {
            print("⚠️ SubscriptionService: 警告 - 未找到accessToken")
            await MainActor.run {
                errorMessage = "User not logged in"
                isLoading = false
            }
            return nil
        }
        
        do {
            let (data, response) = try await URLSession.shared.data(for: request)
            
            if let httpResponse = response as? HTTPURLResponse {
                print("📱 SubscriptionService: 刷新订阅信息响应状态码: \(httpResponse.statusCode)")
                
                if let responseString = String(data: data, encoding: .utf8) {
                    print("📱 SubscriptionService: 刷新订阅信息响应数据: \(responseString)")
                }
                
                if httpResponse.statusCode == 200 {
                    let subscriptionResponse = try JSONDecoder().decode(UserActiveSubscriptionResponse.self, from: data)
                    
                    await MainActor.run {
                        self.isLoading = false
                    }
                    
                    if subscriptionResponse.code == 0 {
                        if let subscriptionData = subscriptionResponse.data {
                            print("✅ SubscriptionService: 刷新订阅信息成功")
                            print("  - 订阅ID: \(subscriptionData.id)")
                            print("  - 订阅状态: \(subscriptionData.status)")
                            print("  - 开始时间: \(subscriptionData.startDate ?? "无")")
                            print("  - 结束时间: \(subscriptionData.endDate ?? "无")")
                            print("  - 自动续订: \(subscriptionData.autoRenewal)")
                            print("  - 环境: \(subscriptionData.environment)")
                            
                            // 🆕 如果订阅状态为ACTIVE，执行建议生成流程
                            if subscriptionData.status == "ACTIVE" {
                                print("👑 SubscriptionService: 检测到激活订阅，开始执行建议生成流程")

                                // 异步执行建议流程，不阻塞刷新接口的返回
                                Task {
                                    await executeAdvicesAfterActiveSubscription()
                                }
                            }

                            return subscriptionData
                        } else {
                            print("ℹ️ SubscriptionService: 未找到对应的订阅信息")
                            return nil
                        }
                    } else {
                        await MainActor.run {
                            self.errorMessage = subscriptionResponse.message
                        }
                        print("❌ SubscriptionService: 刷新订阅信息失败 - 错误码: \(subscriptionResponse.code), 消息: \(subscriptionResponse.message)")
                        return nil
                    }
                } else {
                    await MainActor.run {
                        self.errorMessage = "Failed to refresh subscription info, status code: \(httpResponse.statusCode)"
                        self.isLoading = false
                    }
                    return nil
                }
            }
        } catch {
            await MainActor.run {
                self.errorMessage = "Network request failed: \(error.localizedDescription)"
                self.isLoading = false
            }
            print("❌ SubscriptionService: 刷新订阅信息失败: \(error)")
            return nil
        }
        
        return nil
    }
    
    // 获取所有订阅产品
    func fetchSubscriptionProducts() async {
        await MainActor.run {
            isLoading = true
            errorMessage = nil
        }
        
        guard let url = URL(string: "\(baseURL)/ns/app/subscription-products/all") else {
            await MainActor.run {
                errorMessage = "Invalid URL"
                isLoading = false
            }
            return
        }
        
        var request = URLRequest(url: url)
        request.httpMethod = "GET"
        request.setValue("application/json", forHTTPHeaderField: "Content-Type")
        
        // 添加认证头
        if let token = UserDefaults.standard.string(forKey: "accessToken") {
            request.setValue("Bearer \(token)", forHTTPHeaderField: "Authorization")
        }
        
        do {
            let (data, response) = try await URLSession.shared.data(for: request)
            
            if let httpResponse = response as? HTTPURLResponse {
                print("📱 SubscriptionService: 获取订阅产品响应状态码: \(httpResponse.statusCode)")
                
                if httpResponse.statusCode == 200 {
                    let products = try JSONDecoder().decode([SubscriptionProduct].self, from: data)
                    
                    await MainActor.run {
                        self.subscriptionProducts = products
                        self.isLoading = false
                        print("📱 SubscriptionService: 成功获取 \(products.count) 个订阅产品")
                    }
                } else {
                    await MainActor.run {
                        self.errorMessage = "Failed to fetch subscription products, status code: \(httpResponse.statusCode)"
                        self.isLoading = false
                    }
                }
            }
        } catch {
            await MainActor.run {
                self.errorMessage = "Network request failed: \(error.localizedDescription)"
                self.isLoading = false
            }
            print("❌ SubscriptionService: 获取订阅产品失败: \(error)")
        }
    }
    
    // 创建订阅并获取channelRequestId（使用实际的后端接口）
    func createSubscriptionAndGetChannelId(productId: String) async -> String? {
        await MainActor.run {
            isLoading = true
            errorMessage = nil
        }
        
        guard let url = URL(string: "\(baseURL)/ns/app/subscription") else {
            await MainActor.run {
                errorMessage = "Invalid URL"
                isLoading = false
            }
            return nil
        }
        
        // 查询当前Apple账号的原始交易ID
        var originalTransactionId: String?
        if #available(iOS 15.0, *) {
            originalTransactionId = await getCurrentOriginalTransactionId()
            if let originalId = originalTransactionId {
                print("📱 SubscriptionService: 将使用原始交易ID: \(originalId)")
            } else {
                print("📱 SubscriptionService: 没有找到原始交易ID，将不发送此字段")
            }
        }
        
        let requestBody = SubscriptionCreateRequest(
            isSandbox: isSandbox,
            productId: productId,
            originalTransactionId: originalTransactionId
        )
        
        print("📱 SubscriptionService: 创建订阅请求参数:")
        print("  - productId: \(productId)")
        print("  - 环境: 生产")
        print("  - originalTransactionId: \(originalTransactionId ?? "无")")
        
        var request = URLRequest(url: url)
        request.httpMethod = "POST"
        request.setValue("application/json", forHTTPHeaderField: "Content-Type")
        
        // 添加认证头
        if let token = UserDefaults.standard.string(forKey: "accessToken") {
            request.setValue("Bearer \(token)", forHTTPHeaderField: "Authorization")
        }
        
        do {
            request.httpBody = try JSONEncoder().encode(requestBody)
            
            if let jsonString = String(data: request.httpBody!, encoding: .utf8) {
                print("📱 SubscriptionService: 创建订阅请求JSON: \(jsonString)")
            }
            
            let (data, response) = try await URLSession.shared.data(for: request)
            
            if let httpResponse = response as? HTTPURLResponse {
                print("📱 SubscriptionService: 创建订阅响应状态码: \(httpResponse.statusCode)")
                
                if let responseString = String(data: data, encoding: .utf8) {
                    print("📱 SubscriptionService: 创建订阅响应数据: \(responseString)")
                }
                
                if httpResponse.statusCode == 200 {
                    let createResponse = try JSONDecoder().decode(SubscriptionCreateResponse.self, from: data)
                    
                    await MainActor.run {
                        self.isLoading = false
                    }
                    
                    if createResponse.code == 0, let data = createResponse.data {
                        print("📱 SubscriptionService: 订阅创建成功")
                        print("  - id: \(data.id)")
                        print("  - channelRequestId: \(data.channelRequestId)")
                        print("  - scenario: \(data.scenario)")
                        print("  - originalPayId: \(data.originalPayId ?? "无")")
                        
                        // 保存交易信息，以便后续调用根据苹果原始交易ID获取订阅信息的接口
                        saveTransactionInfo(
                            id: data.id,
                            channelRequestId: data.channelRequestId,
                            productId: productId,
                            scenario: data.scenario,
                            originalPayId: data.originalPayId
                        )
                        
                        return data.channelRequestId
                    } else {
                        await MainActor.run {
                            self.errorMessage = createResponse.message
                        }
                        print("📱 SubscriptionService: 订阅创建失败 - 错误码: \(createResponse.code), 消息: \(createResponse.message)")
                        return nil
                    }
                } else {
                    await MainActor.run {
                        self.errorMessage = "Failed to create subscription, status code: \(httpResponse.statusCode)"
                        self.isLoading = false
                    }
                    return nil
                }
            }
        } catch {
            await MainActor.run {
                self.errorMessage = "Network request failed: \(error.localizedDescription)"
                self.isLoading = false
            }
            print("❌ SubscriptionService: 创建订阅失败: \(error)")
            return nil
        }
        
        return nil
    }
    
    // 检查用户订阅状态 - 优先使用用户最新有效订阅接口，确保账号隔离
    func checkUserSubscriptionStatus() async -> SubscriptionInfo {
        print("📱 SubscriptionService: 开始检查用户订阅状态...")
        
        // 🔧 修复策略：优先使用用户最新有效订阅接口，确保获取当前登录用户的真实订阅状态
        // 避免使用全局的原始交易ID导致账号间状态污染
        
        // 首先尝试获取用户最新有效订阅
        if let activeSubscription = await fetchUserActiveSubscription() {
            print("📱 SubscriptionService: 通过用户最新有效订阅接口获取到订阅记录，状态: \(activeSubscription.status)")
            return createSubscriptionInfo(from: activeSubscription)
        }
        
        // 如果没有有效订阅，再检查是否有原始交易ID可以用于刷新
        // 但这种情况应该很少见，因为用户最新有效订阅接口应该能涵盖所有订阅状态
        
        // 🔧 修复：按用户ID获取原始交易ID，确保账号隔离
        var originalTransactionId: String?
        if let currentUserId = UserDefaults.standard.string(forKey: "userId"), !currentUserId.isEmpty {
            let userSpecificKey = "user_\(currentUserId)_lastOriginalTransactionId"
            originalTransactionId = UserDefaults.standard.string(forKey: userSpecificKey)
            print("📱 SubscriptionService: 尝试获取用户\(currentUserId)的原始交易ID: \(originalTransactionId ?? "无")")
        } else {
            // 兼容旧版本，检查全局key
            originalTransactionId = UserDefaults.standard.string(forKey: "lastOriginalTransactionId")
            print("📱 SubscriptionService: 使用全局原始交易ID（兼容模式）: \(originalTransactionId ?? "无")")
        }
        
        if let transactionId = originalTransactionId,
           !transactionId.isEmpty,
           currentTransactionInfo != nil {
            print("📱 SubscriptionService: 没有有效订阅，尝试使用原始交易ID刷新")
            
            if let refreshedSubscription = await fetchSubscriptionByOriginalTransactionId(originalTransactionId: transactionId) {
                print("📱 SubscriptionService: 通过刷新接口获取到订阅记录，状态: \(refreshedSubscription.status)")
                return createSubscriptionInfo(from: refreshedSubscription)
            } else {
                print("📱 SubscriptionService: 刷新接口也无法获取订阅信息")
            }
        } else {
            print("📱 SubscriptionService: 无原始交易ID或交易信息")
        }
        
        print("📱 SubscriptionService: 用户无订阅记录，显示首次订阅状态")
        // 无订阅记录时，统一显示 "Start 3-day free trial"
        return SubscriptionInfo(status: .firstTime)
    }
    
    // 根据订阅数据创建订阅信息
    private func createSubscriptionInfo(from subscription: SubscriptionUserResponse) -> SubscriptionInfo {
        // 检查订阅状态
        print("🕒 SubscriptionService: 分析订阅状态详情")
        print("  - 订阅状态: \(subscription.status)")
        print("  - 开始时间: \(subscription.startDate ?? "无")")
        print("  - 结束时间: \(subscription.endDate ?? "无")")
        print("  - 当前时间: \(Date())")
        print("  - 设备时区: \(TimeZone.current.identifier)")
        
        // 如果有结束时间，分析时间差异
        if let endDateString = subscription.endDate {
            let inputFormatter = DateFormatter()
            inputFormatter.dateFormat = "yyyy-MM-dd'T'HH:mm:ss"
            inputFormatter.locale = Locale(identifier: "en_US_POSIX")
            
            if let endDate = inputFormatter.date(from: endDateString) {
                let now = Date()
                let timeDifference = endDate.timeIntervalSince(now)
                print("  - 结束时间解析成功: \(endDate)")
                print("  - 时间差异: \(timeDifference) 秒 (\(timeDifference/3600) 小时)")
                print("  - 是否已过期: \(timeDifference < 0 ? "是" : "否")")
            } else {
                print("  - 结束时间解析失败")
            }
        }
        
        switch subscription.status {
        case "ACTIVE":
            print("📱 SubscriptionService: 用户有激活的订阅")
            return SubscriptionInfo(status: .hasActiveSubscription, subscription: subscription)
            
        case "EXPIRED", "CANCELED", "IN_GRACE_PERIOD", "IN_BILLING_RETRY":
            print("📱 SubscriptionService: 用户订阅已过期或取消，状态: \(subscription.status)")
            return SubscriptionInfo(status: .hasExpiredSubscription, subscription: subscription)
            
        case "INIT":
            print("📱 SubscriptionService: 用户订阅初始化中")
            return SubscriptionInfo(status: .hasExpiredSubscription, subscription: subscription)
            
        default:
            print("📱 SubscriptionService: 未知订阅状态: \(subscription.status)")
            return SubscriptionInfo(status: .hasExpiredSubscription, subscription: subscription)
        }
    }
    
    // 获取指定类型的订阅产品
    func getProductByType(_ type: Int) -> SubscriptionProduct? {
        return subscriptionProducts.first { $0.type == type }
    }
    
    // 获取月度订阅产品
    func getMonthlyProduct() -> SubscriptionProduct? {
        return getProductByType(1)
    }
    
    // 获取年度订阅产品
    func getAnnualProduct() -> SubscriptionProduct? {
        return getProductByType(2)
    }
    
    // 获取用户最新有效订阅
    func fetchUserActiveSubscription() async -> SubscriptionUserResponse? {
        await MainActor.run {
            isLoading = true
            errorMessage = nil
        }
        
        guard let url = URL(string: "\(baseURL)/ns/app/subscription/user/active") else {
            await MainActor.run {
                errorMessage = "Invalid URL"
                isLoading = false
            }
            return nil
        }
        
        var request = URLRequest(url: url)
        request.httpMethod = "GET"
        request.setValue("application/x-www-form-urlencoded", forHTTPHeaderField: "Content-Type")
        
        // 添加认证头
        if let token = UserDefaults.standard.string(forKey: "accessToken") {
            request.setValue("Bearer \(token)", forHTTPHeaderField: "Authorization")
            print("📱 SubscriptionService: 查询用户最新有效订阅请求已添加Authorization头，token前缀: \(token.prefix(10))...")
            
            // 检查token是否过期
            if let expiresAt = UserDefaults.standard.object(forKey: "tokenExpiresAt") as? Date {
                if Date() > expiresAt {
                    print("⚠️ SubscriptionService: Token已过期 - 过期时间: \(expiresAt), 当前时间: \(Date())")
                }
            }
        } else {
            print("⚠️ SubscriptionService: 警告 - 未找到accessToken")
            await MainActor.run {
                errorMessage = "User not logged in"
                isLoading = false
            }
            return nil
        }
        
        do {
            let (data, response) = try await URLSession.shared.data(for: request)
            
            if let httpResponse = response as? HTTPURLResponse {
                print("📱 SubscriptionService: 查询用户最新有效订阅响应状态码: \(httpResponse.statusCode)")
                
                if let responseString = String(data: data, encoding: .utf8) {
                    print("📱 SubscriptionService: 查询用户最新有效订阅响应数据: \(responseString)")
                }
                
                if httpResponse.statusCode == 200 {
                    let subscriptionResponse = try JSONDecoder().decode(UserActiveSubscriptionResponse.self, from: data)
                    
                    await MainActor.run {
                        self.isLoading = false
                    }
                    
                    if subscriptionResponse.code == 0 {
                        if let subscription = subscriptionResponse.data {
                            print("✅ SubscriptionService: 查询用户最新有效订阅成功，状态: \(subscription.status)")
                            return subscription
                        } else {
                            print("ℹ️ SubscriptionService: 用户无有效订阅记录")
                            return nil
                        }
                    } else {
                        await MainActor.run {
                            self.errorMessage = subscriptionResponse.message
                        }
                        print("❌ SubscriptionService: 查询用户最新有效订阅失败 - 错误码: \(subscriptionResponse.code), 消息: \(subscriptionResponse.message)")
                        return nil
                    }
                } else {
                    await MainActor.run {
                        self.errorMessage = "Failed to fetch user active subscription, status code: \(httpResponse.statusCode)"
                        self.isLoading = false
                    }
                    return nil
                }
            }
        } catch {
            await MainActor.run {
                self.errorMessage = "Network request failed: \(error.localizedDescription)"
                self.isLoading = false
            }
            print("❌ SubscriptionService: 查询用户最新有效订阅失败: \(error)")
            return nil
        }
        
        return nil
    }
    
    // 格式化订阅日期
    func formatSubscriptionDate(_ dateString: String) -> String {
        // 处理空字符串或"无"的情况
        if dateString.isEmpty || dateString == "无" {
            return "无"
        }
        
        let inputFormatter = DateFormatter()
        inputFormatter.dateFormat = "yyyy-MM-dd'T'HH:mm:ss.SSSZ"
        inputFormatter.locale = Locale(identifier: "en_US_POSIX")
        
        let outputFormatter = DateFormatter()
        outputFormatter.dateFormat = "MMM dd, yyyy"
        outputFormatter.locale = Locale.current
        
        if let date = inputFormatter.date(from: dateString) {
            return outputFormatter.string(from: date)
        } else {
            // 尝试其他日期格式
            inputFormatter.dateFormat = "yyyy-MM-dd'T'HH:mm:ss"
            if let date = inputFormatter.date(from: dateString) {
                return outputFormatter.string(from: date)
            }
            return dateString // 如果解析失败，返回原始字符串
        }
    }
    
    // 计算剩余天数
    func calculateRemainingDays(endDateString: String) -> Int {
        // 处理空字符串的情况
        if endDateString.isEmpty {
            return 0
        }
        
        let inputFormatter = DateFormatter()
        inputFormatter.dateFormat = "yyyy-MM-dd'T'HH:mm:ss.SSSZ"
        inputFormatter.locale = Locale(identifier: "en_US_POSIX")
        
        if let endDate = inputFormatter.date(from: endDateString) {
            let calendar = Calendar.current
            let now = Date()
            let components = calendar.dateComponents([.day], from: now, to: endDate)
            return max(0, components.day ?? 0)
        } else {
            // 尝试其他日期格式
            inputFormatter.dateFormat = "yyyy-MM-dd'T'HH:mm:ss"
            if let endDate = inputFormatter.date(from: endDateString) {
                let calendar = Calendar.current
                let now = Date()
                let components = calendar.dateComponents([.day], from: now, to: endDate)
                return max(0, components.day ?? 0)
            }
        }
        return 0
    }
    
    // 计算订阅进度（0.0 - 1.0）
    func calculateSubscriptionProgress(startDateString: String, endDateString: String) -> Double {
        // 处理空字符串的情况
        if startDateString.isEmpty || endDateString.isEmpty {
            return 0.0
        }
        
        let inputFormatter = DateFormatter()
        inputFormatter.dateFormat = "yyyy-MM-dd'T'HH:mm:ss.SSSZ"
        inputFormatter.locale = Locale(identifier: "en_US_POSIX")
        
        guard let startDate = inputFormatter.date(from: startDateString),
              let endDate = inputFormatter.date(from: endDateString) else {
            // 尝试其他日期格式
            inputFormatter.dateFormat = "yyyy-MM-dd'T'HH:mm:ss"
            guard let startDate = inputFormatter.date(from: startDateString),
                  let endDate = inputFormatter.date(from: endDateString) else {
                return 0.0
            }
            
            let now = Date()
            let totalDuration = endDate.timeIntervalSince(startDate)
            let elapsedDuration = now.timeIntervalSince(startDate)
            
            if totalDuration <= 0 {
                return 1.0
            }
            
            let progress = elapsedDuration / totalDuration
            return max(0.0, min(1.0, progress))
        }
        
        let now = Date()
        let totalDuration = endDate.timeIntervalSince(startDate)
        let elapsedDuration = now.timeIntervalSince(startDate)
        
        if totalDuration <= 0 {
            return 1.0
        }
        
        let progress = elapsedDuration / totalDuration
        return max(0.0, min(1.0, progress))
    }
    
    // 恢复购买功能 - 基于ApplePay.swift中的RestoreButton逻辑
    func restorePurchases() async throws -> Bool {
        print("🔄 SubscriptionService: 开始恢复购买流程")
        // 修复警告：获取系统版本的正确方式
        let systemVersion = await MainActor.run { UIDevice.current.systemVersion }
        print("📱 设备iOS版本: \(systemVersion)")
        print("📱 当前环境: 生产")
        
        if #available(iOS 15, *) {
            print("✅ 使用StoreKit 2恢复购买")
            return try await restoreUsingStoreKit2()
        } else {
            print("✅ 使用Legacy StoreKit恢复购买")
            return try await restoreUsingLegacyMethod()
        }
    }
    
    @available(iOS 15, *)
    private func restoreUsingStoreKit2() async throws -> Bool {
        print("🔄 StoreKit2: 开始获取当前有效交易...")
        var transactions = StoreKit.Transaction.currentEntitlements.makeAsyncIterator()
        var transactionList: [SubscriptionTransactionModel] = []
        var transactionCount = 0
        
        while let transaction = await transactions.next() {
            transactionCount += 1
            print("📦 StoreKit2: 发现交易 #\(transactionCount)")
            
            switch transaction {
            case .verified(let tx):
                print("✅ StoreKit2: 交易验证成功")
                print("  - 交易ID: \(tx.id)")
                print("  - 产品ID: \(tx.productID)")
                print("  - 购买日期: \(tx.purchaseDate)")
                print("  - 过期日期: \(tx.expirationDate?.description ?? "无")")
                print("  - 撤销日期: \(tx.revocationDate?.description ?? "无")")
                
                // 增加交易有效性检查
                if let expirationDate = tx.expirationDate, expirationDate < Date() {
                    print("❌ StoreKit2: 交易已过期: \(tx.id)")
                    continue
                }
                // 修复警告：只检查是否存在撤销日期，不需要绑定变量
                if tx.revocationDate != nil {
                    print("❌ StoreKit2: 交易已撤销: \(tx.id)")
                    continue
                }
                
                print("✅ StoreKit2: 交易有效，添加到恢复列表")
                // 收集有效交易信息
                let model = SubscriptionTransactionModel(
                    id: Int64(tx.id),
                    appAccountToken: tx.appAccountToken?.uuidString ?? "",
                    productID: tx.productID
                )
                transactionList.append(model)
                await tx.finish()
                print("✅ StoreKit2: 交易已完成: \(tx.id)")
                
            case .unverified(_, let error):
                print("❌ StoreKit2: 交易验证失败: \(error.localizedDescription)")
                throw SubscriptionRestoreError.restoreFailed("未验证的交易: \(error.localizedDescription)")
            }
        }
        
        print("📊 StoreKit2: 恢复购买完成")
        print("  - 总交易数: \(transactionCount)")
        print("  - 有效交易数: \(transactionList.count)")
        
        return transactionList.count > 0
    }
    
    private func restoreUsingLegacyMethod() async throws -> Bool {
        return try await withCheckedThrowingContinuation { continuation in
            DispatchQueue.main.async {
                SubscriptionPurchaseRestorer.shared.executeLegacyRestore(continuation: continuation)
            }
        }
    }
    
    // MARK: - 订阅成功后的建议执行
    
    /// 在激活订阅后执行建议生成流程
    private func executeAdvicesAfterActiveSubscription() async {
        print("🚀 SubscriptionService: 开始执行订阅成功后的建议生成流程")
        
        // 在主线程上执行建议流程
        await MainActor.run {
            let planAdviceService = PlanAdviceService.shared
            
            // 🔧 修复：添加短暂延迟，等待UserDefaults状态同步
            print("⏱️ SubscriptionService: 等待订阅状态同步...")
            
            DispatchQueue.main.asyncAfter(deadline: .now() + 1.0) {
                print("🔄 SubscriptionService: 开始执行建议流程（延迟后）")
                
                // 手动更新UserDefaults状态，确保PlanAdviceService能获取到最新状态
                Task {
                    // 创建独立的引用，避免捕获self
                    let subscriptionService = SubscriptionService.shared
                    let subscriptionInfo = await subscriptionService.checkUserSubscriptionStatus()
                    if subscriptionInfo.status == .hasActiveSubscription {
                        await MainActor.run {
                            UserDefaults.standard.set(true, forKey: "isPremium")
                            UserDefaults.standard.set("ACTIVE", forKey: "subscriptionStatus")
                            print("✅ SubscriptionService: 手动更新UserDefaults - isPremium: true, subscriptionStatus: ACTIVE")
                        }
                    }
                }
                
                // 执行完整的建议流程
                planAdviceService.executeAndFetchAdvicesAfterSubscription { success in
                    if success {
                        print("✅ SubscriptionService: 建议生成和获取流程完成成功")
                    } else {
                        print("❌ SubscriptionService: 建议生成和获取流程完成失败")
                    }
                }
            }
        }
        
        print("🏁 SubscriptionService: 订阅成功后的建议执行流程结束")
    }
    
    // 强制清理所有沙箱数据（用于调试）
    func forceCleanSandboxData() {
        print("🧹 SubscriptionService: 强制清理所有沙箱数据")
        
        // 清理所有可能相关的UserDefaults数据
        let keysToRemove = [
            "savedTransactionInfo",
            "lastOriginalTransactionId"
        ]
        
        for key in keysToRemove {
            UserDefaults.standard.removeObject(forKey: key)
            print("🧹 SubscriptionService: 已清理 \(key)")
        }
        
        // 清理所有用户特定的原始交易ID
        let allKeys = UserDefaults.standard.dictionaryRepresentation().keys
        for key in allKeys {
            if key.hasPrefix("user_") && key.hasSuffix("_lastOriginalTransactionId") {
                UserDefaults.standard.removeObject(forKey: key)
                print("🧹 SubscriptionService: 已清理用户数据 \(key)")
            }
        }
        
        // 重置当前交易信息
        currentTransactionInfo = nil
        
        print("✅ SubscriptionService: 强制清理完成")
    }
}

// StoreKit 集成管理器 - 使用苹果官方IAP功能
@available(iOS 15.0, *)
class StoreKitManager: ObservableObject {
    static let shared = StoreKitManager()

    @Published var products: [Product] = []
    @Published var purchasedProductIDs: Set<String> = []

    private let subscriptionService = SubscriptionService.shared
    private var transactionListener: Task<Void, Error>?

    // 生产环境标识
    private let isSandbox = false // 当前为生产环境


    
    private init() {
        // 启动交易监听器
        transactionListener = listenForTransactions()

        Task {
            await loadProducts()
            await updatePurchasedProducts()

            // 产品加载完成后，通知UserData更新订阅按钮文本
            NotificationCenter.default.post(name: Notification.Name("StoreKitProductsLoaded"), object: nil)
        }
    }
    
    deinit {
        transactionListener?.cancel()
    }
    
    // 监听交易更新
    private func listenForTransactions() -> Task<Void, Error> {
        // 避免直接捕获self，使用StoreKitManager.shared
        return Task.detached {
            // 监听交易更新
            for await result in Transaction.updates {
                do {
                    let transaction = try StoreKitManager.shared.checkVerified(result)
                    
                    print("🔄 StoreKitManager: 收到交易更新 - 产品ID: \(transaction.productID), 交易ID: \(transaction.id)")
                    print("🔄 StoreKitManager: 交易类型: \(transaction.productType), 购买日期: \(transaction.purchaseDate)")
                    
                    // 检查是否是订阅产品
                    if transaction.productType == .autoRenewable {
                        print("📱 StoreKitManager: 检测到自动续订订阅交易")
                        print("📱 StoreKitManager: Original Transaction ID: \(transaction.originalID)")
                        
                        // 尝试调用根据苹果原始交易ID刷新订阅信息的接口
                        // 注意：对于自动续订等情况，可能没有保存的交易信息，这是正常的
                        if let subscriptionInfo = await SubscriptionService.shared.fetchSubscriptionByOriginalTransactionId(originalTransactionId: String(transaction.originalID)) {
                            print("✅ StoreKitManager: 成功刷新订阅信息（自动续订）")
                            print("  - 订阅ID: \(subscriptionInfo.id)")
                            print("  - 订阅状态: \(subscriptionInfo.status)")
                            print("  - 开始时间: \(subscriptionInfo.startDate ?? "无")")
                            print("  - 结束时间: \(subscriptionInfo.endDate ?? "无")")
                            print("  - 环境: \(subscriptionInfo.environment)")
                        } else {
                            print("ℹ️ StoreKitManager: 未能刷新订阅信息（可能是自动续订或无保存的交易信息）")
                        }
                        
                        print("✅ StoreKitManager: 订阅交易已由苹果处理")
                    }
                    
                    // 更新购买状态
                    await StoreKitManager.shared.updatePurchasedProducts()
                    
                    // 完成交易
                    await transaction.finish()
                    
                } catch {
                    print("❌ StoreKitManager: 交易验证失败: \(error)")
                }
            }
        }
    }
    
    // 验证交易
    private func checkVerified<T>(_ result: VerificationResult<T>) throws -> T {
        switch result {
        case .unverified:
            throw StoreError.failedVerification
        case .verified(let safe):
            return safe
        }
    }
    
    // 加载产品
    @MainActor
    func loadProducts() async {
        print("🔄 StoreKitManager: 开始加载产品...")
        
        // 从订阅服务获取产品ID列表
        await subscriptionService.fetchSubscriptionProducts()
        
        let productIDs = subscriptionService.subscriptionProducts.map { $0.productId }
        print("📋 StoreKitManager: 从后端获取到的产品ID列表: \(productIDs)")
        
        if productIDs.isEmpty {
            print("⚠️ StoreKitManager: 警告 - 后端未返回任何产品ID")
            print("⚠️ StoreKitManager: 订阅服务错误信息: \(subscriptionService.errorMessage ?? "无错误信息")")
            
            // 如果后端没有返回产品ID，使用默认的测试产品ID
            let fallbackProductIDs = ["monthly_subscription", "yearly_subscription"]
            print("🔄 StoreKitManager: 使用备用产品ID列表: \(fallbackProductIDs)")
            await loadProductsWithIDs(fallbackProductIDs)
        } else {
            await loadProductsWithIDs(productIDs)
        }
    }
    
    // 使用指定的产品ID列表加载产品
    @MainActor
    private func loadProductsWithIDs(_ productIDs: [String]) async {
        print("🔄 StoreKitManager: 开始从App Store加载产品，产品ID列表: \(productIDs)")
        
        // 打印当前环境信息
        print("📱 StoreKitManager: 当前环境信息:")
        print("  - Bundle ID: \(Bundle.main.bundleIdentifier ?? "未知")")
        print("  - 生产模式: \(!isSandbox)")
        print("  - 环境标志: \(isSandbox ? "沙箱" : "生产")")
        // 修复警告：获取系统版本的正确方式
        let systemVersion = await MainActor.run { UIDevice.current.systemVersion }
        print("  - iOS版本: \(systemVersion)")
        
        // 检查是否是调试模式
        #if DEBUG
        print("  - 编译模式: DEBUG")
        #else
        print("  - 编译模式: RELEASE")
        #endif
        
        do {
            print("🔄 StoreKitManager: 正在从App Store加载产品...")
            let storeProducts = try await Product.products(for: productIDs)
            self.products = storeProducts
            
            print("📱 StoreKitManager: 成功加载 \(storeProducts.count) 个StoreKit产品")
            
            // 详细打印每个产品信息
            for product in storeProducts {
                print("✅ StoreKitManager: 产品详情:")
                print("  - ID: \(product.id)")
                print("  - 名称: \(product.displayName)")
                print("  - 价格: \(product.displayPrice)")
                print("  - 类型: \(product.type)")
                
                // 检查是否有免费试用期
                if let subscription = product.subscription {
                    print("  - 订阅详情:")
                    print("    - 组ID: \(subscription.subscriptionGroupID)")
                    
                    // 检查试用期信息
                    if let introOffer = subscription.introductoryOffer {
                        print("    - 试用期信息:")
                        print("      - 类型: \(introOffer.type)")
                        print("      - 价格: \(introOffer.displayPrice)")
                        print("      - 周期: \(introOffer.period)")
                        print("      - 周期数量: \(introOffer.periodCount)")
                        
                        switch introOffer.type {
                        case .introductory:
                            print("      - ✅ 免费试用期: \(introOffer.periodCount) \(introOffer.period.unit)")
                        case .promotional:
                            print("      - ✅ 促销价格: \(introOffer.displayPrice)")
                        default:
                            print("🎁 StoreKitManager: 这是其他类型的优惠交易")
                        }
                    } else {
                        print("    - ⚠️ 无试用期或优惠价格")
                    }
                }
            }
            
            // 检查是否有产品ID不匹配的情况
            let loadedProductIDs = Set(storeProducts.map { $0.id })
            let requestedProductIDs = Set(productIDs)
            let missingProductIDs = requestedProductIDs.subtracting(loadedProductIDs)
            
            if !missingProductIDs.isEmpty {
                print("❌ StoreKitManager: 以下产品ID在App Store中未找到: \(Array(missingProductIDs))")
                print("❌ StoreKitManager: 可能的原因:")
                print("  1. App Store Connect中的产品配置问题")
                print("  2. 产品状态未设置为'Ready for Sale'")
                print("  3. Bundle ID不匹配")
                print("  4. 生产环境同步延迟")
                print("  5. 用户账号问题")
                print("❌ StoreKitManager: 请检查App Store Connect中的产品配置")
                
                // 详细的调试建议
                print("🔧 StoreKitManager: 调试建议:")
                print("  1. 确认App Store Connect中产品ID是否正确: \(Array(missingProductIDs))")
                print("  2. 确认产品状态是否为'Ready for Sale'")
                print("  3. 确认Bundle ID是否匹配: \(Bundle.main.bundleIdentifier ?? "未知")")
                print("  4. 等待5-10分钟后重试（生产环境同步）")
                print("  5. 确认用户已登录App Store账号")
            }
            
        } catch {
            print("❌ StoreKitManager: 加载产品失败: \(error)")
            
            // 详细错误信息
            if let storeKitError = error as? StoreKitError {
                print("❌ StoreKitManager: StoreKit错误类型: \(storeKitError)")
                
                switch storeKitError {
                case .networkError:
                    print("❌ StoreKitManager: 网络连接问题 - 请检查网络连接")
                case .systemError:
                    print("❌ StoreKitManager: 系统错误 - 请重启应用或设备")
                case .userCancelled:
                    print("❌ StoreKitManager: 用户取消操作")
                case .notAvailableInStorefront:
                    print("❌ StoreKitManager: 产品在当前商店不可用 - 检查地区设置")
                case .notEntitled:
                    print("❌ StoreKitManager: 用户无权限")
                case .unsupported:
                    print("❌ StoreKitManager: 不支持的操作")
                case .unknown:
                    print("❌ StoreKitManager: 未知StoreKit错误")
                default:
                    print("❌ StoreKitManager: 新的未知StoreKit错误")
                }
            }
            
            if let nsError = error as NSError? {
                print("❌ StoreKitManager: 错误详情:")
                print("  - Domain: \(nsError.domain)")
                print("  - Code: \(nsError.code)")
                print("  - Description: \(nsError.localizedDescription)")
                print("  - UserInfo: \(nsError.userInfo)")
                
                // 特殊错误处理
                if nsError.domain == "ASDErrorDomain" && nsError.code == 509 {
                    print("❌ StoreKitManager: 检测到'No active account'错误")
                    print("❌ StoreKitManager: 请在设置 > App Store中登录Apple ID账号")
                    
                    // 更新订阅服务的错误信息
                    await MainActor.run {
                        subscriptionService.errorMessage = "请登录Apple ID账号: 设置 > App Store"
                    }
                }
            }
        }
    }
    
    // 购买产品（使用苹果官方IAP流程）
    func purchase(_ product: Product) async -> Bool {
        print("🛒 StoreKitManager: 开始购买产品 - \(product.displayName) (\(product.id))")

        // 检查试用期资格
        let trialEligibility = await checkTrialEligibility(for: product)
        print("📱 StoreKitManager: 试用期资格检查结果: \(trialEligibility)")
        
        // 首先检查用户当前的订阅状态
        let subscriptionInfo = await subscriptionService.checkUserSubscriptionStatus()
        
        var channelRequestId: String?
        
        // 如果用户已经是活跃会员，跳过创建订阅接口调用
        if subscriptionInfo.status == .hasActiveSubscription {
            print("👑 StoreKitManager: 用户已是活跃会员，跳过创建订阅接口调用")
            // 对于已有活跃订阅的用户，使用现有的channelRequestId或生成一个临时ID
            if let existingChannelRequestId = subscriptionInfo.subscription?.channelRequestId {
                channelRequestId = existingChannelRequestId
                print("✅ StoreKitManager: 使用现有的channelRequestId: \(existingChannelRequestId)")
                
                // 为已有订阅创建临时交易信息（用于调用根据苹果原始交易ID获取订阅信息的接口）
                if let subscription = subscriptionInfo.subscription {
                    subscriptionService.saveTransactionInfo(
                        id: subscription.id,
                        channelRequestId: subscription.channelRequestId,
                        productId: product.id,
                        scenario: "SUBSCRIPTION",
                        originalPayId: subscription.originalPayId
                    )
                }
            } else {
                // 如果没有现有的channelRequestId，生成一个临时的UUID
                channelRequestId = UUID().uuidString
                print("✅ StoreKitManager: 生成临时channelRequestId: \(channelRequestId!)")
            }
        } else {
            // 对于非活跃会员，正常调用创建订阅接口
            print("📝 StoreKitManager: 用户非活跃会员，调用创建订阅接口")
            guard let newChannelRequestId = await subscriptionService.createSubscriptionAndGetChannelId(productId: product.id) else {
            print("❌ StoreKitManager: 创建订阅记录失败，无法获取channelRequestId")
            return false
        }
            channelRequestId = newChannelRequestId
            print("✅ StoreKitManager: 获取到新的channelRequestId: \(newChannelRequestId)")
        }
        
        // 确保有有效的channelRequestId
        guard let finalChannelRequestId = channelRequestId else {
            print("❌ StoreKitManager: 无法获取有效的channelRequestId")
            return false
        }
        
        // 发起苹果支付，使用channelRequestId作为applicationUsername
        do {
            // 创建支付请求
            let result = try await product.purchase(options: [
                .appAccountToken(UUID(uuidString: finalChannelRequestId) ?? UUID())
            ])
            
            switch result {
            case .success(let verification):
                print("✅ StoreKitManager: 购买请求成功，开始验证交易...")
                
                switch verification {
                case .verified(let transaction):
                    print("✅ StoreKitManager: 交易验证成功 - 交易ID: \(transaction.id)")
                    print("✅ StoreKitManager: App Account Token: \(transaction.appAccountToken?.uuidString ?? "无")")
                    print("✅ StoreKitManager: Original Transaction ID: \(transaction.originalID)")
                    
                    // 检查是否是试用期交易
                    if let offerType = transaction.offerType {
                        switch offerType {
                        case .introductory:
                            print("🎁 StoreKitManager: 这是试用期或优惠价格交易")
                        case .promotional:
                            print("🎁 StoreKitManager: 这是促销价格交易")
                        default:
                            print("🎁 StoreKitManager: 这是其他类型的优惠交易")
                        }
                    } else {
                        print("💰 StoreKitManager: 这是正常价格交易")
                    }
                    

                    
                    // 保存原始交易ID以供后续使用刷新接口 - 按用户ID隔离存储
                    let originalTransactionId = String(transaction.originalID)
                    
                    // 🔧 重要修复：按用户ID保存原始交易ID，避免账号间交易信息污染
                    if let currentUserId = UserDefaults.standard.string(forKey: "userId"), !currentUserId.isEmpty {
                        let userSpecificKey = "user_\(currentUserId)_lastOriginalTransactionId"
                        UserDefaults.standard.set(originalTransactionId, forKey: userSpecificKey)
                        print("📱 StoreKitManager: 已为用户\(currentUserId)保存原始交易ID: \(originalTransactionId)")
                        
                        // 同时清除全局的key，确保不会被误用
                        UserDefaults.standard.removeObject(forKey: "lastOriginalTransactionId")
                    } else {
                        print("⚠️ StoreKitManager: 无法获取当前用户ID，使用全局保存")
                        UserDefaults.standard.set(originalTransactionId, forKey: "lastOriginalTransactionId")
                    }
                    
                    // 调用根据苹果原始交易ID刷新订阅信息的接口
                    if let subscriptionInfo = await subscriptionService.fetchSubscriptionByOriginalTransactionId(originalTransactionId: originalTransactionId) {
                        print("✅ StoreKitManager: 成功刷新订阅信息")
                        print("  - 订阅ID: \(subscriptionInfo.id)")
                        print("  - 订阅状态: \(subscriptionInfo.status)")
                        print("  - 开始时间: \(subscriptionInfo.startDate ?? "无")")
                        print("  - 结束时间: \(subscriptionInfo.endDate ?? "无")")
                        print("  - 自动续订: \(subscriptionInfo.autoRenewal)")
                        print("  - 环境: \(subscriptionInfo.environment)")
                        
                        // 🆕 如果订阅状态为ACTIVE，执行建议生成流程，并上报 AppsFlyer 购买事件
                        if subscriptionInfo.status == "ACTIVE" {
                            print("👑 StoreKitManager: 检测到激活订阅，开始执行建议生成流程")
                            
                            // 异步执行建议流程，不阻塞购买流程的返回
                            Task {
                                await executeAdvicesAfterActiveSubscription()
                            }
                            // AppsFlyer 购买事件上报
                            let backendProduct = SubscriptionService.shared.subscriptionProducts.first { $0.productId == product.id }
                            let revenue: Double = backendProduct?.price ?? 0.0
                            let currency: String = backendProduct?.currency ?? (Locale.current.currency?.identifier ?? "USD")
                            var afValues: [String: Any] = [
                                AFEventParamRevenue: revenue,
                                AFEventParamCurrency: currency,
                                AFEventParamContentId: product.id,
                                AFEventParamContentType: "subscription",
                                AFEventParamQuantity: 1
                            ]
                            if let original = subscriptionInfo.originalPayId {
                                afValues["original_pay_id"] = original
                            }
                            AppsFlyerLib.shared().logEvent(AFEventPurchase, withValues: afValues)
                            print("[AppsFlyer] af_purchase 已上报: \(afValues)")
                        }
                    } else {
                        print("⚠️ StoreKitManager: 未能刷新订阅信息，但苹果交易已验证成功")
                    }
                    
                    // 苹果会自动处理收据验证和订阅管理
                    // 我们只需要更新本地状态
                    await updatePurchasedProducts()
                    await transaction.finish()

                    print("✅ StoreKitManager: 订阅购买成功")
                    return true
                    
                case .unverified:
                    print("❌ StoreKitManager: 交易验证失败 - 交易可能被篡改")
                    return false
                }
                
            case .userCancelled:
                print("📱 StoreKitManager: 用户取消购买")
                return false
                
            case .pending:
                print("📱 StoreKitManager: 购买待处理 - 可能需要家长批准")
                return false
                
            default:
                print("❌ StoreKitManager: 未知购买结果")
                return false
            }
        } catch {
            print("❌ StoreKitManager: 购买失败，错误详情:")
            print("   错误类型: \(type(of: error))")
            print("   错误描述: \(error.localizedDescription)")
            
            // 检查具体的错误类型
            if let storeKitError = error as? StoreKitError {
                print("   StoreKit错误: \(storeKitError)")
                
                switch storeKitError {
                case .networkError:
                    print("   -> 网络连接问题")
                case .systemError:
                    print("   -> 系统错误")
                case .userCancelled:
                    print("   -> 用户取消")
                case .notAvailableInStorefront:
                    print("   -> 产品在当前商店不可用")
                case .notEntitled:
                    print("   -> 用户无权限")
                case .unsupported:
                    print("   -> 不支持的操作")
                case .unknown:
                    print("   -> 未知StoreKit错误")
                default:
                    print("   -> 新的未知StoreKit错误")
                }
            }
            
            // 检查是否是账号问题
            if let nsError = error as NSError? {
                print("   NSError - Domain: \(nsError.domain), Code: \(nsError.code)")
                print("   UserInfo: \(nsError.userInfo)")
                
                if nsError.domain == "ASDErrorDomain" && nsError.code == 509 {
                    print("   -> 检测到'No active account'错误")
                    // 更新订阅服务的错误信息
                    await MainActor.run {
                        subscriptionService.errorMessage = "No active account - Please sign in with your Apple ID"
                    }
                }
            }
            
            return false
        }
    }
    
    // 检查试用期资格 - 使用苹果官方API
    @MainActor
    func checkUserTrialEligibility(for product: Product) async -> Bool {
        guard let subscription = product.subscription else {
            print("❌ StoreKitManager: 产品不是订阅类型")
            return false
        }

        guard let introOffer = subscription.introductoryOffer else {
            print("❌ StoreKitManager: 产品没有试用期配置")
            return false
        }

        guard introOffer.type == .introductory else {
            print("❌ StoreKitManager: 产品试用期类型不是免费试用期: \(introOffer.type)")
            return false
        }

        // 使用苹果官方API检查用户是否有资格享受试用期
        let isEligible = await subscription.isEligibleForIntroOffer
        print("🔍 StoreKitManager: 苹果官方试用期资格检查结果 - 产品ID: \(product.id), 有资格: \(isEligible)")
        return isEligible
    }

    // 检查试用期资格 - 保持向后兼容的字符串返回版本
    @MainActor
    func checkTrialEligibility(for product: Product) async -> String {
        guard let subscription = product.subscription else {
            return "非订阅产品"
        }

        guard let introOffer = subscription.introductoryOffer else {
            return "无试用期或优惠"
        }

        // 检查用户是否有资格享受试用期
        let isEligible = await subscription.isEligibleForIntroOffer

        if isEligible {
            switch introOffer.type {
            case .introductory:
                let trialPeriod = "\(introOffer.periodCount) \(introOffer.period.unit)"
                return "免费试用期: \(trialPeriod)"
            case .promotional:
                return "促销价格: \(introOffer.displayPrice)"
            default:
                return "未知优惠类型"
            }
        } else {
            return "无试用期资格"
        }
    }
    
    // 获取产品的试用期信息（用于UI显示）- 异步版本，检查真实资格
    @MainActor
    func getTrialInfo(for product: Product) async -> (hasFreeTrial: Bool, trialText: String) {
        print("🔍 StoreKitManager: 开始分析产品试用期信息")
        print("  - 产品ID: \(product.id)")
        print("  - 产品名称: \(product.displayName)")
        print("  - 产品价格: \(product.displayPrice)")
        print("  - 产品类型: \(product.type)")

        guard let subscription = product.subscription else {
            print("❌ StoreKitManager: 产品不是订阅类型")
            return (false, "")
        }

        print("  - 订阅组ID: \(subscription.subscriptionGroupID)")

        guard let introOffer = subscription.introductoryOffer else {
            print("❌ StoreKitManager: 产品没有试用期配置")
            return (false, "")
        }

        guard introOffer.type == .introductory else {
            print("❌ StoreKitManager: 产品试用期类型不是免费试用期: \(introOffer.type)")
            return (false, "")
        }

        // 首先检查用户是否有资格享受试用期
        let isEligible = await checkUserTrialEligibility(for: product)
        if !isEligible {
            print("❌ StoreKitManager: 用户没有试用期资格")
            return (false, "")
        }
        
        // 获取试用期信息
        let actualPeriodCount = introOffer.periodCount
        let actualUnit = introOffer.period.unit
        
        print("🔍 StoreKitManager: 试用期详细信息:")
        print("  - 原始周期数量: \(actualPeriodCount)")
        print("  - 原始周期单位: \(actualUnit)")
        print("  - 显示价格: \(introOffer.displayPrice)")
        print("  - 试用期类型: \(introOffer.type)")
        
        // 修复：正确解析试用期配置
        // 根据您的日志，period显示为"3 Days"但periodCount是1，说明配置是1个3天周期
        let displayPeriodCount: Int
        let periodText: String
        
        // 根据实际配置判断：如果周期单位是Day且周期数量是1，但您期望显示3天
        // 这可能是因为App Store Connect中配置的是"1个3天周期"
        if actualUnit == .day && actualPeriodCount == 1 {
            // 检查是否是3天配置的特殊情况
            // 这里我们假设如果周期数量是1但您期望3天，说明配置是1个3天周期
            displayPeriodCount = 3
            periodText = "days"
            print("🔧 StoreKitManager: 检测到配置为1个3天周期，显示为3天")
        } else {
            // 使用原始配置
            displayPeriodCount = actualPeriodCount
            switch actualUnit {
            case .day:
                periodText = displayPeriodCount == 1 ? "day" : "days"
            case .week:
                periodText = displayPeriodCount == 1 ? "week" : "weeks"
            case .month:
                periodText = displayPeriodCount == 1 ? "month" : "months"
            case .year:
                periodText = displayPeriodCount == 1 ? "year" : "years"
            default:
                periodText = "period"
            }
        }
        
        print("📱 StoreKitManager: 生产环境，试用期正常显示")
        print("  - 显示周期: \(displayPeriodCount) \(periodText)")
        
        let trialText = "\(displayPeriodCount)-\(periodText) free trial"
        print("✅ StoreKitManager: 最终试用期文本: \(trialText)")
        
        return (true, trialText)
    }
    
    // 更新已购买产品
    @MainActor
    func updatePurchasedProducts() async {
        var purchasedIDs: Set<String> = []
        
        for await result in Transaction.currentEntitlements {
            switch result {
            case .verified(let transaction):
                purchasedIDs.insert(transaction.productID)
            case .unverified:
                break
            }
        }
        
        self.purchasedProductIDs = purchasedIDs
        print("✅ StoreKitManager: 已更新购买产品列表，数量: \(purchasedIDs.count)")
    }
    
    // 检查账号状态
    func checkAccountStatus() async -> Bool {
        do {
            // 首先尝试从订阅服务获取产品ID
            var testProductIDs: [String] = []
            
            if !subscriptionService.subscriptionProducts.isEmpty {
                // 如果已经有产品数据，使用这些产品ID
                testProductIDs = subscriptionService.subscriptionProducts.map { $0.productId }
                print("🔍 StoreKitManager: 使用已获取的产品ID进行检查: \(testProductIDs)")
            } else {
                // 如果没有产品数据，先获取产品列表
                await subscriptionService.fetchSubscriptionProducts()
                if !subscriptionService.subscriptionProducts.isEmpty {
                    testProductIDs = subscriptionService.subscriptionProducts.map { $0.productId }
                    print("🔍 StoreKitManager: 获取到产品ID进行检查: \(testProductIDs)")
                } else {
                    // 使用默认的测试产品ID
                    testProductIDs = ["com.fitscanai.monthly", "com.fitscanai.annual"]
                    print("🔍 StoreKitManager: 使用默认产品ID进行检查: \(testProductIDs)")
                }
            }
            
            // 尝试获取产品信息来验证账号状态
            let products = try await Product.products(for: testProductIDs)
            
            print("✅ StoreKitManager: 账号状态检查 - 成功获取到 \(products.count) 个产品")
            
            // 如果能获取到产品，说明账号状态正常
            if !products.isEmpty {
                print("✅ StoreKitManager: 账号状态正常，可以进行购买")
                return true
            } else {
                print("⚠️ StoreKitManager: 未获取到任何产品")
                return true
            }
            
        } catch {
            print("❌ StoreKitManager: 账号状态检查失败: \(error)")
            
            // 检查具体的错误类型
            if let storeKitError = error as? StoreKitError {
                switch storeKitError {
                case .networkError:
                    print("❌ StoreKitManager: 网络错误，可能需要检查网络连接")
                    return false
                case .systemError:
                    print("❌ StoreKitManager: 系统错误，可能需要重启应用")
                    return false
                default:
                    print("❌ StoreKitManager: 其他StoreKit错误: \(storeKitError)")
                    return false
                }
            }
            
            // 检查是否是"No active account"错误
            if let nsError = error as NSError?, 
               nsError.domain == "ASDErrorDomain" && nsError.code == 509 {
                print("❌ StoreKitManager: 检测到'No active account'错误 - 需要登录Apple ID账号")
                return false
            }
            
            // 对于其他错误，我们假设账号状态正常，让用户尝试购买
            print("⚠️ StoreKitManager: 未知错误，假设账号状态正常")
            return true
        }
    }
    
    // 新的恢复购买方法 - 委托给SubscriptionService
    func restorePurchases() async {
        do {
            _ = try await SubscriptionService.shared.restorePurchases()
            await updatePurchasedProducts()
        } catch {
            print("❌ StoreKitManager: 恢复购买失败: \(error)")
        }
    }
    
    // MARK: - 订阅成功后的建议执行
    
    /// 在激活订阅后执行建议生成流程
    private func executeAdvicesAfterActiveSubscription() async {
        print("🚀 StoreKitManager: 开始执行订阅成功后的建议生成流程")
        
        // 在主线程上执行建议流程
        await MainActor.run {
            let planAdviceService = PlanAdviceService.shared
            
            // 🔧 修复：添加短暂延迟，等待UserDefaults状态同步
            print("⏱️ StoreKitManager: 等待订阅状态同步...")
            
            DispatchQueue.main.asyncAfter(deadline: .now() + 1.0) {
                print("🔄 StoreKitManager: 开始执行建议流程（延迟后）")
                
                // 手动更新UserDefaults状态，确保PlanAdviceService能获取到最新状态
                let subscriptionService = SubscriptionService.shared
                Task {
                    let subscriptionInfo = await subscriptionService.checkUserSubscriptionStatus()
                    if subscriptionInfo.status == .hasActiveSubscription {
                        await MainActor.run {
                            UserDefaults.standard.set(true, forKey: "isPremium")
                            UserDefaults.standard.set("ACTIVE", forKey: "subscriptionStatus")
                            print("✅ StoreKitManager: 手动更新UserDefaults - isPremium: true, subscriptionStatus: ACTIVE")
                        }
                    }
                }
                
                // 执行完整的建议流程
                planAdviceService.executeAndFetchAdvicesAfterSubscription { success in
                    if success {
                        print("✅ StoreKitManager: 建议生成和获取流程完成成功")
                    } else {
                        print("❌ StoreKitManager: 建议生成和获取流程完成失败")
                    }
                }
            }
        }
        
        print("🏁 StoreKitManager: 订阅成功后的建议执行流程结束")
    }
}

// 订阅管理视图
struct SubscriptionManagementView: View {
    @EnvironmentObject var userData: UserData
    @StateObject private var subscriptionService = SubscriptionService.shared
    @State private var subscriptionInfo: SubscriptionInfo?
    @State private var isLoading = true
    
    var body: some View {
        NavigationView {
            VStack(spacing: 20) {
                if isLoading {
                    ProgressView("Loading subscription info...")
                        .frame(maxWidth: .infinity, maxHeight: .infinity)
                } else if let subscriptionInfo = subscriptionInfo {
                    if subscriptionInfo.isSubscribed, let subscription = subscriptionInfo.subscription {
                        // 有激活订阅时显示会员卡
                        activeSubscriptionView(subscription: subscription)
                    } else {
                        // 无激活订阅时显示订阅提示
                        noActiveSubscriptionView(subscriptionInfo: subscriptionInfo)
                    }
                } else {
                    // 加载失败时的默认视图
                    errorView()
                }
                
                Spacer()
                
                // 管理按钮
                VStack(spacing: 15) {
                    // 刷新按钮
                    Button(action: {
                        Task {
                            await loadSubscriptionInfo()
                        }
                    }) {
                        Text("Refresh Status")
                            .fontWeight(.semibold)
                            .frame(maxWidth: .infinity)
                            .padding()
                            .background(Color.green)
                            .foregroundColor(.white)
                            .cornerRadius(10)
                    }
                }
                
                // 错误信息显示
                if let errorMessage = subscriptionService.errorMessage {
                    Text(errorMessage)
                        .font(.caption)
                        .foregroundColor(.red)
                        .frame(maxWidth: .infinity, alignment: .center)
                        .padding(.top, 10)
                }
            }
            .padding()
            .navigationTitle("Subscription Management")
            .navigationBarTitleDisplayMode(.inline)
        }
        .onAppear {
            // 统计
            XDTrackTool.shared.appear("订阅管理页面")

            Task {
                await loadSubscriptionInfo()
            }
        }
    }
    
    // 激活订阅视图
    @ViewBuilder
    private func activeSubscriptionView(subscription: SubscriptionUserResponse) -> some View {
                VStack(alignment: .leading, spacing: 15) {
                    Text("Current Subscription")
                        .font(.title2)
                        .fontWeight(.bold)
                    
            // 会员卡
            VStack(alignment: .leading, spacing: 12) {
                    HStack {
                        VStack(alignment: .leading, spacing: 5) {
                        Text("Premium Member")
                                .font(.headline)
                            .foregroundColor(.white)
                            
                        Text("Valid Until: \(subscriptionService.formatSubscriptionDate(subscription.endDate ?? "无"))")
                                .font(.subheadline)
                            .foregroundColor(.white.opacity(0.9))
                            
                        let remainingDays = subscriptionService.calculateRemainingDays(endDateString: subscription.endDate ?? "")
                        Text("Remaining: \(remainingDays) days")
                                .font(.subheadline)
                            .foregroundColor(.white.opacity(0.9))
                        }
                        
                        Spacer()
                        
                        // 订阅状态图标
                        Image(systemName: "checkmark.circle.fill")
                        .foregroundColor(.white)
                            .font(.title)
                    }
                
                // 订阅进度条
                let progress = subscriptionService.calculateSubscriptionProgress(
                    startDateString: subscription.startDate ?? "",
                    endDateString: subscription.endDate ?? ""
                )
                
                VStack(alignment: .leading, spacing: 8) {
                    Text("Subscription Progress")
                        .font(.caption)
                        .foregroundColor(.white.opacity(0.8))
                    
                    ProgressView(value: progress)
                        .progressViewStyle(LinearProgressViewStyle(tint: .white))
                    
                    HStack {
                        Text(subscriptionService.formatSubscriptionDate(subscription.startDate ?? "无"))
                            .font(.caption2)
                            .foregroundColor(.white.opacity(0.7))
                        
                        Spacer()
                        
                        Text(subscriptionService.formatSubscriptionDate(subscription.endDate ?? "无"))
                            .font(.caption2)
                            .foregroundColor(.white.opacity(0.7))
                    }
                }
            }
            .padding()
            .background(
                LinearGradient(
                    gradient: Gradient(colors: [Color.green, Color.green.opacity(0.8)]),
                    startPoint: .topLeading,
                    endPoint: .bottomTrailing
                )
            )
            .cornerRadius(16)
            .shadow(color: .green.opacity(0.3), radius: 10, x: 0, y: 5)
            
            // 订阅详情
                VStack(alignment: .leading, spacing: 10) {
                Text("Subscription Details")
                        .font(.headline)
                    
                    HStack {
                    Text("Status:")
                            .foregroundColor(.secondary)
                    Spacer()
                    Text(subscription.status)
                        .fontWeight(.medium)
                        .foregroundColor(subscription.status == "ACTIVE" ? .green : .orange)
                }
                        
                HStack {
                    Text("Auto Renewal:")
                        .foregroundColor(.secondary)
                        Spacer()
                    Text(subscription.autoRenewal ? "Enabled" : "Disabled")
                        .fontWeight(.medium)
                        .foregroundColor(subscription.autoRenewal ? .green : .red)
                }
                        
                HStack {
                    Text("Environment:")
                            .foregroundColor(.secondary)
                    Spacer()
                    Text(subscription.environment)
                        .fontWeight(.medium)
                        .foregroundColor(subscription.environment == "Production" ? .green : .orange)
                    }
                }
                .padding()
                .background(Color.gray.opacity(0.1))
                .cornerRadius(12)
        }
    }
    
    // 无激活订阅视图
    @ViewBuilder
    private func noActiveSubscriptionView(subscriptionInfo: SubscriptionInfo) -> some View {
        VStack(spacing: 20) {
            Image(systemName: "star.circle")
                .font(.system(size: 60))
                .foregroundColor(.orange)
            
            Text(subscriptionInfo.displayText)
                .font(.title2)
                .fontWeight(.bold)
                .multilineTextAlignment(.center)
            
            Text("Unlock premium features and get personalized nutrition guidance")
                .font(.subheadline)
                .foregroundColor(.secondary)
                .multilineTextAlignment(.center)
                .padding(.horizontal)
            
            // 根据状态显示不同的提示
            if subscriptionInfo.status == .firstTime {
                VStack(spacing: 10) {
                    HStack {
                        Image(systemName: "gift.fill")
                            .foregroundColor(.green)
                        Text("3-day free trial included")
                            .font(.subheadline)
                            .foregroundColor(.green)
                    }
                    
                    Text("No commitment, cancel anytime")
                        .font(.caption)
                        .foregroundColor(.secondary)
                }
                                .padding()
                .background(Color.green.opacity(0.1))
                                .cornerRadius(10)
                        }
                    }
        .padding()
                }
                
    // 错误视图
    @ViewBuilder
    private func errorView() -> some View {
        VStack(spacing: 20) {
            Image(systemName: "exclamationmark.triangle")
                .font(.system(size: 50))
                .foregroundColor(.orange)
            
            Text("Unable to load subscription info")
                .font(.headline)
                .multilineTextAlignment(.center)
            
            Text("Please check your network connection and try again")
                .font(.subheadline)
                .foregroundColor(.secondary)
                .multilineTextAlignment(.center)
            }
            .padding()
    }
    
    // 加载订阅信息
    private func loadSubscriptionInfo() async {
        await MainActor.run {
            isLoading = true
        }
        
        let info = await subscriptionService.checkUserSubscriptionStatus()
        
        await MainActor.run {
            subscriptionInfo = info
            isLoading = false
        }
    }
}

// 错误类型
enum SubscriptionRestoreError: LocalizedError {
    case restoreFailed(String)
    case timeout
    
    var errorDescription: String? {
        switch self {
        case .restoreFailed(let msg):
            return NSLocalizedString("恢复失败: \(msg)", comment: "")
        case .timeout:
            return NSLocalizedString("请求超时，请检查网络连接", comment: "")
        }
    }
}

// 订阅交易模型
struct SubscriptionTransactionModel: Identifiable {
    var id: Int64
    var appAccountToken: String
    var productID: String
}

// 恢复购买处理器
class SubscriptionPurchaseRestorer: NSObject {
    private var continuation: CheckedContinuation<Bool, Error>?
    
    static let shared = SubscriptionPurchaseRestorer()
    private override init() {}
    
    @MainActor
    func executeLegacyRestore(continuation: CheckedContinuation<Bool, Error>) {
        print("🔄 Legacy: 开始Legacy StoreKit恢复购买")
        self.continuation = continuation
        
        print("📱 Legacy: 调用SKPaymentQueue.restoreCompletedTransactions()")
        SKPaymentQueue.default().restoreCompletedTransactions()
        SKPaymentQueue.default().add(self)
        
        print("⏰ Legacy: 设置15秒超时计时器")
        // 超时处理
        DispatchQueue.main.asyncAfter(deadline: .now() + 15) {
            // 使用shared实例避免捕获self
            let restorer = SubscriptionPurchaseRestorer.shared
            guard let cont = restorer.continuation else { return }
            print("⏰ Legacy: 恢复购买超时")
            cont.resume(throwing: SubscriptionRestoreError.timeout)
            restorer.cleanUp()
        }
    }
    
    private func cleanUp() {
        SKPaymentQueue.default().remove(self)
        continuation = nil
    }
}

extension SubscriptionPurchaseRestorer: SKPaymentTransactionObserver {
    func paymentQueue(_ queue: SKPaymentQueue, updatedTransactions transactions: [SKPaymentTransaction]) {
        print("📦 Legacy: 收到交易状态更新，交易数量: \(transactions.count)")
        for transaction in transactions {
            print("  - 交易ID: \(transaction.transactionIdentifier ?? "无")")
            print("  - 产品ID: \(transaction.payment.productIdentifier)")
            print("  - 状态: \(transaction.transactionState.rawValue)")
        }
    }
    
    func paymentQueueRestoreCompletedTransactionsFinished(_ queue: SKPaymentQueue) {
        print("✅ Legacy: 恢复购买完成")
        guard let continuation = continuation else { 
            print("⚠️ Legacy: continuation为空，无法返回结果")
            return 
        }
        continuation.resume(returning: true)
        cleanUp()
    }
    
    func paymentQueue(_ queue: SKPaymentQueue, restoreCompletedTransactionsFailedWithError error: Error) {
        print("❌ Legacy: 恢复购买失败: \(error.localizedDescription)")
        guard let continuation = continuation else { 
            print("⚠️ Legacy: continuation为空，无法返回错误")
            return 
        }
        continuation.resume(throwing: error)
        cleanUp()
    }
} 
