//
//  UserPlanService.swift
//  FitScanAI
//
//  Created by 陈亚宁 on 2025/4/9.
//

import Foundation
import Combine

class UserPlanService {
    static let shared = UserPlanService()
    
    // API端点
    private let baseURL = "https://fsai.pickgoodspro.com"
    private let createPlanEndpoint = "/ns/app/user-plans" // API端点
    private let latestPlanEndpoint = "/ns/app/user-plans/user/latest"
    private let weeklyWeightEndpoint = "/ns/app/user-plans/user/weeks"
    
    // 最大重试次数
    private let maxRetryCount = 3
    // 重试延迟（秒）
    private let retryDelay: TimeInterval = 2.0
    
    private init() {}
    
    // 创建用户计划的请求结构
    struct UserPlanRequest: Codable {
        let targetWeight: Double
        let targetWeeks: Int
        let age: Int
        let height: Double
        let currentWeight: Double
        let gender: String
        
        // 添加自定义编码器来控制数值精度
        func encode(to encoder: Encoder) throws {
            var container = encoder.container(keyedBy: CodingKeys.self)
            // 保留两位小数
            try container.encode(round(targetWeight * 100) / 100, forKey: .targetWeight)
            try container.encode(targetWeeks, forKey: .targetWeeks)
            try container.encode(age, forKey: .age)
            try container.encode(round(height * 100) / 100, forKey: .height)
            try container.encode(round(currentWeight * 100) / 100, forKey: .currentWeight)
            try container.encode(gender, forKey: .gender)
        }
    }
    
    // 服务器响应结构
    struct UserPlanResponse: Codable {
        let createdTime: String?
        let startDate: String?
        let endDate: String?
        let targetWeight: Double
        let targetWeeks: Int
        let currentWeek: Int?
        let age: Int?  // 改回Int类型，因为API返回的是数字
        let height: Double?
        let startWeight: Double?
        let currentWeight: Double?
        let gender: String?
    }
    
    struct APIResponse: Codable {
        let code: Int
        let message: String
        let data: UserPlanResponse?
        let timestamp: Int64?
    }
    
    // 周平均体重数据结构
    struct WeeklyWeightResponse: Codable, Identifiable {
        let id: UUID? = UUID() // 为每个对象生成一个新的UUID
        let weekNumber: String
        let averageWeight: Double
        
        // 自定义编码和解码键
        enum CodingKeys: String, CodingKey {
            case weekNumber
            case averageWeight
        }
    }
    
    struct WeeklyWeightListResponse: Codable {
        let code: Int
        let message: String
        let data: [WeeklyWeightResponse]?
        let timestamp: Int64?
    }
    
    // 创建用户计划
    func createUserPlan(userData: UserData, 
                       targetWeight: Double, 
                       targetWeeks: Int, 
                       age: Int, 
                       height: Double, 
                       currentWeight: Double, 
                       gender: String,
                       completion: @escaping (Result<UserPlanResponse, Error>) -> Void) {
        
        // 检查是否有accessToken
        guard !userData.accessToken.isEmpty else {
            print("❌ [CreatePlan] 创建计划失败: 用户未登录")
            completion(.failure(NSError(domain: "UserPlanService", code: 401, userInfo: [NSLocalizedDescriptionKey: "User not logged in, unable to create plan"])))
            return
        }
        
        // 转换单位 - 如果用户使用英制单位，需要转换为公制单位
        let convertedTargetWeight = userData.weightUnit == "lbs" ? targetWeight / 2.205 : targetWeight
        let convertedCurrentWeight = userData.weightUnit == "lbs" ? currentWeight / 2.205 : currentWeight
        let convertedHeight = userData.heightUnit == "ft" ? convertFeetToCm(height) : height
        
        // 创建请求对象
        let userPlanRequest = UserPlanRequest(
            targetWeight: convertedTargetWeight,
            targetWeeks: targetWeeks,
            age: age,
            height: convertedHeight,
            currentWeight: convertedCurrentWeight,
            gender: mapGender(gender)
        )
        
        // 创建URL
        guard let url = URL(string: baseURL + createPlanEndpoint) else {
            completion(.failure(NSError(domain: "UserPlanService", code: 400, userInfo: [NSLocalizedDescriptionKey: "无效的URL"])))
            return
        }
        
        // 执行带重试的请求
        executeWithRetry(request: userPlanRequest, url: url, userData: userData, retryCount: 0, completion: completion)
    }
    
    // 更新用户计划
    func updateUserPlan(userData: UserData,
                       targetWeight: Double,
                       targetWeeks: Int,
                       age: Int,
                       height: Double,
                       currentWeight: Double,
                       gender: String,
                       completion: @escaping (Result<Bool, Error>) -> Void) {
        // 检查是否有accessToken
        guard !userData.accessToken.isEmpty else {
            print("❌ [UpdatePlan] 更新计划失败: 用户未登录")
            completion(.failure(NSError(domain: "UserPlanService", code: 401, userInfo: [NSLocalizedDescriptionKey: "User not logged in, unable to update plan"])))
            return
        }
        
        print("🔍 [UpdatePlan] 开始更新计划 - 详细参数:")
        print("- 目标体重: \(targetWeight) \(userData.weightUnit)")
        print("- 目标周数: \(targetWeeks) 周")
        print("- 年龄: \(age) 岁")
        print("- 身高: \(height) \(userData.heightUnit)")
        print("- 当前体重: \(currentWeight) \(userData.weightUnit)")
        print("- 性别: \(gender)")
        
        // 转换单位 - 如果用户使用英制单位，需要转换为公制单位
        let convertedTargetWeight = userData.weightUnit == "lbs" ? targetWeight / 2.205 : targetWeight
        let convertedCurrentWeight = userData.weightUnit == "lbs" ? currentWeight / 2.205 : currentWeight
        let convertedHeight = userData.heightUnit == "ft" ? convertFeetToCm(height) : height
        
        print("📊 [UpdatePlan] 单位转换结果:")
        print("- 目标体重(kg): \(convertedTargetWeight)")
        print("- 当前体重(kg): \(convertedCurrentWeight)")
        print("- 身高(cm): \(convertedHeight)")
        
        // 性别映射
        let mappedGender = mapGender(gender)
        print("- 映射后性别: \(mappedGender)")
        
        // 创建请求对象
        let updateRequest: [String: Any] = [
            "targetWeight": round(convertedTargetWeight * 100) / 100,
            "targetWeeks": targetWeeks,
            "age": age,
            "height": round(convertedHeight * 100) / 100,
            "currentWeight": round(convertedCurrentWeight * 100) / 100,
            "gender": mappedGender
        ]
        
        // 创建URL - 使用与创建计划相同的API端点，只是请求方法不同
        guard let url = URL(string: baseURL + createPlanEndpoint) else {
            print("❌ [UpdatePlan] 更新计划失败: 无效的URL")
            completion(.failure(NSError(domain: "UserPlanService", code: 400, userInfo: [NSLocalizedDescriptionKey: "无效的URL"])))
            return
        }
        
        print("🌐 [UpdatePlan] 发送更新计划请求 [PUT] \(baseURL + createPlanEndpoint)")
        
        // 创建请求
        var urlRequest = URLRequest(url: url)
        urlRequest.httpMethod = "PUT" // 使用PUT方法区分创建和更新操作
        urlRequest.addValue("application/json", forHTTPHeaderField: "Content-Type")
        urlRequest.addValue("Bearer \(userData.accessToken)", forHTTPHeaderField: "Authorization")
        
        // 编码请求体
        do {
            let jsonData = try JSONSerialization.data(withJSONObject: updateRequest)
            urlRequest.httpBody = jsonData
            print("📦 [UpdatePlan] 请求体JSON: \(String(data: jsonData, encoding: .utf8) ?? "")")
        } catch {
            print("❌ [UpdatePlan] 序列化请求体失败: \(error.localizedDescription)")
            completion(.failure(error))
            return
        }
        
        // 发送请求
        let task = URLSession.shared.dataTask(with: urlRequest) { data, response, error in
            // 处理网络错误
            if let error = error {
                print("❌ [UpdatePlan] 网络错误: \(error.localizedDescription)")
                completion(.failure(error))
                return
            }
            
            // 检查HTTP响应
            guard let httpResponse = response as? HTTPURLResponse else {
                print("❌ [UpdatePlan] 无效的HTTP响应")
                completion(.failure(NSError(domain: "UserPlanService", code: 0, userInfo: [NSLocalizedDescriptionKey: "无效的HTTP响应"])))
                return
            }
            
            // 打印HTTP状态码
            print("🔢 [UpdatePlan] HTTP状态码: \(httpResponse.statusCode)")
            
            // 处理HTTP错误
            if !(200...299).contains(httpResponse.statusCode) {
                var errorMessage = "Server returned error status code: \(httpResponse.statusCode)"
                
                if let data = data, let responseString = String(data: data, encoding: .utf8) {
                    print("❌ [UpdatePlan] 错误响应: \(responseString)")
                    errorMessage += "\n响应: \(responseString)"
                }
                
                print("❌ [UpdatePlan] 请求失败: \(errorMessage)")
                completion(.failure(NSError(domain: "UserPlanService", code: httpResponse.statusCode, userInfo: [NSLocalizedDescriptionKey: errorMessage])))
                return
            }
            
            // 检查响应数据
            guard let data = data else {
                print("❌ [UpdatePlan] 服务器没有返回数据")
                completion(.failure(NSError(domain: "UserPlanService", code: 0, userInfo: [NSLocalizedDescriptionKey: "服务器没有返回数据"])))
                return
            }
            
            // 打印响应数据
            if let responseString = String(data: data, encoding: .utf8) {
                print("📊 [UpdatePlan] 响应: \(responseString)")
            }
            
            // 解析响应数据
            do {
                let apiResponse = try JSONDecoder().decode(APIResponse.self, from: data)
                
                if apiResponse.code == 0 {
                    // 更新本地用户数据
                    DispatchQueue.main.async {
                        print("✅ [UpdatePlan] 成功更新用户计划")
                        print("📝 [UpdatePlan] 正在更新本地用户数据...")
                        
                        // 更新用户数据
                        userData.goalWeight = convertedTargetWeight
                        userData.currentManagementWeight = convertedCurrentWeight
                        
                        // 设置目标日期
                        let targetDays = targetWeeks * 7
                        if let date = Calendar.current.date(byAdding: .day, value: targetDays, to: Date()) {
                            userData.goalDate = date
                        }
                        
                        // 更新其他用户数据
                        userData.height = convertedHeight
                        userData.age = age
                        userData.gender = gender
                        
                        // 如果是第一次创建计划，设置开始日期和初始体重
                        if userData.goalStartDate == nil {
                            userData.goalStartDate = Date()
                            userData.initialWeight = convertedCurrentWeight
                            userData.startWeight = convertedCurrentWeight
                        }
                        
                        // 设置体重目标标志
                        userData.hasPlan = true
                        
                        // 更新总周数
                        userData.goalTimelineWeeks = targetWeeks
                        
                        // 保存更新后的设置
                        userData.saveSettings()
                        
                        print("💾 [UpdatePlan] 本地用户数据更新完成")
                        print("- 目标体重: \(userData.goalWeight)kg")
                        print("- 当前体重: \(userData.currentManagementWeight)kg")
                        print("- 目标周数: \(userData.goalTimelineWeeks)周")
                        print("- 身高: \(userData.height)cm")
                        print("- 年龄: \(userData.age)岁")
                        print("- 性别: \(userData.gender)")
                        
                        // 返回成功结果
                        completion(.success(true))
                    }
                } else {
                    let errorMessage = apiResponse.message.isEmpty ? "Failed to update plan" : apiResponse.message
                    print("❌ [UpdatePlan] API错误: \(errorMessage) (代码: \(apiResponse.code))")
                    completion(.failure(NSError(domain: "UserPlanService", code: apiResponse.code, userInfo: [NSLocalizedDescriptionKey: errorMessage])))
                }
            } catch {
                print("❌ [UpdatePlan] 解析响应失败: \(error)")
                completion(.failure(error))
            }
        }
        
        task.resume()
        print("🚀 [UpdatePlan] 请求已发送，正在等待响应...")
    }
    
    // 带重试机制的请求执行
    private func executeWithRetry(request: UserPlanRequest,
                                url: URL,
                                userData: UserData,
                                retryCount: Int,
                                completion: @escaping (Result<UserPlanResponse, Error>) -> Void) {
        
        // 创建请求
        var urlRequest = URLRequest(url: url)
        urlRequest.httpMethod = "POST"
        urlRequest.addValue("application/json", forHTTPHeaderField: "Content-Type")
        urlRequest.addValue("Bearer \(userData.accessToken)", forHTTPHeaderField: "Authorization")
        
        // 编码请求体
        do {
            let jsonData = try JSONEncoder().encode(request)
            urlRequest.httpBody = jsonData
            print("发送创建计划请求: \(String(data: jsonData, encoding: .utf8) ?? "")")
        } catch {
            completion(.failure(error))
            return
        }
        
        // 发送请求
        let task = URLSession.shared.dataTask(with: urlRequest) { data, response, error in
            
            // 处理网络错误
            if let error = error {
                self.handleRequestError(error: error,
                                     request: request,
                                     url: url,
                                     userData: userData,
                                     retryCount: retryCount,
                                     completion: completion)
                return
            }
            
            // 检查HTTP响应
            guard let httpResponse = response as? HTTPURLResponse else {
                completion(.failure(NSError(domain: "UserPlanService", code: 0, userInfo: [NSLocalizedDescriptionKey: "无效的HTTP响应"])))
                return
            }
            
            // 处理HTTP错误
            if !(200...299).contains(httpResponse.statusCode) {
                self.handleHttpError(statusCode: httpResponse.statusCode,
                                   request: request,
                                   url: url,
                                   userData: userData,
                                   retryCount: retryCount,
                                   completion: completion)
                return
            }
            
            // 检查响应数据
            guard let data = data else {
                completion(.failure(NSError(domain: "UserPlanService", code: 0, userInfo: [NSLocalizedDescriptionKey: "服务器没有返回数据"])))
                return
            }
            
            // 解析响应数据
            do {
                let apiResponse = try JSONDecoder().decode(APIResponse.self, from: data)
                
                // 检查API响应码
                if apiResponse.code == 0, let planResponse = apiResponse.data {
                    // 成功响应，更新本地数据
                    DispatchQueue.main.async {
                        self.updateUserDataWithPlanResponse(userData: userData, planResponse: planResponse)
                        completion(.success(planResponse))
                    }
                } else {
                    // API错误，尝试重试
                    let error = NSError(domain: "UserPlanService",
                                      code: apiResponse.code,
                                      userInfo: [NSLocalizedDescriptionKey: "服务器返回错误: \(apiResponse.message)"])
                    self.handleRequestError(error: error,
                                         request: request,
                                         url: url,
                                         userData: userData,
                                         retryCount: retryCount,
                                         completion: completion)
                }
            } catch {
                // 解析错误，使用本地备份
                print("解析响应失败: \(error)")
                print("响应数据: \(String(data: data, encoding: .utf8) ?? "无法解码响应数据")")
                self.handleLocalBackup(userData: userData,
                                     request: request,
                                     error: error,
                                     completion: completion)
            }
        }
        
        task.resume()
    }
    
    // 处理请求错误
    private func handleRequestError(error: Error,
                                  request: UserPlanRequest,
                                  url: URL,
                                  userData: UserData,
                                  retryCount: Int,
                                  completion: @escaping (Result<UserPlanResponse, Error>) -> Void) {
        if retryCount < maxRetryCount {
            // 延迟后重试
            DispatchQueue.main.asyncAfter(deadline: .now() + retryDelay) {
                print("重试创建计划请求 (第\(retryCount + 1)次)")
                self.executeWithRetry(request: request,
                                    url: url,
                                    userData: userData,
                                    retryCount: retryCount + 1,
                                    completion: completion)
            }
        } else {
            // 达到最大重试次数，使用本地备份
            print("达到最大重试次数，使用本地备份")
            self.handleLocalBackup(userData: userData,
                                 request: request,
                                 error: error,
                                 completion: completion)
        }
    }
    
    // 处理HTTP错误
    private func handleHttpError(statusCode: Int,
                               request: UserPlanRequest,
                               url: URL,
                               userData: UserData,
                               retryCount: Int,
                               completion: @escaping (Result<UserPlanResponse, Error>) -> Void) {
        // 对于某些HTTP错误码，我们可能想立即使用本地备份
        switch statusCode {
        case 401: // 未授权
            completion(.failure(NSError(domain: "UserPlanService",
                                     code: statusCode,
                                     userInfo: [NSLocalizedDescriptionKey: "User not logged in, unable to get plan"])))
        case 403: // 禁止访问
            completion(.failure(NSError(domain: "UserPlanService",
                                     code: statusCode,
                                     userInfo: [NSLocalizedDescriptionKey: "没有权限访问该资源"])))
        case 404: // 资源不存在
            completion(.failure(NSError(domain: "UserPlanService",
                                     code: statusCode,
                                     userInfo: [NSLocalizedDescriptionKey: "请求的资源不存在"])))
        default:
            // 其他错误码尝试重试
            if retryCount < maxRetryCount {
                DispatchQueue.main.asyncAfter(deadline: .now() + retryDelay) {
                    print("HTTP错误(\(statusCode))，重试创建计划请求 (第\(retryCount + 1)次)")
                    self.executeWithRetry(request: request,
                                        url: url,
                                        userData: userData,
                                        retryCount: retryCount + 1,
                                        completion: completion)
                }
            } else {
                // 达到最大重试次数，使用本地备份
                print("达到最大重试次数，使用本地备份")
                self.handleLocalBackup(userData: userData,
                                     request: request,
                                     error: NSError(domain: "UserPlanService",
                                                  code: statusCode,
                                                  userInfo: [NSLocalizedDescriptionKey: "HTTP错误: \(statusCode)"]),
                                     completion: completion)
            }
        }
    }
    
    // 处理本地备份
    private func handleLocalBackup(userData: UserData,
                                 request: UserPlanRequest,
                                 error: Error,
                                 completion: @escaping (Result<UserPlanResponse, Error>) -> Void) {
        // 创建本地备份响应
        let backupResponse = UserPlanResponse(
            createdTime: ISO8601DateFormatter().string(from: Date()),
            startDate: ISO8601DateFormatter().string(from: Date()),
            endDate: nil,
            targetWeight: request.targetWeight,
            targetWeeks: request.targetWeeks,
            currentWeek: 1,
            age: request.age,
            height: request.height,
            startWeight: request.currentWeight,
            currentWeight: request.currentWeight,
            gender: request.gender
        )
        
        // 更新本地数据
        DispatchQueue.main.async {
            print("计划创建失败，使用本地备份: \(error.localizedDescription)")
            self.updateUserDataWithPlanResponse(userData: userData, planResponse: backupResponse)
            completion(.success(backupResponse))
        }
    }
    
    // 单位转换工具方法 - 将英尺转换为厘米
    private func convertFeetToCm(_ feet: Double) -> Double {
        // 1英尺 = 30.48厘米
        let centimeters = feet * 30.48
        print("📏 单位转换: \(feet)英尺 → \(centimeters)厘米")
        return centimeters
    }
    
    // 性别映射 - 将UI中使用的性别名转换为后端使用的格式
    private func mapGender(_ gender: String) -> String {
        switch gender.lowercased() {
        case "male":
            return "BOY"
        case "female":
            return "GIRL"
        case "other":
            return "NONE"  // 将"Other"映射为后端接受的"NONE"
        default:
            return "NONE"  // 默认情况下也使用"NONE"
        }
    }
    
    // 使用计划响应更新用户数据
    private func updateUserDataWithPlanResponse(userData: UserData, planResponse: UserPlanResponse) {
        print("使用API返回的计划数据更新本地用户数据")
        
        // 设置目标体重
        userData.goalWeight = planResponse.targetWeight
        
        // 设置初始体重和当前体重
        if let startWeight = planResponse.startWeight {
            userData.startWeight = startWeight
        }
        
        if let currentWeight = planResponse.currentWeight {
            userData.currentManagementWeight = currentWeight
        }
        
        // 处理创建时间
        if let createdTimeStr = planResponse.createdTime {
            var createdTime: Date?
            
            // 首先尝试标准ISO8601格式
            let iso8601Formatter = ISO8601DateFormatter()
            createdTime = iso8601Formatter.date(from: createdTimeStr)
            
            // 如果标准格式失败，尝试自定义格式（API返回的格式）
            if createdTime == nil {
                let customFormatter = DateFormatter()
                customFormatter.dateFormat = "yyyy-MM-dd'T'HH:mm:ss"
                customFormatter.timeZone = TimeZone.current // 使用当前时区
                createdTime = customFormatter.date(from: createdTimeStr)
            }
            
            // 如果还是失败，尝试其他可能的格式
            if createdTime == nil {
                let altFormatter = DateFormatter()
                altFormatter.dateFormat = "yyyy-MM-dd'T'HH:mm:ss.SSS"
                altFormatter.timeZone = TimeZone.current
                createdTime = altFormatter.date(from: createdTimeStr)
            }
            
            if let parsedTime = createdTime {
                userData.planCreatedTime = parsedTime
                print("📅 [UpdateUserData] 设置计划创建时间: \(parsedTime) (来源: \(createdTimeStr))")
            } else {
                print("⚠️ [UpdateUserData] 无法解析创建时间字符串: \(createdTimeStr)")
            }
        } else {
            print("⚠️ [UpdateUserData] API响应中没有createdTime字段")
        }
        
        // 处理开始日期和结束日期
        if let startDateStr = planResponse.startDate {
            let formatter = ISO8601DateFormatter()
            userData.goalStartDate = formatter.date(from: startDateStr)
        }
        
        if let endDateStr = planResponse.endDate {
            let formatter = ISO8601DateFormatter()
            if let endDate = formatter.date(from: endDateStr) {
                userData.goalDate = endDate
            }
        } else {
            // 如果没有结束日期，根据开始日期和目标周数计算
        let targetDays = planResponse.targetWeeks * 7
            if let startDate = userData.goalStartDate {
                if let endDate = Calendar.current.date(byAdding: .day, value: targetDays, to: startDate) {
                    userData.goalDate = endDate
                }
            } else if let date = Calendar.current.date(byAdding: .day, value: targetDays, to: Date()) {
            userData.goalDate = date
            }
        }
        
        // 更新总周数
        userData.goalTimelineWeeks = planResponse.targetWeeks
        
        // 如果有年龄字段，更新年龄
        if let age = planResponse.age {
            userData.age = age
        }
        
        // 设置计划标志为true - API返回了计划数据，所以用户肯定有计划
        userData.hasPlan = true
        
        // 如果没有设置开始日期，设置为当前日期
        if userData.goalStartDate == nil {
            userData.goalStartDate = Date()
        }
        
        // 保存更新后的设置
        userData.saveSettings()
        
        print("💾 已将API返回的计划数据保存到本地 - 目标体重: \(userData.goalWeight)kg, 目标周数: \(planResponse.targetWeeks)周, 当前周数: \(planResponse.currentWeek ?? 0), hasPlan=true")
        
        // 如果有startDate和endDate，打印调试信息
        if let startDate = planResponse.startDate, let endDate = planResponse.endDate {
            print("📅 计划日期 - 开始: \(startDate), 结束: \(endDate)")
        }
    }
    
    // 检查用户是否有计划
    func checkUserHasPlan(userData: UserData, completion: @escaping (Bool) -> Void) {
        print("🔍 开始检查用户是否有计划 - 用户ID: \(userData.id)")
        
        // 检查用户是否登录
        guard !userData.accessToken.isEmpty else {
            print("⚠️ 用户未登录，无法从API获取计划状态")
            DispatchQueue.main.async {
                // 如果用户未登录，确保hasPlan标志为false
                userData.hasPlan = false
                userData.saveSettings()
                print("💾 已将用户计划状态保存到本地 (hasPlan=false)")
            }
            completion(false)
            return
        }
        
        print("🌐 从API获取用户最新计划...")
        // 从API获取最新计划
        getLatestPlan(userData: userData) { result in
            switch result {
            case .success(let planResponse):
                print("✅ 成功获取用户计划 - 目标体重: \(planResponse.targetWeight)kg, 目标周数: \(planResponse.targetWeeks)周")
                if let createdTime = planResponse.createdTime {
                    print("📅 [CheckUserHasPlan] 计划创建时间: \(createdTime)")
                }
                DispatchQueue.main.async {
                    // 使用完整的计划数据更新用户信息（包括planCreatedTime）
                    self.updateUserDataWithPlanResponse(userData: userData, planResponse: planResponse)
                    print("💾 已将完整的计划数据保存到本地，包括创建时间")
                }
                completion(true)
            case .failure(let error):
                // 尝试解析错误，检查是404错误（表示没有计划）
                let nsError = error as NSError
                if nsError.code == 404 {
                    print("⚠️ API返回404，确认用户无计划")
                    DispatchQueue.main.async {
                        userData.hasPlan = false
                        userData.saveSettings()
                        print("💾 已将用户计划状态保存到本地 (hasPlan=false)")
                    }
                    completion(false)
                } else {
                    print("❌ 获取用户计划失败: \(nsError.code) - \(error.localizedDescription)")
                    // 即使是其他错误，也将hasPlan设置为false，完全依赖API结果
                    DispatchQueue.main.async {
                        userData.hasPlan = false
                        userData.saveSettings()
                        print("💾 由于API错误，将用户计划状态设置为无计划 (hasPlan=false)")
                    }
                    completion(false)
                }
            }
        }
    }
    
    // 辅助方法: 判断是否应该使用更新接口
    func shouldUseUpdateEndpoint(userData: UserData) -> Bool {
        // 首先尝试从API获取计划状态
        print("🔍 检查是否应使用更新接口...")
        
        // 此处不直接依赖userData.hasPlan，而是要求调用者先调用checkUserHasPlan
        // 确保hasPlan状态是最新的
        let hasExistingPlan = userData.hasPlan
        
        if hasExistingPlan {
            print("🔄 API确认用户有计划，将使用更新接口")
            return true
        }
        
        // 用户没有计划，使用创建接口
        print("🆕 API确认用户无计划，将使用创建接口")
        return false
    }
    
    // 创建或更新用户计划 - 根据条件选择合适的接口
    func createOrUpdateUserPlan(userData: UserData,
                               targetWeight: Double,
                               targetWeeks: Int,
                               age: Int,
                               height: Double,
                               currentWeight: Double,
                               gender: String,
                               completion: @escaping (Result<Any, Error>) -> Void) {
        
        print("📊 开始创建或更新用户计划...")
        print("目标数据 - 目标体重: \(targetWeight)\(userData.weightUnit), 目标周数: \(targetWeeks)周, 年龄: \(age)岁, 身高: \(height)\(userData.heightUnit), 当前体重: \(currentWeight)\(userData.weightUnit), 性别: \(gender)")
        
        // 先检查用户是否有计划，完全依赖API结果
        checkUserHasPlan(userData: userData) { hasPlan in
            // 根据API返回的计划状态决定使用哪个接口
            if hasPlan {
                print("📝 用户ID: \(userData.id) - API确认用户有计划，使用更新接口")
                // 使用更新接口
                self.updateUserPlan(userData: userData,
                              targetWeight: targetWeight,
                              targetWeeks: targetWeeks,
                              age: age,
                              height: height,
                              currentWeight: currentWeight,
                              gender: gender) { result in
                    switch result {
                    case .success(let success):
                        print("✅ 计划更新成功")
                        completion(.success(success))
                    case .failure(let error):
                        print("❌ 计划更新失败: \(error.localizedDescription)")
                        completion(.failure(error))
                    }
                }
            } else {
                print("📝 用户ID: \(userData.id) - API确认用户无计划，使用创建接口")
                // 使用创建接口
                self.createUserPlan(userData: userData,
                              targetWeight: targetWeight,
                              targetWeeks: targetWeeks,
                              age: age,
                              height: height,
                              currentWeight: currentWeight,
                              gender: gender) { result in
                    switch result {
                    case .success(let planResponse):
                        print("✅ 计划创建成功")
                        completion(.success(planResponse))
                    case .failure(let error):
                        print("❌ 计划创建失败: \(error.localizedDescription)")
                        completion(.failure(error))
                    }
                }
            }
        }
    }
    
    // 获取最新计划
    func getLatestPlan(userData: UserData, completion: @escaping (Result<UserPlanResponse, Error>) -> Void) {
        // 检查是否有accessToken
        guard !userData.accessToken.isEmpty else {
            print("❌ 获取最新计划失败: 用户未登录，无法获取计划")
            completion(.failure(NSError(domain: "UserPlanService", code: 401, userInfo: [NSLocalizedDescriptionKey: "User not logged in, unable to get plan"])))
            return
        }
        
        // 创建URL
        guard let url = URL(string: baseURL + latestPlanEndpoint) else {
            print("❌ 获取最新计划失败: 无效的URL")
            completion(.failure(NSError(domain: "UserPlanService", code: 400, userInfo: [NSLocalizedDescriptionKey: "无效的URL"])))
            return
        }
        
        print("🌐 发送获取最新计划请求 [GET] \(baseURL + latestPlanEndpoint)")
        
        // 创建请求
        var urlRequest = URLRequest(url: url)
        urlRequest.httpMethod = "GET"
        urlRequest.addValue("Bearer \(userData.accessToken)", forHTTPHeaderField: "Authorization")
        
        // 发送请求
        let task = URLSession.shared.dataTask(with: urlRequest) { data, response, error in
            // 处理网络错误
            if let error = error {
                print("❌ 获取最新计划网络错误: \(error.localizedDescription)")
                completion(.failure(error))
                return
            }
            
            // 检查HTTP响应
            guard let httpResponse = response as? HTTPURLResponse else {
                print("❌ 获取最新计划失败: 无效的HTTP响应")
                completion(.failure(NSError(domain: "UserPlanService", code: 0, userInfo: [NSLocalizedDescriptionKey: "无效的HTTP响应"])))
                return
            }
            
            // 打印HTTP状态码
            print("🔢 获取最新计划HTTP状态码: \(httpResponse.statusCode)")
            
            // 处理HTTP错误
            if !(200...299).contains(httpResponse.statusCode) {
                // 特别处理404错误，表示用户没有计划
                if httpResponse.statusCode == 404 {
                    print("ℹ️ 服务器返回404，用户没有计划")
                    // 使用一个特定的错误码来表示"无计划"状态
                    let noUserPlanError = NSError(domain: "UserPlanService", 
                                               code: 404, 
                                               userInfo: [NSLocalizedDescriptionKey: "用户没有计划"])
                    completion(.failure(noUserPlanError))
                    return
                }
                
                if let data = data, let responseString = String(data: data, encoding: .utf8) {
                    print("❌ 获取最新计划错误响应: \(responseString)")
                }
                
                print("❌ 获取最新计划失败: 服务器返回错误状态码: \(httpResponse.statusCode)")
                completion(.failure(NSError(domain: "UserPlanService", code: httpResponse.statusCode, userInfo: [NSLocalizedDescriptionKey: "服务器返回错误状态码: \(httpResponse.statusCode)"])))
                return
            }
            
            // 检查响应数据
            guard let data = data else {
                print("❌ 获取最新计划失败: 服务器没有返回数据")
                completion(.failure(NSError(domain: "UserPlanService", code: 0, userInfo: [NSLocalizedDescriptionKey: "服务器没有返回数据"])))
                return
            }
            
            // 打印响应数据
            if let responseString = String(data: data, encoding: .utf8) {
                print("📊 获取最新计划响应: \(responseString)")
            }
            
            // 解析响应数据
            do {
                let apiResponse = try JSONDecoder().decode(APIResponse.self, from: data)
                
                if apiResponse.code == 0, let planResponse = apiResponse.data {
                    // 成功获取最新计划
                    print("✅ 成功获取最新计划 - 目标体重: \(planResponse.targetWeight)kg, 目标周数: \(planResponse.targetWeeks)周")
                    DispatchQueue.main.async {
                        // 更新本地数据
                        self.updateUserDataWithPlanResponse(userData: userData, planResponse: planResponse)
                        completion(.success(planResponse))
                    }
                } else {
                    let errorMessage = apiResponse.message.isEmpty ? "Failed to update plan" : apiResponse.message
                    print("❌ 获取最新计划API错误: \(errorMessage)")
                    completion(.failure(NSError(domain: "UserPlanService", code: apiResponse.code, userInfo: [NSLocalizedDescriptionKey: errorMessage])))
                }
            } catch {
                print("❌ 解析最新计划响应失败: \(error)")
                completion(.failure(error))
            }
        }
        
        task.resume()
    }
    
    // 获取周平均体重数据
    func getWeeklyWeightData(userData: UserData, completion: @escaping (Result<[WeeklyWeightResponse], Error>) -> Void) {
        // 检查是否有accessToken
        guard !userData.accessToken.isEmpty else {
            completion(.failure(NSError(domain: "UserPlanService", code: 401, userInfo: [NSLocalizedDescriptionKey: "用户未登录，无法获取周平均体重数据"])))
            return
        }
        
        // 创建URL
        guard let url = URL(string: baseURL + weeklyWeightEndpoint) else {
            completion(.failure(NSError(domain: "UserPlanService", code: 400, userInfo: [NSLocalizedDescriptionKey: "无效的URL"])))
            return
        }
        
        // 创建请求
        var urlRequest = URLRequest(url: url)
        urlRequest.httpMethod = "GET"
        urlRequest.addValue("Bearer \(userData.accessToken)", forHTTPHeaderField: "Authorization")
        
        // 发送请求
        let task = URLSession.shared.dataTask(with: urlRequest) { data, response, error in
            // 处理网络错误
            if let error = error {
                print("获取周平均体重数据网络错误: \(error.localizedDescription)")
                completion(.failure(error))
                return
            }
            
            // 检查HTTP响应
            guard let httpResponse = response as? HTTPURLResponse else {
                completion(.failure(NSError(domain: "UserPlanService", code: 0, userInfo: [NSLocalizedDescriptionKey: "无效的HTTP响应"])))
                return
            }
            
            // 打印HTTP状态码
            print("获取周平均体重数据HTTP状态码: \(httpResponse.statusCode)")
            
            // 处理HTTP错误
            if !(200...299).contains(httpResponse.statusCode) {
                if let data = data, let responseString = String(data: data, encoding: .utf8) {
                    print("获取周平均体重数据错误响应: \(responseString)")
                }
                
                completion(.failure(NSError(domain: "UserPlanService", code: httpResponse.statusCode, userInfo: [NSLocalizedDescriptionKey: "服务器返回错误状态码: \(httpResponse.statusCode)"])))
                return
            }
            
            // 检查响应数据是否存在
            guard let data = data, !data.isEmpty else {
                print("获取周平均体重数据响应为空")
                // 空数据视为成功但没有内容，返回空数组
                completion(.success([]))
                return
            }
            
            // 打印响应数据
            if let responseString = String(data: data, encoding: .utf8) {
                print("获取周平均体重数据响应: \(responseString)")
                
                // 检查响应字符串是否为有效JSON
                if responseString.trimmingCharacters(in: .whitespacesAndNewlines).isEmpty {
                    print("响应为空字符串，返回空数组")
                    completion(.success([]))
                    return
                }
            }
            
                            // 尝试解析响应数据
            do {
                // 首先尝试解析标准响应格式
                let response = try JSONDecoder().decode(WeeklyWeightListResponse.self, from: data)
                
                if response.code == 0, let weeklyData = response.data, !weeklyData.isEmpty {
                    print("成功获取周平均体重数据: \(weeklyData.count)条记录")
                    // 打印一下返回的数据内容，方便调试
                    for data in weeklyData {
                        print("周数: \(data.weekNumber), 平均体重: \(data.averageWeight)kg")
                    }
                    completion(.success(weeklyData))
                } else if response.code == 0 {
                    // 成功但没有数据
                    print("API返回成功但没有周平均体重数据")
                    completion(.success([]))
                } else {
                    let errorMessage = response.message.isEmpty ? "获取周平均体重数据失败" : response.message
                    completion(.failure(NSError(domain: "UserPlanService", code: response.code, userInfo: [NSLocalizedDescriptionKey: errorMessage])))
                }
            } catch {
                // 如果标准格式解析失败，尝试直接解析数组格式
                print("尝试解析为直接数组格式: \(error.localizedDescription)")
                do {
                    let weeklyData = try JSONDecoder().decode([WeeklyWeightResponse].self, from: data)
                    print("成功解析为直接数组，获取\(weeklyData.count)条记录")
                    completion(.success(weeklyData))
                } catch let arrayError {
                    print("无法解析为数组格式: \(arrayError.localizedDescription)")
                    // 两种格式都解析失败，返回原始错误
                    completion(.failure(error))
                }
            }
        }
        
        task.resume()
    }
}

// 辅助扩展，用于处理跳过状态的通知
extension NotificationCenter {
    static let onboardingSkippedKey = "OnboardingSkipped"
    
    // 发送跳过引导页面的通知
    static func postOnboardingSkipped() {
        UserDefaults.standard.set(true, forKey: onboardingSkippedKey)
        NotificationCenter.default.post(name: Notification.Name("OnboardingSkipped"), object: nil)
    }
    
    // 检查用户是否跳过了引导页面
    static func isOnboardingSkipped() -> Bool {
        return UserDefaults.standard.bool(forKey: onboardingSkippedKey)
    }
    
    // 重置跳过状态
    static func resetOnboardingSkippedStatus() {
        UserDefaults.standard.removeObject(forKey: onboardingSkippedKey)
    }
}
