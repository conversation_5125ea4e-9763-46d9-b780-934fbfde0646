import Foundation
import Security

class KeychainHelper {
    static let shared = KeychainHelper()
    private init() {}

    enum KeychainError: Error {
        case stringToDataConversionError
        case dataToStringConversionError
        case unhandledError(status: OSStatus)
    }

    func save(key: String, data: Data) -> OSStatus {
        let query = [
            kSecClass: kSecClassGenericPassword,
            kSecAttrAccount: key,
            kSecValueData: data,
            kSecAttrAccessible: kSecAttrAccessibleWhenUnlockedThisDeviceOnly // 建议的保护级别
        ] as [String: Any]

        // 先删除已存在的具有相同key的项，以避免重复添加错误
        SecItemDelete(query as CFDictionary)

        return SecItemAdd(query as CFDictionary, nil)
    }

    func load(key: String) -> Data? {
        let query = [
            kSecClass: kSecClassGenericPassword,
            kSecAttrAccount: key,
            kSecReturnData: kCFBooleanTrue!,
            kSecMatchLimit: kSecMatchLimitOne
        ] as [String: Any]

        var dataTypeRef: AnyObject?
        let status: OSStatus = SecItemCopyMatching(query as CFDictionary, &dataTypeRef)

        if status == noErr {
            return dataTypeRef as? Data
        } else {
            // 可以根据需要打印错误状态
            // print("Keychain load error - Status: \(status)")
            return nil
        }
    }

    func delete(key: String) -> OSStatus {
        let query = [
            kSecClass: kSecClassGenericPassword,
            kSecAttrAccount: key
        ] as [String: Any]

        return SecItemDelete(query as CFDictionary)
    }

    // Convenience methods for strings
    func saveString(_ string: String, forKey key: String) throws {
        guard let data = string.data(using: .utf8) else {
            print("❌ Keychain: 无法将字符串转换为Data - Key: \(key)")
            throw KeychainError.stringToDataConversionError
        }
        let status = save(key: key, data: data)
        guard status == errSecSuccess else {
            print("❌ Keychain: 保存字符串失败 - Key: \(key), Status: \(status)")
            throw KeychainError.unhandledError(status: status)
        }
        print("✅ Keychain: 成功保存字符串 - Key: \(key)")
    }

    func loadString(forKey key: String) throws -> String? {
        guard let data = load(key: key) else {
            // print("ℹ️ Keychain: 未找到对应Key的数据 - Key: \(key)")
            return nil // Key不存在不是错误，只是没有值
        }
        guard let string = String(data: data, encoding: .utf8) else {
            print("❌ Keychain: 无法将Data转换为字符串 - Key: \(key)")
            throw KeychainError.dataToStringConversionError
        }
        // print("✅ Keychain: 成功加载字符串 - Key: \(key)")
        return string
    }
    
    func deleteString(forKey key: String) throws {
        let status = delete(key: key)
        guard status == errSecSuccess || status == errSecItemNotFound else {
            print("❌ Keychain: 删除字符串失败 - Key: \(key), Status: \(status)")
            throw KeychainError.unhandledError(status: status)
        }
        if status == errSecSuccess {
            print("✅ Keychain: 成功删除字符串 - Key: \(key)")
        }
    }
} 