import Foundation
import UIKit

/**
 * ImageUploadService - 图片上传服务
 * 
 * 这个服务负责处理用户拍摄或选择的照片上传到服务器的功能，使用multipart/form-data格式
 * 
 * 主要功能:
 * 1. 将UIImage转换为JPEG数据
 * 2. 创建和配置multipart/form-data格式的HTTP请求
 * 3. 添加认证token到请求头
 * 4. 处理服务器响应，解析返回的数据
 * 
 * API接口:
 * - URL: /ns/app/diet-records/upload
 * - 请求方法: POST
 * - 内容类型: multipart/form-data
 * - 认证方式: Bearer token
 * - 参数: file (文件数据)
 */
class ImageUploadService {
    // 单例模式
    static let shared = ImageUploadService()
    
    // 基础URL
    private let baseURL = "https://fsai.pickgoodspro.com"
    
    private init() {}
    
    // 上传图片的方法
    func uploadImage(image: UIImage, authToken: String, completion: @escaping (Result<UploadResponse, Error>) -> Void) {
        // 将图片转换为Data，压缩质量为0.7
        guard let imageData = image.jpegData(compressionQuality: 0.7) else {
            completion(.failure(NSError(domain: "ImageUploadError", code: 0, userInfo: [NSLocalizedDescriptionKey: "无法将图片转换为数据"])))
            return
        }
        
        print("图片转换完成，大小: \(imageData.count) 字节")
        
        // 检查认证令牌是否为空
        if authToken.isEmpty {
            print("警告: 认证令牌为空，API请求可能会失败")
            completion(.failure(NSError(domain: "ImageUploadError", code: 2, userInfo: [NSLocalizedDescriptionKey: "认证令牌为空"])))
            return
        }
        
        // 创建URL
        let pathString = "/ns/app/diet-records/upload"
        let fullUrlString = baseURL + pathString
        
        guard let url = URL(string: fullUrlString) else {
            print("错误: 无效的URL - \(fullUrlString)")
            completion(.failure(NSError(domain: "ImageUploadError", code: 1, userInfo: [NSLocalizedDescriptionKey: "无效的URL"])))
            return
        }
        
        print("准备上传图片到URL: \(url.absoluteString)")
        
        // 创建边界字符串
        let boundary = "Boundary-\(UUID().uuidString)"
        
        // 创建请求
        var request = URLRequest(url: url)
        request.httpMethod = "POST"
        
        // 设置请求头
        request.setValue("multipart/form-data; boundary=\(boundary)", forHTTPHeaderField: "Content-Type")
        request.setValue("Bearer \(authToken)", forHTTPHeaderField: "Authorization")
        
        // 创建请求体
        let httpBody = NSMutableData()
        
        // 添加图片数据
        httpBody.append(convertFileData(fieldName: "file", fileName: "food_image.jpg", mimeType: "image/jpeg", fileData: imageData, using: boundary))
        
        // 添加结束边界
        httpBody.append("--\(boundary)--\r\n".data(using: .utf8)!)
        
        // 设置请求体
        request.httpBody = httpBody as Data
        
        // 创建任务
        let task = URLSession.shared.dataTask(with: request) { data, response, error in
            // 处理HTTP错误
            if let error = error {
                print("上传请求失败: \(error.localizedDescription)")
                DispatchQueue.main.async {
                    completion(.failure(error))
                }
                return
            }
            
            // 检查HTTP响应状态码
            if let httpResponse = response as? HTTPURLResponse {
                print("HTTP状态码: \(httpResponse.statusCode)")
                
                if !(200...299).contains(httpResponse.statusCode) {
                    let errorMessage = "服务器返回错误: HTTP \(httpResponse.statusCode)"
                    print(errorMessage)
                    DispatchQueue.main.async {
                        completion(.failure(NSError(domain: "ImageUploadError", code: httpResponse.statusCode, userInfo: [NSLocalizedDescriptionKey: errorMessage])))
                    }
                    return
                }
            }
            
            // 处理响应数据
            guard let responseData = data else {
                print("没有响应数据")
                DispatchQueue.main.async {
                    completion(.failure(NSError(domain: "ImageUploadError", code: 3, userInfo: [NSLocalizedDescriptionKey: "服务器没有返回数据"])))
                }
                return
            }
            
            // 尝试解析JSON响应
            do {
                // 打印原始响应数据（仅用于调试）
                if let responseString = String(data: responseData, encoding: .utf8) {
                    print("服务器响应: \(responseString)")
                }
                
                // 解析API响应
                let decoder = JSONDecoder()
                let apiResponse = try decoder.decode(APIResponse<UploadResponse>.self, from: responseData)
                
                DispatchQueue.main.async {
                    if let uploadResponse = apiResponse.data {
                        print("图片上传成功，fileId: \(uploadResponse.fileId ?? "未知")")
                        completion(.success(uploadResponse))
                    } else {
                        let message = apiResponse.message ?? "上传失败，无详细信息"
                        print("上传失败: \(message)")
                        completion(.failure(NSError(domain: "ImageUploadError", code: 4, userInfo: [NSLocalizedDescriptionKey: message])))
                    }
                }
            } catch {
                print("JSON解析失败: \(error.localizedDescription)")
                DispatchQueue.main.async {
                    completion(.failure(error))
                }
            }
        }
        
        // 启动任务
        print("开始上传图片...")
        task.resume()
    }
    
    // 照片识别的方法 - 根据上传的图片信息调用识别接口
    func identifyFoodImage(fileLocation: String?, fileId: String?, imageId: String?, authToken: String, completion: @escaping (Result<FoodAnalysisResult, Error>) -> Void) {
        // 创建URL
        let pathString = "/ns/app/diet-records/scan-identify"
        let fullUrlString = baseURL + pathString
        
        guard let url = URL(string: fullUrlString) else {
            print("错误: 无效的URL - \(fullUrlString)")
            completion(.failure(NSError(domain: "FoodIdentifyError", code: 1, userInfo: [NSLocalizedDescriptionKey: "无效的URL"])))
            return
        }
        
        print("准备调用照片识别接口: \(url.absoluteString)")
        
        // 创建请求
        var request = URLRequest(url: url)
        request.httpMethod = "POST"
        
        // 设置请求头
        request.setValue("application/json", forHTTPHeaderField: "Content-Type")
        request.setValue("Bearer \(authToken)", forHTTPHeaderField: "Authorization")
        
        // 创建请求体
        let requestBody: [String: Any?] = [
            "fileLocation": fileLocation,
            "fileId": fileId,
            "imageId": imageId
        ]
        
        // 过滤掉nil值并转换为JSON数据
        let jsonData: Data
        do {
            let filteredBody = requestBody.compactMapValues { $0 }
            jsonData = try JSONSerialization.data(withJSONObject: filteredBody)
            
            // 打印请求体（仅用于调试）
            if let jsonString = String(data: jsonData, encoding: .utf8) {
                print("识别请求体: \(jsonString)")
            }
        } catch {
            print("JSON序列化失败: \(error.localizedDescription)")
            completion(.failure(error))
            return
        }
        
        // 设置请求体
        request.httpBody = jsonData
        
        // 创建任务
        let task = URLSession.shared.dataTask(with: request) { data, response, error in
            // 处理HTTP错误
            if let error = error {
                print("识别请求失败: \(error.localizedDescription)")
                DispatchQueue.main.async {
                    completion(.failure(error))
                }
                return
            }
            
            // 检查HTTP响应状态码
            if let httpResponse = response as? HTTPURLResponse {
                print("HTTP状态码: \(httpResponse.statusCode)")
                
                if !(200...299).contains(httpResponse.statusCode) {
                    let errorMessage = "服务器返回错误: HTTP \(httpResponse.statusCode)"
                    print(errorMessage)
                    DispatchQueue.main.async {
                        completion(.failure(NSError(domain: "FoodIdentifyError", code: httpResponse.statusCode, userInfo: [NSLocalizedDescriptionKey: errorMessage])))
                    }
                    return
                }
            }
            
            // 处理响应数据
            guard let responseData = data else {
                print("没有响应数据")
                DispatchQueue.main.async {
                    completion(.failure(NSError(domain: "FoodIdentifyError", code: 3, userInfo: [NSLocalizedDescriptionKey: "服务器没有返回数据"])))
                }
                return
            }
            
            // 尝试解析JSON响应
            do {
                // 打印原始响应数据（仅用于调试）
                if let responseString = String(data: responseData, encoding: .utf8) {
                    print("识别响应: \(responseString)")
                }
                
                // 先解析外层API响应
                let decoder = JSONDecoder()
                
                // 解析外层JSON
                let outerResponse = try decoder.decode(OuterAPIResponse.self, from: responseData)
                
                // 确认响应成功
                if outerResponse.code == 0 && outerResponse.message == "success" {
                    // 解析内层JSON字符串
                    if let dataString = outerResponse.data, let innerData = dataString.data(using: .utf8) {
                        // 解析内层数据
                        let innerResponse = try decoder.decode(InnerFoodAPIResponse.self, from: innerData)
                        
                        // 从内层响应中提取食物分析数据
                        let foodResponse = innerResponse.response
                        
                        // 检查是否是食物
                        if foodResponse.is_food.lowercased() == "false" || 
                           foodResponse.is_food.lowercased() == "no" ||
                           foodResponse.is_food == "0" {
                            
                            let message = "The image does not appear to contain food"
                            print("识别结果: 非食物 - \(message)")
                            DispatchQueue.main.async {
                                completion(.failure(NSError(domain: "FoodIdentifyError", code: 7, userInfo: [NSLocalizedDescriptionKey: "not food"])))
                            }
                            return
                        }
                        
                        // 检查食物名称是否为空或无效
                        if foodResponse.food_name.isEmpty || 
                           foodResponse.food_name.lowercased().contains("unknown") ||
                           foodResponse.food_name.lowercased().contains("无法识别") ||
                           foodResponse.food_name.lowercased().contains("not identified") {
                            
                            let message = "Unable to identify the food in the image"
                            print("识别结果: 无法识别食物 - \(message)")
                            DispatchQueue.main.async {
                                completion(.failure(NSError(domain: "FoodIdentifyError", code: 8, userInfo: [NSLocalizedDescriptionKey: "no result"])))
                            }
                            return
                        }
                        
                        // 检查营养成分是否为空
                        if foodResponse.nutritional_components.isEmpty {
                            let message = "No nutritional information available"
                            print("识别结果: 无营养信息 - \(message)")
                            DispatchQueue.main.async {
                                completion(.failure(NSError(domain: "FoodIdentifyError", code: 9, userInfo: [NSLocalizedDescriptionKey: "empty nutritional data"])))
                            }
                            return
                        }
                        
                        // 创建FoodAnalysisResult对象
                        let analysisResult = FoodAnalysisResult(
                            foodName: foodResponse.food_name,
                            nutritionScore: foodResponse.food_score,
                            calories: getNutritionValue(components: foodResponse.nutritional_components, name: "Calories"),
                            protein: getNutritionValue(components: foodResponse.nutritional_components, name: "Protein"),
                            carbs: getNutritionValue(components: foodResponse.nutritional_components, name: "Carbohydrates"),
                            fat: getNutritionValue(components: foodResponse.nutritional_components, name: "Fat"),
                            water: getNutritionValue(components: foodResponse.nutritional_components, name: "Water"),
                            fiber: getNutritionValue(components: foodResponse.nutritional_components, name: "Fiber"),
                            sugar: getNutritionValue(components: foodResponse.nutritional_components, name: "Sugar"),
                            caloriePercentage: 30, // 默认值
                            recommendations: foodResponse.health_advice,
                            conversationId: innerResponse.conversation_id
                        )
                
                DispatchQueue.main.async {
                        print("食物识别成功，食物名称: \(analysisResult.foodName)")
                        completion(.success(analysisResult))
                        }
                    } else {
                        let message = "数据字段解析失败"
                        print("识别失败: \(message)")
                        DispatchQueue.main.async {
                            completion(.failure(NSError(domain: "FoodIdentifyError", code: 5, userInfo: [NSLocalizedDescriptionKey: message])))
                        }
                    }
                } else {
                    let message = outerResponse.message
                    print("识别失败: \(message)")
                    DispatchQueue.main.async {
                        completion(.failure(NSError(domain: "FoodIdentifyError", code: 6, userInfo: [NSLocalizedDescriptionKey: message])))
                    }
                }
            } catch {
                print("JSON解析失败: \(error.localizedDescription)")
                DispatchQueue.main.async {
                    completion(.failure(error))
                }
            }
        }
        
        // 启动任务
        print("开始识别食物图片...")
        task.resume()
    }
    
    // 将文件数据转换为multipart格式
    private func convertFileData(fieldName: String, fileName: String, mimeType: String, fileData: Data, using boundary: String) -> Data {
        let data = NSMutableData()
        
        // 添加边界和Content-Disposition
        let boundaryPrefix = "--\(boundary)\r\n"
        data.append(boundaryPrefix.data(using: .utf8)!)
        
        // 添加Content-Disposition头
        let disposition = "Content-Disposition: form-data; name=\"\(fieldName)\"; filename=\"\(fileName)\"\r\n"
        data.append(disposition.data(using: .utf8)!)
        
        // 添加Content-Type头
        let contentType = "Content-Type: \(mimeType)\r\n\r\n"
        data.append(contentType.data(using: .utf8)!)
        
        // 添加文件数据
        data.append(fileData)
        
        // 添加结尾的换行符
        data.append("\r\n".data(using: .utf8)!)
        
        return data as Data
    }
    
    // 添加创建食物记录的方法
    func createDietRecord(conversationId: String, authToken: String, completion: @escaping (Result<Bool, Error>) -> Void) {
        // 创建URL
        let pathString = "/ns/app/diet-records"
        let fullUrlString = baseURL + pathString
        
        guard let url = URL(string: fullUrlString) else {
            print("错误: 无效的URL - \(fullUrlString)")
            completion(.failure(NSError(domain: "DietRecordError", code: 1, userInfo: [NSLocalizedDescriptionKey: "无效的URL"])))
            return
        }
        
        print("准备调用创建食物记录接口: \(url.absoluteString)")
        
        // 创建请求
        var request = URLRequest(url: url)
        request.httpMethod = "POST"
        
        // 设置请求头
        request.setValue("application/json", forHTTPHeaderField: "Content-Type")
        request.setValue("Bearer \(authToken)", forHTTPHeaderField: "Authorization")
        
        // 创建请求体
        let requestBody = DietRecordCreateRequest(conversationId: conversationId)
        
        // 转换为JSON数据
        let jsonData: Data
        do {
            let encoder = JSONEncoder()
            jsonData = try encoder.encode(requestBody)
            
            // 打印请求体（仅用于调试）
            if let jsonString = String(data: jsonData, encoding: .utf8) {
                print("创建食物记录请求体: \(jsonString)")
            }
        } catch {
            print("JSON序列化失败: \(error.localizedDescription)")
            completion(.failure(error))
            return
        }
        
        // 设置请求体
        request.httpBody = jsonData
        
        // 创建任务
        let task = URLSession.shared.dataTask(with: request) { data, response, error in
            // 处理HTTP错误
            if let error = error {
                print("创建食物记录请求失败: \(error.localizedDescription)")
                DispatchQueue.main.async {
                    completion(.failure(error))
                }
                return
            }
            
            // 检查HTTP响应状态码
            if let httpResponse = response as? HTTPURLResponse {
                print("HTTP状态码: \(httpResponse.statusCode)")
                
                if !(200...299).contains(httpResponse.statusCode) {
                    let errorMessage = "服务器返回错误: HTTP \(httpResponse.statusCode)"
                    print(errorMessage)
                    DispatchQueue.main.async {
                        completion(.failure(NSError(domain: "DietRecordError", code: httpResponse.statusCode, userInfo: [NSLocalizedDescriptionKey: errorMessage])))
                    }
                    return
                }
            }
            
            // 处理响应数据
            guard let responseData = data else {
                print("没有响应数据")
                DispatchQueue.main.async {
                    completion(.failure(NSError(domain: "DietRecordError", code: 3, userInfo: [NSLocalizedDescriptionKey: "服务器没有返回数据"])))
                }
                return
            }
            
            // 尝试解析JSON响应
            do {
                // 打印原始响应数据（仅用于调试）
                if let responseString = String(data: responseData, encoding: .utf8) {
                    print("创建食物记录响应: \(responseString)")
                }
                
                // 解析API响应
                let decoder = JSONDecoder()
                let apiResponse = try decoder.decode(APIResponse<Bool>.self, from: responseData)
                
                DispatchQueue.main.async {
                    if let success = apiResponse.data, success {
                        print("成功创建食物记录")
                        completion(.success(true))
                    } else {
                        let message = apiResponse.message ?? "创建食物记录失败，无详细信息"
                        print("创建食物记录失败: \(message)")
                        completion(.failure(NSError(domain: "DietRecordError", code: 4, userInfo: [NSLocalizedDescriptionKey: message])))
                    }
                }
            } catch {
                print("JSON解析失败: \(error.localizedDescription)")
                DispatchQueue.main.async {
                    completion(.failure(error))
                }
            }
        }
        
        // 启动任务
        print("开始创建食物记录...")
        task.resume()
    }
    
    // 获取用户食物记录的方法
    func getFoodRecords(dateStr: String? = nil, authToken: String, completion: @escaping (Result<[DietRecordResponse], Error>) -> Void) {
        // 创建URL
        var pathString = "/ns/app/diet-records/user"
        
        // 如果有日期参数，添加到URL中
        if let dateStr = dateStr, !dateStr.isEmpty {
            pathString += "?dateStr=\(dateStr)"
        }
        
        let fullUrlString = baseURL + pathString
        
        guard let url = URL(string: fullUrlString) else {
            print("错误: 无效的URL - \(fullUrlString)")
            completion(.failure(NSError(domain: "DietRecordError", code: 1, userInfo: [NSLocalizedDescriptionKey: "无效的URL"])))
            return
        }
        
        print("准备调用获取食物记录接口: \(url.absoluteString)")
        
        // 创建请求
        var request = URLRequest(url: url)
        request.httpMethod = "GET"
        
        // 设置请求头
        request.setValue("Bearer \(authToken)", forHTTPHeaderField: "Authorization")
        
        // 创建任务
        let task = URLSession.shared.dataTask(with: request) { data, response, error in
            // 处理HTTP错误
            if let error = error {
                print("获取食物记录请求失败: \(error.localizedDescription)")
                DispatchQueue.main.async {
                    completion(.failure(error))
                }
                return
            }
            
            // 检查HTTP响应状态码
            if let httpResponse = response as? HTTPURLResponse {
                print("HTTP状态码: \(httpResponse.statusCode)")
                
                if !(200...299).contains(httpResponse.statusCode) {
                    let errorMessage = "服务器返回错误: HTTP \(httpResponse.statusCode)"
                    print(errorMessage)
                    DispatchQueue.main.async {
                        completion(.failure(NSError(domain: "DietRecordError", code: httpResponse.statusCode, userInfo: [NSLocalizedDescriptionKey: errorMessage])))
                    }
                    return
                }
            }
            
            // 处理响应数据
            guard let responseData = data else {
                print("没有响应数据")
                DispatchQueue.main.async {
                    completion(.failure(NSError(domain: "DietRecordError", code: 3, userInfo: [NSLocalizedDescriptionKey: "服务器没有返回数据"])))
                }
                return
            }
            
            // 尝试解析JSON响应
            do {
                // 打印原始响应数据（仅用于调试）
                if let responseString = String(data: responseData, encoding: .utf8) {
                    print("获取食物记录响应: \(responseString)")
                }
                
                // 解析API响应
                let decoder = JSONDecoder()
                
                // 设置日期格式化器 - 修复：使用本地时区而不是UTC，确保凌晨时间正确显示
                let dateFormatter = DateFormatter()
                dateFormatter.dateFormat = "yyyy-MM-dd'T'HH:mm:ss.SSSSSS"
                dateFormatter.timeZone = TimeZone.current // 使用用户当前时区
                decoder.dateDecodingStrategy = .formatted(dateFormatter)
                
                let apiResponse = try decoder.decode(APIResponse<[DietRecordResponse]>.self, from: responseData)
                
                DispatchQueue.main.async {
                    if let dietRecords = apiResponse.data {
                        print("成功获取食物记录，数量: \(dietRecords.count)")
                        
                        // 保存食物ID到本地UserDefaults
                        self.saveFoodRecordIds(dietRecords)
                        
                        completion(.success(dietRecords))
                    } else {
                        let message = apiResponse.message ?? "获取食物记录失败，无详细信息"
                        print("获取食物记录失败: \(message)")
                        completion(.failure(NSError(domain: "DietRecordError", code: 4, userInfo: [NSLocalizedDescriptionKey: message])))
                    }
                }
            } catch {
                print("JSON解析失败: \(error.localizedDescription)")
                DispatchQueue.main.async {
                    completion(.failure(error))
                }
            }
        }
        
        // 启动任务
        print("开始获取食物记录...")
        task.resume()
    }
    
    // 保存食物记录ID到本地
    private func saveFoodRecordIds(_ records: [DietRecordResponse]) {
        var foodIds: [Int] = []
        
        for record in records {
            foodIds.append(record.id)
        }
        
        // 获取现有的ID列表
        var existingIds = getSavedFoodRecordIds()
        
        // 合并新的ID
        existingIds.append(contentsOf: foodIds)
        
        // 去重并限制数量（最多保留最近的500个记录ID，进一步减少存储）
        let uniqueIds = Array(Set(existingIds))
        let limitedIds = Array(uniqueIds.suffix(500))
        
        UserDefaults.standard.set(limitedIds, forKey: "savedFoodRecordIds")
        print("已保存\(limitedIds.count)个食物记录ID到本地（限制最多500个）")
    }
    
    // 获取本地保存的食物记录ID
    func getSavedFoodRecordIds() -> [Int] {
        return UserDefaults.standard.array(forKey: "savedFoodRecordIds") as? [Int] ?? []
    }
    
    // 清理过多的食物记录ID，防止UserDefaults数据过大
    func cleanupFoodRecordIds() {
        let existingIds = getSavedFoodRecordIds()
        
        // 如果超过500个，只保留最近的500个
        if existingIds.count > 500 {
            let limitedIds = Array(existingIds.suffix(500))
            UserDefaults.standard.set(limitedIds, forKey: "savedFoodRecordIds")
            print("清理食物记录ID：从\(existingIds.count)个减少到\(limitedIds.count)个")
        }
        
        // 检查UserDefaults的大小（估算）
        let idsData = try? JSONSerialization.data(withJSONObject: existingIds)
        if let dataSize = idsData?.count {
            print("食物记录ID数据大小：\(dataSize) bytes")
            
            // 如果数据过大（超过100KB），进一步减少到200个
            if dataSize > 100 * 1024 {
                let reducedIds = Array(existingIds.suffix(200))
                UserDefaults.standard.set(reducedIds, forKey: "savedFoodRecordIds")
                print("数据过大，进一步减少到\(reducedIds.count)个记录ID")
            }
        }
    }
    
    // 根据ID获取详细食物记录的方法
    func getDetailedFoodRecord(recordId: Int, authToken: String, completion: @escaping (Result<DetailedFoodRecord, Error>) -> Void) {
        // 创建URL
        let pathString = "/ns/app/diet-records/\(recordId)"
        let fullUrlString = baseURL + pathString
        
        guard let url = URL(string: fullUrlString) else {
            print("错误: 无效的URL - \(fullUrlString)")
            completion(.failure(NSError(domain: "DetailedFoodRecordError", code: 1, userInfo: [NSLocalizedDescriptionKey: "无效的URL"])))
            return
        }
        
        print("准备调用获取详细食物记录接口: \(url.absoluteString)")
        
        // 创建请求
        var request = URLRequest(url: url)
        request.httpMethod = "GET"
        
        // 设置请求头
        request.setValue("Bearer \(authToken)", forHTTPHeaderField: "Authorization")
        
        // 创建任务
        let task = URLSession.shared.dataTask(with: request) { data, response, error in
            // 处理HTTP错误
            if let error = error {
                print("获取详细食物记录请求失败: \(error.localizedDescription)")
                DispatchQueue.main.async {
                    completion(.failure(error))
                }
                return
            }
            
            // 检查HTTP响应状态码
            if let httpResponse = response as? HTTPURLResponse {
                print("HTTP状态码: \(httpResponse.statusCode)")
                
                if !(200...299).contains(httpResponse.statusCode) {
                    let errorMessage = "服务器返回错误: HTTP \(httpResponse.statusCode)"
                    print(errorMessage)
                    DispatchQueue.main.async {
                        completion(.failure(NSError(domain: "DetailedFoodRecordError", code: httpResponse.statusCode, userInfo: [NSLocalizedDescriptionKey: errorMessage])))
                    }
                    return
                }
            }
            
            // 处理响应数据
            guard let responseData = data else {
                print("没有响应数据")
                DispatchQueue.main.async {
                    completion(.failure(NSError(domain: "DetailedFoodRecordError", code: 3, userInfo: [NSLocalizedDescriptionKey: "服务器没有返回数据"])))
                }
                return
            }
            
            // 尝试解析JSON响应
            do {
                // 打印原始响应数据（仅用于调试）
                if let responseString = String(data: responseData, encoding: .utf8) {
                    print("获取详细食物记录响应: \(responseString)")
                }
                
                // 解析API响应
                let decoder = JSONDecoder()
                let apiResponse = try decoder.decode(APIResponse<DetailedFoodRecord>.self, from: responseData)
                
                DispatchQueue.main.async {
                    if let detailedRecord = apiResponse.data {
                        print("成功获取详细食物记录，食物名称: \(detailedRecord.foodName)")
                        completion(.success(detailedRecord))
                    } else {
                        let message = apiResponse.message ?? "获取详细食物记录失败，无详细信息"
                        print("获取详细食物记录失败: \(message)")
                        completion(.failure(NSError(domain: "DetailedFoodRecordError", code: 4, userInfo: [NSLocalizedDescriptionKey: message])))
                    }
                }
            } catch {
                print("JSON解析失败: \(error.localizedDescription)")
                DispatchQueue.main.async {
                    completion(.failure(error))
                }
            }
        }
        
        // 启动任务
        print("开始获取详细食物记录...")
        task.resume()
    }
}

// 上传响应模型
struct UploadResponse: Codable {
    let fileLocation: String?
    let fileId: String?
    let imageId: String?
}

// 食物分析结果模型
struct FoodAnalysisResult: Codable {
    // 食物基本信息
    let foodName: String
    let nutritionScore: Int
    
    // 营养素信息
    let calories: Int
    let protein: Int
    let carbs: Int
    let fat: Int
    let water: Int
    let fiber: Int
    let sugar: Int
    
    // 百分比信息
    let caloriePercentage: Int
    
    // 推荐信息
    let recommendations: [String]
    
    // 添加conversationId字段，用于创建食物记录
    let conversationId: String
    
    // 使用CodingKeys以便服务器返回的字段名称可能与结构体属性名不同
    enum CodingKeys: String, CodingKey {
        case foodName = "foodName"
        case nutritionScore = "nutritionScore"
        case calories = "calories"
        case protein = "protein"
        case carbs = "carbs"
        case fat = "fat"
        case water = "water"
        case fiber = "fiber"
        case sugar = "sugar"
        case caloriePercentage = "caloriePercentage"
        case recommendations = "recommendations"
        case conversationId = "conversationId"
    }
}

// 外层API响应结构体
struct OuterAPIResponse: Codable {
    let code: Int
    let message: String
    let data: String?
    let timestamp: Int64
}

// 内层JSON解析所需的结构体
struct InnerFoodAPIResponse: Codable {
    let conversation_id: String
    let response: FoodResponse
    let created_at: String
}

struct FoodResponse: Codable {
    let is_food: String
    let food_name: String
    let food_score: Int
    let score_reason: String
    let health_analysis_result: String
    let analysis_reason: String
    let health_advice: [String]
    let nutritional_components: [NutritionalComponent]
}

struct NutritionalComponent: Codable {
    let chinese_name: String
    let english_name: String
    let storage_unit: String
    let actual_content: NutritionValue
}

// 营养素值可能是整数、浮点数或字符串
enum NutritionValue: Codable {
    case integer(Int)
    case double(Double)
    case string(String)
    
    init(from decoder: Decoder) throws {
        let container = try decoder.singleValueContainer()
        
        if let intValue = try? container.decode(Int.self) {
            self = .integer(intValue)
            return
        }
        
        if let doubleValue = try? container.decode(Double.self) {
            self = .double(doubleValue)
            return
        }
        
        if let stringValue = try? container.decode(String.self) {
            self = .string(stringValue)
            return
        }
        
        throw DecodingError.dataCorruptedError(in: container, debugDescription: "Cannot decode NutritionValue")
    }
}

// 从营养成分数组中获取特定营养素的值
func getNutritionValue(components: [NutritionalComponent], name: String) -> Int {
    let component = components.first { $0.english_name == name }
    
    if let component = component {
        switch component.actual_content {
        case .integer(let intValue):
            return intValue
        case .double(let doubleValue):
            return Int(doubleValue)
        case .string(let stringValue):
            if let intValue = Int(stringValue) {
                return intValue
            } else if let doubleValue = Double(stringValue) {
                return Int(doubleValue)
            }
        }
    }
    
    // 如果没有找到或无法解析，返回默认值
    return name == "Calories" ? 300 : 0
}

// 添加创建食物记录接口请求模型
struct DietRecordCreateRequest: Codable {
    let conversationId: String
}

// 食物记录响应模型
struct DietRecordResponse: Codable, Identifiable {
    let id: Int
    let createdTime: String
    let fileLocation: String?
    let foodName: String
    let calories: Int
    let caloriesUnit: String
    let protein: Int
    let proteinUnit: String
    let fat: Int
    let fatUnit: String
    let timeStr: String
    
    // 用于转换创建时间 - 修复：使用本地时区而不是UTC，确保凌晨时间正确显示
    var createdDate: Date? {
        let formatter = DateFormatter()
        formatter.dateFormat = "yyyy-MM-dd'T'HH:mm:ss.SSSSSS"
        formatter.timeZone = TimeZone.current // 使用用户当前时区
        return formatter.date(from: createdTime)
    }
    
    // 用于显示时间
    var displayTime: String {
        return timeStr
    }
    
    // 用于显示卡路里信息
    var displayCalories: String {
        return "\(calories) \(caloriesUnit)"
    }
    
    // 检查是否有图片
    var hasImage: Bool {
        return fileLocation != nil && !fileLocation!.isEmpty
    }
}

// 图片缓存管理器
class ImageCacheManager {
    static let shared = ImageCacheManager()
    private let cache = NSCache<NSString, UIImage>()
    private let fileManager = FileManager.default
    private let cacheDirectory: URL
    
    // 压缩配置
    private struct CompressionConfig {
        static let thumbnailSize = CGSize(width: 300, height: 300)  // 缩略图尺寸（进一步减小）
        static let mediumSize = CGSize(width: 600, height: 600)     // 中等尺寸（进一步减小）
        static let largeSize = CGSize(width: 800, height: 800)      // 大尺寸
        static let ultraHighCompressionQuality: CGFloat = 0.3      // 超高压缩质量
        static let highCompressionQuality: CGFloat = 0.5           // 高压缩质量
        static let mediumCompressionQuality: CGFloat = 0.7         // 中等压缩质量
        static let lowCompressionQuality: CGFloat = 0.9            // 低压缩质量
        
        // 文件大小限制
        static let maxThumbnailSize = 50 * 1024    // 50KB
        static let maxMediumSize = 150 * 1024      // 150KB
        static let maxLargeSize = 300 * 1024       // 300KB
    }
    
    private init() {
        // 设置内存缓存限制
        cache.countLimit = 100 // 最多缓存100张图片
        cache.totalCostLimit = 50 * 1024 * 1024 // 最多使用50MB内存
        
        // 创建本地缓存目录
        let documentsDirectory = fileManager.urls(for: .documentDirectory, in: .userDomainMask).first!
        cacheDirectory = documentsDirectory.appendingPathComponent("FoodImageCache")
        
        // 如果目录不存在则创建
        if !fileManager.fileExists(atPath: cacheDirectory.path) {
            try? fileManager.createDirectory(at: cacheDirectory, withIntermediateDirectories: true, attributes: nil)
        }
        
        // 清理过期的缓存文件（启动时清理超过7天的文件）
        cleanupExpiredCacheFiles()
    }
    
    // 智能压缩图片
    private func compressImageForFoodDiary(_ image: UIImage, compressionLevel: CompressionLevel = .medium) -> UIImage {
        let originalSize = image.size
        let originalDataSize = image.jpegData(compressionQuality: 1.0)?.count ?? 0
        
        print("🖼️ 原始图片尺寸: \(originalSize), 原始大小: \(originalDataSize) bytes")
        
        var targetSize: CGSize
        var compressionQuality: CGFloat
        
        // 根据压缩级别设置参数
        switch compressionLevel {
        case .ultra:
            targetSize = CompressionConfig.thumbnailSize
            compressionQuality = CompressionConfig.ultraHighCompressionQuality
        case .high:
            targetSize = CompressionConfig.thumbnailSize
            compressionQuality = CompressionConfig.highCompressionQuality
        case .medium:
            targetSize = CompressionConfig.mediumSize
            compressionQuality = CompressionConfig.mediumCompressionQuality
        case .low:
            targetSize = CompressionConfig.largeSize
            compressionQuality = CompressionConfig.lowCompressionQuality
        }
        
        // 计算合适的缩放比例
        let scaleX = targetSize.width / originalSize.width
        let scaleY = targetSize.height / originalSize.height
        let scale = min(scaleX, scaleY, 1.0) // 不放大图片
        
        let newSize = CGSize(
            width: originalSize.width * scale,
            height: originalSize.height * scale
        )
        
        // 调整图片尺寸
        let resizedImage = resizeImageEfficiently(image, to: newSize)
        
        // 渐进式压缩，确保文件大小符合要求
        var finalImage = resizedImage
        var currentQuality = compressionQuality
        var targetFileSize: Int
        
        // 根据压缩级别设置目标文件大小
        switch compressionLevel {
        case .ultra:
            targetFileSize = CompressionConfig.maxThumbnailSize
        case .high:
            targetFileSize = CompressionConfig.maxThumbnailSize
        case .medium:
            targetFileSize = CompressionConfig.maxMediumSize
        case .low:
            targetFileSize = CompressionConfig.maxLargeSize
        }
        
        // 渐进式压缩循环
        var attempts = 0
        let maxAttempts = 5
        
        while attempts < maxAttempts {
            if let compressedData = finalImage.jpegData(compressionQuality: currentQuality) {
                let compressionRatio = Float(originalDataSize) / Float(compressedData.count)
                
                if compressedData.count <= targetFileSize {
                    print("✅ 图片压缩完成 - 新尺寸: \(newSize), 新大小: \(compressedData.count) bytes, 压缩比: \(String(format: "%.1f", compressionRatio)):1, 尝试次数: \(attempts + 1)")
                    return UIImage(data: compressedData) ?? finalImage
                }
                
                // 如果文件仍然太大，继续压缩
                print("🔄 第\(attempts + 1)次压缩：当前大小 \(compressedData.count) bytes > 目标 \(targetFileSize) bytes，继续压缩...")
                
                // 降低质量
                currentQuality = max(currentQuality - 0.15, 0.1)
                
                // 如果质量已经很低，进一步减小尺寸
                if currentQuality <= 0.25 && newSize.width > 200 {
                    let furtherReducedSize = CGSize(
                        width: newSize.width * 0.8,
                        height: newSize.height * 0.8
                    )
                    finalImage = resizeImageEfficiently(resizedImage, to: furtherReducedSize)
                    currentQuality = 0.4 // 重置质量
                    print("🔄 进一步减小尺寸到: \(furtherReducedSize)")
                }
            }
            
            attempts += 1
        }
        
        // 如果经过多次尝试仍然太大，返回最后的结果
        if let finalData = finalImage.jpegData(compressionQuality: currentQuality) {
            print("⚠️ 经过\(maxAttempts)次压缩后，最终大小: \(finalData.count) bytes")
            return UIImage(data: finalData) ?? finalImage
        }
        
        return finalImage
    }
    
    // 高效的图片尺寸调整
    private func resizeImageEfficiently(_ image: UIImage, to targetSize: CGSize) -> UIImage {
        // 如果目标尺寸比原图大，直接返回原图
        if targetSize.width >= image.size.width && targetSize.height >= image.size.height {
            return image
        }
        
        let renderer = UIGraphicsImageRenderer(size: targetSize)
        return renderer.image { _ in
            image.draw(in: CGRect(origin: .zero, size: targetSize))
        }
    }
    
    // 网络下载时的图片数据压缩（在解码前压缩）
    private func compressNetworkImageData(_ data: Data, targetSizeKB: Int) -> Data {
        guard let originalImage = UIImage(data: data) else { return data }
        
        let originalSizeKB = data.count / 1024
        let targetSizeBytes = targetSizeKB * 1024
        
        print("🔄 开始网络下载压缩: \(originalSizeKB)KB → 目标\(targetSizeKB)KB")
        
        // 计算合适的压缩参数
        var compressionQuality: CGFloat = 0.8
        var targetSize = originalImage.size
        
        // 根据图片大小智能调整尺寸和质量
        if originalSizeKB > 3000 { // 超过3MB的超大图片
            // 激进压缩：大幅减小尺寸
            let scaleFactor = max(0.3, sqrt(Double(targetSizeKB) / Double(originalSizeKB)))
            targetSize = CGSize(
                width: originalImage.size.width * scaleFactor,
                height: originalImage.size.height * scaleFactor
            )
            compressionQuality = 0.5 // 降低初始质量
            print("🔄 网络压缩（超大图片）：激进压缩，尺寸调整到 \(targetSize)，初始质量: \(compressionQuality)")
        } else if originalSizeKB > targetSizeKB * 4 { // 超过目标4倍大小
            let scaleFactor = sqrt(Double(targetSizeKB) / Double(originalSizeKB))
            targetSize = CGSize(
                width: originalImage.size.width * scaleFactor,
                height: originalImage.size.height * scaleFactor
            )
            print("🔄 网络压缩：调整尺寸到 \(targetSize)")
        } else if originalSizeKB > targetSizeKB * 2 { // 超过目标2倍大小
            // 中等压缩：降低初始质量
            compressionQuality = 0.6
            print("🔄 网络压缩：降低初始质量到 \(compressionQuality)")
        }
        
        // 调整图片尺寸
        let resizedImage = resizeImageEfficiently(originalImage, to: targetSize)
        
        // 渐进式质量压缩
        var attempts = 0
        let maxAttempts = 5
        
        while attempts < maxAttempts {
            if let compressedData = resizedImage.jpegData(compressionQuality: compressionQuality) {
                if compressedData.count <= targetSizeBytes {
                    let finalSizeKB = compressedData.count / 1024
                    print("✅ 网络压缩成功: \(originalSizeKB)KB → \(finalSizeKB)KB, 质量: \(compressionQuality)")
                    return compressedData
                }
                
                // 如果仍然太大，降低质量
                compressionQuality = max(compressionQuality - 0.15, 0.1)
                print("🔄 网络压缩第\(attempts + 1)次: 降低质量到 \(compressionQuality)")
            }
            
            attempts += 1
        }
        
        // 如果无法压缩到目标大小，返回最后的尝试结果
        if let finalData = resizedImage.jpegData(compressionQuality: compressionQuality) {
            let finalSizeKB = finalData.count / 1024
            print("⚠️ 网络压缩达到最大尝试次数: \(originalSizeKB)KB → \(finalSizeKB)KB")
            return finalData
        }
        
        // 压缩完全失败，返回原数据
        print("❌ 网络压缩失败，返回原始数据")
        return data
    }
    
    // 压缩级别枚举
    enum CompressionLevel {
        case ultra   // 超高压缩，适用于第一次加载和列表视图
        case high    // 高压缩，适用于列表视图
        case medium  // 中等压缩，适用于详情视图
        case low     // 低压缩，适用于全屏查看
    }
    
    // 生成缓存文件名
    private func cacheFileName(for url: String) -> String {
        return url.data(using: .utf8)?.base64EncodedString()
            .replacingOccurrences(of: "/", with: "_")
            .replacingOccurrences(of: "+", with: "-") ?? "unknown"
    }
    
    // 获取缓存文件路径
    private func cacheFilePath(for url: String) -> URL {
        let fileName = cacheFileName(for: url) + ".jpg"
        return cacheDirectory.appendingPathComponent(fileName)
    }
    
    // 获取缓存的图片（先检查内存，再检查本地存储）- 公开方法
    func getImage(for url: String) -> UIImage? {
        // 先检查内存缓存
        if let cachedImage = cache.object(forKey: NSString(string: url)) {
            return cachedImage
        }
        
        // 再检查本地存储
        let filePath = cacheFilePath(for: url)
        if fileManager.fileExists(atPath: filePath.path),
           let imageData = try? Data(contentsOf: filePath),
           let image = UIImage(data: imageData) {
            
            // 重新加载到内存缓存
            setImageToMemoryCache(image, for: url)
            return image
        }
        
        return nil
    }
    
    // 缓存图片到内存
    private func setImageToMemoryCache(_ image: UIImage, for url: String) {
        let cost = image.jpegData(compressionQuality: 0.8)?.count ?? 0
        cache.setObject(image, forKey: NSString(string: url), cost: cost)
    }
    
    // 缓存图片（同时保存到内存和本地存储）
    func setImage(_ image: UIImage, for url: String, compressionLevel: CompressionLevel = .medium) {
        // 对于食物日记图片，先进行压缩再保存到内存缓存
        let compressedImage = compressImageForFoodDiary(image, compressionLevel: compressionLevel)
        setImageToMemoryCache(compressedImage, for: url)
        
        // 保存到本地存储
        DispatchQueue.global(qos: .background).async {
            self.saveImageToLocalStorage(compressedImage, for: url, compressionLevel: compressionLevel)
        }
    }
    
    // 保存图片到本地存储（使用智能压缩）
    private func saveImageToLocalStorage(_ image: UIImage, for url: String, compressionLevel: CompressionLevel = .medium) {
        let filePath = cacheFilePath(for: url)
        
        // 使用智能压缩
        let compressedImage = compressImageForFoodDiary(image, compressionLevel: compressionLevel)
        
        // 根据压缩级别选择质量
        let quality: CGFloat
        switch compressionLevel {
        case .ultra:
            quality = 0.3
        case .high:
            quality = 0.5
        case .medium:
            quality = 0.7
        case .low:
            quality = 0.8
        }
        guard let imageData = compressedImage.jpegData(compressionQuality: quality) else { return }
        
        do {
            try imageData.write(to: filePath)
            print("🖼️ 压缩图片已缓存到本地: \(url), 大小: \(imageData.count) bytes")
            
            // 设置文件的创建时间属性用于后续清理
            try fileManager.setAttributes([.creationDate: Date()], ofItemAtPath: filePath.path)
        } catch {
            print("❌ 保存图片到本地失败: \(error.localizedDescription)")
        }
    }
    
    // 清除所有缓存
    func clearCache() {
        // 清除内存缓存
        cache.removeAllObjects()
        
        // 清除本地存储
        DispatchQueue.global(qos: .background).async {
            self.clearLocalStorage()
        }
    }
    
    // 清除本地存储
    private func clearLocalStorage() {
        do {
            let fileURLs = try fileManager.contentsOfDirectory(at: cacheDirectory, includingPropertiesForKeys: nil)
            for fileURL in fileURLs {
                try fileManager.removeItem(at: fileURL)
            }
            print("🗑️ 已清除所有本地图片缓存")
        } catch {
            print("❌ 清除本地图片缓存失败: \(error.localizedDescription)")
        }
    }
    
    // 清理过期的缓存文件（超过7天的文件）
    private func cleanupExpiredCacheFiles() {
        DispatchQueue.global(qos: .background).async {
            do {
                let fileURLs = try self.fileManager.contentsOfDirectory(at: self.cacheDirectory, includingPropertiesForKeys: [.creationDateKey])
                let cutoffDate = Date().addingTimeInterval(-7 * 24 * 60 * 60) // 7天前
                
                var removedCount = 0
                for fileURL in fileURLs {
                    if let attributes = try? self.fileManager.attributesOfItem(atPath: fileURL.path),
                       let creationDate = attributes[.creationDate] as? Date,
                       creationDate < cutoffDate {
                        
                        try self.fileManager.removeItem(at: fileURL)
                        removedCount += 1
                    }
                }
                
                if removedCount > 0 {
                    print("🧹 已清理\(removedCount)个过期的图片缓存文件")
                }
            } catch {
                print("❌ 清理过期缓存文件失败: \(error.localizedDescription)")
            }
        }
    }
    
    // 获取缓存使用情况
    func getCacheInfo() -> (fileCount: Int, totalSize: Int64) {
        var fileCount = 0
        var totalSize: Int64 = 0
        
        do {
            let fileURLs = try fileManager.contentsOfDirectory(at: cacheDirectory, includingPropertiesForKeys: [.fileSizeKey])
            fileCount = fileURLs.count
            
            for fileURL in fileURLs {
                if let attributes = try? fileManager.attributesOfItem(atPath: fileURL.path),
                   let fileSize = attributes[.size] as? Int64 {
                    totalSize += fileSize
                }
            }
        } catch {
            print("❌ 获取缓存信息失败: \(error.localizedDescription)")
        }
        
        return (fileCount, totalSize)
    }
    
    // 异步加载图片，带本地持久化缓存和智能压缩
    func loadImage(from url: String, compressionLevel: CompressionLevel = .medium, completion: @escaping (UIImage?) -> Void) {
        // 先检查缓存（内存 + 本地存储）
        if let cachedImage = getImage(for: url) {
            completion(cachedImage)
            return
        }
        
        // 如果缓存中没有，从网络加载
        DispatchQueue.global(qos: .background).async {
            self.loadImageFromNetwork(url: url, compressionLevel: compressionLevel, completion: completion)
        }
    }
    
    // 从网络加载图片的具体实现
    private func loadImageFromNetwork(url: String, compressionLevel: CompressionLevel, completion: @escaping (UIImage?) -> Void) {
        guard let imageUrl = URL(string: url) else {
            DispatchQueue.main.async {
                completion(nil)
            }
            return
        }
        
        // 创建URLSession配置，支持大文件下载
        let config = URLSessionConfiguration.default
        config.timeoutIntervalForRequest = 30.0
        config.timeoutIntervalForResource = 60.0
        let session = URLSession(configuration: config)
        
        // 监控下载进度（对于大文件）
        let startTime = Date()
        let task = session.dataTask(with: imageUrl) { [weak self] data, response, error in
            let downloadTime = Date().timeIntervalSince(startTime)
            guard let self = self else { return }
            
            if let error = error {
                print("❌ 网络请求失败: \(error.localizedDescription)")
                DispatchQueue.main.async {
                    completion(nil)
                }
                return
            }
            
            guard let data = data else {
                print("❌ 网络数据为空")
                DispatchQueue.main.async {
                    completion(nil)
                }
                return
            }
            
            let originalSizeKB = data.count / 1024
            let downloadSpeedKBps = downloadTime > 0 ? Double(originalSizeKB) / downloadTime : 0
            print("📥 从网络下载图片成功: \(url)")
            print("   - 原始大小: \(originalSizeKB)KB (\(data.count) bytes)")
            print("   - 下载时间: \(String(format: "%.2f", downloadTime))秒")
            print("   - 下载速度: \(String(format: "%.1f", downloadSpeedKBps))KB/s")
            
            // 根据压缩级别和文件大小决定网络下载时的压缩策略
            var imageData = data
            
            // 设置网络下载压缩目标
            var networkCompressionTargetKB: Int
            switch compressionLevel {
            case .ultra:
                networkCompressionTargetKB = 150  // 超高压缩：150KB
            case .high:
                networkCompressionTargetKB = 250  // 高压缩：250KB
            case .medium:
                networkCompressionTargetKB = 500  // 中等压缩：500KB
            case .low:
                networkCompressionTargetKB = 800  // 低压缩：800KB
            }
            
            // 如果下载的图片超过目标大小，进行网络压缩
            if originalSizeKB > networkCompressionTargetKB {
                print("🔄 图片过大(\(originalSizeKB)KB > \(networkCompressionTargetKB)KB)，进行网络下载时压缩")
                imageData = self.compressNetworkImageData(data, targetSizeKB: networkCompressionTargetKB)
            }
            
            guard let originalImage = UIImage(data: imageData) else {
                print("❌ 无法解码图片数据")
                DispatchQueue.main.async {
                    completion(nil)
                }
                return
            }
            
            if imageData.count != data.count {
                let compressionRatio = Float(data.count) / Float(imageData.count)
                let finalSizeKB = imageData.count / 1024
                let spaceSavedKB = originalSizeKB - finalSizeKB
                let spaceSavedPercent = Float(spaceSavedKB) / Float(originalSizeKB) * 100
                print("✅ 网络下载压缩完成:")
                print("   - 压缩前: \(originalSizeKB)KB → 压缩后: \(finalSizeKB)KB")
                print("   - 节省空间: \(spaceSavedKB)KB (\(String(format: "%.1f", spaceSavedPercent))%)")
                print("   - 压缩比: \(String(format: "%.1f", compressionRatio)):1")
            } else {
                print("📋 图片无需网络压缩（已在目标大小范围内）")
            }
            
            // 应用智能压缩并缓存图片
            let compressedImage = self.compressImageForFoodDiary(originalImage, compressionLevel: compressionLevel)
            self.setImage(compressedImage, for: url, compressionLevel: compressionLevel)
            
            DispatchQueue.main.async {
                completion(compressedImage)
            }
        }
        
        task.resume()
    }
    
    // 预加载图片（后台静默缓存，专门为食物日记优化）
    func preloadImage(from url: String, compressionLevel: CompressionLevel = .ultra) {
        // 如果已经缓存则不需要预加载
        if getImage(for: url) != nil { return }
        
        DispatchQueue.global(qos: .background).async {
            self.loadImage(from: url, compressionLevel: compressionLevel) { _ in
                // 静默预加载，不需要处理结果
            }
        }
    }
    
    // 批量预加载食物日记图片（使用超高压缩率）
    func preloadImages(from urls: [String], compressionLevel: CompressionLevel = .ultra) {
        print("🚀 开始批量预加载\(urls.count)张图片，压缩级别: \(compressionLevel)")
        for url in urls {
            preloadImage(from: url, compressionLevel: compressionLevel)
        }
    }
    
    // 检查是否为第一次加载（缓存中没有图片）
    private func isFirstTimeLoad(for url: String) -> Bool {
        return getImage(for: url) == nil
    }
    
    // 为食物日记专门优化的图片加载方法
    func loadFoodDiaryImage(from url: String, isListView: Bool = true, completion: @escaping (UIImage?) -> Void) {
        let isFirstLoad = isFirstTimeLoad(for: url)
        let compressionLevel: CompressionLevel
        
        if isFirstLoad {
            // 第一次加载使用超高压缩，优先保证速度
            compressionLevel = .ultra
            print("📥 第一次加载图片，使用超高压缩: \(url)")
        } else if isListView {
            // 列表视图使用高压缩
            compressionLevel = .high
        } else {
            // 详情视图使用中等压缩
            compressionLevel = .medium
        }
        
        // 添加加载时间监控
        let startTime = Date()
        loadImage(from: url, compressionLevel: compressionLevel) { image in
            let loadTime = Date().timeIntervalSince(startTime)
            if loadTime > 0.5 {
                print("⚠️ 图片加载较慢: \(String(format: "%.2f", loadTime))秒, URL: \(url)")
            }
            completion(image)
        }
    }
    
    // 快速加载缩略图（用于第一次获取时的快速预览）
    func loadThumbnailFirst(from url: String, completion: @escaping (UIImage?) -> Void) {
        // 首先尝试加载超压缩版本
        loadImage(from: url, compressionLevel: .ultra) { [weak self] ultraImage in
            completion(ultraImage)
            
            // 后台预加载中等质量版本
            if ultraImage != nil {
                DispatchQueue.global(qos: .background).async {
                    self?.loadImage(from: url, compressionLevel: .medium) { _ in
                        // 静默预加载更高质量版本，不回调
                    }
                }
            }
        }
    }
    
    // 获取食物日记图片的优化状态
    func getFoodDiaryCacheStatus() {
        let (fileCount, totalSize) = getCacheInfo()
        let totalSizeMB = Double(totalSize) / (1024 * 1024)
        
        print("📊 食物日记图片缓存状态:")
        print("   - 缓存文件数: \(fileCount)")
        print("   - 总缓存大小: \(String(format: "%.2f", totalSizeMB)) MB")
        print("   - 内存缓存数量: \(cache.countLimit)")
        print("   - 内存缓存限制: \(cache.totalCostLimit / (1024 * 1024)) MB")
        
        // 显示不同压缩级别的效果
        if fileCount > 0 {
            print("   - 超高压缩(Ultra): 目标50KB以下，适用于第一次加载")
            print("   - 高压缩(High): 目标50KB以下，适用于列表视图")
            print("   - 中等压缩(Medium): 目标150KB以下，适用于详情视图")
            print("   - 低压缩(Low): 目标300KB以下，适用于全屏查看")
        }
    }
    
    // 智能清理缓存（当缓存过大时）
    func smartCacheCleanup() {
        let (_, totalSize) = getCacheInfo()
        let totalSizeMB = Double(totalSize) / (1024 * 1024)
        
        // 如果缓存超过100MB，进行清理
        if totalSizeMB > 100 {
            print("🧹 缓存大小超过100MB，开始智能清理...")
            
            // 清理过期文件（超过3天）
            DispatchQueue.global(qos: .background).async {
                do {
                    let fileURLs = try self.fileManager.contentsOfDirectory(at: self.cacheDirectory, includingPropertiesForKeys: [.creationDateKey, .fileSizeKey])
                    let cutoffDate = Date().addingTimeInterval(-3 * 24 * 60 * 60) // 3天前
                    
                    var removedCount = 0
                    var freedSpace: Int64 = 0
                    
                    for fileURL in fileURLs {
                        if let attributes = try? self.fileManager.attributesOfItem(atPath: fileURL.path),
                           let creationDate = attributes[.creationDate] as? Date,
                           let fileSize = attributes[.size] as? Int64,
                           creationDate < cutoffDate {
                            
                            try self.fileManager.removeItem(at: fileURL)
                            removedCount += 1
                            freedSpace += fileSize
                        }
                    }
                    
                    let freedMB = Double(freedSpace) / (1024 * 1024)
                    print("✅ 智能清理完成：删除\(removedCount)个文件，释放\(String(format: "%.2f", freedMB)) MB空间")
                    
                } catch {
                    print("❌ 智能清理失败: \(error.localizedDescription)")
                }
            }
            
            // 清理内存缓存
            cache.removeAllObjects()
            print("🧹 已清理内存缓存")
        }
    }
}

// 详细食物记录响应模型
struct DetailedFoodRecord: Codable {
    let id: Int
    let createdTime: String
    let fileLocation: String?
    let isFood: Bool
    let foodName: String
    let foodScore: Double
    let scoreReason: String
    let nutritionalComponents: String
    let healthAdvice: String
    let analysisReason: String
    let healthAnalysisResult: String
    
    // 用于显示的计算属性
    var displayScore: Int {
        return Int(round(foodScore))
    }
    
    // 检查是否有图片
    var hasImage: Bool {
        return fileLocation != nil && !fileLocation!.isEmpty
    }
    
    // 解析营养成分JSON字符串 - API返回的是数组格式
    var parsedNutritionalComponents: [[String: Any]]? {
        guard let data = nutritionalComponents.data(using: .utf8) else { return nil }
        return try? JSONSerialization.jsonObject(with: data) as? [[String: Any]]
    }
    
    // 解析健康建议JSON字符串
    var parsedHealthAdvice: [String] {
        guard let data = healthAdvice.data(using: .utf8) else { return [] }
        if let array = try? JSONSerialization.jsonObject(with: data) as? [String] {
            return array
        }
        // 如果不是数组格式，尝试按换行符分割
        return healthAdvice.components(separatedBy: .newlines).filter { !$0.isEmpty }
    }
    
    // 获取特定营养素的值 - 从数组中查找
    func getNutritionValue(for nutrient: String) -> Int {
        guard let components = parsedNutritionalComponents else { return 0 }
        
        // 在数组中查找匹配的营养素
        for component in components {
            if let englishName = component["english_name"] as? String,
               englishName.lowercased() == nutrient.lowercased() {
                
                // 尝试获取actual_content值
                if let content = component["actual_content"] {
                    // 处理不同类型的值
                    if let intValue = content as? Int {
                        return intValue
                    } else if let doubleValue = content as? Double {
                        return Int(doubleValue)
                    } else if let stringValue = content as? String {
                        // 尝试从字符串转换
                        if let intValue = Int(stringValue) {
                            return intValue
                        } else if let doubleValue = Double(stringValue) {
                            return Int(doubleValue)
                        }
                    }
                }
            }
        }
        
        return 0
    }
    
    // 获取主要营养素 - 使用API中的英文名称
    var calories: Int {
        return getNutritionValue(for: "Calories")
    }
    
    var protein: Int {
        return getNutritionValue(for: "Protein")
    }
    
    var carbs: Int {
        return getNutritionValue(for: "Carbohydrates")
    }
    
    var fat: Int {
        return getNutritionValue(for: "Fat")
    }
    
    var water: Int {
        return getNutritionValue(for: "Water")
    }
    
    var fiber: Int {
        return getNutritionValue(for: "Fiber")
    }
    
    var sugar: Int {
        return getNutritionValue(for: "Sugar")
    }
} 
