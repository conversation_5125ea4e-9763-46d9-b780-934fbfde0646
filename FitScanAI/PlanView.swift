import SwiftUI
import Charts
import WebKit

// MARK: - 模型定义

// 周体重数据模型
struct WeeklyWeightData: Identifiable {
    var id: UUID
    var week: String // 例如 "W1", "W2"
    var weight: Double
}

// 营养摘要模型
struct NutritionSummary {
    var protein: Int
    var proteinGoal: Int
    var fat: Int
    var fatGoal: Int
    var calories: Int
    var calorieGoal: Int
}

// 推荐活动模型
struct RecommendedActivity: Identifiable {
    var id: UUID
    var type: String
    var duration: Int
    var calories: Int
    var icon: String
}

// 每日任务模型
struct DailyTask: Identifiable {
    var id: UUID
    var name: String
    var isCompleted: Bool
}

// 周提示模型
struct WeeklyTip: Identifiable {
    var id: UUID
    var content: String
    var icon: String
}

// MARK: - 子视图组件

// 推荐活动卡片组件
struct RecommendedActivityCard: View {
    let icon: String
    let title: String
    let duration: Int
    let calories: Int
    
    var body: some View {
        VStack(alignment: .center, spacing: 10) {
            // 图标区域固定高度
            Image(systemName: icon)
                .font(.system(size: 24))
                .foregroundColor(.green)
                .frame(height: 30)
            
            // 标题区域固定高度
            Text(title)
                .font(.subheadline)
                .frame(height: 20)
                .lineLimit(1)
            
            // 时间信息区域固定高度
            HStack(spacing: 8) {
                Image(systemName: "clock")
                    .font(.caption2)
                
                Text("\(duration) mins")
                    .font(.caption)
            }
            .foregroundColor(.secondary)
            .frame(height: 20)
            
            // 卡路里信息区域固定高度
            HStack(spacing: 8) {
                Image(systemName: "flame")
                    .font(.caption2)
                
                Text("\(calories) kcal")
                    .font(.caption)
            }
            .foregroundColor(.secondary)
            .frame(height: 20)
        }
        .padding()
        .frame(width: 120, height: 150)
        .background(Color.white)
        .cornerRadius(12)
        .shadow(color: Color.black.opacity(0.1), radius: 4, x: 0, y: 2)
    }
}

// 打卡项目行组件
struct CheckInRow: View {
    let title: String
    let isCompleted: Bool
    
    var body: some View {
        HStack {
            Text(title)
                .font(.subheadline)
            
            Spacer()
            
            if isCompleted {
                Image(systemName: "checkmark.circle.fill")
                    .foregroundColor(.green)
            } else {
                // 未完成时显示灰色圆圈
                Image(systemName: "circle")
                    .foregroundColor(.gray)
            }
        }
        .padding(.vertical, 5)
    }
}

// MARK: - 主视图

struct PlanView: View {
    @EnvironmentObject var userData: UserData
    @StateObject private var planAdviceService = PlanAdviceService.shared
    @StateObject private var checkInService = CheckInService.shared
    @StateObject private var checkInHistoryService = CheckInHistoryService.shared
    @State private var showAddExercise = false
    @State private var weeklyProgress: Double = 0
    @State private var currentWeek: Int = 1
    @State private var totalWeeks: Int = 12
    @State private var weeklyWeightData: [WeeklyWeightData] = []
    @State private var exerciseCalories: Int = 847
    @State private var exerciseCalorieGoal: Int = 1200
    @State private var recentExercises: [ExerciseRecord] = []
    @State private var isLoading = false
    @State private var weeklyTips: [WeeklyTip] = []
    @State private var isCheckingPlan = true
    @State private var userHasPlan = false
    @State private var dailyTasks: [DailyTask] = []
    @State private var currentWeekOffset: Int = 0 // 0为本周，-1为上周，1为下周
    @State private var selectedCheckInDate: Date = Date() // 选中的打卡日期，默认为今天
    @State private var isLoadingCheckInData: Bool = false // 打卡数据加载状态
    @State private var dateCheckInStatus: [String: Bool] = [:] // 缓存日期的打卡完成状态
    
    // 打卡历史数据
    @State private var checkInHistoryLogs: [UserPlansLogsResponse] = []
    @State private var isLoadingCheckInHistory = false
    @State private var realStreakDays = 0 // 基于API数据的真实连续天数
    @State private var realDaysSinceLastCheckIn = 0 // 基于API数据的真实未打卡天数
    @State private var consecutiveNonCheckInDays = 0 // 连续未打卡天数（用于激励卡片判断）
    
    // 添加营养分母的状态变量
    @State private var weeklyCalorieGoal: Int = 0
    @State private var weeklyProteinGoal: Int = 0
    @State private var weeklyFatGoal: Int = 0
    
    // 添加API数据状态变量
    @State private var weeklyAPICalories: Int = 0
    @State private var weeklyAPIProtein: Int = 0
    @State private var weeklyAPIFat: Int = 0
    @State private var isLoadingWeeklyNutrition = false
    
    // 添加锁定状态变量防止重复添加
    @State private var isAddingExercise = false
    // 添加用于防止重复刷新的标志
    @State private var isRefreshingExerciseData = false
    @State private var showHealthInfoSheet = false
    
    // 推荐运动活动数据
    private var recommendedActivities: [RecommendedActivity] {
        return [
            RecommendedActivity(id: UUID(), type: "Walking", duration: 30, calories: 150, icon: "figure.walk"),
            RecommendedActivity(id: UUID(), type: "Swimming", duration: 45, calories: 350, icon: "figure.pool.swim"),
            RecommendedActivity(id: UUID(), type: "Cycling", duration: 40, calories: 280, icon: "bicycle"),
            RecommendedActivity(id: UUID(), type: "Running", duration: 20, calories: 200, icon: "figure.run"),
            RecommendedActivity(id: UUID(), type: "Yoga", duration: 50, calories: 180, icon: "figure.yoga"),
            RecommendedActivity(id: UUID(), type: "Gym", duration: 60, calories: 400, icon: "dumbbell")
        ]
    }
    
    var body: some View {
        Group {
            if userData.isGuestMode {
                // 访客模式显示
                GuestPlanView()
                    .environmentObject(userData)
            } else {
                // 正常用户显示
                ScrollView {
                    VStack(alignment: .leading, spacing: userData.isPremium ? 25 : 15) {
                        headerView()
                        progressView()
                        
                        // 将体重进度和周折线图组合在一个白色边框和阴影框架中
                        VStack(spacing: 20) {
                            weightProgressView()
                            weightChartView()
                        }
                        .padding()
                        .background(Color.white)
                        .cornerRadius(12)
                        .shadow(color: Color.black.opacity(0.1), radius: 4, x: 0, y: 2)
                        .padding(.horizontal)
                        
                        nutritionView()
                        exerciseCaloriesView()
                        recommendedActivitiesView()
                        dailyCheckInView()
                        streakView()
                        weeklyTipsView()
                        
                        // 底部间距
                        Spacer().frame(height: 30)
                        
                        // 添加底部文本和下划线
                        VStack(spacing: 8) {
                            HStack(spacing: 0) {
                                Text("View ")
                                    .font(.system(size: 14, weight: .light))
                                    .foregroundColor(Color(red: 0.42, green: 0.45, blue: 0.5))
                                
                                Text("Authoritative Sources of Health Information")
                                    .font(.system(size: 14, weight: .light))
                                    .foregroundColor(Color(red: 0.42, green: 0.45, blue: 0.5))
                                    .underline()
                                    .onTapGesture {
                                        showHealthInfoSheet = true
                                    }
                            }
                            .frame(maxWidth: .infinity)
                            .multilineTextAlignment(.center)
                        }
                        .padding(.bottom, 20)
                    }
                    .padding(.top)
                }
                .overlay(
                    Group {
                        if showAddExercise && !userData.isGuestMode {
                            AddExerciseView(isShowing: $showAddExercise, selectedDate: Date(), onSave: { record in
                                // 添加锁定状态检查，防止重复添加
                                guard !isAddingExercise else { return }
                                isAddingExercise = true
                                
                                // 确保在主线程上执行UI更新操作
                                DispatchQueue.main.async {
                                    // 保存新添加的运动数据，但不在这里刷新UI
                                    saveNewExerciseRecord(record)
                                    
                                    // 移除这里的刷新调用，让saveNewExerciseRecord内部处理刷新
                                    // refreshExerciseData()
                                    
                                    // 恢复状态
                                    self.isAddingExercise = false
                                }
                            })
                            .environmentObject(userData)
                        }
                    }
                )
            }
        }
        .onAppear {
            // 统计
            XDTrackTool.shared.appear("计划页面")

            // 确保selectedCheckInDate正确初始化为今天
            let calendar = Calendar.current
            let today = Date()
            if !calendar.isDate(selectedCheckInDate, inSameDayAs: today) {
                selectedCheckInDate = today
                print("📅 [Plan] 初始化选中日期为今天: \(today)")
            }
            
            // 首先对运动记录进行去重
            userData.deduplicateExerciseRecords()
            
            // 立即使用本地数据填充UI，避免空白状态
            loadLocalDataImmediately()
            
            // 优先同步加载当天的打卡数据，立即显示内容
            loadTodayCheckInDataSync()
            
            // 使用优化的AI建议加载方法
            planAdviceService.loadAllAdvicesOptimized {
                // AI建议加载完成后更新分母数据
                let nutritionGoals = self.planAdviceService.getSafeWeeklyNutritionGoals()
                    self.weeklyCalorieGoal = nutritionGoals.calories
                    self.weeklyProteinGoal = nutritionGoals.protein
                    self.weeklyFatGoal = nutritionGoals.fat
                self.exerciseCalorieGoal = self.planAdviceService.getSafeWeeklyExerciseBurnGoal()
                    
                print("💪 [Plan] AI建议数据加载完成，分母已更新")
            }
            
            // 立即检查用户计划状态（不延迟）
            checkUserPlanStatusFast()
            
            // 后台异步同步远程数据，不阻塞UI
            DispatchQueue.global(qos: .background).async {
                self.syncRemoteDataInBackground()
            }
            
            // 加载打卡历史记录（用于计算真实的连续打卡天数）
            loadCheckInHistory()
            
            // 延迟加载非关键数据
            DispatchQueue.main.asyncAfter(deadline: .now() + 2.0) {
            // 更新连续打卡天数
                _ = self.calculateCurrentStreak()
                
                // 预加载其他日期的打卡状态（低优先级）
                self.preloadNonTodayCheckInStatus()
            }
        }
        // 添加通知监听器，用于刷新运动数据
        .sheet(isPresented: $showHealthInfoSheet) {
            HealthInfoWebView(url: "https://fsai.pickgoodspro.com/fitScanAiDoc.html")
        }
        .onReceive(NotificationCenter.default.publisher(for: Notification.Name("ExerciseDataUpdated"))) { _ in
            // 检查是否已经在刷新过程中，避免重复刷新
            guard !isRefreshingExerciseData else {
                print("📋 [Plan] 已有刷新操作正在进行中，忽略此次通知")
                return
            }
            refreshExerciseData()
        }
        // 添加通知监听器，用于刷新食物记录状态
        .onReceive(NotificationCenter.default.publisher(for: Notification.Name("FoodEntryAdded"))) { _ in
            // loadDailyTasks() - 移除，现在由CheckInService管理
            // 刷新本周营养数据
            if userHasPlan {
                loadWeeklyNutritionData()
            }
        }
        // 添加通知监听器，用于刷新体重状态和打卡状态 
        .onReceive(NotificationCenter.default.publisher(for: Notification.Name("WeightDataUpdated"))) { _ in
            // 当体重更新时刷新打卡状态
            // loadDailyTasks() - 移除，现在由CheckInService管理
            // 刷新周平均体重数据
            DispatchQueue.global(qos: .background).async {
                self.syncRemoteWeightData {
                    print("体重数据更新后同步完成")
                }
            }
        }
        // 添加通知监听器，从OnboardingView导航后强制刷新Plan页面
        .onReceive(NotificationCenter.default.publisher(for: Notification.Name("RefreshPlanView"))) { _ in
            // 从API检查用户是否有计划
            isCheckingPlan = true
            UserPlanService.shared.checkUserHasPlan(userData: userData) { hasPlan in
                DispatchQueue.main.async {
                    self.userHasPlan = hasPlan
                    self.isCheckingPlan = false
                    
                    // 如果有计划，再计算当前周数和进度
                    if hasPlan {
                        self.calculateProgress()
                        self.loadData()
                        self.loadWeeklyNutritionData() // 加载本周营养数据
                    }
                    
                    // 加载每日任务
                    self.loadDailyTasks()
                    
                    _ = self.calculateCurrentStreak()
                    
                    print("Plan页面已收到刷新通知并更新数据, API返回hasPlan=\(hasPlan)")
                }
            }
        }
        // 添加时间变化监听器，确保日期更新时刷新打卡状态
        .onReceive(NotificationCenter.default.publisher(for: NSNotification.Name.NSCalendarDayChanged)) { _ in
            print("日期已变更，刷新打卡状态")
            // loadDailyTasks() - 移除，现在由CheckInService管理
        }
        // 添加通知监听器，在目标设置状态改变时刷新打卡状态显示
        .onReceive(NotificationCenter.default.publisher(for: Notification.Name("WeightGoalUpdated"))) { _ in
            // 当用户设置或更新目标体重时，重新从API检查计划状态
            isCheckingPlan = true
            UserPlanService.shared.checkUserHasPlan(userData: userData) { hasPlan in
                DispatchQueue.main.async {
                    self.userHasPlan = hasPlan
                    self.isCheckingPlan = false
                    
                    // 如果有计划，再计算当前周数和进度和加载数据
                    if hasPlan {
                        self.calculateProgress()
                        self.loadData()
                        self.loadWeeklyNutritionData() // 加载本周营养数据
                    }
                    
                    _ = self.calculateCurrentStreak()
                    
                    print("Weight目标更新通知已处理, API返回hasPlan=\(hasPlan)")
                }
            }
        }
    }
    
    // MARK: - 子视图函数
    
    private func headerView() -> some View {
        VStack(alignment: .leading, spacing: 5) {
            Text("Your Health Plan")
                .font(adaptiveTitleFont())
                .fontWeight(.bold)
            
            if userData.hasPlan {
                Text("\(userData.goalTimelineWeeks)-Week Journey")
                    .font(adaptiveSubheadlineFont())
                    .foregroundColor(.secondary)
            }
        }
        .padding(.horizontal)
    }
    
    // 适配函数
    private func adaptiveTitleFont() -> Font {
        if UIScreen.main.bounds.height < 700 {
            return .title2 // iPhone 12 mini等小屏使用稍小的标题
        } else {
            return .title // 大屏使用标准标题
        }
    }
    
    private func adaptiveSubheadlineFont() -> Font {
        if UIScreen.main.bounds.height < 700 {
            return .caption // 小屏使用更小的副标题
        } else {
            return .subheadline // 大屏使用标准副标题
        }
    }
    
    private func adaptiveHorizontalPadding() -> CGFloat {
        let screenWidth = UIScreen.main.bounds.width
        
        if screenWidth < 375 {
            return 12 // iPhone 12 mini等小屏 (width: 360)
        } else if screenWidth < 390 {
            return 16 // iPhone 12/13/14 标准尺寸 (width: 375)
        } else if screenWidth < 430 {
            return 20 // iPhone 13 Pro/14 Pro等 (width: 390) - 增加边距以改善无目标状态显示
        } else {
            return 20 // iPhone 13 Pro Max/14 Pro Max等大屏 (width: 428+)
        }
    }
    
    // PlanView无计划状态的适配函数
    private func adaptivePlanNoGoalSpacing() -> CGFloat {
        let screenHeight = UIScreen.main.bounds.height
        let screenWidth = UIScreen.main.bounds.width
        
        if screenHeight < 700 || screenWidth < 375 {
            return 8 // iPhone 12 mini等小屏进一步减少间距
        } else if screenHeight < 850 && screenWidth < 430 {
            return 8 // iPhone 13 Pro等中等屏幕 - 进一步减少间距以节省空间
        } else {
            return 12 // iPhone 13 Pro Max等大屏适当减少间距
        }
    }
    
    private func adaptiveSetGoalCircleSize() -> CGFloat {
        let screenHeight = UIScreen.main.bounds.height
        
        if screenHeight < 700 {
            return 100 // iPhone 12 mini等小屏
        } else if screenHeight < 850 {
            return 110 // iPhone 13 Pro等中等屏幕
        } else {
            return 120 // iPhone 13 Pro Max等大屏
        }
    }
    
    private func adaptiveSetGoalButtonFont() -> Font {
        if UIScreen.main.bounds.height < 700 {
            return .subheadline // 小屏使用较小字体
        } else {
            return .headline // 大屏使用标准字体
        }
    }
    
    private func adaptiveSetGoalTopPadding() -> CGFloat {
        if UIScreen.main.bounds.height < 700 {
            return 15 // 小屏减少顶部间距
        } else {
            return 20 // 大屏标准间距
        }
    }
    
    private func adaptiveCurrentWeightSpacing() -> CGFloat {
        if UIScreen.main.bounds.height < 700 {
            return 4 // 小屏减少间距
        } else {
            return 6 // 大屏标准间距
        }
    }
    
    private func adaptiveCurrentWeightLabelFont() -> Font {
        if UIScreen.main.bounds.height < 700 {
            return .caption // 小屏使用更小字体
        } else {
            return .subheadline // 大屏使用标准字体
        }
    }
    
    private func adaptiveCurrentWeightValueFont() -> Font {
        if UIScreen.main.bounds.height < 700 {
            return .title2 // 小屏使用较小字体
        } else {
            return .title // 大屏使用标准字体
        }
    }
    
    private func adaptiveCurrentWeightUnitFont() -> Font {
        if UIScreen.main.bounds.height < 700 {
            return .caption // 小屏使用较小字体
        } else {
            return .subheadline // 大屏使用标准字体
        }
    }
    
    private func adaptiveSetGoalHintFont() -> Font {
        if UIScreen.main.bounds.height < 700 {
            return .caption2 // 小屏使用更小字体
        } else {
            return .caption // 大屏使用标准字体
        }
    }
    
    private func adaptiveHintTextPadding() -> CGFloat {
        if UIScreen.main.bounds.height < 700 {
            return 8 // 小屏减少间距
        } else {
            return 10 // 大屏标准间距
        }
    }
    
    private func adaptivePlanContainerPadding() -> EdgeInsets {
        let screenWidth = UIScreen.main.bounds.width
        let screenHeight = UIScreen.main.bounds.height
        
        if screenHeight < 700 || screenWidth < 375 {
            return EdgeInsets(top: 10, leading: 10, bottom: 10, trailing: 10) // 小屏进一步减少内边距
        } else if screenWidth < 390 {
            return EdgeInsets(top: 12, leading: 12, bottom: 12, trailing: 12) // iPhone 12/13/14 标准尺寸
        } else if screenWidth < 430 {
            return EdgeInsets(top: 12, leading: 12, bottom: 12, trailing: 12) // iPhone 13 Pro/14 Pro等 - 减少内边距以缩小组件
        } else {
            return EdgeInsets(top: 16, leading: 16, bottom: 16, trailing: 16) // 大屏设备
        }
    }
    
    // 营养部分适配函数
    private func adaptiveNutritionTitleFont() -> Font {
        if UIScreen.main.bounds.height < 700 {
            return .subheadline // 小屏使用较小字体
        } else {
            return .headline // 大屏使用标准字体
        }
    }
    
    private func adaptiveNutritionCircleSpacing() -> CGFloat {
        let screenWidth = UIScreen.main.bounds.width
        
        if screenWidth < 375 {
            return 8 // iPhone 12 mini等小屏进一步减少间距
        } else if screenWidth < 390 {
            return 10 // iPhone 12/13/14 标准尺寸
        } else if screenWidth < 430 {
            return 10 // iPhone 13 Pro/14 Pro等 - 进一步减少间距以缩小组件
        } else {
            return 14 // iPhone 13 Pro Max/14 Pro Max等大屏
        }
    }
    
    // 添加营养圆环大小适配函数
    private func adaptiveNutritionCircleSize() -> CGFloat {
        let screenWidth = UIScreen.main.bounds.width
        let screenHeight = UIScreen.main.bounds.height
        
        if screenHeight < 700 || screenWidth < 375 {
            return 50 // iPhone 12 mini等小屏 - 进一步减小
        } else if screenHeight < 850 && screenWidth < 430 {
            return 50 // iPhone 13 Pro等中等屏幕 - 进一步减小尺寸以改善无目标状态显示
        } else {
            return 60 // iPhone 13 Pro Max等大屏 - 适当减小
        }
    }
    
    private func adaptiveNutritionCircleLineWidth() -> CGFloat {
        let screenWidth = UIScreen.main.bounds.width
        
        if screenWidth < 375 {
            return 3 // iPhone 12 mini等小屏
        } else if screenWidth < 430 {
            return 3.5 // iPhone 13 Pro等
        } else {
            return 4 // iPhone 13 Pro Max等大屏
        }
    }
    
    private func adaptiveNutritionCircleTextFont() -> Font {
        let screenHeight = UIScreen.main.bounds.height
        
        if screenHeight < 700 {
            return .caption // iPhone 12 mini等小屏
        } else if screenHeight < 850 {
            return .subheadline // iPhone 13 Pro等中等屏幕
        } else {
            return .headline // iPhone 13 Pro Max等大屏
        }
    }
    
    private func adaptiveNutritionCircleTitleFont() -> Font {
        let screenHeight = UIScreen.main.bounds.height
        
        if screenHeight < 700 {
            return .caption2 // iPhone 12 mini等小屏
        } else if screenHeight < 850 {
            return .caption // iPhone 13 Pro等中等屏幕
        } else {
            return .subheadline // iPhone 13 Pro Max等大屏
        }
    }
    
    private func adaptiveNutritionCircleContentSpacing() -> CGFloat {
        let screenHeight = UIScreen.main.bounds.height
        
        if screenHeight < 700 {
            return 6 // iPhone 12 mini等小屏
        } else if screenHeight < 850 {
            return 8 // iPhone 13 Pro等中等屏幕
        } else {
            return 10 // iPhone 13 Pro Max等大屏
        }
    }
    
    // 无计划时运动卡路里部分的适配函数
    private func adaptiveNoGoalCalorieSpacing() -> CGFloat {
        let screenWidth = UIScreen.main.bounds.width
        let screenHeight = UIScreen.main.bounds.height
        
        if screenWidth < 375 {
            return 8 // iPhone 12 mini等小屏减少间距
        } else if screenHeight < 850 && screenWidth < 430 {
            return 8 // iPhone 13 Pro等中等屏幕 - 减少间距以缩小组件
        } else {
            return 12 // iPhone 13 Pro Max等大屏标准间距
        }
    }
    

    
    private func adaptiveNoGoalCalorieTitleFont() -> Font {
        if UIScreen.main.bounds.height < 700 {
            return .system(size: 24, weight: .bold) // 小屏减小字体
        } else {
            return .system(size: 28, weight: .bold) // 大屏标准字体
        }
    }
    
    private func adaptiveNoGoalCaptionFont() -> Font {
        if UIScreen.main.bounds.height < 700 {
            return .caption2 // 小屏更小字体
        } else {
            return .caption // 大屏标准字体
        }
    }
    
    private func adaptiveNoGoalProgressCircleSize() -> CGFloat {
        if UIScreen.main.bounds.height < 700 {
            return 45 // 小屏较小圆圈
        } else if UIScreen.main.bounds.height < 850 && UIScreen.main.bounds.width < 430 {
            return 45 // iPhone 13 Pro等中等屏幕 - 减小圆圈以缩小组件
        } else {
            return 50 // 大屏标准大小
        }
    }
    
    private func adaptiveNoGoalProgressTextFont() -> Font {
        if UIScreen.main.bounds.height < 700 {
            return .system(.caption2, design: .rounded) // 小屏更小字体
        } else {
            return .system(.caption, design: .rounded) // 大屏标准字体
        }
    }
    
    private func adaptiveRecommendedSpacing() -> CGFloat {
        if UIScreen.main.bounds.height < 700 {
            return 8 // 小屏减少间距
        } else {
            return 10 // 大屏标准间距
        }
    }
    
    private func adaptiveRecommendedTitleFont() -> Font {
        if UIScreen.main.bounds.height < 700 {
            return .caption // 小屏使用较小字体
        } else {
            return .subheadline // 大屏使用标准字体
        }
    }
    
    private func adaptiveActivityCardSpacing() -> CGFloat {
        let screenWidth = UIScreen.main.bounds.width
        
        if screenWidth < 375 {
            return 4 // iPhone 12 mini等小屏进一步减少间距
        } else if screenWidth < 390 {
            return 6 // iPhone 12/13/14 标准尺寸
        } else if screenWidth < 430 {
            return 6 // iPhone 13 Pro/14 Pro等 - 减少间距以缩小组件
        } else {
            return 10 // iPhone 13 Pro Max/14 Pro Max等大屏
        }
    }
    
    private func adaptiveButtonFont() -> Font {
        if UIScreen.main.bounds.height < 700 {
            return .subheadline // 小屏使用较小字体
        } else {
            return .headline // 大屏使用标准字体
        }
    }
    
    private func adaptiveButtonVerticalPadding() -> CGFloat {
        if UIScreen.main.bounds.height < 700 {
            return 10 // 小屏减少按钮内边距
        } else {
            return 12 // 大屏标准内边距
        }
    }
    
    private func progressView() -> some View {
        if isCheckingPlan {
            return AnyView(
                HStack {
                    Spacer()
                    ProgressView()
                    Spacer()
                }
                .padding()
            )
        } else if userHasPlan {
            return AnyView(
                HStack(spacing: 30) {
                    // 圆形进度指示器
                    ZStack {
                        Circle()
                            .stroke(Color.green.opacity(0.2), lineWidth: 8)
                            .frame(width: 80, height: 80)
                        
                        Circle()
                            .trim(from: 0, to: CGFloat(weeklyProgress))
                            .stroke(Color.green, style: StrokeStyle(lineWidth: 8, lineCap: .round))
                            .frame(width: 80, height: 80)
                            .rotationEffect(.degrees(-90))
                        
                        Text("\(Int(weeklyProgress * 100))%")
                            .font(.system(.title3, design: .rounded))
                            .fontWeight(.bold)
                    }
                    
                    Spacer()
                    
                    // 当前周数
                    Text("Week \(currentWeek) of \(userData.goalTimelineWeeks)")
                        .font(.subheadline)
                        .foregroundColor(.secondary)
                }
                .padding(.horizontal)
            )
        } else {
            return AnyView(EmptyView())
        }
    }
    
    private func weightProgressView() -> some View {
        if isCheckingPlan {
            return AnyView(
                HStack {
                    Spacer()
                    ProgressView()
                    Spacer()
                }
                .padding()
            )
        } else if userHasPlan {
            // 原来的进度视图
            return AnyView(
                VStack(spacing: 15) {
                    // 起始、当前、目标体重
                    HStack {
                        weightInfoView(title: "Start", value: userData.startWeight, highlight: false)
                        weightInfoView(title: "Current", value: userData.currentManagementWeight, highlight: true)
                        weightInfoView(title: "Goal", value: userData.goalWeight, highlight: false)
                    }
                    
                    // 进度条
                    GeometryReader { geometry in
                        ZStack(alignment: .leading) {
                            // 背景条
                            Rectangle()
                                .foregroundColor(Color.green.opacity(0.2))
                                .frame(height: 8)
                                .cornerRadius(4)
                            
                            // 进度条
                            Rectangle()
                                .foregroundColor(.green)
                                .frame(width: calculateProgressBarWidth(totalWidth: geometry.size.width), height: 8)
                                .cornerRadius(4)
                        }
                    }
                    .frame(height: 8)
                    
                    // 进度信息
                    HStack {
                        let lostOrGained = userData.startWeight - userData.currentManagementWeight
                        Text("\(String(format: "%.0f", abs(lostOrGained))) \(userData.weightUnit) \(lostOrGained < 0 ? "lost" : "gained")")
                            .font(.caption)
                            .foregroundColor(lostOrGained < 0 ? .green : .orange)
                        
                        Spacer()
                        
                        let toGo = abs(userData.weightToGoal())
                        Text("\(String(format: "%.0f", toGo)) \(userData.weightUnit) to go")
                            .font(.caption)
                            .foregroundColor(.secondary)
                    }
                    
                    // 鼓励信息
                    HStack {
                        Image(systemName: "face.smiling")
                            .foregroundColor(.green)
                        
                        Text("Amazing progress! You're \(Int(weeklyProgress * 100))% there!")
                            .font(.system(size: UIFont.preferredFont(forTextStyle: .caption1).pointSize + 2))
                            .foregroundColor(.green)
                    }
                }
            )
        } else {
            // 显示设置目标的视图（图二的样式）
            return AnyView(
                VStack(spacing: adaptivePlanNoGoalSpacing()) {
                    // Set Goal按钮
                    Button(action: {
                        navigateToWeightGoalEdit()
                    }) {
                        ZStack {
                            Circle()
                                .stroke(Color.green, lineWidth: 2)
                                .frame(width: adaptiveSetGoalCircleSize(), height: adaptiveSetGoalCircleSize())
                            
                            Text("Set Goal")
                                .font(adaptiveSetGoalButtonFont())
                                .foregroundColor(.green)
                        }
                    }
                    .padding(.top, adaptiveSetGoalTopPadding())
                    
                    // Current Weight
                    VStack(spacing: adaptiveCurrentWeightSpacing()) {
                        Text("Current")
                            .font(adaptiveCurrentWeightLabelFont())
                            .foregroundColor(.secondary)
                        
                        HStack(alignment: .firstTextBaseline) {
                            Text("? ? ?")
                                .font(adaptiveCurrentWeightValueFont())
                                .fontWeight(.semibold)
                            
                            Text(userData.weightUnit)
                                .font(adaptiveCurrentWeightUnitFont())
                                .foregroundColor(.secondary)
                                .padding(.leading, 5)
                        }
                    }
                    
                    // 提示文本
                    Text("Set your goal weight to track progress")
                        .font(adaptiveSetGoalHintFont())
                        .foregroundColor(.secondary)
                        .padding(.vertical, adaptiveHintTextPadding())
                        .multilineTextAlignment(.center)
                        .lineLimit(2)
                }
                .frame(maxWidth: .infinity)
                .padding(adaptivePlanContainerPadding())
                .background(Color(red: 0.98, green: 0.98, blue: 0.98))
                .cornerRadius(12)
                .padding(.horizontal)
            )
        }
    }
    
    private func weightInfoView(title: String, value: Double, highlight: Bool) -> some View {
        VStack(alignment: .center, spacing: 5) {
            Text(title)
                .font(.caption)
                .foregroundColor(.secondary)
            
            HStack(alignment: .bottom, spacing: 2) {
                Text(String(format: "%.0f", userData.getWeightValue(value)))
                    .font(.headline)
                    .foregroundColor(highlight ? .green : .primary)
                
                Text(userData.weightUnit)
                    .font(.caption)
                    .foregroundColor(.secondary)
            }
        }
        .frame(maxWidth: .infinity)
    }
    
    // 计算周体重图表的Y轴范围
    private var yAxisRange: (min: Double, max: Double) {
        if weeklyWeightData.isEmpty {
            // 默认范围围绕当前体重
            let baseWeight = userData.getWeightValue(userData.morningWeight)
            return (baseWeight - 3, baseWeight + 3)
        }
        
        let weights = weeklyWeightData.map { $0.weight }
        
        // 如果只有一个点，创建一个合理的范围
        if weights.count == 1, let singleWeight = weights.first {
            return (singleWeight - 3, singleWeight + 3)
        }
        
        let minWeight = weights.min() ?? userData.getWeightValue(userData.morningWeight) - 3
        let maxWeight = weights.max() ?? userData.getWeightValue(userData.morningWeight) + 3
        
        // 计算范围并添加一些边距
        let range = maxWeight - minWeight
        let padding = max(1.0, range * 0.1) // 至少1单位或范围的10%
        
        return (minWeight - padding, maxWeight + padding)
    }
    
    private func weightChartView() -> some View {
        if isCheckingPlan {
            return AnyView(
                HStack {
                    Spacer()
                    ProgressView()
                    Spacer()
                }
                .padding()
            )
        } else if userHasPlan {
            return AnyView(
                VStack(alignment: .leading, spacing: 15) {
                    // 绘制折线图
                    if weeklyWeightData.isEmpty {
                        if isLoading {
                            HStack {
                                Spacer()
                                ProgressView()
                                Spacer()
                            }
                            .frame(height: 150)
                        } else {
                            Text("No weight data available")
                                .font(.caption)
                                .foregroundColor(.secondary)
                                .frame(maxWidth: .infinity, alignment: .center)
                                .frame(height: 150)
                        }
                    } else {
                        Chart {
                            ForEach(weeklyWeightData) { dataPoint in
                                LineMark(
                                    x: .value("Week", dataPoint.week),
                                    y: .value("Weight", dataPoint.weight)
                                )
                                .foregroundStyle(Color.green)
                                .lineStyle(StrokeStyle(lineWidth: 2))
                                .interpolationMethod(.catmullRom)
                                
                                PointMark(
                                    x: .value("Week", dataPoint.week),
                                    y: .value("Weight", dataPoint.weight)
                                )
                                .foregroundStyle(Color.green)
                                .symbolSize(40)
                                .annotation(position: .top) {
                                    Text(String(format: "%.1f", dataPoint.weight))
                                        .font(.caption2)
                                        .foregroundColor(.green)
                                        .fontWeight(.medium)
                                }
                            }
                        }
                        .chartYScale(domain: yAxisRange.min...yAxisRange.max)
                        .frame(height: 150)
                        
                        // 添加单位显示
                        HStack {
                            Spacer()
                            Text("Weight in \(userData.weightUnit)")
                                .font(.caption)
                                .foregroundColor(.secondary)
                        }
                        .padding(.top, 5)
                    }
                }
            )
        } else {
            return AnyView(EmptyView())
        }
    }
    
    private func nutritionView() -> some View {
        VStack(alignment: .leading, spacing: 15) {
            if isCheckingPlan || isLoadingWeeklyNutrition {
                HStack {
                    Spacer()
                    ProgressView()
                    Spacer()
                }
                .padding()
            } else if userHasPlan {
                // 有计划时根据订阅状态显示不同UI
                if userData.isPremium {
                    // Premium用户显示完整营养数据
                    VStack(alignment: .leading, spacing: 15) {
                        Text("This Week's Nutrition")
                            .font(.headline)
                            .padding(.horizontal)
                        
                        HStack(alignment: .top, spacing: 15) {
                            nutritionCircleView(
                                title: "Protein",
                                value: weeklyAPIProtein,
                                goal: weeklyProteinGoal,
                                unit: "g"
                            )
                            .padding(12)
                            .background(Color.white)
                            .cornerRadius(12)
                            .shadow(color: Color.black.opacity(0.1), radius: 4, x: 0, y: 2)
                            
                            nutritionCircleView(
                                title: "Fat",
                                value: weeklyAPIFat,
                                goal: weeklyFatGoal,
                                unit: "g"
                            )
                            .padding(12)
                            .background(Color.white)
                            .cornerRadius(12)
                            .shadow(color: Color.black.opacity(0.1), radius: 4, x: 0, y: 2)
                            
                            nutritionCircleView(
                                title: "Calories",
                                value: weeklyAPICalories,
                                goal: weeklyCalorieGoal,
                                unit: "kcal"
                            )
                            .padding(12)
                            .background(Color.white)
                            .cornerRadius(12)
                            .shadow(color: Color.black.opacity(0.1), radius: 4, x: 0, y: 2)
                        }
                        .padding(.horizontal)
                    }
                } else {
                    // 非Premium用户显示锁定的营养UI
                    WeeklyNutritionPremiumView()
                        .environmentObject(userData)
                }
            } else {
                // 显示未设置目标时的UI
                VStack(alignment: .leading, spacing: adaptivePlanNoGoalSpacing()) {
                    Text("This Week's Nutrition")
                        .font(adaptiveNutritionTitleFont())
                    
                    VStack(spacing: adaptivePlanNoGoalSpacing()) {
                        HStack(spacing: adaptiveNutritionCircleSpacing()) {
                            placeholderNutritionCircle(title: "Protein")
                            placeholderNutritionCircle(title: "Fat")
                            placeholderNutritionCircle(title: "Calories")
                        }
                        
                        Text("Set your goal weight to track progress")
                            .font(adaptiveSetGoalHintFont())
                            .foregroundColor(.secondary)
                            .frame(maxWidth: .infinity, alignment: .center)
                            .padding(.vertical, adaptiveHintTextPadding())
                            .multilineTextAlignment(.center)
                            .lineLimit(2)
                    }
                    .padding(adaptivePlanContainerPadding())
                    .background(Color.white)
                    .cornerRadius(12)
                }
                .padding(.horizontal)
            }
        }
    }
    
    // 添加一个营养圆环的占位视图
    private func placeholderNutritionCircle(title: String) -> some View {
        VStack(spacing: adaptiveNutritionCircleContentSpacing()) {
            ZStack {
                Circle()
                    .stroke(Color.gray.opacity(0.2), lineWidth: adaptiveNutritionCircleLineWidth())
                    .frame(width: adaptiveNutritionCircleSize(), height: adaptiveNutritionCircleSize())
                
                Text("?")
                    .font(adaptiveNutritionCircleTextFont())
                    .fontWeight(.medium)
                    .foregroundColor(.gray)
            }
            
            Text(title)
                .font(adaptiveNutritionCircleTitleFont())
                .foregroundColor(.secondary)
        }
        .frame(maxWidth: .infinity)
    }
    
    private func nutritionCircleView(title: String, value: Int, goal: Int, unit: String) -> some View {
        // 安全计算进度和百分比
        let safeGoal = max(1, goal) // 确保目标至少为1，避免除零
        let progress = min(Double(value) / Double(safeGoal), 1.0) // 限制进度最大为1.0
        let percentage = Int(progress * 100)
        
        return VStack(spacing: 8) {
            ZStack {
                Circle()
                    .stroke(Color.green.opacity(0.2), lineWidth: 4)
                    .frame(width: 70, height: 70)
                
                Circle()
                    .trim(from: 0, to: CGFloat(progress))
                    .stroke(Color.green, style: StrokeStyle(lineWidth: 4, lineCap: .round))
                    .frame(width: 70, height: 70)
                    .rotationEffect(.degrees(-90))
                
                Text("\(percentage)%")
                    .font(.system(.body, design: .rounded))
                    .fontWeight(.medium)
                    .lineLimit(1)
                    .minimumScaleFactor(0.8)
            }
            .frame(height: 70)
            
            Text(title)
                .font(.caption)
                .foregroundColor(.secondary)
                .lineLimit(1)
                .frame(height: 16)
            
            Text("\(value)/\(goal)\(unit)")
                .font(.caption)
                .foregroundColor(.secondary)
                .lineLimit(1)
                .minimumScaleFactor(0.8)
                .frame(height: 16)
                .multilineTextAlignment(.center)
        }
        .frame(maxWidth: .infinity, minHeight: 120, maxHeight: 120)
        .fixedSize(horizontal: false, vertical: true)
    }
    
    private func exerciseCaloriesView() -> some View {
        VStack(alignment: .leading, spacing: 15) {
            if isCheckingPlan {
                HStack {
                    Spacer()
                    ProgressView()
                    Spacer()
                }
                .padding()
            } else if userHasPlan {
                // 有计划时根据订阅状态显示不同UI
                if userData.isPremium {
                    // Premium用户显示完整的运动卡路里数据
                    VStack(alignment: .leading, spacing: 15) {
                        HStack {
                            Text("This Week's Exercise Calories")
                                .font(.headline)
                            
                            Spacer()
                            
                            // 添加"more"按钮
                            Button(action: {
                                navigateToExerciseHistory()
                            }) {
                                HStack(spacing: 4) {
                                    Text("more")
                                        .font(.subheadline)
                                        .foregroundColor(.gray)
                                    
                                    Image(systemName: "chevron.right")
                                        .font(.caption)
                                        .foregroundColor(.gray)
                                }
                            }
                        }
                        .padding(.horizontal)
                        
                        // 原有的运动卡路里详情视图
                        exerciseCaloriesContentView()
                            .padding()
                            .background(Color.white)
                            .cornerRadius(12)
                            .shadow(color: Color.black.opacity(0.1), radius: 4, x: 0, y: 2)
                            .padding(.horizontal)
                    }
                } else {
                    // 非Premium用户显示锁定的运动卡路里UI
                    WeeklyExerciseCaloriesPremiumView()
                        .environmentObject(userData)
                }
            } else {
                // 未设置目标时的占位符
                VStack(alignment: .leading, spacing: adaptivePlanNoGoalSpacing()) {
                    Text("This Week's Exercise Calories")
                        .font(adaptiveNutritionTitleFont())
                    
                    VStack(spacing: adaptivePlanNoGoalSpacing()) {
                        // 卡路里统计的占位符
                        HStack(alignment: .center, spacing: adaptiveNoGoalCalorieSpacing()) {
                            VStack(alignment: .leading, spacing: adaptiveNoGoalCalorieValueSpacing()) {
                                HStack(alignment: .firstTextBaseline, spacing: 4) {
                                    Text("-- / ---")
                                        .font(adaptiveNoGoalCalorieTitleFont())
                                        .foregroundColor(.gray)
                                }
                                
                                Text("calories burned")
                                    .font(adaptiveNoGoalCaptionFont())
                                    .foregroundColor(.secondary)
                            }
                            
                            Spacer()
                            
                            // 圆形进度指示器占位符
                            ZStack {
                                Circle()
                                    .stroke(Color.gray.opacity(0.2), lineWidth: adaptiveNoGoalProgressCircleLineWidth())
                                    .frame(width: adaptiveNoGoalProgressCircleSize(), height: adaptiveNoGoalProgressCircleSize())
                                
                                Text("???")
                                    .font(adaptiveNoGoalProgressTextFont())
                                    .fontWeight(.medium)
                                    .foregroundColor(.gray)
                            }
                        }
                        
                        // 分隔线
                        Divider()
                            .padding(.horizontal, 0)
                    }
                    .padding(adaptivePlanContainerPadding())
                    .background(Color.white)
                    .cornerRadius(12)
                }
                .padding(.horizontal)
            }
        }
    }
    
    private func exerciseCaloriesContentView() -> some View {
        // 安全计算运动卡路里进度和百分比
        let safeGoal = max(1, exerciseCalorieGoal) // 确保目标至少为1，避免除零
        let progress = min(Double(exerciseCalories) / Double(safeGoal), 1.0) // 限制进度最大为1.0
        let percentage = Int(progress * 100)
        
        return VStack(spacing: 15) {
            // 卡路里统计
            HStack(alignment: .firstTextBaseline) {
                VStack(alignment: .leading, spacing: 5) {
                    HStack(alignment: .firstTextBaseline, spacing: 4) {
                        Text("\(exerciseCalories)")
                            .font(.system(size: 28, weight: .bold))
                        
                        Text("/ \(exerciseCalorieGoal)")
                            .foregroundColor(.secondary)
                    }
                    
                    Text("calories burned")
                        .font(.caption)
                        .foregroundColor(.secondary)
                }
            
                Spacer()
                
                // 圆形进度指示器
                ZStack {
                    Circle()
                        .stroke(Color.green.opacity(0.2), lineWidth: 4)
                        .frame(width: 50, height: 50)
                    
                    Circle()
                        .trim(from: 0, to: CGFloat(progress))
                        .stroke(Color.green, style: StrokeStyle(lineWidth: 4, lineCap: .round))
                        .frame(width: 50, height: 50)
                        .rotationEffect(.degrees(-90))
                    
                    Text("\(percentage)%")
                        .font(.system(.caption, design: .rounded))
                        .fontWeight(.medium)
                }
            }
            
            
            // 分隔线
            Divider()
            
            // 运动记录列表
            exerciseListView()
            
            // 添加运动按钮
            Button(action: {
                showAddExercise = true
            }) {
                HStack {
                    Spacer()
                    Image(systemName: "plus")
                    Text("Add Exercise")
                    Spacer()
                }
                .padding(.vertical, 12)
                .background(Color.green)
                .foregroundColor(.white)
                .cornerRadius(20)
            }
            .padding(.top, 5)
        }
    }
    
    private func exerciseListView() -> some View {
        Group {
            if isLoading {
                HStack {
                    Spacer()
                    ProgressView()
                    Spacer()
                }
            } else if recentExercises.isEmpty {
                Text("No exercise recorded this week")
                    .font(.caption)
                    .foregroundColor(.secondary)
                    .frame(maxWidth: .infinity, alignment: .center)
                    .padding(.vertical, 10)
            } else {
                VStack(spacing: 0) {
                    ForEach(recentExercises.prefix(2)) { exercise in
                        exerciseRowView(exercise: exercise)
                        
                        if exercise.id != recentExercises.prefix(2).last?.id {
                            Divider()
                        }
                    }
                    
                    // 删除更多链接按钮，只在标题栏保留一个more按钮
                }
            }
        }
    }
    
    private func exerciseRowView(exercise: ExerciseRecord) -> some View {
        HStack {
            // 运动图标
            ZStack {
                Circle()
                    .fill(Color.green.opacity(0.2))
                    .frame(width: 40, height: 40)
                
                Image(systemName: iconForExerciseType(exercise.type))
                    .foregroundColor(.green)
            }
            
            VStack(alignment: .leading, spacing: 3) {
                Text(exercise.type)
                    .font(.subheadline)
                
                HStack {
                    Image(systemName: "clock")
                        .font(.caption2)
                    
                    Text("\(exercise.duration) min")
                        .font(.caption)
                }
                .foregroundColor(.secondary)
            }
            
            Spacer()
            
            VStack(alignment: .trailing, spacing: 3) {
                Text("\(exercise.calories) kcal")
                    .font(.subheadline)
                    .foregroundColor(.green)
                
                // 使用API返回的时间或默认时间
                Text(exercise.timeString)
                    .font(.caption)
                    .foregroundColor(.secondary)
            }
        }
        .padding(.vertical, 5)
    }
    
    private func recommendedActivitiesView() -> some View {
        VStack(alignment: .leading, spacing: 15) {
            if isCheckingPlan {
                HStack {
                    Spacer()
                    ProgressView()
                    Spacer()
                }
                .padding()
            } else if userHasPlan {
                // 有计划时根据订阅状态显示不同UI
                if userData.isPremium {
                    // Premium用户显示完整推荐活动
                    VStack(alignment: .leading, spacing: 15) {
                        Text("Recommended Activities")
                            .font(.headline)
                            .padding(.horizontal)
                        
                        ScrollView(.horizontal, showsIndicators: false) {
                            HStack(spacing: 15) {
                                ForEach(recommendedActivities) { activity in
                                    RecommendedActivityCard(
                                        icon: activity.icon,
                                        title: activity.type,
                                        duration: activity.duration,
                                        calories: activity.calories
                                    )
                                }
                            }
                            .padding(.horizontal, 20)
                            .padding(.vertical, 8)
                        }
                    }
                } else {
                    // 非Premium用户显示锁定的推荐活动UI
                    RecommendedActivitiesPremiumView()
                        .environmentObject(userData)
                }
            } else {
                // 未设置目标时显示简化版UI，如图所示
                VStack(alignment: .leading, spacing: adaptivePlanNoGoalSpacing()) {
                    Text("Recommended Activities")
                        .font(adaptiveNutritionTitleFont())
                    
                    VStack(spacing: adaptivePlanNoGoalSpacing()) {
                        // 显示三个分离的运动卡片
                        HStack(spacing: adaptiveActivityCardSpacing()) {
                            Spacer()
                            exerciseCardView(iconName: "figure.walk", title: "Walking")
                            exerciseCardView(iconName: "figure.pool.swim", title: "Swimming")
                            exerciseCardView(iconName: "bicycle", title: "Cycling")
                            Spacer()
                        }
                        .padding(.vertical, adaptiveExerciseCardGroupVerticalPadding())
                        
                        // Set Goal按钮
                        Button(action: {
                            navigateToWeightGoalEdit()
                        }) {
                            Text("Set Goal")
                                .font(adaptiveButtonFont())
                                .foregroundColor(.white)
                                .frame(maxWidth: .infinity)
                                .padding(.vertical, adaptiveButtonVerticalPadding())
                                .background(Color.green)
                                .cornerRadius(adaptiveButtonCornerRadius())
                        }
                        .padding(.top, adaptiveButtonTopPadding())
                    }
                    .padding(adaptivePlanContainerPadding())
                    .background(Color.white)
                    .cornerRadius(12)
                }
                .padding(.horizontal)
            }
        }
    }
    
    // 创建运动卡片的辅助视图
    private func exerciseCardView(iconName: String, title: String) -> some View {
        VStack(alignment: .center, spacing: adaptiveExerciseCardSpacing()) {
            Image(systemName: iconName)
                .font(adaptiveExerciseCardIconFont())
                .foregroundColor(.gray)
                .frame(height: adaptiveExerciseCardIconHeight())
            
            Text(title)
                .font(adaptiveExerciseCardTitleFont())
                .foregroundColor(.gray)
                .lineLimit(1)
                .frame(height: adaptiveExerciseCardTitleHeight())
        }
        .padding(.vertical, adaptiveExerciseCardVerticalPadding())
        .frame(width: adaptiveExerciseCardWidth(), height: adaptiveExerciseCardHeight())
        .background(Color.gray.opacity(0.08))
        .cornerRadius(12)
    }
    
    private func dailyCheckInView() -> some View {
        VStack(alignment: .leading, spacing: adaptivePlanNoGoalSpacing()) {
            Text("Daily Check-in")
                .font(adaptiveNutritionTitleFont())
                .padding(.horizontal)
            
            VStack(spacing: adaptiveCheckInContentSpacing()) {
                // 显示两周的日期（本周+上周，共14天）
                ScrollViewReader { proxy in
                    ScrollView(.horizontal, showsIndicators: false) {
                        HStack(spacing: 15) {
                            ForEach(Array(getTwoWeeksDates().enumerated()), id: \.offset) { index, date in
                                let calendar = Calendar.current
                                let isToday = calendar.isDate(date, inSameDayAs: Date())
                                let isPastDate = date < calendar.startOfDay(for: Date())
                                let canSelect = isToday || isPastDate
                                let isSelected = calendar.isDate(date, inSameDayAs: selectedCheckInDate)
                                
                                Button(action: {
                                    if canSelect {
                                        print("📅 [Plan] 用户选择日期: \(date)")
                                        selectedCheckInDate = date
                                        // 强制加载选中日期的数据
                                        loadCheckInDataForDate(date)
                                        
                                        // 同时预加载相邻日期的状态
                                        preloadAdjacentDatesStatus(around: date)
                                    }
                                }) {
                        VStack(spacing: adaptiveWeekdaySpacing()) {
                                        Text(getWeekdayShortName(for: date))
                                .font(adaptiveWeekdayFont())
                                            .foregroundColor(canSelect ? .secondary : .gray.opacity(0.5))
                            
                            ZStack {
                                            // 根据打卡完成状态显示颜色
                                            let isFullyCheckedIn = isDateFullyCheckedIn(date)
                                            let circleSize = isSelected ? adaptiveWeekdayCircleSize() * 1.2 : adaptiveWeekdayCircleSize()
                                            
                                Circle()
                                                .fill(isFullyCheckedIn ? Color.green : Color(red: 0.98, green: 0.98, blue: 0.98))
                                                .frame(width: circleSize, height: circleSize)
                                
                                            Text("\(calendar.component(.day, from: date))")
                                                .font(isSelected ? adaptiveWeekdayNumberFont().weight(.bold) : adaptiveWeekdayNumberFont())
                                                .foregroundColor(canSelect ? (isFullyCheckedIn ? .white : .primary) : .gray.opacity(0.5))
                    }
                                    }
                                }
                                .disabled(!canSelect)
                                .id(index) // 使用索引作为ID用于滚动定位
                            }
                }
                        .padding(.horizontal)
                    }
                    .onAppear {
                        // 页面出现时直接滚动到今天的位置
                        DispatchQueue.main.asyncAfter(deadline: .now() + 0.1) {
                            let calendar = Calendar.current
                            let today = Date()
                            let dates = self.getTwoWeeksDates()
                            
                            // 确保selectedCheckInDate已经设置为今天
                            if !calendar.isDate(self.selectedCheckInDate, inSameDayAs: today) {
                                self.selectedCheckInDate = today
                            }
                            
                            // 找到今天在日期数组中的索引
                            if let todayIndex = dates.firstIndex(where: { calendar.isDate($0, inSameDayAs: today) }) {
                                withAnimation(.easeInOut(duration: 0.5)) {
                                    // 直接滚动到今天的位置，使用center锚点确保今天显示在视图中心
                                    proxy.scrollTo(todayIndex, anchor: .center)
                                }
                                print("📅 自动滚动到今天的位置，索引: \(todayIndex)")
                            } else {
                                print("📅 未找到今天的日期在数组中")
                            }
                        }
                    }
                    .onChange(of: selectedCheckInDate) { oldValue, newDate in
                        // 当选中日期变化时，滚动到该日期
                        let calendar = Calendar.current
                        let today = Date()
                        let isToday = calendar.isDate(newDate, inSameDayAs: today)
                        
                        withAnimation(.easeInOut(duration: 0.3)) {
                            if isToday {
                                // 如果选中的是今天，滚动到末尾
                                proxy.scrollTo(newDate, anchor: .trailing)
                            } else {
                                // 如果选中的是其他日期，居中显示
                                proxy.scrollTo(newDate, anchor: .center)
                            }
                        }
                    }
                }
                .padding(.vertical, adaptiveWeekdayVerticalPadding())
                
                // 选中日期的打卡任务详情
                selectedDateCheckInDetailView()
            }
            .padding(adaptivePlanContainerPadding())
            .background(Color.white)
            .cornerRadius(12)
            .shadow(color: Color.black.opacity(0.1), radius: 4, x: 0, y: 2)
            .padding(.horizontal)
        }
    }
    
    // 获取两周的日期数组（上周+本周）
    private func getTwoWeeksDates() -> [Date] {
        let calendar = Calendar.current
        let today = Date()
        
        // 获取本周一
        let weekday = calendar.component(.weekday, from: today)
        let adjustedWeekday = weekday == 1 ? 7 : weekday - 1
        let daysToSubtract = adjustedWeekday - 1
        guard let thisWeekMonday = calendar.date(byAdding: .day, value: -daysToSubtract, to: today) else { return [] }
    
        // 获取上周一
        guard let lastWeekMonday = calendar.date(byAdding: .day, value: -7, to: thisWeekMonday) else { return [] }
        
        // 生成14天的日期数组
        var dates: [Date] = []
        for i in 0..<14 {
            if let date = calendar.date(byAdding: .day, value: i, to: lastWeekMonday) {
                dates.append(date)
            }
        }
        
        return dates
    }
    
    // 获取日期的星期几简称
    private func getWeekdayShortName(for date: Date) -> String {
        let formatter = DateFormatter()
        formatter.locale = Locale(identifier: "en_US")
        formatter.dateFormat = "EEE"
        return formatter.string(from: date)
    }
    
    // 选中日期的打卡详情视图
    private func selectedDateCheckInDetailView() -> some View {
        VStack(spacing: adaptiveTaskSpacing()) {
            if dailyTasks.isEmpty {
                HStack {
                    Spacer()
                    if isLoadingCheckInData {
                        ProgressView()
                            .scaleEffect(0.8)
                    } else {
                        Text("No check-in data")
                            .font(.caption)
                            .foregroundColor(.secondary)
                    }
                    Spacer()
                }
                .padding(.vertical, 20)
            } else {
                // 显示打卡任务
                ForEach(dailyTasks) { task in
                    checkInTaskRow(task: task)
                }
            }
        }
        .padding(adaptiveTaskContainerPadding())
        .background(allTasksCompleted() ? Color.green.opacity(0.1) : Color.white)
        .cornerRadius(adaptiveTaskContainerCornerRadius())
    }
    
    // 打卡任务行视图
    private func checkInTaskRow(task: DailyTask) -> some View {
        HStack {
            Text(task.name)
                .font(.subheadline)
            
            Spacer()
            
        let calendar = Calendar.current
            let isToday = calendar.isDate(selectedCheckInDate, inSameDayAs: Date())
            let isPastDate = selectedCheckInDate < calendar.startOfDay(for: Date())
            
            if task.isCompleted {
                // 已完成显示绿色对勾
                Image(systemName: "checkmark.circle.fill")
                    .foregroundColor(.green)
                    .font(.title3)
            } else {
                // 未完成的处理
                if isToday {
                    // 当天未完成显示灰圈
                    Image(systemName: "circle")
                        .foregroundColor(.gray)
                        .font(.title3)
                } else if isPastDate {
                    // 过去日期未完成不显示任何标记
                    EmptyView()
                }
                // 未来日期不会显示（因为canSelect限制）
            }
        }
        .padding(.vertical, 5)
    }
    
    // 判断所有任务是否完成
    private func allTasksCompleted() -> Bool {
        return !dailyTasks.isEmpty && dailyTasks.allSatisfy { $0.isCompleted }
    }
    
    // 为指定日期加载打卡数据（优化版本）
    private func loadCheckInDataForDate(_ date: Date) {
        let calendar = Calendar.current
        let isToday = calendar.isDate(date, inSameDayAs: Date())
        
        // 立即显示加载状态，但不阻塞UI
        isLoadingCheckInData = true
        
        let dateFormatter = DateFormatter()
        dateFormatter.dateFormat = "yyyyMMdd"
        let dateKey = dateFormatter.string(from: date)
        
        print("📅 [Plan] 开始加载日期 \(dateKey) 的打卡数据，是否为今天: \(isToday)")
        
        // 立即显示基础任务列表，避免显示"No check-in data"
        self.dailyTasks = LogsType.allCases.map { logType in
            DailyTask(
                id: UUID(),
                name: logType.displayName,
                isCompleted: false // 先显示未完成状态，等API更新
            )
        }
        
        if isToday {
            // 当天数据高优先级同步加载
            print("📅 [Plan] 使用当天数据加载方法")
            loadTodayCheckInDataSync()
        } else {
            // 非当天数据低优先级异步加载
            print("📅 [Plan] 使用非当天数据加载方法")
            DispatchQueue.global(qos: .utility).async {
                self.loadNonTodayCheckInData(for: date, dateKey: dateKey)
            }
        }
    }
    
    // 加载非当天的打卡数据
    private func loadNonTodayCheckInData(for date: Date, dateKey: String) {
        // 强制重新加载数据，不使用缓存，确保数据准确性
        print("📅 强制重新加载日期 \(dateKey) 的打卡数据")
        
        // 调用CheckInService获取指定日期的打卡状态
        checkInService.getCurrentCheckInStatus(for: date, userData: userData) { status in
            DispatchQueue.main.async {
                // 确保我们还在处理相同的日期，避免数据混乱
                let currentDateFormatter = DateFormatter()
                currentDateFormatter.dateFormat = "yyyyMMdd"
                let currentDateKey = currentDateFormatter.string(from: self.selectedCheckInDate)
                guard currentDateKey == dateKey else {
                    print("📅 日期已变更，跳过过期的数据更新")
                    return
                }
                
                // 将CheckInTask转换为DailyTask
                self.dailyTasks = status.tasks.map { task in
                    DailyTask(
                        id: UUID(),
                        name: task.displayName,
                        isCompleted: task.isCompleted
                    )
                }
                
                // 更新打卡状态到userData
                let allCompleted = status.allCompleted
                self.userData.updateCheckInStatus(for: date, isCompleted: allCompleted)
                
                // 更新缓存状态
                self.dateCheckInStatus[dateKey] = allCompleted
                
                self.isLoadingCheckInData = false
                print("✅ 成功加载日期 \(dateKey) 的打卡数据: \(status.completedCount)/\(status.totalCount)")
            }
        }
    }
    
    // 使用缓存状态更新任务列表（已废弃，现在强制重新加载）
    private func updateTasksWithCachedStatus(_ isCompleted: Bool) {
        // 这个函数已废弃，因为我们现在强制重新加载所有数据
        // 保留函数定义以避免编译错误
        print("⚠️ [Plan] updateTasksWithCachedStatus已废弃，不应该被调用")
    }
    
    private func streakView() -> some View {
        // 只有在有目标时才可能显示卡片
        let hasGoal = userData.hasPlan
        
        if !hasGoal {
            // 未设置目标时显示"Start Your Journey"
            return AnyView(
                StreakCardView(
                    streakDays: 0,
                    hasGoal: false,
                    daysSinceLastCheckIn: 0
                )
            )
        } else {
            // 有目标时，只在特定条件下显示卡片
            
            // 检查是否符合显示条件
            let shouldShowCard = shouldDisplayStreakCard()
            
            if shouldShowCard {
                return AnyView(
                    StreakCardView(
                        streakDays: realStreakDays,
                        hasGoal: true,
                        daysSinceLastCheckIn: realDaysSinceLastCheckIn,
                        consecutiveNonCheckInDays: consecutiveNonCheckInDays
                    )
                )
            } else {
                // 不符合显示条件时不显示任何卡片
                return AnyView(EmptyView())
            }
        }
    }
    
    // 判断是否应该显示Streak卡片
    private func shouldDisplayStreakCard() -> Bool {
        // 首先检查是否今天创建的计划，如果是则不显示任何激励卡片
        if let planCreatedTime = userData.planCreatedTime {
            let calendar = Calendar.current
            let today = Date()
            let daysSinceCreation = calendar.dateComponents([.day], from: calendar.startOfDay(for: planCreatedTime), to: calendar.startOfDay(for: today)).day ?? 0
            
            if daysSinceCreation == 0 {
                // 今天创建计划，不显示任何激励卡片
                return false
            }
        }
        
        // 连续N天没打卡（N<5且N>0）显示Back on Track
        if consecutiveNonCheckInDays > 0 && consecutiveNonCheckInDays < 5 {
            return true
        }
        
        // 连续N天没打卡（N≥5）显示Don't Give Up
        if consecutiveNonCheckInDays >= 5 {
            return true
        }
        
        // 图一：连续打卡7天时显示（需要有目标）
        if userData.hasPlan && realStreakDays == 7 {
            return true
        }
        
        // 图二：连续打卡15天时显示（需要有目标）
        if userData.hasPlan && realStreakDays == 15 {
            return true
        }
        
        // 图三：连续打卡17天时显示（需要有目标）
        if userData.hasPlan && realStreakDays == 17 {
            return true
        }
        
        // 图四：连续打卡30天时显示（需要有目标）
        if userData.hasPlan && realStreakDays == 30 {
            return true
        }
        
        // 图五：连续打卡50天时显示（需要有目标）
        if userData.hasPlan && realStreakDays == 50 {
            return true
        }
        
        // 新增：连续打卡51天时显示（需要有目标）
        if userData.hasPlan && realStreakDays == 51 {
            return true
        }
        
        // 图六：前天未打卡，但昨天打卡完成，今天显示Welcome Back（需要有目标）
        if userData.hasPlan && realStreakDays == 1 && realDaysSinceLastCheckIn >= 1 {
            return true
        }
        
        // 图七：如周一未打卡，但周二周三周四成功连续打卡，周五显示Back on Track Streak（需要有目标）
        if userData.hasPlan && realStreakDays == 3 && realDaysSinceLastCheckIn >= 1 {
            return true
        }
        
        // 其他情况都不显示卡片
        return false
    }
    
    // 计算上次打卡距今的天数
    private func calculateDaysSinceLastCheckIn() -> Int {
        let calendar = Calendar.current
        let today = calendar.startOfDay(for: Date())
        
        // 遍历过去30天，检查最近的打卡记录
        for dayOffset in 0..<30 {
            guard let checkDate = calendar.date(byAdding: .day, value: -dayOffset, to: today) else { continue }
            
            // 今天不算未打卡
            if dayOffset == 0 { continue }
            
            // 如果找到打卡记录，返回天数差
            if userData.isDateCheckedIn(checkDate) {
                return dayOffset
            }
        }
        
        // 如果30天内没有打卡记录，返回1天
        // 这样确保会显示"Back on Track!"卡片，且从当天开始计算
        return 1
    }
    
    // 计算当前连续打卡天数
    private func calculateCurrentStreak() -> Int {
        let calendar = Calendar.current
        let today = calendar.startOfDay(for: Date())
        var streakDays = 0
        
        // 从今天开始向前检查
        var checkDate = today
        var daysBefore = 0
        
        while true {
            // 检查该日期是否打卡
            if userData.isDateCheckedIn(checkDate) {
                streakDays += 1
                
                // 获取前一天
                daysBefore += 1
                guard let previousDate = calendar.date(byAdding: .day, value: -1, to: checkDate) else { break }
                checkDate = previousDate
            } else {
                // 一旦发现未打卡的日期，立即中断
                break
            }
            
            // 为了安全，最多检查100天
            if daysBefore >= 100 {
                break
            }
        }
        
        print("当前连续打卡天数: \(streakDays)")
        return streakDays
    }
    
    private func weeklyTipsView() -> some View {
        VStack(alignment: .leading, spacing: 15) {
            // 根据订阅状态显示不同的UI
            if userData.isPremium {
                // Premium用户显示完整的周提示
                Text("This Week's Tips")
                    .font(.headline)
                    .padding(.horizontal)
                
                Group {
                    if planAdviceService.isLoading {
                        HStack {
                            Spacer()
                            ProgressView()
                            Spacer()
                        }
                        .padding()
                    } else {
                        let adviceTexts = planAdviceService.getPlanAdviceTexts()
                        if adviceTexts.isEmpty {
                            Text("No tips available")
                                .font(.subheadline)
                                .foregroundColor(.secondary)
                                .frame(maxWidth: .infinity, alignment: .center)
                                .padding()
                        } else {
                            VStack(alignment: .leading, spacing: 15) {
                                ForEach(Array(adviceTexts.enumerated()), id: \.offset) { index, advice in
                                    HStack(alignment: .top, spacing: 10) {
                                        let iconName = getIconForAdviceIndex(index)
                                        Image(systemName: iconName)
                                            .foregroundColor(iconColor(for: iconName))
                                            .font(.system(size: 14))
                                            .frame(width: 32, height: 32)
                                            .background(backgroundColorForIcon(iconName))
                                            .cornerRadius(8)
                                        
                                        Text(advice)
                                            .font(.subheadline)
                                            .fixedSize(horizontal: false, vertical: true)
                                            .frame(maxWidth: .infinity, alignment: .leading)
                                    }
                                    .frame(maxWidth: .infinity, alignment: .leading)
                                }
                            }
                            .padding()
                            .frame(maxWidth: .infinity, alignment: .leading)
                        }
                    }
                }
                .background(Color.white)
                .cornerRadius(12)
                .shadow(color: Color.black.opacity(0.1), radius: 4, x: 0, y: 2)
                .padding(.horizontal)
            } else {
                // 非Premium用户显示锁定的提示UI
                PremiumTipsView()
                    .environmentObject(userData)
            }
        }
    }
    
    // MARK: - 辅助方法
    
    // 立即加载本地数据，优先显示UI
    private func loadLocalDataImmediately() {
        print("🚀 [Plan] 立即加载本地数据，优先显示UI")
        
        // 立即使用本地运动数据
        loadLocalExerciseDataImmediately()
        
        // 立即使用本地体重数据
        loadLocalWeightDataImmediately()
        
        // 立即使用本地营养数据
        loadLocalNutritionDataImmediately()
        
        print("✅ [Plan] 本地数据加载完成，UI立即可用")
    }
    
    // 立即加载本地运动数据
    private func loadLocalExerciseDataImmediately() {
        let calendar = Calendar.current
        let today = Date()
        
        // 获取本周运动记录
        let components = calendar.dateComponents([.yearForWeekOfYear, .weekOfYear], from: today)
        guard let startOfWeek = calendar.date(from: components) else { return }
        
        let weeklyRecords = userData.exerciseRecords.filter { record in
            return record.date >= startOfWeek && record.date <= today
        }
        
        // 立即计算并显示运动数据
        let totalCalories = weeklyRecords.reduce(0) { $0 + $1.calories }
        self.exerciseCalories = totalCalories
        self.recentExercises = weeklyRecords.sorted(by: { $0.createdAt > $1.createdAt })
        
        print("💪 [Plan] 本地运动数据: \(weeklyRecords.count)条记录，\(totalCalories)卡路里")
    }
    
    // 立即加载本地体重数据
    private func loadLocalWeightDataImmediately() {
        // 使用本地体重记录生成简单的周数据
        let calendar = Calendar.current
        let today = Date()
        
        // 获取最近几周的体重记录
        var localWeightData: [WeeklyWeightData] = []
        
        for weekOffset in 0..<min(userData.goalTimelineWeeks, 8) {
            if let weekStart = calendar.date(byAdding: .weekOfYear, value: -weekOffset, to: today) {
                // 获取该周的体重记录
                let weekRecords = userData.weightHistory.filter { record in
                    calendar.isDate(record.date, equalTo: weekStart, toGranularity: .weekOfYear)
                }
                
                if !weekRecords.isEmpty {
                    let avgWeight = weekRecords.reduce(0) { $0 + $1.weight } / Double(weekRecords.count)
                    let convertedWeight = userData.getWeightValue(avgWeight)
                    
                    localWeightData.append(WeeklyWeightData(
                        id: UUID(),
                        week: "W\(userData.goalTimelineWeeks - weekOffset)",
                        weight: convertedWeight
                    ))
                }
            }
        }
        
        self.weeklyWeightData = localWeightData.reversed()
        print("📊 [Plan] 本地体重数据: \(localWeightData.count)周记录")
    }
    
    // 立即加载本地营养数据
    private func loadLocalNutritionDataImmediately() {
        // 使用本地食物记录计算本周营养
        let calendar = Calendar.current
        let today = Date()
        
        // 获取本周开始日期
        var components = calendar.dateComponents([.yearForWeekOfYear, .weekOfYear], from: today)
        components.weekday = 2 // 周一
        guard let startOfWeek = calendar.date(from: components) else { return }
        
        // 模拟本周营养数据（基于本地食物记录估算）
        var weeklyCalories = 0
        var weeklyProtein = 0
        var weeklyFat = 0
        
        // 遍历本周每一天，累计营养数据
        for dayOffset in 0..<7 {
            if let date = calendar.date(byAdding: .day, value: dayOffset, to: startOfWeek),
               date <= today {
                // 基于本地数据估算每日营养
                let dayCalories = userData.foodEntries.filter { 
                    calendar.isDate($0.time, inSameDayAs: date) 
                }.reduce(0) { $0 + $1.calories }
                
                weeklyCalories += dayCalories
                weeklyProtein += dayCalories / 10 // 简化估算
                weeklyFat += dayCalories / 15 // 简化估算
            }
        }
        
        self.weeklyAPICalories = weeklyCalories
        self.weeklyAPIProtein = weeklyProtein
        self.weeklyAPIFat = weeklyFat
        
        print("🥗 [Plan] 本地营养数据: \(weeklyCalories)卡路里, \(weeklyProtein)g蛋白质, \(weeklyFat)g脂肪")
    }
    
    // 异步加载当天打卡数据（立即响应）
    private func loadTodayCheckInDataSync() {
        let today = Date()
        print("📅 [Plan] 异步加载当天打卡数据")
        
        // 立即显示基础任务列表，避免空白状态
        self.dailyTasks = LogsType.allCases.map { logType in
            DailyTask(
                id: UUID(),
                name: logType.displayName,
                isCompleted: false
            )
        }
        self.isLoadingCheckInData = false // 立即停止loading状态
        
        // 异步获取当天打卡状态，不阻塞UI
        checkInService.getCurrentCheckInStatus(for: today, userData: userData) { status in
                    DispatchQueue.main.async {
                // 成功获取数据，更新UI
                self.dailyTasks = status.tasks.map { task in
                    DailyTask(
                        id: UUID(),
                        name: task.displayName,
                        isCompleted: task.isCompleted
                    )
                }
                
                // 更新缓存
                let dateFormatter = DateFormatter()
                dateFormatter.dateFormat = "yyyyMMdd"
                let dateKey = dateFormatter.string(from: today)
                self.dateCheckInStatus[dateKey] = status.allCompleted
                
                print("✅ [Plan] 当天打卡数据异步加载完成: \(status.completedCount)/\(status.totalCount)")
                
                // 检查是否有已完成的任务，如果有就记录打卡
                if status.completedCount > 0 {
                    print("📝 [Plan] 发现\(status.completedCount)个已完成的打卡项目")
                    
                    // 异步更新打卡状态到服务器
                    DispatchQueue.global(qos: .utility).async {
                        self.updateRemoteCheckInStatus(for: today, status: status)
                    }
                }
            }
        }
    }
    
    // 更新远程打卡状态
    private func updateRemoteCheckInStatus(for date: Date, status: DailyCheckInStatus) {
        guard !userData.accessToken.isEmpty else { return }
        
        let completedLogs = status.tasks.filter { $0.isCompleted }.map { $0.type }
        
        if !completedLogs.isEmpty {
            checkInService.setCheckInLogs(for: date, logTypes: completedLogs, userData: userData) { result in
                switch result {
                case .success(let success):
                    if success {
                        print("✅ [Plan] 成功同步打卡状态到服务器: \(completedLogs.map { $0.rawValue })")
                    } else {
                        print("❌ [Plan] 同步打卡状态失败")
                    }
                case .failure(let error):
                    print("❌ [Plan] 同步打卡状态错误: \(error.localizedDescription)")
                }
            }
        }
    }
    
    // 快速检查用户计划状态
    private func checkUserPlanStatusFast() {
        // 立即使用本地状态
        userHasPlan = userData.hasPlan
                        self.calculateProgress()
        
        if userHasPlan {
            // 立即加载本地数据
            self.loadLocalDataForPlan()
                }
                
        // 后台验证远程状态
        DispatchQueue.global(qos: .background).async {
            UserPlanService.shared.checkUserHasPlan(userData: self.userData) { hasPlan in
                DispatchQueue.main.async {
                    if self.userHasPlan != hasPlan {
                        self.userHasPlan = hasPlan
                        if hasPlan {
                            self.loadLocalDataForPlan()
                        }
                    }
                    self.isCheckingPlan = false
                    print("📊 [Plan] 远程计划状态验证: hasPlan=\(hasPlan)")
                }
            }
        }
    }
    
    // 为有计划的用户加载本地数据
    private func loadLocalDataForPlan() {
        // 立即计算进度
                    self.calculateProgress()
        
        // 使用本地数据，避免等待API
        DispatchQueue.main.async {
            // 这些数据已经在loadLocalDataImmediately中加载过了
            print("📊 [Plan] 计划用户的本地数据已就绪")
        }
    }
    
    // 后台同步远程数据
    private func syncRemoteDataInBackground() {
        print("🔄 [Plan] 开始后台同步远程数据")
        
        let group = DispatchGroup()
        
        // 同步运动数据
        group.enter()
        self.syncRemoteExerciseData {
            group.leave()
        }
        
        // 同步体重数据
        group.enter()
        self.syncRemoteWeightData {
            group.leave()
        }
        
        // 同步营养数据
        group.enter()
        self.syncRemoteNutritionData {
            group.leave()
        }
        
        group.notify(queue: .main) {
            print("✅ [Plan] 后台数据同步完成")
        }
    }
    
    // 同步远程运动数据
    private func syncRemoteExerciseData(completion: @escaping () -> Void) {
        ExerciseAPIService.shared.getUserExerciseRecords(userData: userData, date: nil) { result in
            switch result {
            case .success(let records):
                print("✅ [Plan] 远程运动数据同步成功: \(records.count)条")
                DispatchQueue.main.async {
                    // 更新本地数据
                    self.loadLocalExerciseDataImmediately()
                }
            case .failure(let error):
                print("❌ [Plan] 远程运动数据同步失败: \(error.localizedDescription)")
            }
            completion()
        }
    }

    // 同步远程体重数据
    private func syncRemoteWeightData(completion: @escaping () -> Void) {
        guard !userData.accessToken.isEmpty, userData.hasPlan else {
            completion()
            return
        }
        
        UserPlanService.shared.getWeeklyWeightData(userData: userData) { result in
            switch result {
            case .success(let weeklyData):
                print("✅ [Plan] 远程体重数据同步成功: \(weeklyData.count)周")
                DispatchQueue.main.async {
                    // 过滤掉体重为0或无效的数据，只保留真实有效的周数据
                    self.weeklyWeightData = weeklyData.compactMap { apiData in
                        // 只有当averageWeight大于0且有意义时才创建数据点
                        guard apiData.averageWeight > 0 else {
                            print("📊 [Plan] 跳过无效周数据: 周\(apiData.weekNumber), 体重=\(apiData.averageWeight)")
                            return nil
                        }
                        
                        let convertedWeight = self.userData.getWeightValue(apiData.averageWeight)
                        print("📊 [Plan] 添加有效周数据: 周\(apiData.weekNumber), 体重=\(convertedWeight)")
                        
                        return WeeklyWeightData(
                            id: apiData.id ?? UUID(),
                            week: apiData.weekNumber,
                            weight: convertedWeight
                        )
                    }
                    
                    print("📊 [Plan] 周体重折线图最终数据: \(self.weeklyWeightData.count)个有效数据点")
                }
            case .failure(let error):
                print("❌ [Plan] 远程体重数据同步失败: \(error.localizedDescription)")
            }
            completion()
        }
    }
    
    // 同步远程营养数据
    private func syncRemoteNutritionData(completion: @escaping () -> Void) {
        guard !userData.accessToken.isEmpty else {
            completion()
            return
        }
        
        // 获取本周营养数据
        self.loadWeeklyNutritionDataBackground { success in
            if success {
                print("✅ [Plan] 远程营养数据同步成功")
                    } else {
                print("❌ [Plan] 远程营养数据同步失败")
            }
            completion()
        }
    }
    
    // 后台加载营养数据
    private func loadWeeklyNutritionDataBackground(completion: @escaping (Bool) -> Void) {
        let calendar = Calendar.current
        let today = Date()
        
        var components = calendar.dateComponents([.yearForWeekOfYear, .weekOfYear], from: today)
        components.weekday = 2
        guard let startOfWeek = calendar.date(from: components) else {
            completion(false)
            return
        }
        
        let endDate = min(today, calendar.date(byAdding: .day, value: 6, to: startOfWeek) ?? today)
        let daysDiff = calendar.dateComponents([.day], from: startOfWeek, to: endDate).day ?? 0
        let daysToLoad = min(daysDiff + 1, 7)
        
        let dateFormatter = DateFormatter()
        dateFormatter.dateFormat = "yyyyMMdd"
        
        var weekDates: [String] = []
        for i in 0..<daysToLoad {
            if let date = calendar.date(byAdding: .day, value: i, to: startOfWeek) {
                weekDates.append(dateFormatter.string(from: date))
            }
        }
        
        let group = DispatchGroup()
        var allRecords: [DietRecordResponse] = []
        let recordsQueue = DispatchQueue(label: "background.records.queue")
        
        for dateStr in weekDates {
            group.enter()
            ImageUploadService.shared.getFoodRecords(dateStr: dateStr, authToken: userData.accessToken) { result in
                defer { group.leave() }
                recordsQueue.sync {
                    if case .success(let records) = result {
                        allRecords.append(contentsOf: records)
                        }
                    }
                }
        }
        
        group.notify(queue: .main) {
            let calories = allRecords.reduce(0) { $0 + $1.calories }
            let protein = allRecords.reduce(0) { $0 + $1.protein }
            let fat = allRecords.reduce(0) { $0 + $1.fat }
            
            self.weeklyAPICalories = calories
            self.weeklyAPIProtein = protein
            self.weeklyAPIFat = fat
            
            completion(true)
        }
    }
    
    // 预加载非当天的打卡状态（低优先级）
    private func preloadNonTodayCheckInStatus() {
        let calendar = Calendar.current
        let today = Date()
        let dates = getTwoWeeksDates()
        
        print("📅 [Plan] 开始低优先级预加载非当天打卡状态")
        
        DispatchQueue.global(qos: .utility).async {
            for date in dates {
                // 跳过今天
                if calendar.isDate(date, inSameDayAs: today) { continue }
                
                // 只处理过去的日期
                guard date < calendar.startOfDay(for: today) else { continue }
                
                let dateFormatter = DateFormatter()
                dateFormatter.dateFormat = "yyyyMMdd"
                let dateKey = dateFormatter.string(from: date)
                
                // 如果已有缓存就跳过
                if self.dateCheckInStatus[dateKey] != nil { continue }
                
                // 检查状态
                self.checkDateCompletionStatus(date) { isComplete in
                DispatchQueue.main.async {
                        self.dateCheckInStatus[dateKey] = isComplete
                    }
                }
                
                // 添加延迟，避免过多并发请求
                Thread.sleep(forTimeInterval: 0.1)
            }
            
            DispatchQueue.main.async {
                print("✅ [Plan] 非当天打卡状态预加载完成")
            }
        }
    }
    
    // 检查用户计划状态
    private func checkUserPlanStatus() {
        isCheckingPlan = true
        
        // 立即设置基础状态，避免长时间显示loading
        if userData.hasPlan {
            userHasPlan = true
            self.calculateProgress()
        }
        
        UserPlanService.shared.checkUserHasPlan(userData: userData) { hasPlan in
            DispatchQueue.main.async {
                self.userHasPlan = hasPlan
                self.isCheckingPlan = false
                
                // 如果有计划，加载相关数据
                if hasPlan {
                    self.calculateProgress()
                    
                    // 延迟加载较重的数据
                    DispatchQueue.main.asyncAfter(deadline: .now() + 0.1) {
                        self.loadData()
                        self.loadWeeklyNutritionData()
                    }
                }
                
                print("📊 [Plan] 计划状态检查完成: hasPlan=\(hasPlan)")
            }
        }
    }
    
    // 根据建议索引获取对应的图标（营养、运动、睡眠）
    private func getIconForAdviceIndex(_ index: Int) -> String {
        switch index {
        case 0:
            return "apple.logo" // 营养
        case 1:
            return "dumbbell.fill" // 运动
        case 2:
            return "bed.double.fill" // 睡眠
        default:
            return "lightbulb.fill" // 默认
        }
    }
    
    // 加载所有数据（轻量化版本）
    private func loadData() {
        print("📊 [Plan] 开始轻量化加载数据")
        
        // 只加载关键数据，其他数据已通过本地加载完成
        
        // 获取最新计划数据（必要时）
        if userData.hasPlan {
            DispatchQueue.global(qos: .utility).async {
                self.fetchLatestPlanDataLight()
            }
        }
        
        // 其他数据通过后台同步完成，这里不重复加载
        print("✅ [Plan] 轻量化数据加载完成")
        isLoading = false
    }
    
    // 轻量化获取最新计划数据
    private func fetchLatestPlanDataLight() {
        guard !userData.accessToken.isEmpty else { return }
        
        UserPlanService.shared.getLatestPlan(userData: userData) { result in
            switch result {
            case .success(let planResponse):
                if let currentWeek = planResponse.currentWeek {
                    DispatchQueue.main.async {
                        self.currentWeek = currentWeek
                        self.calculateProgress()
                    }
                }
            case .failure(let error):
                print("❌ [Plan] 获取最新计划失败: \(error.localizedDescription)")
            }
        }
    }
    
    // 计算当前周数和进度
    private func calculateProgress() {
        // 当前设置总周数
        totalWeeks = userData.goalTimelineWeeks
        
        // 如果currentWeek还没有从API设置（即仍为默认值1），才进行本地计算
        // 这样可以避免覆盖从API获取的准确currentWeek值
        if currentWeek == 1 && userData.goalStartDate != nil {
            // 获取开始日期 - 如果goalStartDate为nil，使用创建账户的日期
            let startDate = userData.goalStartDate ?? userData.accountCreationDate
            let today = Date()
            
            // 计算从开始到现在经过的天数
            let calendar = Calendar.current
            let elapsedDays = calendar.dateComponents([.day], from: startDate, to: today).day ?? 0
            
            // 从开始计算周数，从1开始
            let calculatedWeek = max(1, min(Int(ceil(Double(elapsedDays) / 7.0)), totalWeeks))
            
            // 只有当没有从API获取到currentWeek时才使用本地计算
            if currentWeek == 1 {
                currentWeek = calculatedWeek
            }
            
            // 输出调试信息
            print("📊 本地计算周进度: 从 \(startDate) 到今天 \(today) 已过去 \(elapsedDays) 天")
            print("📊 本地计算当前周数: \(calculatedWeek) / API设置的当前周数: \(currentWeek) / 总周数: \(totalWeeks)")
        }
        
        // 计算进度 - 使用已经过的周数占总周数的百分比
        let oldProgress = weeklyProgress
        
        // 使用周数计算进度
        weeklyProgress = min(Double(currentWeek) / Double(totalWeeks), 1.0)
        
        print("📈 基于周数的进度: \(weeklyProgress * 100)% (Week \(currentWeek) of \(totalWeeks))")
        
        // 如果进度有显著变化，强制刷新UI
        if abs(oldProgress - weeklyProgress) > 0.01 {
            // 使用主线程更新UI
            DispatchQueue.main.async {
                // 直接修改@State属性会自动触发UI刷新
                // 不需要额外的objectWillChange.send()
            }
        }
    }
    
    // 加载运动数据
    private func loadExerciseData() {
        // 显示加载中状态
        isLoading = true
        
        // 获取本周的开始日期（周一）
        let calendar = Calendar.current
        let today = Date()
        let components = calendar.dateComponents([.yearForWeekOfYear, .weekOfYear], from: today)
        guard let startOfWeek = calendar.date(from: components) else { return }
        
        print("📅 [Plan] 加载本周(\(formatDate(startOfWeek)))运动数据...")
        
        // 调用API获取本周所有运动记录
        ExerciseAPIService.shared.getUserExerciseRecords(userData: userData, date: nil) { result in
            switch result {
            case .success(let records):
                print("✅ [Plan] 成功获取\(records.count)条运动记录")
                
                // 过滤出本周的记录
                DispatchQueue.main.async {
                    // 获取本周日期范围
                    let calendar = Calendar.current
                    let today = Date()
                    let components = calendar.dateComponents([.yearForWeekOfYear, .weekOfYear], from: today)
                    guard let startOfWeek = calendar.date(from: components),
                          let endOfWeek = calendar.date(byAdding: .day, value: 7, to: startOfWeek) else {
                        self.isLoading = false
                        return
                    }
                    
                    // 过滤本周的记录
                    let dateFormatter = DateFormatter()
                    dateFormatter.dateFormat = "yyyyMMdd"
                    
                    let weeklyRecords = records.filter { record in
                        guard let date = dateFormatter.date(from: record.dateStr) else { return false }
                        return date >= startOfWeek && date < endOfWeek
                    }
                    
                    print("📊 [Plan] 本周运动记录: \(weeklyRecords.count)条")
                    
                    // 计算总卡路里
                    let totalCalories = weeklyRecords.reduce(0) { $0 + Int($1.calories) }
                    
                    // 确保按创建时间降序排序，最新添加的运动会显示在前面
                    // API返回的记录可能没有明确的创建时间顺序，我们使用ID倒序作为替代
                    let sortedRecords = self.userData.exerciseRecords.sorted(by: { $0.createdAt > $1.createdAt })
                    
                    // 设置实际燃烧的卡路里（分子）
                    self.exerciseCalories = totalCalories
                    
                    // 设置卡路里目标（分母）
                    self.exerciseCalorieGoal = planAdviceService.getSafeWeeklyExerciseBurnGoal()
                    
                    // 更新UI数据
                    self.recentExercises = sortedRecords.filter { record in
                        let calendar = Calendar.current
                        return calendar.isDate(record.date, equalTo: today, toGranularity: .weekOfYear)
                    }
                    
                    print("💪 [Plan] 本周运动卡路里: \(totalCalories)/\(self.exerciseCalorieGoal)")
                    self.isLoading = false
                }
                
            case .failure(let error):
                print("❌ [Plan] 获取运动记录失败: \(error.localizedDescription)")
                DispatchQueue.main.async {
                    // 错误发生时，尝试使用本地缓存的记录
                    self.useLocalExerciseRecords()
                    self.isLoading = false
                }
            }
        }
    }
    
    // 使用本地缓存的运动记录（当API请求失败时）
    private func useLocalExerciseRecords() {
        // 获取本周日期范围
        let calendar = Calendar.current
        let today = Date()
        let components = calendar.dateComponents([.yearForWeekOfYear, .weekOfYear], from: today)
        guard let startOfWeek = calendar.date(from: components),
              let endOfWeek = calendar.date(byAdding: .day, value: 7, to: startOfWeek) else { return }
        
        // 过滤本周运动记录
        let weeklyRecords = userData.exerciseRecords.filter { record in
            return record.date >= startOfWeek && record.date < endOfWeek
        }
        
        // 计算总卡路里
        let totalCalories = weeklyRecords.reduce(0) { $0 + $1.calories }
        
        // 更新UI数据
        self.recentExercises = weeklyRecords.sorted(by: { $0.createdAt > $1.createdAt })
        self.exerciseCalories = totalCalories
        self.exerciseCalorieGoal = planAdviceService.getSafeWeeklyExerciseBurnGoal()
        
        print("ℹ️ [Plan] 使用本地缓存的运动记录: \(weeklyRecords.count)条记录，总卡路里: \(totalCalories)")
    }
    
    // 辅助函数：格式化日期为字符串
    private func formatDate(_ date: Date) -> String {
        let formatter = DateFormatter()
        formatter.dateFormat = "yyyyMMdd"
        return formatter.string(from: date)
    }
    
    // 刷新运动数据 - 用于添加新运动后立即更新UI
    private func refreshExerciseData() {
        // 防止重复刷新
        guard !isRefreshingExerciseData else {
            print("📋 [Plan] 已有刷新操作正在进行中，忽略此次刷新请求")
            return
        }
        
        // 设置刷新状态标志
        isRefreshingExerciseData = true
        
        // 设置加载状态
        isLoading = true
        
        // 调用API获取最新运动数据
        ExerciseAPIService.shared.getUserExerciseRecords(userData: userData, date: nil) { result in
            DispatchQueue.main.async {
                self.isLoading = false
                // 重置刷新状态标志
                self.isRefreshingExerciseData = false
                
                switch result {
                case .success(let records):
                    print("✅ [Plan] 成功刷新运动记录: \(records.count)条")
                    
                    // 加载最新的本地运动记录（已由API服务更新）
                    self.loadExerciseData()
                    
                case .failure(let error):
                    print("❌ [Plan] 刷新运动记录失败: \(error.localizedDescription)")
                    // 失败时尝试使用本地缓存
                    self.useLocalExerciseRecords()
                }
            }
        }
    }
    
    // 保存新添加的运动记录
    private func saveNewExerciseRecord(_ record: ExerciseRecord) {
        // 在函数开始处检查锁定状态，防止重复调用
        guard !isAddingExercise else { 
            print("⚠️ [Plan] 已经有添加运动记录的操作正在进行中，忽略此次请求")
            return 
        }
        
        print("🏋️‍♂️ [Plan] 添加新运动记录: \(record.type), \(record.duration)分钟, \(record.calories)卡路里")
        
        // 将时长转换为小时和分钟
        let hours = record.duration / 60
        let minutes = record.duration % 60
        
        // 调用API创建运动记录
        ExerciseAPIService.shared.createExerciseRecord(
            userData: userData,
            exerciseItem: record.type,
            intensity: record.intensity,
            hours: hours,
            minutes: minutes,
            date: record.date
        ) { result in
            DispatchQueue.main.async {
                switch result {
                case .success(_):
                    print("✅ [Plan] 成功创建运动记录")
                    // 成功创建记录后刷新UI
                    self.refreshExerciseData()
                    
                case .failure(let error):
                    print("❌ [Plan] 创建运动记录失败: \(error.localizedDescription)")
                    // 即使API失败，也将记录添加到本地以确保用户体验
                    self.userData.exerciseRecords.append(record)
                    self.userData.saveSettings()
                    // API失败后刷新UI
                    self.refreshExerciseData()
                }
                
                // 解除锁定状态
                self.isAddingExercise = false
            }
        }
    }
    
    // 加载营养数据
    // 原loadNutritionData方法已移除，营养数据通过loadWeeklyNutritionData从API获取
    
    // 获取星期几名称
    private func weekdayName(for day: Int) -> String {
        let days = ["Mon", "Tue", "Wed", "Thu", "Fri", "Sat", "Sun"]
        return days[day - 1]
    }
    
    // 根据运动类型返回对应的图标名称
    private func iconForExerciseType(_ type: String) -> String {
        switch type {
        case "Running":
            return "figure.run"
        case "Walking":
            return "figure.walk"
        case "Cycling":
            return "bicycle"
        case "Swimming":
            return "figure.pool.swim"
        case "Weight Training":
            return "dumbbell"
        case "Gym":
            return "dumbbell"
        case "Yoga":
            return "figure.yoga"
        case "Jump Rope":
            return "figure.jumprope"
        case "Basketball":
            return "basketball"
        case "Climbing":
            return "mountain.2"
        default:
            return "figure.walk"
        }
    }
    
    // 加载每日任务 - 使用新的CheckInService
    private func loadDailyTasks() {
        // 使用选中的日期加载打卡数据
        loadCheckInDataForDate(selectedCheckInDate)
    }

    // 辅助函数，用于更新每日任务和打卡状态
    private func updateDailyTasksAndCheckInStatus(isMorningWeightSet: Bool, hasBreakfastDiary: Bool, hasLunchDiary: Bool, hasDinnerDiary: Bool, isEveningWeightSet: Bool) {
        let completedTasks = [isMorningWeightSet, hasBreakfastDiary, hasLunchDiary, hasDinnerDiary, isEveningWeightSet].filter { $0 }.count
        let totalTasks = 5
        let allTasksCompleted = completedTasks == totalTasks
        userData.updateCheckInStatus(for: Date(), isCompleted: allTasksCompleted)
        
        print("Daily Tasks Status Updated: Morning Weight: \(isMorningWeightSet), Breakfast: \(hasBreakfastDiary), Lunch: \(hasLunchDiary), Dinner: \(hasDinnerDiary), Evening Weight: \(isEveningWeightSet)")
        print("完成状态: \(completedTasks)/\(totalTasks), 全部完成: \(allTasksCompleted)")
    }
    
    // 加载周提示
    private func loadWeeklyTips() {
        // 模拟从后端获取提示（根据用户完成情况）
        weeklyTips = [
            WeeklyTip(id: UUID(), content: "Try to eat protein with every meal", icon: "apple.logo"),
            WeeklyTip(id: UUID(), content: "Take a 10-minute walk after lunch", icon: "dumbbell.fill"),
            WeeklyTip(id: UUID(), content: "Get 7-8 hours of sleep tonight", icon: "bed.double.fill")
        ]
    }
    
    // 添加推荐的运动
    private func addRecommendedExercise(_ activity: RecommendedActivity) {
        // 将时长转换为小时和分钟
        let hours = activity.duration / 60
        let minutes = activity.duration % 60
        
        // 调用API创建运动记录
        ExerciseAPIService.shared.createExerciseRecord(
            userData: userData,
            exerciseItem: activity.type,
            intensity: "Medium", // 默认中等强度
            hours: hours,
            minutes: minutes,
            date: Date() // 推荐活动默认添加到当天
        ) { result in
            DispatchQueue.main.async {
                switch result {
                case .success(_):
                    print("✅ [Plan] 成功添加推荐运动: \(activity.type)")
                    
                    // 刷新UI
                    self.refreshExerciseData()
                    
                    // 发送通知让其他页面也更新
                    NotificationCenter.default.post(name: Notification.Name("ExerciseDataUpdated"), object: nil)
                    
                case .failure(let error):
                    print("❌ [Plan] 添加推荐运动失败: \(error.localizedDescription)")
                    
                    // 创建本地记录作为备份
                    let record = ExerciseRecord(
                        type: activity.type,
                        duration: activity.duration,
                        intensity: "Medium",
                        calories: activity.calories,
                        date: Date(),
                        timeString: self.getCurrentTimeString(),
                        id: UUID(),
                        createdAt: Date()
                    )
                    
                    // 添加到本地数据
                    self.userData.exerciseRecords.append(record)
                    self.userData.saveSettings()
                    
                    // 刷新UI
                    self.refreshExerciseData()
                    
                    // 发送通知让其他页面也更新
                    NotificationCenter.default.post(name: Notification.Name("ExerciseDataUpdated"), object: nil)
                }
            }
        }
    }
    
    // 获取当前时间字符串
    private func getCurrentTimeString() -> String {
        let dateFormatter = DateFormatter()
        dateFormatter.dateFormat = "HH:mm"
        return dateFormatter.string(from: Date())
    }
    
    // 计算进度条宽度
    private func calculateProgressBarWidth(totalWidth: CGFloat) -> CGFloat {
        return totalWidth * CGFloat(weeklyProgress)
    }
    
    // 导航到更多运动记录的页面
    private func navigateToExerciseHistory() {
        print("导航到运动历史记录页面")
        
        // 确保在主线程上发送通知
        DispatchQueue.main.async {
            // 发送DirectShowExerciseHistory通知，这是一个新通知，用于直接显示运动历史记录页面
            NotificationCenter.default.post(name: Notification.Name("DirectShowExerciseHistory"), object: nil)
        }
    }
    
    // 更新导航到体重目标编辑页面的方法
    @MainActor private func navigateToWeightGoalEdit() {
        // 直接发送通知，让ContentView显示GoalWeightDetailView
        NotificationCenter.default.post(name: Notification.Name("DirectShowWeightGoalEdit"), object: nil)
    }
    
    // 根据图标类型返回对应的图标颜色
    private func iconColor(for icon: String) -> Color {
        switch icon {
        case "apple.logo":
            return .white
        case "dumbbell.fill":
            return .white
        case "bed.double.fill":
            return .white
        default:
            return .white
        }
    }
    
    // 根据图标类型返回对应的背景颜色
    private func backgroundColorForIcon(_ icon: String) -> Color {
        switch icon {
        case "apple.logo":
            return .green
        case "dumbbell.fill":
            return .blue
        case "bed.double.fill":
            return .purple
        default:
            return .gray
        }
    }
    
    // 添加加载本周营养数据的方法（已优化为轻量版本，原方法移至后台同步）
    private func loadWeeklyNutritionData() {
        // 此方法已优化，现在主要使用本地数据
        // 远程数据同步已移至后台进行
        if !userData.accessToken.isEmpty {
            print("📊 [Plan] 营养数据使用本地+后台同步模式")
            // 立即使用本地数据
            loadLocalNutritionDataImmediately()
        }
    }
    
    // 推荐活动卡片
    private func recommendedActivityCard(iconName: String, title: String) -> some View {
        VStack(alignment: .center, spacing: adaptiveRecommendedCardSpacing()) {
            Image(systemName: iconName)
                .font(adaptiveRecommendedCardIconFont())
                .foregroundColor(.gray)
                .frame(height: adaptiveRecommendedCardIconHeight())
            
            Text(title)
                .font(adaptiveRecommendedCardTitleFont())
                .foregroundColor(.gray)
                .lineLimit(1)
                .frame(height: adaptiveRecommendedCardTitleHeight())
        }
        .padding(.vertical, adaptiveRecommendedCardVerticalPadding())
        .frame(width: adaptiveRecommendedCardWidth(), height: adaptiveRecommendedCardHeight())
        .background(Color.gray.opacity(0.08))
        .cornerRadius(12)
    }
    
    // 推荐活动卡片的适配函数
    private func adaptiveRecommendedCardSpacing() -> CGFloat {
        if UIScreen.main.bounds.height < 700 {
            return 6 // 小屏减少内部间距
        } else {
            return 8 // 大屏标准间距
        }
    }
    
    private func adaptiveRecommendedCardIconFont() -> Font {
        if UIScreen.main.bounds.height < 700 {
            return .system(size: 18) // 小屏较小图标
        } else {
            return .system(size: 20) // 大屏标准图标
        }
    }
    
    private func adaptiveRecommendedCardIconHeight() -> CGFloat {
        if UIScreen.main.bounds.height < 700 {
            return 22 // 小屏较小高度
        } else {
            return 25 // 大屏标准高度
        }
    }
    
    private func adaptiveRecommendedCardTitleFont() -> Font {
        if UIScreen.main.bounds.width < 375 {
            return .caption2 // 小屏更小字体
        } else {
            return .caption // 大屏标准字体
        }
    }
    
    private func adaptiveRecommendedCardTitleHeight() -> CGFloat {
        return 12 // 固定高度
    }
    
    private func adaptiveRecommendedCardVerticalPadding() -> CGFloat {
        if UIScreen.main.bounds.height < 700 {
            return 8 // 小屏减少内边距
        } else {
            return 10 // 大屏标准内边距
        }
    }
    
    private func adaptiveRecommendedCardWidth() -> CGFloat {
        if UIScreen.main.bounds.width < 375 {
            return 90 // iPhone 12 mini等小屏减少宽度
        } else {
            return 100 // 大屏标准宽度
        }
    }
    
    private func adaptiveRecommendedCardHeight() -> CGFloat {
        if UIScreen.main.bounds.height < 700 {
            return 60 // 小屏减少高度
        } else {
            return 70 // 大屏标准高度
        }
    }
    
    // 新增的自适应函数
    private func adaptiveNoGoalCalorieValueSpacing() -> CGFloat {
        if UIScreen.main.bounds.height < 700 {
            return 2 // 小屏减少间距
        } else {
            return 4 // 大屏标准间距
        }
    }
    
    private func adaptiveNoGoalProgressCircleLineWidth() -> CGFloat {
        if UIScreen.main.bounds.height < 700 {
            return 3 // 小屏较细线条
        } else if UIScreen.main.bounds.height < 850 && UIScreen.main.bounds.width < 430 {
            return 3 // iPhone 13 Pro等中等屏幕 - 减少线宽以缩小组件
        } else {
            return 4 // 大屏标准线条
        }
    }
    
    private func adaptiveButtonCornerRadius() -> CGFloat {
        if UIScreen.main.bounds.height < 700 {
            return 20 // 小屏较小圆角
        } else {
            return 25 // 大屏标准圆角
        }
    }
    
    private func adaptiveButtonTopPadding() -> CGFloat {
        if UIScreen.main.bounds.height < 700 {
            return 3 // 小屏减少顶部间距
        } else {
            return 5 // 大屏标准间距
        }
    }
    
    private func adaptiveExerciseCardSpacing() -> CGFloat {
        if UIScreen.main.bounds.height < 700 {
            return 4 // 小屏进一步减少内部间距
        } else {
            return 10 // 大屏标准间距
        }
    }
    
    private func adaptiveExerciseCardIconFont() -> Font {
        if UIScreen.main.bounds.height < 700 {
            return .system(size: 16) // 小屏进一步减小图标
        } else {
            return .system(size: 24) // 大屏标准图标
        }
    }
    
    private func adaptiveExerciseCardIconHeight() -> CGFloat {
        if UIScreen.main.bounds.height < 700 {
            return 18 // 小屏进一步减小高度
        } else {
            return 24 // 大屏标准高度
        }
    }
    
    private func adaptiveExerciseCardTitleFont() -> Font {
        if UIScreen.main.bounds.width < 375 {
            return .system(size: 10) // 小屏更小字体
        } else {
            return .caption // 大屏标准字体
        }
    }
    
    private func adaptiveExerciseCardTitleHeight() -> CGFloat {
        if UIScreen.main.bounds.height < 700 {
            return 10 // 小屏进一步减小高度
        } else {
            return 15 // 大屏标准高度
        }
    }
    
    private func adaptiveExerciseCardVerticalPadding() -> CGFloat {
        if UIScreen.main.bounds.height < 700 {
            return 6 // 小屏进一步减少内边距
        } else {
            return 12 // 大屏标准内边距
        }
    }
    
    private func adaptiveExerciseCardWidth() -> CGFloat {
        if UIScreen.main.bounds.width < 375 {
            return 75 // iPhone 12 mini等小屏进一步减少宽度
        } else if UIScreen.main.bounds.width < 390 {
            return 85 // iPhone 12等中等屏幕
        } else if UIScreen.main.bounds.width < 430 {
            return 90 // iPhone 13 Pro等 - 减少宽度以缩小组件
        } else {
            return 105 // 大屏标准宽度
        }
    }
    
    private func adaptiveExerciseCardHeight() -> CGFloat {
        if UIScreen.main.bounds.height < 700 {
            return 50 // 小屏进一步减少高度
        } else if UIScreen.main.bounds.height < 850 && UIScreen.main.bounds.width < 430 {
            return 65 // iPhone 13 Pro等中等屏幕 - 减少高度以缩小组件
        } else {
            return 75 // 大屏标准高度
        }
    }
    

    
    // Daily Check-in相关的自适应函数
    private func adaptiveCheckInContentSpacing() -> CGFloat {
        if UIScreen.main.bounds.height < 700 {
            return 6 // 小屏减少间距
        } else {
            return 10 // 大屏标准间距
        }
    }
    
    private func adaptiveWeekdaySpacing() -> CGFloat {
        if UIScreen.main.bounds.height < 700 {
            return 2 // 小屏减少间距
        } else {
            return 4 // 大屏标准间距
        }
    }
    
    private func adaptiveWeekdayFont() -> Font {
        if UIScreen.main.bounds.height < 700 {
            return .caption2 // 小屏更小字体
        } else {
            return .caption // 大屏标准字体
        }
    }
    
    private func adaptiveWeekdayCircleSize() -> CGFloat {
        if UIScreen.main.bounds.height < 700 {
            return 25 // 小屏减小圆圈
        } else {
            return 30 // 大屏标准圆圈
        }
    }
    
    private func adaptiveWeekdayCircleLineWidth() -> CGFloat {
        if UIScreen.main.bounds.height < 700 {
            return 1.5 // 小屏较细线条
        } else {
            return 2 // 大屏标准线条
        }
    }
    
    private func adaptiveWeekdayNumberFont() -> Font {
        if UIScreen.main.bounds.height < 700 {
            return .caption2 // 小屏更小字体
        } else {
            return .caption // 大屏标准字体
        }
    }
    
    private func adaptiveWeekdayVerticalPadding() -> CGFloat {
        if UIScreen.main.bounds.height < 700 {
            return 5 // 小屏减少间距
        } else {
            return 8 // 大屏标准间距
        }
    }
    
    private func adaptiveTaskSpacing() -> CGFloat {
        if UIScreen.main.bounds.height < 700 {
            return 6 // 小屏减少间距
        } else {
            return 10 // 大屏标准间距
        }
    }
    
    private func adaptiveTaskContainerPadding() -> EdgeInsets {
        if UIScreen.main.bounds.height < 700 || UIScreen.main.bounds.width < 375 {
            return EdgeInsets(top: 8, leading: 12, bottom: 8, trailing: 12) // 小屏减少内边距
        } else {
            return EdgeInsets(top: 12, leading: 16, bottom: 12, trailing: 16) // 大屏标准内边距
        }
    }
    
    private func adaptiveTaskContainerCornerRadius() -> CGFloat {
        if UIScreen.main.bounds.height < 700 {
            return 6 // 小屏较小圆角
        } else {
            return 8 // 大屏标准圆角
        }
    }
    
    // 运动卡片组相关的自适应函数
    private func adaptiveExerciseCardGroupVerticalPadding() -> CGFloat {
        if UIScreen.main.bounds.height < 700 {
            return 3 // 小屏进一步减少间距
        } else {
            return 8 // 大屏标准间距
        }
    }
    
    // 检查指定日期是否完全打卡
    private func isDateFullyCheckedIn(_ date: Date) -> Bool {
        let dateFormatter = DateFormatter()
        dateFormatter.dateFormat = "yyyyMMdd"
        let dateKey = dateFormatter.string(from: date)
        
        // 先检查缓存
        if let cachedStatus = dateCheckInStatus[dateKey] {
            return cachedStatus
        }
        
        // 如果没有缓存，返回默认值（未完成）
        // 避免在UI渲染过程中进行异步操作
        return false
    }
    
    // 异步检查日期的完成状态
    private func checkDateCompletionStatus(_ date: Date, completion: @escaping (Bool) -> Void) {
        checkInService.getCurrentCheckInStatus(for: date, userData: userData) { status in
            let isComplete = status.allCompleted
            completion(isComplete)
        }
    }
    
    // 预加载最近几天的打卡状态（优化版本）
    private func preloadRecentDatesCheckInStatus() {
        let calendar = Calendar.current
        let today = Date()
        
        print("📅 [Plan] 开始预加载最近几天的打卡状态")
        
        // 并发预加载最近几天的数据
        let group = DispatchGroup()
        let datesToPreload = [1, 2] // 昨天和前天
        
        for i in datesToPreload {
            guard let date = calendar.date(byAdding: .day, value: -i, to: today) else { continue }
            
            let dateFormatter = DateFormatter()
            dateFormatter.dateFormat = "yyyyMMdd"
            let dateKey = dateFormatter.string(from: date)
            
            // 如果已经有缓存就跳过
            if dateCheckInStatus[dateKey] != nil {
                print("📅 [Plan] 日期\(dateKey)已有缓存，跳过预加载")
                continue
            }
            
            group.enter()
            DispatchQueue.global(qos: .utility).async {
                self.checkDateCompletionStatus(date) { isComplete in
                    DispatchQueue.main.async {
                        self.dateCheckInStatus[dateKey] = isComplete
                        print("📅 [Plan] 预加载日期\(dateKey)完成: \(isComplete ? "✅" : "❌")")
                        group.leave()
                    }
                }
            }
        }
        
        group.notify(queue: .main) {
            print("✅ [Plan] 最近几天打卡状态预加载完成")
        }
    }
    
    // 预加载两周的打卡状态
    private func preloadTwoWeeksCheckInStatus() {
        let dates = getTwoWeeksDates()
        for date in dates {
            // 只预加载当天及之前的日期
            let calendar = Calendar.current
            let today = Date()
            if date <= calendar.startOfDay(for: today) || calendar.isDate(date, inSameDayAs: today) {
                checkDateCompletionStatus(date) { isComplete in
                    DispatchQueue.main.async {
                        let dateFormatter = DateFormatter()
                        dateFormatter.dateFormat = "yyyyMMdd"
                        let dateKey = dateFormatter.string(from: date)
                        self.dateCheckInStatus[dateKey] = isComplete
                    }
                }
            }
        }
    }
    
    // 预加载相邻日期的状态
    private func preloadAdjacentDatesStatus(around centerDate: Date) {
        let calendar = Calendar.current
        let today = Date()
        
        // 预加载前后各1天的数据
        for offset in [-1, 1] {
            if let adjacentDate = calendar.date(byAdding: .day, value: offset, to: centerDate) {
                // 只预加载当天及之前的日期
                if adjacentDate <= calendar.startOfDay(for: today) || calendar.isDate(adjacentDate, inSameDayAs: today) {
                    let dateFormatter = DateFormatter()
                    dateFormatter.dateFormat = "yyyyMMdd"
                    let dateKey = dateFormatter.string(from: adjacentDate)
                    
                    // 如果已经有缓存就跳过
                    if dateCheckInStatus[dateKey] == nil {
                        checkDateCompletionStatus(adjacentDate) { isComplete in
                            DispatchQueue.main.async {
                                self.dateCheckInStatus[dateKey] = isComplete
                            }
                        }
                    }
                }
            }
        }
    }
    
    // MARK: - 辅助方法
    
    // 加载打卡历史记录
    private func loadCheckInHistory() {
        guard userData.hasPlan && !userData.accessToken.isEmpty else {
            print("📅 [Plan] 用户无计划或未登录，跳过打卡历史加载")
            return
        }
        
        isLoadingCheckInHistory = true
        print("📅 [Plan] 开始加载打卡历史记录（基于计划创建时间）")
        
        // 首先确保我们有最新的计划数据（包括planCreatedTime）
        UserPlanService.shared.checkUserHasPlan(userData: userData) { hasPlan in
            
            DispatchQueue.main.async {
                if !hasPlan {
                    // 如果没有计划，直接返回
                    self.isLoadingCheckInHistory = false
                    print("📅 [Plan] API确认用户没有计划，停止打卡历史加载")
                    return
                }
                
                // 检查计划创建时间
                if let planCreatedTime = self.userData.planCreatedTime {
                    let calendar = Calendar.current
                    let today = Date()
                    let daysSinceCreation = calendar.dateComponents([.day], from: calendar.startOfDay(for: planCreatedTime), to: calendar.startOfDay(for: today)).day ?? 0
                    
                    print("📅 [Plan] 计划创建于: \(planCreatedTime), 距今 \(daysSinceCreation) 天")
                    
                    if daysSinceCreation == 0 {
                        // 今天创建计划，直接设置为0，不需要查询历史数据
                        print("📅 [Plan] 今天创建计划，直接设置默认值")
                        self.consecutiveNonCheckInDays = 0
                        self.realStreakDays = 0
                        self.realDaysSinceLastCheckIn = 0
                        self.isLoadingCheckInHistory = false
                        return
                    }
                }
                
                // 继续原有的打卡历史查询逻辑
                self.performCheckInHistoryQuery()
            }
        }
    }
    
    // 执行实际的打卡历史查询
    private func performCheckInHistoryQuery() {
        
        checkInHistoryService.getCheckInLogsBasedOnPlanCreation(userData: userData) { result in
            DispatchQueue.main.async {
                self.isLoadingCheckInHistory = false
                
                switch result {
                case .success(let logs):
                    print("✅ [Plan] 成功获取打卡历史: \(logs.count)条记录")
                    self.checkInHistoryLogs = logs
                    
                    // 计算真实的连续打卡天数
                    self.realStreakDays = self.checkInHistoryService.calculateStreakDays(from: logs)
                    
                    // 计算真实的未打卡天数
                    self.realDaysSinceLastCheckIn = self.checkInHistoryService.calculateDaysSinceLastCheckIn(from: logs)
                    
                    // 计算连续未打卡天数（用于激励卡片判断），传入查询开始日期限制范围
                    self.consecutiveNonCheckInDays = self.checkInHistoryService.calculateConsecutiveNonCheckInDays(from: logs, queryStartDate: self.checkInHistoryService.lastQueryStartDate)
                    
                    print("📊 [Plan] 真实连续打卡天数: \(self.realStreakDays)天")
                    print("📊 [Plan] 距离上次打卡: \(self.realDaysSinceLastCheckIn)天")
                    print("📊 [Plan] 连续未打卡天数: \(self.consecutiveNonCheckInDays)天")
                    
                case .failure(let error):
                    print("❌ [Plan] 获取打卡历史失败: \(error.localizedDescription)")
                    // 失败时使用本地计算的数据作为备用
                    self.realStreakDays = self.calculateCurrentStreak()
                    self.realDaysSinceLastCheckIn = self.calculateDaysSinceLastCheckIn()
                }
            }
        }
    }
}

// MARK: - 访客模式Plan页面

struct GuestPlanView: View {
    @EnvironmentObject var userData: UserData
    
    var body: some View {
        ScrollView {
            VStack(alignment: .leading, spacing: 20) {
                // 头部
                headerView()
                
                // 计划进度区域（复制无目标时的UI）
                planProgressView()
                
                // 周营养组件（非会员UI）
                weeklyNutritionView()
                
                // 推荐活动组件（非会员UI）
                recommendedActivitiesView()
                
                // Login按钮
                loginButtonView()
                
                // 底部间距
                Spacer().frame(height: 30)
            }
            .padding(.top)
        }
    }
    
    private func headerView() -> some View {
        VStack(alignment: .leading, spacing: 5) {
            Text("Your Health Plan")
                .font(.title)
                .fontWeight(.bold)
        }
        .padding(.horizontal, 20)
    }
    
    private func planProgressView() -> some View {
        VStack(spacing: 20) {
            // Set Goal圆圈（但显示Guest）
            ZStack {
                Circle()
                    .stroke(Color.green, lineWidth: 2)
                    .frame(width: 80, height: 80)
                
                Text("Guest")
                    .font(.system(size: 16, weight: .medium))
                    .foregroundColor(.green)
            }
            
            // Current Weight
            VStack(spacing: 8) {
                Text("Current")
                    .font(.subheadline)
                    .foregroundColor(.secondary)
                
                HStack(alignment: .firstTextBaseline) {
                    Text("? ? ?")
                        .font(.title2)
                        .fontWeight(.semibold)
                    
                    Text("lbs")
                        .font(.title2)
                        .fontWeight(.semibold)
                        .padding(.leading, 5)
                }
            }
            
            // 提示文本
            Text("Login to view personal data and unlock premium features")
                .font(.system(size: 16, weight: .regular))
                .foregroundColor(Color(red: 0.42, green: 0.45, blue: 0.5))
                .multilineTextAlignment(.center)
                .lineLimit(nil)
                .padding(.vertical, 10)
        }
        .frame(maxWidth: .infinity)
        .padding()
        .background(Color(red: 0.98, green: 0.98, blue: 0.98))
        .cornerRadius(12)
        .padding(.horizontal, 20)
    }
    
    private func weeklyNutritionView() -> some View {
        VStack(alignment: .leading, spacing: 15) {
            Text("This Week's Nutrition")
                .font(.headline)
                .fontWeight(.semibold)
            
            // 营养数据容器，每个组件单独加锁
            HStack(spacing: 10) {
                // Protein
                VStack(spacing: 8) {
                    ZStack {
                        Circle()
                            .stroke(Color.gray.opacity(0.2), lineWidth: 4)
                            .frame(width: 70, height: 70)
                        
                        Image(systemName: "lock.fill")
                            .font(.title2)
                            .foregroundColor(.gray)
                    }
                    
                    Text("Protein")
                        .font(.system(size: 14, weight: .regular))
                        .foregroundColor(Color(red: 0.42, green: 0.45, blue: 0.5))
                        .lineLimit(1)
                    
                    Text("--/--g")
                        .font(.system(size: 14, weight: .regular))
                        .foregroundColor(Color(red: 0.42, green: 0.45, blue: 0.5))
                        .lineLimit(1)
                }
                .frame(maxWidth: .infinity)
                
                // Fat
                VStack(spacing: 8) {
                    ZStack {
                        Circle()
                            .stroke(Color.gray.opacity(0.2), lineWidth: 4)
                            .frame(width: 70, height: 70)
                        
                        Image(systemName: "lock.fill")
                            .font(.title2)
                            .foregroundColor(.gray)
                    }
                    
                    Text("Fat")
                        .font(.system(size: 14, weight: .regular))
                        .foregroundColor(Color(red: 0.42, green: 0.45, blue: 0.5))
                        .lineLimit(1)
                    
                    Text("--/--g")
                        .font(.system(size: 14, weight: .regular))
                        .foregroundColor(Color(red: 0.42, green: 0.45, blue: 0.5))
                        .lineLimit(1)
                }
                .frame(maxWidth: .infinity)
                
                // Calories
                VStack(spacing: 8) {
                    ZStack {
                        Circle()
                            .stroke(Color.gray.opacity(0.2), lineWidth: 4)
                            .frame(width: 70, height: 70)
                        
                        Image(systemName: "lock.fill")
                            .font(.title2)
                            .foregroundColor(.gray)
                    }
                    
                    Text("Calories")
                        .font(.system(size: 14, weight: .regular))
                        .foregroundColor(Color(red: 0.42, green: 0.45, blue: 0.5))
                        .lineLimit(1)
                    
                    Text("--/--kcal")
                        .font(.system(size: 14, weight: .regular))
                        .foregroundColor(Color(red: 0.42, green: 0.45, blue: 0.5))
                        .lineLimit(1)
                }
                .frame(maxWidth: .infinity)
            }
        }
        .padding()
        .background(Color.white)
        .cornerRadius(12)
    }
    
    private func recommendedActivitiesView() -> some View {
        VStack(alignment: .leading, spacing: 15) {
            Text("Recommended Activities")
                .font(.headline)
                .fontWeight(.semibold)
            
            // 运动卡片，删除灰色背景
            HStack(spacing: 10) {
                // Walking
                VStack(spacing: 16) {
                    ZStack {
                        Circle()
                            .fill(Color.gray.opacity(0.1))
                            .frame(width: 75, height: 75)
                        
                        VStack(spacing: 4) {
                            Image(systemName: "figure.walk")
                                .font(.title2)
                                .foregroundColor(.gray)
                                .frame(width: 24, height: 20)
                            
                            Image(systemName: "lock.fill")
                                .font(.caption)
                                .foregroundColor(.gray)
                                .frame(width: 14, height: 14)
                        }
                    }
                    
                    Text("Walking")
                        .font(.system(size: 14, weight: .regular))
                        .foregroundColor(Color(red: 0.42, green: 0.45, blue: 0.5))
                        .lineLimit(1)
                }
                .frame(maxWidth: .infinity)
                
                // Swimming
                VStack(spacing: 16) {
                    ZStack {
                        Circle()
                            .fill(Color.gray.opacity(0.1))
                            .frame(width: 75, height: 75)
                        
                        VStack(spacing: 4) {
                            Image(systemName: "figure.pool.swim")
                                .font(.title2)
                                .foregroundColor(.gray)
                                .frame(width: 24, height: 20)
                            
                            Image(systemName: "lock.fill")
                                .font(.caption)
                                .foregroundColor(.gray)
                                .frame(width: 14, height: 14)
                        }
                    }
                    
                    Text("Swimming")
                        .font(.system(size: 14, weight: .regular))
                        .foregroundColor(Color(red: 0.42, green: 0.45, blue: 0.5))
                        .lineLimit(1)
                }
                .frame(maxWidth: .infinity)
                
                // Cycling
                VStack(spacing: 16) {
                    ZStack {
                        Circle()
                            .fill(Color.gray.opacity(0.1))
                            .frame(width: 75, height: 75)
                        
                        VStack(spacing: 4) {
                            Image(systemName: "bicycle")
                                .font(.title2)
                                .foregroundColor(.gray)
                                .frame(width: 24, height: 20)
                            
                            Image(systemName: "lock.fill")
                                .font(.caption)
                                .foregroundColor(.gray)
                                .frame(width: 14, height: 14)
                        }
                    }
                    
                    Text("Cycling")
                        .font(.system(size: 14, weight: .regular))
                        .foregroundColor(Color(red: 0.42, green: 0.45, blue: 0.5))
                        .lineLimit(1)
                }
                .frame(maxWidth: .infinity)
            }
        }
        .padding()
        .background(Color.white)
        .cornerRadius(12)
    }
    
    private func loginButtonView() -> some View {
        Button(action: {
            userData.exitGuestMode()
        }) {
            Text("Login")
                .font(.system(size: 16, weight: .medium)) // 字重500
                .foregroundColor(Color(red: 1, green: 1, blue: 1)) // 白色文本
                .frame(maxWidth: .infinity)
                .padding(.vertical, 16)
                .background(Color(red: 0.3, green: 0.69, blue: 0.31)) // 绿色背景
                .cornerRadius(9999) // 圆角9999px
        }
        .padding(.top, 20)
        .padding(.horizontal, 20)
    }
}

// MARK: - 预览

// 健康信息网站视图
struct HealthInfoWebView: View {
    @Environment(\.presentationMode) var presentationMode
    let url: String
    
    var body: some View {
        NavigationView {
            WebPageView(url: URL(string: url) ?? URL(string: "https://fsai.pickgoodspro.com/fitScanAiDoc.html")!, title: "Authoritative Sources of Health Information")
        }
    }
}

struct PlanView_Previews: PreviewProvider {
    static var previews: some View {
        PlanView()
            .environmentObject(UserData())
    }
}