import SwiftUI

// 数字键盘输入视图已移到NumberKeyboardView.swift

// 单位选择视图
struct UnitPickerView: View {
    @EnvironmentObject var userData: UserData
    @Binding var isShowing: Bool
    @Binding var selectedUnit: String
    let title: String
    let units: [(String, String)]
    var onUnitChanged: ((String, String) -> Void)? = nil // 当单位改变时的回调，参数是(旧单位,新单位)
    
    var body: some View {
        ZStack {
            // 半透明背景
            Color.black.opacity(0.5)
                .edgesIgnoringSafeArea(.all)
                .onTapGesture {
                    isShowing = false
                }
            
            VStack {
                // 标题
                HStack {
                    Text(title)
                        .font(.headline)
                    
                    Spacer()
                    
                    Button(action: {
                        isShowing = false
                    }) {
                        Image(systemName: "xmark")
                            .foregroundColor(.gray)
                    }
                }
                .padding()
                
                // 选项列表
                VStack(spacing: 0) {
                    ForEach(units, id: \.0) { unit in
                        Button(action: {
                            // 获取旧单位
                            let oldUnit = selectedUnit
                            // 设置新单位
                            selectedUnit = unit.0
                            // 更新UserData中的单位设置
                            if title.contains("Height") {
                                userData.heightUnit = unit.0
                            } else {
                                userData.weightUnit = unit.0
                            }
                            
                            // 调用单位变更回调函数
                            if oldUnit != unit.0 {
                                onUnitChanged?(oldUnit, unit.0)
                            }
                            
                            isShowing = false
                        }) {
                            HStack {
                                Text(unit.1)
                                    .foregroundColor(.primary)
                                
                                Spacer()
                                
                                if selectedUnit == unit.0 {
                                    Image(systemName: "checkmark")
                                        .foregroundColor(.green)
                                }
                            }
                            .padding()
                            .background(selectedUnit == unit.0 ? Color.green.opacity(0.1) : Color.white)
                        }
                    }
                }
            }
            .background(Color.white)
            .cornerRadius(12)
            .padding(.horizontal, 20)
            .frame(maxWidth: .infinity, maxHeight: 200)
        }
    }
}

// 检查用户是否为新用户的API接口
class OnboardingAPI {
    static let shared = OnboardingAPI()
    
    private init() {}
    
    // 检查用户是否为新用户 - 使用后端API接口
    func checkIsNewUser(userData: UserData, completion: @escaping (Bool) -> Void) {
        // 调用UserExtendService获取用户是否为新用户状态
        UserExtendService.shared.getUserExtendInfo(userData: userData) { result in
            DispatchQueue.main.async {
                switch result {
                case .success(let isNewUser):
                    print("检查用户状态: userId=\(userData.id), 后端返回是\(isNewUser ? "新用户" : "老用户")")
                    completion(isNewUser)
                case .failure(let error):
                    print("获取用户新旧状态失败，使用本地判断: \(error.localizedDescription)")
                    // 发生错误时使用本地逻辑作为回退方案
                    let isNewUser = userData.id.isEmpty || !userData.hasSetWeightGoal
                    print("本地判断: userId=\(userData.id), hasSetWeightGoal=\(userData.hasSetWeightGoal), 视为\(isNewUser ? "新用户" : "老用户")")
            completion(isNewUser)
                }
            }
        }
    }
    
    // 保存用户设置数据到后端
    func saveUserSetup(
        targetWeight: Double,
        timelineWeeks: Int,
        gender: String,
        currentWeight: Double,
        age: Int,
        height: Double,
        completion: @escaping (Bool) -> Void
    ) {
        // 模拟网络请求延迟
        DispatchQueue.main.asyncAfter(deadline: .now() + 1.0) {
            // 在实际应用中，这里会发送数据到后端并获取返回结果
            // 包括计算后的计划数据和各种目标值
            
            // 模拟成功响应
            completion(true)
            
            // 打印提交的数据（调试用）
            print("保存用户设置：")
            print("目标体重: \(targetWeight)")
            print("时间线: \(timelineWeeks) 周")
            print("性别: \(gender)")
            print("当前体重: \(currentWeight)")
            print("年龄: \(age)")
            print("身高: \(height)")
        }
    }
}

// 主引导视图
struct OnboardingView: View {
    @EnvironmentObject var userData: UserData
    @State private var isNewUser: Bool = true
    @State private var isLoading: Bool = true
    @State private var showLoadingView: Bool = false
    @State private var showSecondWelcome: Bool = false
    @State private var showCreateAccount: Bool = false
    @State private var isCreatePlanPressed: Bool = false
    
    // 用户设置数据
    @State private var targetWeight: Double = 65.0
    @State private var targetWeightString: String = "65.0"
    @State private var timelineWeeks: Double = 12
    @State private var selectedGender: String = ""
    @State private var currentWeight: String = ""
    @State private var age: String = ""
    @State private var height: String = ""
    
    // 单位设置
    @State private var weightUnit: String = "kg"
    @State private var heightUnit: String = "cm"
    
    // 控制弹窗显示
    @State private var showTargetWeightKeyboard = false
    @State private var showCurrentWeightKeyboard = false
    @State private var showWeightUnitPicker = false
    @State private var showHeightUnitPicker = false
    
    // 错误提示
    @State private var showValidationAlert = false
    @State private var validationErrorMessage = ""
    
    // 体重单位选项
    private let weightUnits = [("kg", "Kilograms (kg)"), ("lbs", "Pounds (lbs)")]
    
    // 身高单位选项
    private let heightUnits = [("cm", "Centimeters (CM)"), ("ft", "Feet/Inches (FT)")]
    
    var body: some View {
        Group {
            if isLoading {
                // 加载指示器
                ProgressView()
                    .progressViewStyle(CircularProgressViewStyle())
                    .scaleEffect(1.5)
                    .background(Color.white)
            } else if isNewUser {
                if showSecondWelcome {
                    // 第二个欢迎页面（已废弃，不再展示）
                    Color.clear
                        .onAppear { showSecondWelcome = false }
                } else if showLoadingView {
                    // 加载页面（图二）
                    LoadingView(onComplete: {
                        // LoadingView完成后直接进入注册页面
                        showCreateAccount = true
                    })
                    .environmentObject(userData)
                } else {
                    // 新用户设置页面（图一）
                    setupView
                        .background(Color.white)
                }
            } else {
                // 空视图，实际使用时会直接进入主应用
                Color.clear.onAppear {
                    // 发送通知，导航到Nutrition页面（默认首页）
                    NotificationCenter.default.post(name: Notification.Name("NavigateToNutritionView"), object: nil)
                }
            }
        }
        .onAppear {
            // 统计
            XDTrackTool.shared.appear("引导页面")

            // 检查用户是否为新用户
            checkUserStatus()

            // 初始化单位设置
            weightUnit = userData.weightUnit
            heightUnit = userData.heightUnit

            print("OnboardingView出现")
        }
        .onReceive(NotificationCenter.default.publisher(for: Notification.Name("NavigateToPlanView"))) { _ in }
        .alert("Input Error", isPresented: $showValidationAlert) {
            Button("OK", role: .cancel) { }
        } message: {
            Text(validationErrorMessage)
        }
        // 引导完成后，进入注册页面（不可下拉返回）
        .fullScreenCover(isPresented: $showCreateAccount) {
            NavigationView {
                CreateAccountView(disableBackNavigation: true)
                    .environmentObject(userData)
            }
            .interactiveDismissDisabled(true)
        }
    }
    
    // 检查用户状态
    private func checkUserStatus() {
        isLoading = true
        
        // 仅在用户未登录时显示引导；一旦登录成功则不再出现
        DispatchQueue.main.async {
            self.isNewUser = userData.accessToken.isEmpty
            self.isLoading = false
        }
    }
    
    // 新用户设置视图
    private var setupView: some View {
        ScrollView {
            VStack(alignment: .leading, spacing: 30) {
                // 标题
                VStack(alignment: .leading, spacing: 8) {
                    Text("Let's Get Started")
                        .font(.largeTitle)
                        .fontWeight(.bold)
                    
                    Text("Help us create your personalized weight management plan")
                        .font(.body)
                        .foregroundColor(.secondary)
                }
                .padding(.horizontal)
                
                // 目标体重设置
                VStack(alignment: .leading, spacing: 10) {
                    Text("Weight Goal")
                        .font(.headline)
                        .padding(.horizontal)
                    
                    // 目标体重显示和编辑按钮
                    Button(action: {
                        targetWeightString = String(format: "%.1f", targetWeight)
                        showTargetWeightKeyboard = true
                    }) {
                        HStack {
                            Text("\(String(format: "%.1f", targetWeight)) \(weightUnit)")
                                .font(.title)
                                .fontWeight(.bold)
                                .foregroundColor(.green)
                            
                            Spacer()
                            
                            Image(systemName: "pencil.circle")
                                .font(.title2)
                                .foregroundColor(.green)
                        }
                        .padding()
                        .background(Color(UIColor.secondarySystemBackground))
                        .cornerRadius(10)
                    }
                    .padding(.horizontal)
                    
                    Text("Set your target weight")
                        .font(.caption)
                        .foregroundColor(.secondary)
                        .padding(.horizontal)
                }
                
                // 目标时间线
                VStack(alignment: .leading, spacing: 10) {
                    Text("Your Goal Timeline")
                        .font(.headline)
                        .padding(.horizontal)
                    
                    // 滑块
                    VStack(spacing: 5) {
                        Slider(value: $timelineWeeks, in: 4...24, step: 1)
                            .accentColor(.green)
                            .padding(.horizontal)
                        
                        HStack {
                            Spacer()
                            Text("\(Int(timelineWeeks)) weeks")
                                .font(.caption)
                                .foregroundColor(.secondary)
                        }
                        .padding(.horizontal)
                    }
                }
                
                // 个人详细信息
                VStack(alignment: .leading, spacing: 20) {
                    Text("Personal Details")
                        .font(.headline)
                        .padding(.horizontal)
                    
                    // 性别选择
                    VStack(alignment: .leading, spacing: 10) {
                        Text("Gender")
                            .font(.subheadline)
                            .padding(.horizontal)
                        
                        HStack(spacing: 15) {
                            // 男性选项
                            Button(action: {
                                selectedGender = "Male"
                            }) {
                                HStack {
                                    ZStack {
                                        Circle()
                                            .stroke(Color.gray, lineWidth: 1)
                                            .fill(Color.white)
                                            .frame(width: 20, height: 20)
                                        
                                        if selectedGender == "Male" {
                                            Image(systemName: "checkmark")
                                                .foregroundColor(.green)
                                                .font(.system(size: 12, weight: .bold))
                                        }
                                    }
                                    
                                    Text("Male")
                                }
                            }
                            .buttonStyle(PlainButtonStyle())
                            
                            // 女性选项
                            Button(action: {
                                selectedGender = "Female"
                            }) {
                                HStack {
                                    ZStack {
                                        Circle()
                                            .stroke(Color.gray, lineWidth: 1)
                                            .fill(Color.white)
                                            .frame(width: 20, height: 20)
                                        
                                        if selectedGender == "Female" {
                                            Image(systemName: "checkmark")
                                                .foregroundColor(.green)
                                                .font(.system(size: 12, weight: .bold))
                                        }
                                    }
                                    
                                    Text("Female")
                                }
                            }
                            .buttonStyle(PlainButtonStyle())
                            
                            // 其他选项
                            Button(action: {
                                selectedGender = "Other"
                            }) {
                                HStack {
                                    ZStack {
                                        Circle()
                                            .stroke(Color.gray, lineWidth: 1)
                                            .fill(Color.white)
                                            .frame(width: 20, height: 20)
                                        
                                        if selectedGender == "Other" {
                                            Image(systemName: "checkmark")
                                                .foregroundColor(.green)
                                                .font(.system(size: 12, weight: .bold))
                                        }
                                    }
                                    
                                    Text("Other")
                                }
                            }
                            .buttonStyle(PlainButtonStyle())
                        }
                        .padding(.horizontal)
                    }
                    
                    // 当前体重
                    VStack(alignment: .leading, spacing: 8) {
                        Text("Current Weight")
                            .font(.subheadline)
                            .padding(.horizontal)
                        
                        HStack {
                            TextField("Enter weight", text: $currentWeight)
                                .keyboardType(.decimalPad)
                                .padding()
                                .background(Color(UIColor.secondarySystemBackground))
                                .cornerRadius(8)
                                .adaptiveKeyboard()
                                .onChange(of: currentWeight) { oldValue, newValue in
                                    currentWeight = newValue.limitedTo999_99()
                                }
                            
                            // 单位选择按钮
                            Button(action: {
                                showWeightUnitPicker = true
                            }) {
                                HStack {
                                    Text(weightUnit)
                                    Image(systemName: "chevron.down")
                                        .font(.caption)
                                }
                                .padding(.horizontal, 10)
                                .padding(.vertical, 8)
                                .background(Color(UIColor.secondarySystemBackground))
                                .cornerRadius(8)
                            }
                        }
                        .padding(.horizontal)
                    }
                    
                    // 年龄
                    VStack(alignment: .leading, spacing: 8) {
                        Text("Age")
                            .font(.subheadline)
                            .padding(.horizontal)
                        
                        TextField("Enter age", text: $age)
                            .keyboardType(.numberPad)
                            .padding()
                            .background(Color(UIColor.secondarySystemBackground))
                            .cornerRadius(8)
                            .padding(.horizontal)
                            .adaptiveKeyboard()
                            .onChange(of: age) { oldValue, newValue in
                                // 年龄只允许整数，限制最大值为999
                                let filtered = newValue.filter { "0123456789".contains($0) }
                                let limited = String(filtered.prefix(3))
                                if let intValue = Int(limited), intValue > 999 {
                                    age = "999"
                                } else {
                                    age = limited
                                }
                            }
                    }
                    
                    // 身高
                    VStack(alignment: .leading, spacing: 8) {
                        Text("height")
                            .font(.subheadline)
                            .padding(.horizontal)
                        
                        HStack {
                            TextField("Enter height", text: $height)
                                .keyboardType(.decimalPad)
                                .padding()
                                .background(Color(UIColor.secondarySystemBackground))
                                .cornerRadius(8)
                                .adaptiveKeyboard()
                                .onChange(of: height) { oldValue, newValue in
                                    height = newValue.limitedTo999_99()
                                }
                            
                            // 单位选择按钮
                            Button(action: {
                                showHeightUnitPicker = true
                            }) {
                                HStack {
                                    Text(heightUnit)
                                    Image(systemName: "chevron.down")
                                        .font(.caption)
                                }
                                .padding(.horizontal, 10)
                                .padding(.vertical, 8)
                                .background(Color(UIColor.secondarySystemBackground))
                                .cornerRadius(8)
                            }
                        }
                        .padding(.horizontal)
                    }
                    
                    // 提示信息
                    HStack(alignment: .top, spacing: 10) {
                        Image(systemName: "info.circle.fill")
                            .foregroundColor(.green)
                        
                        Text("These details help us provide more accurate weight management recommendations")
                            .font(.caption)
                            .foregroundColor(.secondary)
                            .fixedSize(horizontal: false, vertical: true)
                            .frame(maxWidth: .infinity, alignment: .leading)
                            .lineLimit(nil)
                            .multilineTextAlignment(.leading)
                    }
                    .padding()
                    .frame(maxWidth: .infinity)
                    .background(Color.green.opacity(0.1))
                    .cornerRadius(8)
                    .padding(.horizontal)
                }
                
                // 创建计划按钮
                VStack(spacing: 15) {
                    Button(action: {
                        // 先进行数据验证
                        let validationResult = validateOnboardingData()
                        if !validationResult.isValid {
                            validationErrorMessage = validationResult.errorMessage
                            showValidationAlert = true
                            return
                        }
                        
                        isCreatePlanPressed = true
                        DispatchQueue.main.asyncAfter(deadline: .now() + 0.2) {
                            isCreatePlanPressed = false
                            createPlan()
                        }
                    }) {
                        Text("Create My Plan")
                            .font(.headline)
                            .foregroundColor(.white)
                            .frame(maxWidth: .infinity)
                            .padding()
                            .background(canCreatePlan() ? (isCreatePlanPressed ? Color.green.opacity(0.7) : Color.green) : Color.gray)
                            .cornerRadius(10)
                    }
                    .disabled(!canCreatePlan())
                    .padding(.horizontal)
                    
                    // 跳过按钮
                    Button(action: {
                        // 跳过设置
                        skipSetup()
                    }) {
                        Text("Skip for Now")
                            .font(.subheadline)
                            .foregroundColor(.secondary)
                    }
                    .buttonStyle(PressableButtonStyle(color: .secondary))
                }
                
                Spacer(minLength: 30)
            }
            .padding(.vertical, 20)
            // 目标体重数字键盘
            .overlay(
                Group {
                    if showTargetWeightKeyboard {
                        NumberKeyboardView(
                            value: $targetWeightString,
                            showKeyboard: $showTargetWeightKeyboard,
                            selectedUnit: $weightUnit,
                            title: "Enter Weight",
                            units: ["kg", "lbs"],
                            onConfirm: { value, unit in
                                if let doubleValue = Double(value) {
                                    targetWeight = doubleValue
                                    weightUnit = unit
                                    
                                    // 同步更新到用户数据
                                    userData.weightUnit = unit
                                }
                            }
                        )
                    }
                    
                    if showCurrentWeightKeyboard {
                        NumberKeyboardView(
                            value: $currentWeight,
                            showKeyboard: $showCurrentWeightKeyboard,
                            selectedUnit: $weightUnit,
                            title: "Enter Weight",
                            units: ["kg", "lbs"],
                            onConfirm: { value, unit in
                                currentWeight = value
                                weightUnit = unit
                                
                                // 同步更新到用户数据
                                userData.weightUnit = unit
                            }
                        )
                    }
                    
                    if showWeightUnitPicker {
                        UnitPickerView(
                            isShowing: $showWeightUnitPicker,
                            selectedUnit: $weightUnit,
                            title: "Select Unit",
                            units: weightUnits,
                            onUnitChanged: { oldUnit, newUnit in
                                // 转换当前体重
                                if let weight = Double(currentWeight) {
                                    if oldUnit == "kg" && newUnit == "lbs" {
                                        // 从千克转换为磅
                                        currentWeight = String(format: "%.1f", weight * 2.20462)
                                    } else if oldUnit == "lbs" && newUnit == "kg" {
                                        // 从磅转换为千克
                                        currentWeight = String(format: "%.1f", weight / 2.20462)
                                    }
                                }
                                
                                // 转换目标体重
                                if oldUnit == "kg" && newUnit == "lbs" {
                                    // 从千克转换为磅
                                    targetWeight = targetWeight * 2.20462
                                } else if oldUnit == "lbs" && newUnit == "kg" {
                                    // 从磅转换为千克
                                    targetWeight = targetWeight / 2.20462
                                }
                            }
                        )
                    }
                    
                    if showHeightUnitPicker {
                        UnitPickerView(
                            isShowing: $showHeightUnitPicker,
                            selectedUnit: $heightUnit,
                            title: "Height Unit",
                            units: heightUnits,
                            onUnitChanged: { oldUnit, newUnit in
                                // 转换身高
                                if let heightValue = Double(height) {
                                    if oldUnit == "cm" && newUnit == "ft" {
                                        // 从厘米转换为英尺
                                        height = String(format: "%.1f", heightValue / 30.48)
                                        print("身高单位转换: \(heightValue)厘米 → \(height)英尺")
                                    } else if oldUnit == "ft" && newUnit == "cm" {
                                        // 从英尺转换为厘米
                                        height = String(format: "%.1f", heightValue * 30.48)
                                        print("身高单位转换: \(heightValue)英尺 → \(height)厘米")
                                    }
                                }
                            }
                        )
                    }
                }
            )
            // 添加点击手势来隐藏键盘
            .contentShape(Rectangle())
            .onTapGesture {
                hideKeyboard()
            }
        }
    }
    
    // 创建计划
    private func createPlan() {
        // 数据验证
        let validationResult = validateOnboardingData()
        if !validationResult.isValid {
            // 显示错误提示（这里可以添加Alert或其他UI提示）
            print("❌ 数据验证失败: \(validationResult.errorMessage)")
            return
        }
        
        // 验证输入
        guard let currentWeightValue = Double(currentWeight),
              let ageValue = Int(age),
              let heightValue = Double(height) else {
            // 输入验证失败，显示错误提示
            print("❌ 数据类型验证失败")
            return
        }
        
        // 计算正确的体重值（根据单位）并写入本地（暂存，待注册后绑定）
        var targetWeightKg = targetWeight
        var currentWeightKg = currentWeightValue
        var heightCm = heightValue

        if weightUnit == "lbs" {
            targetWeightKg = targetWeight / 2.20462
            currentWeightKg = currentWeightValue / 2.20462
        }

        if heightUnit == "ft" {
            heightCm = heightValue * 30.48
            print("身高单位转换: \(heightValue)英尺 → \(heightCm)厘米")
        }

        // 保存用户数据到UserData（仅本地暂存）
        userData.goalWeight = targetWeightKg
        userData.initialWeight = currentWeightKg
        userData.startWeight = currentWeightKg
        userData.currentManagementWeight = currentWeightKg
        userData.goalTimelineWeeks = Int(timelineWeeks)
        userData.gender = selectedGender
        userData.age = ageValue
        userData.height = heightCm
        userData.weightUnit = weightUnit
        userData.heightUnit = heightUnit
        userData.goalStartDate = Date()
        userData.hasSetWeightGoal = true
        userData.goalDate = Calendar.current.date(byAdding: .day, value: Int(timelineWeeks) * 7, to: Date()) ?? Date()
        userData.saveSettings()

        // 标记：待注册后将这些数据绑定到账号
        UserDefaults.standard.set(true, forKey: "pendingOnboardingBinding")

        // 显示加载视图
        showLoadingView = true
    }
    
    // 数据验证函数
    private func validateOnboardingData() -> (isValid: Bool, errorMessage: String) {
        // 检查所有字段是否都已填写
        if currentWeight.trimmingCharacters(in: .whitespacesAndNewlines).isEmpty {
            return (false, "Please enter your current weight")
        }
        
        if age.trimmingCharacters(in: .whitespacesAndNewlines).isEmpty {
            return (false, "Please enter your age")
        }
        
        if height.trimmingCharacters(in: .whitespacesAndNewlines).isEmpty {
            return (false, "Please enter your height")
        }
        
        if selectedGender.trimmingCharacters(in: .whitespacesAndNewlines).isEmpty {
            return (false, "Please select your gender")
        }
        
        // 验证数据类型和范围
        guard let currentWeightValue = Double(currentWeight) else {
            return (false, "Please enter a valid weight")
        }
        
        guard let ageValue = Int(age) else {
            return (false, "Please enter a valid age")
        }
        
        guard let heightValue = Double(height) else {
            return (false, "Please enter a valid height")
        }
        
        // 验证体重范围（考虑单位）
        let minWeight = weightUnit == "kg" ? 20.0 : 44.0  // 20kg or 44lbs
        let maxWeight = weightUnit == "kg" ? 300.0 : 660.0  // 300kg or 660lbs
        
        if currentWeightValue < minWeight || currentWeightValue > maxWeight {
            return (false, "Weight must be between \(Int(minWeight)) and \(Int(maxWeight)) \(weightUnit)")
        }
        
        // 验证目标体重范围
        if targetWeight < minWeight || targetWeight > maxWeight {
            return (false, "Goal weight must be between \(Int(minWeight)) and \(Int(maxWeight)) \(weightUnit)")
        }
        
        // 验证年龄范围
        if ageValue < 1 || ageValue > 120 {
            return (false, "Age must be between 1 and 120 years")
        }
        
        // 验证身高范围（考虑单位）
        let minHeight = heightUnit == "cm" ? 10.0 : 0.33  // 10cm or 0.33ft
        let maxHeight = heightUnit == "cm" ? 250.0 : 8.2  // 250cm or 8.2ft
        
        if heightValue < minHeight || heightValue > maxHeight {
            let minStr = heightUnit == "cm" ? "10" : "0.33"
            let maxStr = heightUnit == "cm" ? "250" : "8.2"
            return (false, "Height must be between \(minStr) and \(maxStr) \(heightUnit)")
        }
        
        // 验证时间线范围
        if timelineWeeks < 4 || timelineWeeks > 24 {  // 4-24周
            return (false, "Timeline must be between 4 and 24 weeks")
        }
        
        return (true, "")
    }
    
    // 检查是否可以创建计划
    private func canCreatePlan() -> Bool {
        let validationResult = validateOnboardingData()
        return validationResult.isValid
    }
    
    // 跳过设置
    private func skipSetup() {
        // 跳过设置：进入游客模式并进入首页
        userData.setGuestMode()
        userData.saveSettings()
        isNewUser = false

        // 通知App退出首次启动并进入主界面
        NotificationCenter.default.post(name: Notification.Name("WelcomeSkipped"), object: nil)
        NotificationCenter.default.post(name: Notification.Name("GuestLoggedIn"), object: nil)
        NotificationCenter.default.post(name: Notification.Name("UserCompletedOnboarding"), object: nil)

        print("跳过设置，已进入游客模式并导航到首页")
    }
    
    // 进入应用的主界面
    private func navigateToMainView() {
        // 确保选中的标签是Nutrition页面（索引为0）
        userData.selectedTab = 0
        
        // 通知UI更新
        userData.objectWillChange.send()
        
        // 直接标记为非新用户
        isNewUser = false
        
        // 确保OnboardingView被隐藏，导航到Nutrition页面
        NotificationCenter.default.post(name: Notification.Name("UserCompletedOnboarding"), object: nil)
        
        print("完成设置，导航到Nutrition页面")
    }
}

// 加载视图（图二所示）
struct LoadingView: View {
    @EnvironmentObject var userData: UserData
    let onComplete: () -> Void
    
    @State private var greenCircleScale: CGFloat = 1.0
    @State private var progressValue: CGFloat = 0.0
    @State private var isAnimating = false
    
    var body: some View {
        ZStack {
            // 添加不透明白色背景
            Color.white
                .edgesIgnoringSafeArea(.all)
            
            VStack(spacing: 30) {
                // 绿圈动画
                Circle()
                    .fill(Color.green.opacity(0.3))
                    .frame(width: 200, height: 200)
                    .overlay(
                        Circle()
                            .fill(Color.green.opacity(0.5))
                            .frame(width: 150, height: 150)
                            .overlay(
                                Circle()
                                    .fill(Color.green.opacity(0.7))
                                    .frame(width: 100, height: 100)
                                    .overlay(
                                        Circle()
                                            .fill(Color.green)
                                            .frame(width: 50, height: 50)
                                            .scaleEffect(greenCircleScale)
                                    )
                            )
                    )
                    .scaleEffect(isAnimating ? 1.05 : 1.0)
                
                // 标题
                Text("HealthAI")
                    .font(.largeTitle)
                    .fontWeight(.bold)
                    .foregroundColor(.green)
                
                // 副标题
                Text("Creating Your Health Plan")
                    .font(.title2)
                    .fontWeight(.medium)
                
                // 描述
                Text("Our AI is analyzing your data to create a personalized health management plan")
                    .font(.body)
                    .foregroundColor(.secondary)
                    .multilineTextAlignment(.center)
                    .padding(.horizontal, 40)
                
                // 进度条
                ZStack(alignment: .leading) {
                    Rectangle()
                        .foregroundColor(Color.green.opacity(0.2))
                        .frame(height: 8)
                        .cornerRadius(4)
                    
                    Rectangle()
                        .foregroundColor(.green)
                        .frame(width: progressValue, height: 8)
                        .cornerRadius(4)
                }
                .frame(width: 250)
                
                Text("This may take a few moments")
                    .font(.caption)
                    .foregroundColor(.secondary)
                    .padding(.top, 5)
                
                Spacer()
            }
            .padding(.top, 80)
        }
        .onAppear {
            // 启动动画
            startAnimations()
            
            // 模拟加载完成
            DispatchQueue.main.asyncAfter(deadline: .now() + 2.0) {
                // 保存设置到UserDefaults
                userData.saveSettings()
                
                DispatchQueue.main.async {
                    print("LoadingView完成，跳转到注册页面")
                    onComplete()
                }
            }
        }
    }
    
    // 开始动画
    private func startAnimations() {
        // 圆形脉动动画
        withAnimation(Animation.easeInOut(duration: 0.7).repeatForever(autoreverses: true)) {
            greenCircleScale = 1.2
            isAnimating = true
        }
        
        // 进度条动画
        withAnimation(Animation.linear(duration: 2.0)) {
            progressValue = 250  // 进度条最大宽度
        }
    }
}

// 预览
struct OnboardingView_Previews: PreviewProvider {
    static var previews: some View {
        OnboardingView()
            .environmentObject(UserData())
    }
}
