/*
  Localizable.strings
  FitScanAI

  Created by wxd on 2024/11/21.
  Modified at May 9 25
*/

"test" = "test";

// 登录页面
"FitScanAI" = "FitScanAI";
"Your AI-Powered Health & Nutrition Partner" = "Your AI-Powered Health & Nutrition Partner";
"Continue with Apple" = "Continue with Apple";
"Continue with Google" = "Continue with Google";
"or" = "or";
"Email address" = "Email address";
"Password" = "Password";
"Forgot Password?" = "Forgot Password?";
"Sign In" = "Sign In";
"Continue as Guest" = "Continue as Guest";
"Don't have an account?" = "Don't have an account?";
"Sign Up" = "Sign Up";
"By continuing, you agree to our" = "By continuing, you agree to our";
"Terms of Service" = "Terms of Service";
" and " = " and ";
"and" = "and";
"Privacy Policy" = "Privacy Policy";

// 登录页面 - 弹窗与错误
"Input Error" = "Input Error";
"Email and password cannot be empty" = "Email and password cannot be empty";
"Network Error" = "Network Error";
"No internet connection available. Please check your network settings and try again." = "No internet connection available. Please check your network settings and try again.";
"Login Failed" = "Login Failed";
"Invalid email or password. Please check your credentials and try again." = "Invalid email or password. Please check your credentials and try again.";
"Login failed. Please verify your account information." = "Login failed. Please verify your account information.";
"Connection timeout. Please check your network connection and try again." = "Connection timeout. Please check your network connection and try again.";
"No internet connection. Please check your network settings and ensure the app has network access permission." = "No internet connection. Please check your network settings and ensure the app has network access permission.";
"Account issue detected. Your account may be locked or disabled. Please contact support." = "Account issue detected. Your account may be locked or disabled. Please contact support.";
"Login failed. Please check your email and password, then try again." = "Login failed. Please check your email and password, then try again.";

// 引导页面
"Skip" = "Skip";
"Get Started" = "Get Started";
"Your journey to better health starts now" = "Your journey to better health starts now";
"Let's Get Started" = "Let's Get Started";
"Help us create your personalized weight management plan" = "Help us create your personalized weight management plan";
"Weight Goal" = "Weight Goal";
"Set your target weight" = "Set your target weight";
"Your Goal Timeline" = "Your Goal Timeline";
"Personal Details" = "Personal Details";
"Gender" = "Gender";
"Male" = "Male";
"Female" = "Female";
"Other" = "Other";
"Current Weight" = "Current Weight";
"Enter weight" = "Enter weight";
"Age" = "Age";
"Enter age" = "Enter age";
"height" = "height";
"Enter height" = "Enter height";
"These details help us provide more accurate weight management recommendations" = "These details help us provide more accurate weight management recommendations";
"Create My Plan" = "Create My Plan";
"Skip for Now" = "Skip for Now";
"Enter Weight" = "Enter Weight";
"Select Unit" = "Select Unit";
"Height Unit" = "Height Unit";

// 引导加载页面
"HealthAI" = "HealthAI";
"Creating Your Health Plan" = "Creating Your Health Plan";
"Our AI is analyzing your data to create a personalized health management plan" = "Our AI is analyzing your data to create a personalized health management plan";
"This may take a few moments" = "This may take a few moments";

// 引导页面（第二欢迎页）
"Processing..." = "Processing...";
"Start 3-Day Free Trial - $5.99/month" = "Start 3-Day Free Trial - $5.99/month";
"Skip for now (limited features)" = "Skip for now (limited features)";
"Cancel anytime" = "Cancel anytime";
"Subscription Failed" = "Subscription Failed";

// Home 页面
"Failed to load page" = "Failed to load page";
"Loading..." = "Loading...";
"Nutrition" = "Nutrition";
"Plan" = "Plan";
"Weight" = "Weight";
"Profile" = "Profile";
"This feature requires AI analysis and needs login to access. Please login to use the food scanning feature." = "This feature requires AI analysis and needs login to access. Please login to use the food scanning feature.";
"Guest Mode" = "Guest Mode";
"Login to view personal data" = "Login to view personal data";
"Scan Food Now" = "Scan Food Now";
"Calories" = "Calories";
"kcal" = "kcal";
"Protein" = "Protein";
"Today's Food Diary" = "Today's Food Diary";
"more" = "more";
"Loading food records..." = "Loading food records...";
"Login for Full Features" = "Login for Full Features";
"Login to view personal data and unlock premium features" = "Login to view personal data and unlock premium features";
"Login" = "Login";
"Unlock Premium Features" = "Unlock Premium Features";
"Get the most out of FitScanAI >" = "Get the most out of FitScanAI >";
"Premium Features" = "Premium Features";
"Unlimited food analysis" = "Unlimited food analysis";
"Detailed nutrition insights" = "Detailed nutrition insights";
"Personal nutrition coach" = "Personal nutrition coach";
"Monthly Plan" = "Monthly Plan";
"$5.99" = "$5.99";
"per month" = "per month";
"Annual Plan" = "Annual Plan";
"$59.9" = "$59.9";
"per year" = "per year";
"Save17%" = "Save17%";
"Home" = "Home";
"Analysis" = "Analysis";
"Position food in the frame" = "Position food in the frame";
"For best results, ensure good lighting" = "For best results, ensure good lighting";
"Choose Image Source" = "Choose Image Source";
"Select how you want to add your image" = "Select how you want to add your image";
"Take Photo" = "Take Photo";
"Choose from Gallery" = "Choose from Gallery";
"Go to Settings" = "Go to Settings";
"Device Not Supported" = "Device Not Supported";
"Limited photo access requires iOS 14 or later.\nPlease update your device to use this feature." = "Limited photo access requires iOS 14 or later.\nPlease update your device to use this feature.";
"OK" = "OK";
"Food Analysis" = "Food Analysis";
"Loading image..." = "Loading image...";
"Analysis in Progress" = "Analysis in Progress";
"Estimated Calories" = "Estimated Calories";
"Water Content" = "Water Content";
"Total Fat" = "Total Fat";
"Analyzing additional nutrients..." = "Analyzing additional nutrients...";
"Analysis Error" = "Analysis Error";
"Nutrition Score" = "Nutrition Score";
"Impact on Daily Goals" = "Impact on Daily Goals";
"High in Calories" = "High in Calories";
"Nutrition Breakdown" = "Nutrition Breakdown";
"Carbs" = "Carbs";
"Error" = "Error";
"Membership Status" = "Membership Status";
"Premium Member" = "Premium Member";
"Settings" = "Settings";
"Notifications" = "Notifications";
"Units (kg/lbs)" = "Units (kg/lbs)";
"Password Management" = "Password Management";
"Legal & Privacy" = "Legal & Privacy";
"Delete Account" = "Delete Account";
"Logout" = "Logout";
"Edit Profile" = "Edit Profile";
"Change Avatar" = "Change Avatar";
"Nickname" = "Nickname";
"2-30 characters" = "2-30 characters";
"Save Changes" = "Save Changes";
"Reminder Time" = "Reminder Time";
"07:30 AM / 10:30 PM" = "07:30 AM / 10:30 PM";
"Notifications help you maintain consistent weight tracking" = "Notifications help you maintain consistent weight tracking";
"Weight Unit" = "Weight Unit";
"KG (Kilograms)" = "KG (Kilograms)";
"1 kg = 2.205 lbs" = "1 kg = 2.205 lbs";
"LBS (Pounds)" = "LBS (Pounds)";
"1 lb = 0.454 kg" = "1 lb = 0.454 kg";
"CM (Centimeters)" = "CM (Centimeters)";
"FT (Feet/Inches)" = "FT (Feet/Inches)";
"Changes will apply to all future measurements" = "Changes will apply to all future measurements";
"Food Journal" = "Food Journal";
"No Food Records" = "No Food Records";
"No food recorded for this day" = "No food recorded for this day";
"Total" = "Total";
"Set Your Health Goals" = "Set Your Health Goals";
"Create personalized nutrition targets for better tracking" = "Create personalized nutrition targets for better tracking";
"Set Goals" = "Set Goals";

// 注册页面
"Create Account" = "Create Account";
"Register with your email" = "Register with your email";
"Full Name" = "Full Name";
"Enter your full name" = "Enter your full name";
"Email Address" = "Email Address";
"Enter your email address" = "Enter your email address";
"Verification Code" = "Verification Code";
"Enter code" = "Enter code";
"Sending..." = "Sending...";
"Get Code" = "Get Code";
"Why create an account?" = "Why create an account?";
"Save your preferences and settings" = "Save your preferences and settings";
"Access exclusive content and features" = "Access exclusive content and features";
"Get personalized recommendations" = "Get personalized recommendations";
"Password Requirements" = "Password Requirements";
"6+ chars" = "6+ chars";
"Numbers" = "Numbers";
"Uppercase" = "Uppercase";
"Lowercase" = "Lowercase";
"Special chars" = "Special chars";
"At least 6 characters" = "At least 6 characters";
"Include numbers" = "Include numbers";
"Include uppercase letters" = "Include uppercase letters";
"Include lowercase letters" = "Include lowercase letters";
"Include special characters" = "Include special characters";
"Create a password" = "Create a password";
"or continue with" = "or continue with";
"Apple" = "Apple";
"Google" = "Google";
"By creating an account, you agree to our" = "By creating an account, you agree to our";
"Invalid Email" = "Invalid Email";
"Please enter a valid email address" = "Please enter a valid email address";
"Verification Code Error" = "Verification Code Error";
"This email is already registered. Please use another email." = "This email is already registered. Please use another email.";
"Invalid verification code. Please check and try again." = "Invalid verification code. Please check and try again.";
"Please use a stronger password." = "Please use a stronger password.";
"Password confirmation doesn't match." = "Password confirmation doesn't match.";
"Password doesn't meet the security requirements." = "Password doesn't meet the security requirements.";
"Invalid request. Please check your information and try again." = "Invalid request. Please check your information and try again.";
"Authorization error. Please try again later." = "Authorization error. Please try again later.";
"The service is currently unavailable. Please try again later." = "The service is currently unavailable. Please try again later.";
"This email address is already in use." = "This email address is already in use.";
"Too many attempts. Please try again later." = "Too many attempts. Please try again later.";
"Network connection error. Please check your internet connection." = "Network connection error. Please check your internet connection.";
"Request timeout. Please try again later." = "Request timeout. Please try again later.";
"Server error. Please try again later." = "Server error. Please try again later.";
"Registration failed. Please try again later." = "Registration failed. Please try again later.";
"Please fill in all fields" = "Please fill in all fields";
"Weak Password" = "Weak Password";
"Account created successfully, but login failed. Please try to log in manually." = "Account created successfully, but login failed. Please try to log in manually.";
"Email Already Registered" = "Email Already Registered";
"This email address is already registered. Please use a different email or try logging in." = "This email address is already registered. Please use a different email or try logging in.";

// 忘记密码页面
"Reset Password" = "Reset Password";
"Forgot your password?" = "Forgot your password?";
"Don't worry! Enter your email and we'll help you reset it." = "Don't worry! Enter your email and we'll help you reset it.";
"Email Verification Code" = "Email Verification Code";
"Enter verification code" = "Enter verification code";
"Send Code" = "Send Code";
"Enter your password" = "Enter your password";
"Confirm Password" = "Confirm Password";
"Re-enter your password" = "Re-enter your password";
"Passwords match" = "Passwords match";
"Passwords don't match" = "Passwords don't match";
"Remember your password?" = "Remember your password?";
"Password Mismatch" = "Password Mismatch";
"The passwords you entered do not match. Please try again." = "The passwords you entered do not match. Please try again.";
"Password Reset Successful" = "Password Reset Successful";
"Your password has been reset successfully. You can now log in with your new password." = "Your password has been reset successfully. You can now log in with your new password.";
"Password Reset Failed" = "Password Reset Failed";
"This email is not registered. Please check the email address." = "This email is not registered. Please check the email address.";
"You're requesting codes too frequently. Please try again later." = "You're requesting codes too frequently. Please try again later.";
"Invalid email format. Please check and try again." = "Invalid email format. Please check and try again.";
"Failed to send verification code. Please try again later." = "Failed to send verification code. Please try again later.";
"User not found. This email is not registered." = "User not found. This email is not registered.";
"Password reset failed. Please try again later." = "Password reset failed. Please try again later.";

// Weight 页面
"Weight Management" = "Weight Management";
"Track your progress daily" = "Track your progress daily";
"Morning Weight (Fasting)" = "Morning Weight (Fasting)";
"7:30 AM" = "7:30 AM";
"Evening Weight (Before bed)" = "Evening Weight (Before bed)";
"10:30 PM" = "10:30 PM";
"Goal Weight" = "Goal Weight";
"to go" = "to go";
"30-Day Weight Trend" = "30-Day Weight Trend";
"Confirm" = "Confirm";
"No Weight Data Available" = "No Weight Data Available";
"Start recording your morning and evening weight to see your 30-day trend" = "Start recording your morning and evening weight to see your 30-day trend";
"Goal Weight ment" = "Goal Weight ment";
"Select Date" = "Select Date";
"Today's Exercise" = "Today's Exercise";
"No Exercise Recorded Today" = "No Exercise Recorded Today";
"Exercise Type" = "Exercise Type";
"Duration" = "Duration";
"hours" = "hours";
"minutes" = "minutes";
"Intensity" = "Intensity";
"Save" = "Save";
"Edit Goal Weight" = "Edit Goal Weight";
"Recent Changes" = "Recent Changes";
"Daily Statistics" = "Daily Statistics";
"Weight Change" = "Weight Change";
"Average Weight" = "Average Weight";
"Total Time:" = "Total Time:";
"mins" = "mins";
"Exercise Records" = "Exercise Records";
"No Exercise Records" = "No Exercise Records";
"Start adding your exercise activities" = "Start adding your exercise activities";
"No weight data to display" = "No weight data to display";
"No weight records in the selected time period" = "No weight records in the selected time period";

// Weight 页面 - 无会员/无目标 提示
"Login to view personal data and\nunlock premium features" = "Login to view personal data and\nunlock premium features";

// Plan 页面 - 正常显示
"Your Health Plan" = "Your Health Plan";
"This Week's Nutrition" = "This Week's Nutrition";
"This Week's Exercise Calories" = "This Week's Exercise Calories";
"calories burned" = "calories burned";
"Recommended Activities" = "Recommended Activities";
"Daily Check-in" = "Daily Check-in";
"No check-in data" = "No check-in data";
"Current" = "Current";
"No weight data available" = "No weight data available";
"-- / ---" = "-- / ---";
"--/--g" = "--/--g";
"--/--kcal" = "--/--kcal";
"Week" = "Week";
"of" = "of";

// Plan 页面 - 无会员/无目标 提示
"Guest" = "Guest";
"Set your goal weight to track progress" = "Set your goal weight to track progress";
"No exercise recorded this week" = "No exercise recorded this week";
"Walking" = "Walking";
"Swimming" = "Swimming";
"Cycling" = "Cycling";

// 运动详情页面（Plan -> Exercise detail）
"min" = "min";

// Premium / 订阅相关（完整订阅入口与覆盖层）
"Premium Feature" = "Premium Feature";
"upgrade to pro" = "upgrade to pro";
"Premium Tips" = "Premium Tips";
"Upgrade to Premium to access personalized tips" = "Upgrade to Premium to access personalized tips";
"Today's Recommendations" = "Today's Recommendations";
"Upgrade to Premium" = "Upgrade to Premium";
"Close" = "Close";
"Unlock Premium" = "Unlock Premium";
"View your complete 30-day weight trend" = "View your complete 30-day weight trend";
"Get Premium" = "Get Premium";
"Subscribe to unlock personalized health goals" = "Subscribe to unlock personalized health goals";
"Get personalized recommendations" = "Get personalized recommendations";

// 订阅恢复/试用相关（订阅弹窗/入口）
"Subscribe Now" = "Subscribe Now";
"Restoring..." = "Restoring...";
"Restore Purchase" = "Restore Purchase";
"Subscription Restored!" = "Subscription Restored!";
"No Purchases Found" = "No Purchases Found";
"Purchase failed or was cancelled" = "Purchase failed or was cancelled";

// 详细食物分析页面（DetailedFoodAnalysisView）
"Loading food analysis..." = "Loading food analysis...";
"Image not available" = "Image not available";
"No image available" = "No image available";
"This meal has excellent nutritional value!" = "This meal has excellent nutritional value!";
"This meal has good nutritional value." = "This meal has good nutritional value.";
"Water" = "Water";
"Fiber" = "Fiber";
"Sugar" = "Sugar";
"Analysis Details" = "Analysis Details";
"Failed to load food analysis" = "Failed to load food analysis";
"Load Error" = "Load Error";
"Unknown error occurred" = "Unknown error occurred";
"Please login to view food details" = "Please login to view food details";

// 食物分析页面和食物日记页面相关文本
"Uploading..." = "Uploading...";
"This meal contains %@ calories" = "This meal contains %@ calories";
"This meal contains %@%% of your daily calorie allowance" = "This meal contains %@%% of your daily calorie allowance";
"Retry" = "Retry";

// 食物分析页面营养素相关文本
"Protein" = "Protein";
"Carbs" = "Carbs";
"Fat" = "Fat";
"Calories" = "Calories";
"Water" = "Water";
"Fiber" = "Fiber";
"Sugar" = "Sugar";

// 食物分析页面其他相关文本
"Loading image..." = "Loading image...";
"Analysis in Progress" = "Analysis in Progress";
"Estimated Calories" = "Estimated Calories";
"Water Content" = "Water Content";
"Total Fat" = "Total Fat";
"Analyzing additional nutrients..." = "Analyzing additional nutrients...";
"Analysis Error" = "Analysis Error";
"Analyzing food image..." = "Analyzing food image...";
"Position food in the frame" = "Position food in the frame";
"For best results, ensure good lighting" = "For best results, ensure good lighting";
"Choose Image Source" = "Choose Image Source";
"Select how you want to add your image" = "Select how you want to add your image";
"Take Photo" = "Take Photo";
"Choose from Gallery" = "Choose from Gallery";
"Go to Settings" = "Go to Settings";
"Device Not Supported" = "Device Not Supported";
"Limited photo access requires iOS 14 or later.\nPlease update your device to use this feature." = "Limited photo access requires iOS 14 or later.\nPlease update your device to use this feature.";

// 通用按钮和操作文本
"Cancel" = "Cancel";
"Close" = "Close";

// Profile 页面 - 会员状态细节
"Valid until " = "Valid until ";
"Total subscription: " = "Total subscription: ";
" days" = " days";
" days remaining" = " days remaining";

// Profile 页面 - 非会员/游客提示（补充）

// 修改密码页面（Profile -> Password Management）
"Current Password" = "Current Password";
"Enter current password" = "Enter current password";
"Enter new password" = "Enter new password";
"Confirm New Password" = "Confirm New Password";
"Confirm new password" = "Confirm new password";
"Update Password" = "Update Password";
"Your password will be encrypted and stored securely" = "Your password will be encrypted and stored securely";
"Password Updated" = "Password Updated";
"Your password has been successfully updated. You will be logged out and need to sign in again with your new password." = "Your password has been successfully updated. You will be logged out and need to sign in again with your new password.";
"Password Update Failed" = "Password Update Failed";

// 删除账户页面（Profile -> Delete Account）
"Account Operation" = "Account Operation";
"Warning" = "Warning";
"This action cannot be undone. All your data will be permanently deleted." = "This action cannot be undone. All your data will be permanently deleted.";
"Why are you leaving?" = "Why are you leaving?";
"Select a reason" = "Select a reason";
"I found a better alternative" = "I found a better alternative";
"App features don't meet my needs" = "App features don't meet my needs";
"I no longer need this type of app" = "I no longer need this type of app";
"App performance or stability issues" = "App performance or stability issues";
"Privacy concerns" = "Privacy concerns";
"App is too complicated to use" = "App is too complicated to use";
"Other reason" = "Other reason";
"Confirm your password" = "Confirm your password";
"Your account has been successfully deleted. Thank you for using our service." = "Your account has been successfully deleted. Thank you for using our service.";

// 其他遗漏补充（导航标题/弹窗/按钮/占位符等）
"Login Required" = "Login Required";
"Subscription Error" = "Subscription Error";
"Analysis Result" = "Analysis Result";
"Open Settings" = "Open Settings";
"Enter your nickname" = "Enter your nickname";
"Daily Weight Record" = "Daily Weight Record";
"Exercise" = "Exercise";
"Units" = "Units";
"Subscription Management" = "Subscription Management";
"kg" = "kg";
"lbs" = "lbs";

// 新增底部文献提示文本
"View" = "View";
"Authoritative Sources of Health Information" = "Authoritative Sources of Health Information";

// 新增未本地化的文本

// StreakCardView 连续打卡相关
"Start Your Journey!" = "Start Your Journey!";
"Set a goal to begin tracking" = "Set a goal to begin tracking";
"Back on Track!" = "Back on Track!";
"days since your last break" = "days since your last break";
"Keep Going!" = "Keep Going!";
"Current streak: 7days" = "Current streak: 7days";
"Fantastic Progress!" = "Fantastic Progress!";
"Current streak: 15 days" = "Current streak: 15 days";
"Nearly There!" = "Nearly There!";
"Just 3 days to reach 20 days streak" = "Just 3 days to reach 20 days streak";
"You're On Fire!" = "You're On Fire!";
"Current streak: 30 days" = "Current streak: 30 days";
"Outstanding!" = "Outstanding!";
"You've reached 50 days total" = "You've reached 50 days total";
"Don't Give Up!" = "Don't Give Up!";
"Streak reset - Start fresh today" = "Streak reset - Start fresh today";
"Welcome Back!" = "Welcome Back!";
"You've taken a break, but you're back on track now" = "You've taken a break, but you're back on track now";
"You've been consistent, keep it up!" = "You've been consistent, keep it up!";
"Amazing!" = "Amazing!";
"Keep going! Every day counts!" = "Keep going! Every day counts!";

// AI建议和提示相关
"This Week's Tips" = "This Week's Tips";
"No tips available" = "No tips available";
"No Today's recommendations available" = "No Today's recommendations available";

// Premium功能相关
"This Week's Nutrition" = "This Week's Nutrition";
"Premium Feature" = "Premium Feature";
"upgrade to pro" = "upgrade to pro";
"Recommended Activities" = "Recommended Activities";
"Premium Tips" = "Premium Tips";
"Upgrade to Premium to access personalized tips" = "Upgrade to Premium to access personalized tips";
"This Week's Exercise Calories" = "This Week's Exercise Calories";

// OnboardingView 引导页面
"Let's Get Started" = "Let's Get Started";
"Help us create your personalized weight management plan" = "Help us create your personalized weight management plan";
"Weight Goal" = "Weight Goal";
"Set your target weight" = "Set your target weight";
"Your Goal Timeline" = "Your Goal Timeline";
"Personal Details" = "Personal Details";
"Gender" = "Gender";
"Male" = "Male";
"Female" = "Female";
"Current Weight" = "Current Weight";
"Enter weight" = "Enter weight";
"Age" = "Age";
"Enter age" = "Enter age";
"Height" = "Height";
"Enter height" = "Enter height";
"These details help us provide more accurate weight management recommendations" = "These details help us provide more accurate weight management recommendations";
"Create My Plan" = "Create My Plan";
"Skip for Now" = "Skip for Now";

// 其他常用文本
"Confirm" = "Confirm";
"weeks" = "weeks";
"days" = "days";
"Start" = "Start";
"Goal" = "Goal";
"Current" = "Current";
"Target" = "Target";
"Progress" = "Progress";
"Complete" = "Complete";
"Continue" = "Continue";
"Next" = "Next";
"Previous" = "Previous";
"Done" = "Done";
"Back" = "Back";
"Finish" = "Finish";


// 时间相关
"AM" = "AM";
"PM" = "PM";
"Today" = "Today";
"Yesterday" = "Yesterday";
"Tomorrow" = "Tomorrow";
"This Week" = "This Week";
"Last Week" = "Last Week";
"Next Week" = "Next Week";
"This Month" = "This Month";

// 状态相关
"Loading" = "Loading";
"Saving" = "Saving";
"Updating" = "Updating";
"Deleting" = "Deleting";
"Success" = "Success";
"Failed" = "Failed";
"Completed" = "Completed";
"Pending" = "Pending";
"Active" = "Active";
"Inactive" = "Inactive";

// 通用动作
"Add" = "Add";
"Edit" = "Edit";
"Delete" = "Delete";
"Update" = "Update";
"Remove" = "Remove";
"Clear" = "Clear";
"Reset" = "Reset";
"Refresh" = "Refresh";
"Search" = "Search";
"Filter" = "Filter";
"Sort" = "Sort";
"Share" = "Share";
"Export" = "Export";
"Import" = "Import";

// 表单验证
"Required" = "Required";
"Optional" = "Optional";
"Valid" = "Valid";
"Invalid" = "Invalid";
"Too short" = "Too short";
"Too long" = "Too long";
"Format error" = "Format error";

// 网络状态
"Connected" = "Connected";
"Disconnected" = "Disconnected";
"Connecting" = "Connecting";
"Reconnecting" = "Reconnecting";
"Timeout" = "Timeout";
"No internet" = "No internet";

// 权限相关
"Permission required" = "Permission required";
"Access denied" = "Access denied";
"Camera permission" = "Camera permission";
"Photo library permission" = "Photo library permission";
"Notification permission" = "Notification permission";

// 数据相关
"No data" = "No data";
"Empty" = "Empty";
"Not available" = "Not available";
"Unknown" = "Unknown";
"None" = "None";
"All" = "All";
"Other" = "Other";
