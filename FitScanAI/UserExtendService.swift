import Foundation

class UserExtendService {
    static let shared = UserExtendService()
    
    private let baseURL = "https://fsai.pickgoodspro.com"
    
    private init() {}
    
    // 从后端API获取用户是否为新用户
    func getUserExtendInfo(userData: UserData, completion: @escaping (Result<Bool, Error>) -> Void) {
        // 构建URL
        let urlString = "\(baseURL)/ns/app/user-extends/user"
        guard let url = URL(string: urlString) else {
            let error = NSError(domain: "UserExtendServiceError", code: 0, userInfo: [NSLocalizedDescriptionKey: "无效的URL"])
            completion(.failure(error))
            return
        }
        
        // 创建请求
        var request = URLRequest(url: url)
        request.httpMethod = "GET"
        
        // 添加授权头
        guard let accessToken = userData.accessToken as String?, !accessToken.isEmpty else {
            let error = NSError(domain: "UserExtendServiceError", code: 401, userInfo: [NSLocalizedDescriptionKey: "授权令牌缺失"])
            completion(.failure(error))
            return
        }
        request.addValue("Bearer \(accessToken)", forHTTPHeaderField: "Authorization")
        
        // 打印请求信息
        print("🌐 获取用户扩展信息 [GET] \(urlString)")
        print("🔑 请求头: \(request.allHTTPHeaderFields ?? [:])")
        
        // 发送请求
        URLSession.shared.dataTask(with: request) { data, response, error in
            if let error = error {
                print("❌ 获取用户扩展信息错误: \(error.localizedDescription)")
                completion(.failure(error))
                return
            }
            
            guard let data = data else {
                let error = NSError(domain: "UserExtendServiceError", code: 0, userInfo: [NSLocalizedDescriptionKey: "服务器返回空数据"])
                print("❌ 获取用户扩展信息错误: 服务器返回空数据")
                completion(.failure(error))
                return
            }
            
            // 打印响应数据（调试用）
            if let jsonString = String(data: data, encoding: .utf8) {
                print("📊 获取用户扩展信息响应: \(jsonString)")
            }
            
            do {
                // 解析JSON响应
                let decoder = JSONDecoder()
                let response = try decoder.decode(UserExtendResponse.self, from: data)
                
                if response.code == 0 {
                    if let userExtend = response.data {
                        // 成功获取用户扩展信息
                        print("✅ 获取用户扩展信息成功，用户是否为新用户: \(userExtend.isNewUser)")
                        
                        // 返回用户是否为新用户的状态
                        completion(.success(userExtend.isNewUser))
                    } else {
                        let error = NSError(domain: "UserExtendServiceError", code: 0, userInfo: [NSLocalizedDescriptionKey: "响应数据为空"])
                        completion(.failure(error))
                    }
                } else {
                    // API返回错误
                    let error = NSError(domain: "UserExtendServiceError", code: response.code, userInfo: [NSLocalizedDescriptionKey: response.message ?? "获取用户扩展信息失败"])
                    print("❌ 获取用户扩展信息API错误: \(response.message ?? "未知错误")")
                    completion(.failure(error))
                }
            } catch {
                print("❌ 解析用户扩展信息响应失败: \(error.localizedDescription)")
                completion(.failure(error))
            }
        }.resume()
    }
    
    // 设置用户不再是新用户
    func setUserIsNotNew(userData: UserData, completion: @escaping (Result<Bool, Error>) -> Void) {
        // 构建URL
        let urlString = "\(baseURL)/ns/app/user-extends"
        guard let url = URL(string: urlString) else {
            let error = NSError(domain: "UserExtendServiceError", code: 0, userInfo: [NSLocalizedDescriptionKey: "无效的URL"])
            completion(.failure(error))
            return
        }
        
        // 创建请求
        var request = URLRequest(url: url)
        request.httpMethod = "POST"
        request.addValue("application/json", forHTTPHeaderField: "Content-Type")
        
        // 添加授权头
        guard let accessToken = userData.accessToken as String?, !accessToken.isEmpty else {
            let error = NSError(domain: "UserExtendServiceError", code: 401, userInfo: [NSLocalizedDescriptionKey: "授权令牌缺失"])
            completion(.failure(error))
            return
        }
        request.addValue("Bearer \(accessToken)", forHTTPHeaderField: "Authorization")
        
        // 准备请求体
        let requestBody = ["isNewUser": false]
        
        do {
            let jsonData = try JSONSerialization.data(withJSONObject: requestBody, options: [])
            request.httpBody = jsonData
        } catch {
            print("❌ 序列化请求体失败: \(error.localizedDescription)")
            completion(.failure(error))
            return
        }
        
        // 打印请求信息
        print("🌐 设置用户不是新用户 [POST] \(urlString)")
        print("🔑 请求头: \(request.allHTTPHeaderFields ?? [:])")
        print("📝 请求体: \(requestBody)")
        
        // 发送请求
        URLSession.shared.dataTask(with: request) { data, response, error in
            if let error = error {
                print("❌ 设置用户不是新用户错误: \(error.localizedDescription)")
                completion(.failure(error))
                return
            }
            
            guard let data = data else {
                let error = NSError(domain: "UserExtendServiceError", code: 0, userInfo: [NSLocalizedDescriptionKey: "服务器返回空数据"])
                print("❌ 设置用户不是新用户错误: 服务器返回空数据")
                completion(.failure(error))
                return
            }
            
            // 打印响应数据（调试用）
            if let jsonString = String(data: data, encoding: .utf8) {
                print("📊 设置用户不是新用户响应: \(jsonString)")
            }
            
            do {
                // 解析JSON响应 - 使用新的响应类型处理布尔值data
                let decoder = JSONDecoder()
                let response = try decoder.decode(UserExtendBoolResponse.self, from: data)
                
                if response.code == 0 {
                    // 成功设置用户不是新用户
                    print("✅ 设置用户不是新用户成功")
                    completion(.success(true))
                } else {
                    // API返回错误
                    let error = NSError(domain: "UserExtendServiceError", code: response.code, userInfo: [NSLocalizedDescriptionKey: response.message ?? "设置用户不是新用户失败"])
                    print("❌ 设置用户不是新用户API错误: \(response.message ?? "未知错误")")
                    completion(.failure(error))
                }
            } catch {
                print("❌ 解析设置用户不是新用户响应失败: \(error.localizedDescription)")
                completion(.failure(error))
            }
        }.resume()
    }
    
    // 快速检查用户是否为新用户的便捷方法
    func isNewUser(completion: @escaping (Result<Bool, Error>) -> Void) {
        // 创建一个默认的UserData实例用于获取token
        let userData = UserData()
        
        if userData.accessToken.isEmpty {
            // 如果没有令牌，视为新用户
            completion(.success(true))
            return
        }
        
        // 调用完整方法获取用户状态
        getUserExtendInfo(userData: userData) { result in
            switch result {
            case .success(let isNewUser):
                completion(.success(isNewUser))
            case .failure(let error):
                // 如果出错，默认为新用户
                print("获取用户新用户状态失败，默认为新用户: \(error.localizedDescription)")
                completion(.success(true))
            }
        }
    }
}

// API响应模型 - 用于获取用户扩展信息
struct UserExtendResponse: Codable {
    let code: Int
    let message: String?
    let data: UserExtendData?
    let timestamp: Int64?
}

struct UserExtendData: Codable {
    let userId: String?
    let isNewUser: Bool
}

// 新的API响应模型 - 用于设置用户不是新用户时的布尔值响应
struct UserExtendBoolResponse: Codable {
    let code: Int
    let message: String?
    let data: Bool?
    let timestamp: Int64?
} 