import SwiftUI

struct SplashScreenView: View {
    @State private var isLoading = false
    @State private var scaleEffect: CGFloat = 0.9
    @State private var opacity: Double = 0
    @Binding var isActive: Bool
    
    var body: some View {
        ZStack {
            // 绿色背景
            Color.green
                .ignoresSafeArea()
            
            VStack(spacing: 20) {
                // App Logo
                Image("AppLogo")
                    .resizable()
                    .aspectRatio(contentMode: .fit)
                    .frame(width: 120, height: 120)
                    .clipShape(RoundedRectangle(cornerRadius: 20))
                    .shadow(radius: 10)
                    .scaleEffect(scaleEffect)
                    .opacity(opacity)
                
                // App 名称
                Text("FitScanAI")
                    .font(.system(size: 32, weight: .bold))
                    .foregroundColor(.white)
                    .padding(.top, 10)
                    .opacity(opacity)
                
                // 加载指示器
                if isLoading {
                    ProgressView()
                        .progressViewStyle(CircularProgressViewStyle(tint: .white))
                        .scaleEffect(1.5)
                        .padding(.top, 20)
                }
            }
        }
        .onAppear {
            // 统计
            XDTrackTool.shared.appear("启动页面")

            // 启动动画
            withAnimation(.easeInOut(duration: 0.8)) {
                scaleEffect = 1.0
                opacity = 1.0
            }
            
            // 显示加载指示器
            DispatchQueue.main.asyncAfter(deadline: .now() + 0.8) {
                isLoading = true
            }
            
            // 延迟后自动进入应用
            DispatchQueue.main.asyncAfter(deadline: .now() + 2.0) {
                withAnimation {
                    isActive = true
                }
            }
        }
    }
}

#Preview {
    SplashScreenView(isActive: .constant(false))
} 