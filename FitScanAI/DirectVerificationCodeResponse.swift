import Foundation

// 直接验证码响应模型 - 匹配实际API返回格式
struct DirectVerificationCodeResponse: Codable {
    let identity: String?
    let expiredAt: String?
    let validated: Bool?
    
    // 将ISO 8601日期字符串转换为Date对象
    var expiredAtDate: Date? {
        guard let expiredAt = expiredAt else { return nil }
        let formatter = DateFormatter()
        formatter.dateFormat = "yyyy-MM-dd HH:mm:ss"
        return formatter.date(from: expiredAt)
    }
}