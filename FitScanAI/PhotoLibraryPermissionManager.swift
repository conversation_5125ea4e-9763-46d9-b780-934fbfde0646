import Foundation
import Photos
import PhotosUI
import UIKit
import SwiftUI

class PhotoLibraryPermissionManager: NSObject, ObservableObject {
    static let shared = PhotoLibraryPermissionManager()
    
    @Published var authorizationStatus: PHAuthorizationStatus = .notDetermined
    
    override private init() {
        super.init()
        checkCurrentStatus()
    }
    
    // MARK: - 权限状态检查
    func checkCurrentStatus() {
        authorizationStatus = PHPhotoLibrary.authorizationStatus(for: .readWrite)
    }
    
    // MARK: - 简化的权限请求方法
    func requestPhotoLibraryAccess(
        from viewController: UIViewController? = nil,
        completion: @escaping (PHAuthorizationStatus) -> Void
    ) {
        let currentStatus = PHPhotoLibrary.authorizationStatus(for: .readWrite)
        
        switch currentStatus {
        case .notDetermined:
            // 直接请求系统权限，不显示自定义说明
            requestSystemPermission(completion: completion)
            
        case .denied, .restricted:
            // 被拒绝或受限，引导用户到设置
            showSettingsRedirectAlert(from: viewController, completion: completion)
            
        case .authorized:
            // 已经有完全权限
            completion(.authorized)
            
        case .limited:
            // 有限权限，直接使用
            completion(.limited)
            
        @unknown default:
            completion(.denied)
        }
    }
    
    // MARK: - 显示设置引导对话框
    private func showSettingsRedirectAlert(
        from viewController: UIViewController?,
        completion: @escaping (PHAuthorizationStatus) -> Void
    ) {
        let alert = UIAlertController(
            title: "Photo Access Required",
            message: "Please enable photo access in Settings to select photos for nutrition analysis.",
            preferredStyle: .alert
        )
        
        alert.addAction(UIAlertAction(title: "Go to Settings", style: .default) { _ in
            if let settingsUrl = URL(string: UIApplication.openSettingsURLString) {
                UIApplication.shared.open(settingsUrl)
            }
            completion(PHPhotoLibrary.authorizationStatus(for: .readWrite))
        })
        
        alert.addAction(UIAlertAction(title: "Cancel", style: .cancel) { _ in
            completion(.denied)
        })
        
        let presenter = viewController ?? topViewController()
        DispatchQueue.main.async {
            presenter.present(alert, animated: true)
        }
    }
    
    // MARK: - 请求系统权限
    private func requestSystemPermission(completion: @escaping (PHAuthorizationStatus) -> Void) {
        PHPhotoLibrary.requestAuthorization(for: .readWrite) { status in
            DispatchQueue.main.async {
                self.authorizationStatus = status
                completion(status)
            }
        }
    }
    
    // MARK: - 工具方法
    private func topViewController() -> UIViewController {
        guard let windowScene = UIApplication.shared.connectedScenes.first as? UIWindowScene,
              let window = windowScene.windows.first(where: { $0.isKeyWindow }),
              let rootViewController = window.rootViewController else {
            return UIViewController()
        }
        
        var topController = rootViewController
        while let presentedViewController = topController.presentedViewController {
            topController = presentedViewController
        }
        
        return topController
    }
}

// MARK: - 简化版照片选择器，确保显示标准相册界面
@available(iOS 14, *)
struct StandardPhotoPicker: UIViewControllerRepresentable {
    @Binding var selectedImage: UIImage?
    @Binding var isPresented: Bool
    var onImageSelected: (() -> Void)?
    
    func makeUIViewController(context: Context) -> PHPickerViewController {
        var configuration = PHPickerConfiguration()
        
        // 使用最基本的配置，确保显示标准相册网格界面
        configuration.filter = .images
        configuration.selectionLimit = 1
        
        // 不设置任何可能导致显示问题的高级配置
        // 让系统使用默认的展示方式
        
        let picker = PHPickerViewController(configuration: configuration)
        picker.delegate = context.coordinator
        
        return picker
    }
    
    func updateUIViewController(_ uiViewController: PHPickerViewController, context: Context) {
        // 不需要更新
    }
    
    func makeCoordinator() -> Coordinator {
        Coordinator(parent: self)
    }
    
    class Coordinator: NSObject, PHPickerViewControllerDelegate {
        private let parent: StandardPhotoPicker
        
        init(parent: StandardPhotoPicker) {
            self.parent = parent
            super.init()
        }
        
        func picker(_ picker: PHPickerViewController, didFinishPicking results: [PHPickerResult]) {
            parent.isPresented = false
            
            guard let result = results.first else {
                print("📸 StandardPhotoPicker: No photo selected")
                return
            }
            
            guard result.itemProvider.canLoadObject(ofClass: UIImage.self) else {
                print("❌ StandardPhotoPicker: Cannot load as UIImage")
                return
            }
            
            print("📸 StandardPhotoPicker: 用户选择了图片，开始加载...")
            
            // 直接加载图片，不使用占位符，等真正的图片加载完成后再触发回调
            result.itemProvider.loadObject(ofClass: UIImage.self) { [weak self] object, error in
                DispatchQueue.main.async {
                    if let error = error {
                        print("❌ StandardPhotoPicker: Error loading image: \(error.localizedDescription)")
                        return
                    }
                    
                    if let image = object as? UIImage {
                        print("✅ StandardPhotoPicker: Successfully loaded image")
                        // 设置真正的图片并触发回调
                        self?.parent.selectedImage = image
                        self?.parent.onImageSelected?()
                        print("📸 StandardPhotoPicker: 已设置真实图片并触发回调")

                        // 上报相册选择事件
                        XDTrackTool.shared.trackPhotoCapture(source: "gallery")
                    } else {
                        print("❌ StandardPhotoPicker: Failed to convert to UIImage")
                    }
                }
            }
        }
    }
}

// MARK: - 优化版照片选择器，最大程度减少AX Lookup错误
@available(iOS 14, *)
struct EnhancedPhotoPicker: UIViewControllerRepresentable {
    @Binding var selectedImage: UIImage?
    @Binding var isPresented: Bool
    var onImageSelected: (() -> Void)?
    
    @StateObject private var permissionManager = PhotoLibraryPermissionManager.shared
    
    func makeUIViewController(context: Context) -> PHPickerViewController {
        var configuration = PHPickerConfiguration()
        configuration.filter = .images
        configuration.selectionLimit = 1
        
        // 修复配置 - 使用标准设置确保显示相册网格而非大图预览
        configuration.preferredAssetRepresentationMode = .automatic
        
        // 移除可能导致显示问题的iOS 15特定配置
        // 不设置 configuration.mode，使用默认值确保显示标准相册界面
        
        let picker = PHPickerViewController(configuration: configuration)
        picker.delegate = context.coordinator
        
        // 使用标准的模态展示样式
        picker.modalPresentationStyle = .pageSheet
        picker.modalTransitionStyle = .coverVertical
        
        return picker
    }
    
    func updateUIViewController(_ uiViewController: PHPickerViewController, context: Context) {
        // 避免不必要的更新，减少AX服务调用
    }
    
    func makeCoordinator() -> Coordinator {
        Coordinator(parent: self)
    }
    
    class Coordinator: NSObject, PHPickerViewControllerDelegate {
        private let parent: EnhancedPhotoPicker
        private var isProcessing = false // 防止重复处理
        private var hasDismissed = false // 防止重复关闭
        
        init(parent: EnhancedPhotoPicker) {
            self.parent = parent
            super.init()
        }
        
        func picker(_ picker: PHPickerViewController, didFinishPicking results: [PHPickerResult]) {
            // 防止重复处理
            guard !isProcessing else {
                print("📸 EnhancedPhotoPicker: Already processing, ignoring duplicate call")
                return
            }
            
            // 防止重复关闭
            guard !hasDismissed else {
                print("📸 EnhancedPhotoPicker: Already dismissed, ignoring")
                return
            }
            
            isProcessing = true
            hasDismissed = true
            print("📸 EnhancedPhotoPicker: didFinishPicking called with \(results.count) results")
            
            // 立即标记为已关闭，避免重复操作
            self.parent.isPresented = false
            
            // 检查是否有选择结果
            guard let result = results.first else {
                print("📸 EnhancedPhotoPicker: No photo selected or user cancelled")
                return
            }
            
            // 检查是否可以加载为UIImage
            guard result.itemProvider.canLoadObject(ofClass: UIImage.self) else {
                print("❌ EnhancedPhotoPicker: Selected item cannot be loaded as UIImage")
                DispatchQueue.main.asyncAfter(deadline: .now() + 0.8) {
                    self.showLoadingError(error: nil)
                }
                return
            }
            
            // 加载图片 - 使用更安全的方式，延迟处理避免与picker关闭冲突
            DispatchQueue.main.asyncAfter(deadline: .now() + 0.3) {
                self.loadImageSafely(from: result.itemProvider)
            }
        }
        
        // 安全加载图片的方法
        private func loadImageSafely(from itemProvider: NSItemProvider) {
            // 设置超时时间
            let timeoutWorkItem = DispatchWorkItem {
                DispatchQueue.main.async {
                    print("⏰ EnhancedPhotoPicker: Image loading timeout")
                    self.showLoadingError(error: NSError(domain: "PhotoLoadingError", code: -1, userInfo: [NSLocalizedDescriptionKey: "Image loading timeout"]))
                }
            }
            
            // 8秒后超时（增加超时时间，给picker更多关闭时间）
            DispatchQueue.main.asyncAfter(deadline: .now() + 8.0, execute: timeoutWorkItem)
            
            itemProvider.loadObject(ofClass: UIImage.self) { [weak self] object, error in
                guard let self = self else { return }
                
                // 取消超时任务
                timeoutWorkItem.cancel()
                
                DispatchQueue.main.async {
                    if let error = error {
                        print("❌ EnhancedPhotoPicker: Error loading image: \(error.localizedDescription)")
                        self.showLoadingError(error: error)
                        return
                    }
                    
                    if let image = object as? UIImage {
                        print("✅ EnhancedPhotoPicker: Successfully loaded image with size: \(image.size)")
                        
                        // 验证图片有效性
                        guard image.size.width > 0 && image.size.height > 0 else {
                            print("❌ EnhancedPhotoPicker: Invalid image dimensions")
                            self.showLoadingError(error: NSError(domain: "PhotoLoadingError", code: -2, userInfo: [NSLocalizedDescriptionKey: "Invalid image"]))
                            return
                        }
                        
                        self.parent.selectedImage = image
                        self.parent.onImageSelected?()
                    } else {
                        print("❌ EnhancedPhotoPicker: Failed to convert object to UIImage")
                        self.showLoadingError(error: NSError(domain: "PhotoLoadingError", code: -3, userInfo: [NSLocalizedDescriptionKey: "Unable to process image"]))
                    }
                }
            }
        }
        
        // 显示加载错误
        private func showLoadingError(error: Error?) {
            let title = "Unable to Load Photo"
            let message: String
            
            if let error = error {
                let nsError = error as NSError
                switch nsError.code {
                case -1:
                    message = "Image loading took too long. Please try again with a smaller image."
                case -2:
                    message = "The selected image appears to be corrupted. Please choose a different photo."
                case -3:
                    message = "Unable to process the selected image. Please try a different photo."
                default:
                    message = "Error: \(error.localizedDescription)"
                }
            } else {
                message = "The selected photo cannot be loaded. Please try selecting a different photo."
            }
            
            let alert = UIAlertController(
                title: title,
                message: message,
                preferredStyle: .alert
            )
            
            alert.addAction(UIAlertAction(title: "OK", style: .default))
            
            self.presentAlert(alert)
        }
        
        // 统一的alert显示方法 - 最大程度避免AX错误
        private func presentAlert(_ alert: UIAlertController) {
            // 增加延迟时间，确保PHPickerViewController完全关闭
            DispatchQueue.main.asyncAfter(deadline: .now() + 1.0) {
                // 减少辅助功能交互（iOS 14+已可用）
                alert.isModalInPresentation = true
                
                guard let windowScene = UIApplication.shared.connectedScenes
                    .compactMap({ $0 as? UIWindowScene })
                    .first(where: { $0.activationState == .foregroundActive }),
                      let window = windowScene.windows.first(where: { $0.isKeyWindow }),
                      let rootViewController = window.rootViewController else {
                    print("❌ EnhancedPhotoPicker: Unable to find root view controller for alert")
                    return
                }
                
                var topController = rootViewController
                while let presentedViewController = topController.presentedViewController {
                    topController = presentedViewController
                }
                
                // 确保在主线程上显示，并避免与其他模态视图冲突
                if Thread.isMainThread {
                    // 检查是否已经有模态视图在显示
                    if topController.presentedViewController == nil {
                        topController.present(alert, animated: true)
                    } else {
                        print("⚠️ EnhancedPhotoPicker: Another modal is already presented, skipping alert")
                    }
                } else {
                    DispatchQueue.main.async {
                        if topController.presentedViewController == nil {
                            topController.present(alert, animated: true)
                        } else {
                            print("⚠️ EnhancedPhotoPicker: Another modal is already presented, skipping alert")
                        }
                    }
                }
            }
        }
    }
} 
