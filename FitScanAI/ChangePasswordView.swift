import SwiftUI

struct ChangePasswordView: View {
    @Environment(\.presentationMode) var presentationMode
    @EnvironmentObject var userData: UserData
    
    // 用户输入数据
    @State private var currentPassword: String = ""
    @State private var newPassword: String = ""
    @State private var confirmPassword: String = ""
    @State private var isCurrentPasswordVisible: Bool = false
    @State private var isNewPasswordVisible: Bool = false
    @State private var isConfirmPasswordVisible: Bool = false
    
    // 密码要求验证
    @State private var hasEightChars: Bool = false
    @State private var hasNumbers: Bool = false
    @State private var hasUppercase: Bool = false
    @State private var hasLowercase: Bool = false
    @State private var hasSpecialChars: Bool = false
    
    // 密码匹配状态
    @State private var passwordsMatch: Bool = false
    
    // 加载状态和提示
    @State private var isLoading: Bool = false
    @State private var showAlert: Bool = false
    @State private var alertTitle: String = ""
    @State private var alertMessage: String = ""
    
    // 用于控制是否返回到登录页面
    @State private var navigateToLogin: Bool = false
    
    var body: some View {
        ZStack {
            Color.white.edgesIgnoringSafeArea(.all)
            
            ScrollView {
                VStack(alignment: .leading, spacing: 24) {
                    // 顶部间距
                    Spacer()
                        .frame(height: 20)
                    
                    // 当前密码输入
                    VStack(alignment: .leading, spacing: 8) {
                        Text("Current Password")
                            .font(.system(size: 16, weight: .medium))
                            .foregroundColor(.black)
                        
                        HStack {
                            if isCurrentPasswordVisible {
                                TextField("Enter current password", text: $currentPassword)
                                    .autocapitalization(.none)
                                    .multilineTextAlignment(.leading)
                                    .adaptivePasswordKeyboard()
                                    .padding(.vertical, 12)
                                    .padding(.leading, 12)
                            } else {
                                SecureField("Enter current password", text: $currentPassword)
                                    .autocapitalization(.none)
                                    .multilineTextAlignment(.leading)
                                    .adaptivePasswordKeyboard()
                                    .padding(.vertical, 12)
                                    .padding(.leading, 12)
                            }
                            
                            Button(action: {
                                isCurrentPasswordVisible.toggle()
                            }) {
                                Image(systemName: isCurrentPasswordVisible ? "eye.fill" : "eye.slash.fill")
                                    .foregroundColor(.gray)
                            }
                            .padding(.trailing, 12)
                        }
                        .background(Color(.systemGray6))
                        .cornerRadius(8)
                    }
                    
                    // 新密码输入
                    VStack(alignment: .leading, spacing: 8) {
                        Text("New Password")
                            .font(.system(size: 16, weight: .medium))
                            .foregroundColor(.black)
                        
                        HStack {
                            if isNewPasswordVisible {
                                TextField("Enter new password", text: $newPassword)
                                    .autocapitalization(.none)
                                    .onChange(of: newPassword) { _, newValue in
                                        validatePassword(newValue)
                                        checkPasswordsMatch()
                                    }
                                    .multilineTextAlignment(.leading)
                                    .adaptivePasswordKeyboard()
                                    .padding(.vertical, 12)
                                    .padding(.leading, 12)
                            } else {
                                SecureField("Enter new password", text: $newPassword)
                                    .autocapitalization(.none)
                                    .onChange(of: newPassword) { _, newValue in
                                        validatePassword(newValue)
                                        checkPasswordsMatch()
                                    }
                                    .multilineTextAlignment(.leading)
                                    .adaptivePasswordKeyboard()
                                    .padding(.vertical, 12)
                                    .padding(.leading, 12)
                            }
                            
                            Button(action: {
                                isNewPasswordVisible.toggle()
                            }) {
                                Image(systemName: isNewPasswordVisible ? "eye.fill" : "eye.slash.fill")
                                    .foregroundColor(.gray)
                            }
                            .padding(.trailing, 12)
                        }
                        .background(Color(.systemGray6))
                        .cornerRadius(8)
                    }
                    
                    // 密码强度指示器
                    VStack(alignment: .leading, spacing: 8) {
                        HStack(spacing: 4) {
                            ForEach(0..<5, id: \.self) { index in
                                Rectangle()
                                    .fill(passwordStrengthColor(for: index))
                                    .frame(height: 6)
                                    .cornerRadius(3)
                            }
                        }
                        
                        Text(passwordStrengthText)
                            .font(.system(size: 14, weight: .medium))
                            .foregroundColor(passwordStrengthTextColor)
                    }
                    
                    // 密码要求提示 - 修改为与ResetPasswordView一致
                    VStack(alignment: .leading, spacing: 12) {
                        Text("Password Requirements")
                            .font(.system(size: 16, weight: .medium))
                            .foregroundColor(.black)
                            .padding(.bottom, 4)
                        
                        // 至少6个字符
                        HStack {
                            Image(systemName: hasEightChars ? "checkmark.circle.fill" : "xmark.circle")
                                .foregroundColor(hasEightChars ? .green : .gray)
                            
                            Text("At least 6 characters")
                                .font(.system(size: 14))
                                .foregroundColor(hasEightChars ? .green : .gray)
                            
                            Spacer()
                        }
                        
                        // 包含数字
                        HStack {
                            Image(systemName: hasNumbers ? "checkmark.circle.fill" : "xmark.circle")
                                .foregroundColor(hasNumbers ? .green : .gray)
                            
                            Text("Include numbers")
                                .font(.system(size: 14))
                                .foregroundColor(hasNumbers ? .green : .gray)
                            
                            Spacer()
                        }
                        
                        // 包含大写字母
                        HStack {
                            Image(systemName: hasUppercase ? "checkmark.circle.fill" : "xmark.circle")
                                .foregroundColor(hasUppercase ? .green : .gray)
                            
                            Text("Include uppercase letters")
                                .font(.system(size: 14))
                                .foregroundColor(hasUppercase ? .green : .gray)
                            
                            Spacer()
                        }
                        
                        // 包含小写字母
                        HStack {
                            Image(systemName: hasLowercase ? "checkmark.circle.fill" : "xmark.circle")
                                .foregroundColor(hasLowercase ? .green : .gray)
                            
                            Text("Include lowercase letters")
                                .font(.system(size: 14))
                                .foregroundColor(hasLowercase ? .green : .gray)
                            
                            Spacer()
                        }
                        
                        // 包含特殊字符
                        HStack {
                            Image(systemName: hasSpecialChars ? "checkmark.circle.fill" : "xmark.circle")
                                .foregroundColor(hasSpecialChars ? .green : .gray)
                            
                            Text("Include special characters")
                                .font(.system(size: 14))
                                .foregroundColor(hasSpecialChars ? .green : .gray)
                            
                            Spacer()
                        }
                    }
                    .padding()
                    .frame(maxWidth: .infinity)
                    .background(Color(.systemGray6).opacity(0.5))
                    .cornerRadius(8)
                    
                    // 确认新密码输入
                    VStack(alignment: .leading, spacing: 8) {
                        Text("Confirm New Password")
                            .font(.system(size: 16, weight: .medium))
                            .foregroundColor(.black)
                        
                        HStack {
                            if isConfirmPasswordVisible {
                                TextField("Confirm new password", text: $confirmPassword)
                                    .autocapitalization(.none)
                                    .onChange(of: confirmPassword) { _, _ in
                                        checkPasswordsMatch()
                                    }
                                    .multilineTextAlignment(.leading)
                                    .adaptivePasswordKeyboard()
                                    .padding(.vertical, 12)
                                    .padding(.leading, 12)
                            } else {
                                SecureField("Confirm new password", text: $confirmPassword)
                                    .autocapitalization(.none)
                                    .onChange(of: confirmPassword) { _, _ in
                                        checkPasswordsMatch()
                                    }
                                    .multilineTextAlignment(.leading)
                                    .adaptivePasswordKeyboard()
                                    .padding(.vertical, 12)
                                    .padding(.leading, 12)
                            }
                            
                            Button(action: {
                                isConfirmPasswordVisible.toggle()
                            }) {
                                Image(systemName: isConfirmPasswordVisible ? "eye.fill" : "eye.slash.fill")
                                    .foregroundColor(.gray)
                            }
                            .padding(.trailing, 12)
                        }
                        .background(Color(.systemGray6))
                        .cornerRadius(8)
                    }
                    
                    // 密码匹配状态 - 修改为与ResetPasswordView一致
                    if !confirmPassword.isEmpty {
                    HStack {
                            Image(systemName: passwordsMatch ? "checkmark.circle.fill" : "xmark.circle.fill")
                                .foregroundColor(passwordsMatch ? .green : .red)
                        
                            Text(passwordsMatch ? "Passwords match" : "Passwords don't match")
                            .font(.system(size: 14))
                                .foregroundColor(passwordsMatch ? .green : .red)
                        
                        Spacer()
                        }
                    }
                    
                    // 更新密码按钮
                    Button(action: {
                        updatePassword()
                    }) {
                        ZStack {
                            Rectangle()
                                .fill(isPasswordValid() && passwordsMatch && !currentPassword.isEmpty ? Color.green : Color.gray)
                                .cornerRadius(8)
                            
                            if isLoading {
                                ProgressView()
                                    .progressViewStyle(CircularProgressViewStyle(tint: .white))
                            } else {
                                Text("Update Password")
                                    .font(.system(size: 16, weight: .semibold))
                                    .foregroundColor(.white)
                            }
                        }
                        .frame(height: 50)
                    }
                    .disabled(isLoading || !isPasswordValid() || !passwordsMatch || currentPassword.isEmpty)
                    .padding(.top, 12)
                    
                    // 安全提示
                    HStack(spacing: 12) {
                        Image(systemName: "lock.fill")
                            .foregroundColor(.green)
                        
                        Text("Your password will be encrypted and stored securely")
                            .font(.system(size: 14))
                            .foregroundColor(.gray)
                            .lineLimit(1)
                            .minimumScaleFactor(0.8)
                    }
                    .padding(.top, 8)
                    
                    Spacer()
                }
                .padding(.horizontal, 24)
                .padding(.bottom, 30)
                .frame(maxWidth: .infinity, maxHeight: .infinity)
                .contentShape(Rectangle())
                .onTapGesture {
                    UIApplication.shared.sendAction(#selector(UIResponder.resignFirstResponder), to: nil, from: nil, for: nil)
                }
            }
            }
            .alert(isPresented: $showAlert) {
                Alert(
                    title: Text(alertTitle),
                    message: Text(alertMessage),
                    dismissButton: .default(Text("OK"))
                )
            }
            .onAppear {
                // 统计
                XDTrackTool.shared.appear("修改密码页面")
            }
        .ignoresSafeArea(.keyboard, edges: .bottom) // 忽略键盘底部安全区域
    }
    
    // 验证密码 - 修改为与ResetPasswordView一致
    private func validatePassword(_ password: String) {
        // 至少6个字符
        hasEightChars = password.count >= 6
        
        // 包含数字
        hasNumbers = password.contains(where: { $0.isNumber })
        
        // 包含大写字母
        hasUppercase = password.contains(where: { $0.isUppercase })
        
        // 包含小写字母
        hasLowercase = password.contains(where: { $0.isLowercase })
        
        // 包含特殊字符
        let specialCharacters = CharacterSet(charactersIn: "`!@#$%^&*?()-+_=;':\",./<>")
        hasSpecialChars = password.contains { char in
            guard let scalar = String(char).unicodeScalars.first else { return false }
            return specialCharacters.contains(scalar)
        }
    }
    
    // 检查两个密码是否匹配
    private func checkPasswordsMatch() {
        passwordsMatch = !confirmPassword.isEmpty && confirmPassword == newPassword
    }
    
    // 密码是否满足所有要求
    private func isPasswordValid() -> Bool {
        return hasEightChars && hasNumbers && hasUppercase && hasLowercase && hasSpecialChars
    }
    
    // 密码强度文本
    private var passwordStrengthText: String {
        let metRequirements = [hasEightChars, hasNumbers, hasUppercase, hasLowercase, hasSpecialChars].filter { $0 }.count
        
        switch metRequirements {
        case 0:
            return ""
        case 1:
            return "Weak password"
        case 2, 3:
            return "Fair password"
        case 4:
            return "Good password"
        case 5:
            return "Strong password"
        default:
            return ""
        }
    }
    
    // 密码强度文本颜色
    private var passwordStrengthTextColor: Color {
        let metRequirements = [hasEightChars, hasNumbers, hasUppercase, hasLowercase, hasSpecialChars].filter { $0 }.count
        
        switch metRequirements {
        case 0:
            return .gray
        case 1:
            return .red
        case 2, 3:
            return .orange
        case 4:
            return .yellow
        case 5:
            return .green
        default:
            return .gray
        }
    }
    
    // 密码强度颜色（用于进度条）
    private func passwordStrengthColor(for index: Int) -> Color {
        let metRequirements = [hasEightChars, hasNumbers, hasUppercase, hasLowercase, hasSpecialChars].filter { $0 }.count
        
        if index < metRequirements {
            switch metRequirements {
            case 1:
                return .red
            case 2, 3:
                return .orange
            case 4:
                return .yellow
            case 5:
                return .green
            default:
                return Color(.systemGray5)
            }
        } else {
            return Color(.systemGray5)
        }
    }
    
    // 更新密码
    private func updatePassword() {
        // 验证输入
        if currentPassword.isEmpty || newPassword.isEmpty || confirmPassword.isEmpty {
            alertTitle = "Input Error"
            alertMessage = "Please fill in all fields"
            showAlert = true
            return
        }
        
        // 验证密码是否满足所有要求
        if !hasEightChars || !hasNumbers || !hasUppercase || !hasLowercase || !hasSpecialChars {
            alertTitle = "Weak Password"
            alertMessage = "Your new password does not meet all requirements. Please choose a stronger password."
            showAlert = true
            return
        }
        
        // 验证两次密码输入是否一致
        if !passwordsMatch {
            alertTitle = "Password Mismatch"
            alertMessage = "The passwords you entered do not match. Please try again."
            showAlert = true
            return
        }
        
        // 开始加载
        isLoading = true
        
        // 调用API更新密码
        UserService.shared.changePassword(currentPassword: currentPassword, newPassword: newPassword, userData: userData) { result in
            self.isLoading = false
            
            switch result {
            case .success:
                // 更新成功
                self.alertTitle = "Password Updated"
                self.alertMessage = "Your password has been successfully updated. You will be logged out and need to sign in again with your new password."
                self.showAlert = true
                
                // 3秒后退出登录并返回到登录页面
                DispatchQueue.main.asyncAfter(deadline: .now() + 3) {
                    // 清除用户数据并退出登录
                    self.userData.logout()
                    
                    // 发送用户退出登录通知
                    NotificationCenter.default.post(name: Notification.Name("UserLoggedOut"), object: nil)
                    
                    // 设置导航到登录页面
                    self.navigateToLogin = true
                    
                    // 关闭当前页面
                    self.presentationMode.wrappedValue.dismiss()
                }
                
            case .failure(let error):
                // 处理错误
                self.alertTitle = "Password Update Failed"
                
                let nsError = error as NSError
                
                // 解析后端返回的具体错误信息
                if let errorMessage = self.parseBackendErrorMessage(from: nsError) {
                    self.alertMessage = errorMessage
                } else {
                    // 根据HTTP状态码提供友好的错误提示
                    switch nsError.code {
                    case 400:
                        self.alertMessage = "Invalid request. Please check your password requirements and try again."
                    case 401:
                        self.alertMessage = "Authentication failed. Your current password is incorrect or your session has expired. Please try again."
                    case 403:
                        self.alertMessage = "Access denied. You don't have permission to change the password."
                    case 404:
                        self.alertMessage = "User account not found. Please contact support."
                    case 409:
                        self.alertMessage = "The new password cannot be the same as your current password."
                    case 422:
                        self.alertMessage = "The new password does not meet security requirements. Please choose a stronger password."
                    case 429:
                        self.alertMessage = "Too many password change attempts. Please wait a few minutes before trying again."
                    case 500:
                        self.alertMessage = "Server error occurred. Please try again later or contact support."
                    case 503:
                        self.alertMessage = "Service temporarily unavailable. Please try again later."
                    default:
                        if nsError.localizedDescription.contains("current password") || nsError.localizedDescription.contains("当前密码") {
                            self.alertMessage = "The current password you entered is incorrect. Please try again."
                        } else if nsError.localizedDescription.contains("new password") || nsError.localizedDescription.contains("新密码") {
                            self.alertMessage = "The new password is invalid. Please ensure it meets all requirements."
                        } else if nsError.localizedDescription.contains("network") || nsError.localizedDescription.contains("connection") {
                            self.alertMessage = "Network connection error. Please check your internet connection and try again."
                        } else {
                            self.alertMessage = "Password update failed. Please try again later."
                        }
                    }
                }
                
                // 如果是授权问题，可能需要重新登录
                if nsError.code == 401 {
                    DispatchQueue.main.asyncAfter(deadline: .now() + 2) {
                        // 清除用户数据
                        self.userData.logout()
                        
                        // 发送用户退出登录通知
                        NotificationCenter.default.post(name: Notification.Name("UserLoggedOut"), object: nil)
                        
                        // 设置导航到登录页面
                        self.navigateToLogin = true
                    }
                }
                
                self.showAlert = true
                print("密码更新失败: \(error)")
            }
        }
    }
    
    // 解析后端返回的错误信息
    private func parseBackendErrorMessage(from error: NSError) -> String? {
        let errorDescription = error.localizedDescription
        
        // 尝试从错误描述中提取JSON格式的错误信息
        if let data = errorDescription.data(using: .utf8),
           let json = try? JSONSerialization.jsonObject(with: data) as? [String: Any] {
            
            // 检查常见的错误字段
            if let message = json["message"] as? String {
                return translateErrorMessage(message)
            } else if let error = json["error"] as? String {
                return translateErrorMessage(error)
            } else if let detail = json["detail"] as? String {
                return translateErrorMessage(detail)
            }
        }
        
        // 尝试从错误描述中提取包含"响应内容"的部分
        if errorDescription.contains("响应内容:") {
            let components = errorDescription.components(separatedBy: "响应内容:")
            if components.count > 1 {
                let responseContent = components[1].trimmingCharacters(in: .whitespacesAndNewlines)
                if let data = responseContent.data(using: .utf8),
                   let json = try? JSONSerialization.jsonObject(with: data) as? [String: Any] {
                    
                    if let message = json["message"] as? String {
                        return translateErrorMessage(message)
                    } else if let error = json["error"] as? String {
                        return translateErrorMessage(error)
                    }
                }
            }
        }
        
        return nil
    }
    
    // 翻译错误信息为用户友好的英文提示
    private func translateErrorMessage(_ message: String) -> String {
        let lowercaseMessage = message.lowercased()
        
        if lowercaseMessage.contains("current password") || lowercaseMessage.contains("当前密码") || lowercaseMessage.contains("原密码") {
            return "The current password you entered is incorrect. Please try again."
        } else if lowercaseMessage.contains("new password") || lowercaseMessage.contains("新密码") {
            return "The new password is invalid. Please ensure it meets all security requirements."
        } else if lowercaseMessage.contains("password too weak") || lowercaseMessage.contains("密码强度") {
            return "The new password is too weak. Please choose a stronger password with at least 8 characters, including numbers, uppercase letters, and special characters."
        } else if lowercaseMessage.contains("same password") || lowercaseMessage.contains("相同密码") {
            return "The new password cannot be the same as your current password."
        } else if lowercaseMessage.contains("unauthorized") || lowercaseMessage.contains("未授权") {
            return "Authentication failed. Please log in again."
        } else if lowercaseMessage.contains("forbidden") || lowercaseMessage.contains("禁止") {
            return "Access denied. You don't have permission to perform this action."
        } else if lowercaseMessage.contains("not found") || lowercaseMessage.contains("未找到") {
            return "User account not found. Please contact support."
        } else if lowercaseMessage.contains("too many") || lowercaseMessage.contains("频繁") {
            return "Too many attempts. Please wait a few minutes before trying again."
        } else if lowercaseMessage.contains("server error") || lowercaseMessage.contains("服务器错误") {
            return "Server error occurred. Please try again later."
        } else {
            // 如果无法识别具体错误，返回原始消息（如果是英文）或通用提示
            if message.range(of: "[a-zA-Z]", options: .regularExpression) != nil {
                return message
            } else {
                return "Password update failed. Please try again later."
            }
        }
    }
}

// 为了在预览中显示
struct ChangePasswordView_Previews: PreviewProvider {
    static var previews: some View {
        ChangePasswordView()
    }
}

// 虚拟键盘适配扩展
extension View {
    func adaptivePasswordKeyboard() -> some View {
        #if os(iOS)
        return self
            .autocorrectionDisabled(true)
            .textInputAutocapitalization(.never)
        #else
        return self
        #endif
    }
}
